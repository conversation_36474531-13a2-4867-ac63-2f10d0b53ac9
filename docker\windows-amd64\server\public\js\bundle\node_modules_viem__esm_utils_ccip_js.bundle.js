"use strict";
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkfula_webui"] = self["webpackChunkfula_webui"] || []).push([["node_modules_viem__esm_utils_ccip_js"],{

/***/ "./node_modules/viem/_esm/errors/ccip.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/ccip.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OffchainLookupError: () => (/* binding */ OffchainLookupError),\n/* harmony export */   OffchainLookupResponseMalformedError: () => (/* binding */ OffchainLookupResponseMalformedError),\n/* harmony export */   OffchainLookupSenderMismatchError: () => (/* binding */ OffchainLookupSenderMismatchError)\n/* harmony export */ });\n/* harmony import */ var _utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/stringify.js */ \"./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"./node_modules/viem/_esm/errors/utils.js\");\n\n\n\nclass OffchainLookupError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ callbackSelector, cause, data, extraData, sender, urls, }) {\n        super(cause.shortMessage ||\n            'An error occurred while fetching for an offchain result.', {\n            cause,\n            metaMessages: [\n                ...(cause.metaMessages || []),\n                cause.metaMessages?.length ? '' : [],\n                'Offchain Gateway Call:',\n                urls && [\n                    '  Gateway URL(s):',\n                    ...urls.map((url) => `    ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`),\n                ],\n                `  Sender: ${sender}`,\n                `  Data: ${data}`,\n                `  Callback selector: ${callbackSelector}`,\n                `  Extra data: ${extraData}`,\n            ].flat(),\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'OffchainLookupError'\n        });\n    }\n}\nclass OffchainLookupResponseMalformedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ result, url }) {\n        super('Offchain gateway response is malformed. Response data must be a hex value.', {\n            metaMessages: [\n                `Gateway URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                `Response: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(result)}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'OffchainLookupResponseMalformedError'\n        });\n    }\n}\nclass OffchainLookupSenderMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ sender, to }) {\n        super('Reverted sender address does not match target contract address (`to`).', {\n            metaMessages: [\n                `Contract address: ${to}`,\n                `OffchainLookup sender address: ${sender}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'OffchainLookupSenderMismatchError'\n        });\n    }\n}\n//# sourceMappingURL=ccip.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/errors/ccip.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/utils/address/isAddressEqual.js":
/*!****************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/address/isAddressEqual.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAddressEqual: () => (/* binding */ isAddressEqual)\n/* harmony export */ });\n/* harmony import */ var _errors_address_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/address.js */ \"./node_modules/viem/_esm/errors/address.js\");\n/* harmony import */ var _isAddress_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isAddress.js */ \"./node_modules/viem/_esm/utils/address/isAddress.js\");\n\n\nfunction isAddressEqual(a, b) {\n    if (!(0,_isAddress_js__WEBPACK_IMPORTED_MODULE_0__.isAddress)(a, { strict: false }))\n        throw new _errors_address_js__WEBPACK_IMPORTED_MODULE_1__.InvalidAddressError({ address: a });\n    if (!(0,_isAddress_js__WEBPACK_IMPORTED_MODULE_0__.isAddress)(b, { strict: false }))\n        throw new _errors_address_js__WEBPACK_IMPORTED_MODULE_1__.InvalidAddressError({ address: b });\n    return a.toLowerCase() === b.toLowerCase();\n}\n//# sourceMappingURL=isAddressEqual.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/utils/address/isAddressEqual.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/utils/ccip.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/utils/ccip.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ccipRequest: () => (/* binding */ ccipRequest),\n/* harmony export */   offchainLookup: () => (/* binding */ offchainLookup),\n/* harmony export */   offchainLookupAbiItem: () => (/* binding */ offchainLookupAbiItem),\n/* harmony export */   offchainLookupSignature: () => (/* binding */ offchainLookupSignature)\n/* harmony export */ });\n/* harmony import */ var _actions_public_call_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../actions/public/call.js */ \"./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors/ccip.js */ \"./node_modules/viem/_esm/errors/ccip.js\");\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/request.js */ \"./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abi/decodeErrorResult.js */ \"./node_modules/viem/_esm/utils/abi/decodeErrorResult.js\");\n/* harmony import */ var _abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abi/encodeAbiParameters.js */ \"./node_modules/viem/_esm/utils/abi/encodeAbiParameters.js\");\n/* harmony import */ var _address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./address/isAddressEqual.js */ \"./node_modules/viem/_esm/utils/address/isAddressEqual.js\");\n/* harmony import */ var _data_concat_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./data/concat.js */ \"./node_modules/viem/_esm/utils/data/concat.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./data/isHex.js */ \"./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./stringify.js */ \"./node_modules/viem/_esm/utils/stringify.js\");\n\n\n\n\n\n\n\n\n\nconst offchainLookupSignature = '0x556f1830';\nconst offchainLookupAbiItem = {\n    name: 'OffchainLookup',\n    type: 'error',\n    inputs: [\n        {\n            name: 'sender',\n            type: 'address',\n        },\n        {\n            name: 'urls',\n            type: 'string[]',\n        },\n        {\n            name: 'callData',\n            type: 'bytes',\n        },\n        {\n            name: 'callbackFunction',\n            type: 'bytes4',\n        },\n        {\n            name: 'extraData',\n            type: 'bytes',\n        },\n    ],\n};\nasync function offchainLookup(client, { blockNumber, blockTag, data, to, }) {\n    const { args } = (0,_abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__.decodeErrorResult)({\n        data,\n        abi: [offchainLookupAbiItem],\n    });\n    const [sender, urls, callData, callbackSelector, extraData] = args;\n    const { ccipRead } = client;\n    const ccipRequest_ = ccipRead && typeof ccipRead?.request === 'function'\n        ? ccipRead.request\n        : ccipRequest;\n    try {\n        if (!(0,_address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__.isAddressEqual)(to, sender))\n            throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupSenderMismatchError({ sender, to });\n        const result = await ccipRequest_({ data: callData, sender, urls });\n        const { data: data_ } = await (0,_actions_public_call_js__WEBPACK_IMPORTED_MODULE_3__.call)(client, {\n            blockNumber,\n            blockTag,\n            data: (0,_data_concat_js__WEBPACK_IMPORTED_MODULE_4__.concat)([\n                callbackSelector,\n                (0,_abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_5__.encodeAbiParameters)([{ type: 'bytes' }, { type: 'bytes' }], [result, extraData]),\n            ]),\n            to,\n        });\n        return data_;\n    }\n    catch (err) {\n        throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupError({\n            callbackSelector,\n            cause: err,\n            data,\n            extraData,\n            sender,\n            urls,\n        });\n    }\n}\nasync function ccipRequest({ data, sender, urls, }) {\n    let error = new Error('An unknown error occurred.');\n    for (let i = 0; i < urls.length; i++) {\n        const url = urls[i];\n        const method = url.includes('{data}') ? 'GET' : 'POST';\n        const body = method === 'POST' ? { data, sender } : undefined;\n        try {\n            const response = await fetch(url.replace('{sender}', sender).replace('{data}', data), {\n                body: JSON.stringify(body),\n                method,\n            });\n            let result;\n            if (response.headers.get('Content-Type')?.startsWith('application/json')) {\n                result = (await response.json()).data;\n            }\n            else {\n                result = (await response.text());\n            }\n            if (!response.ok) {\n                error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_6__.HttpRequestError({\n                    body,\n                    details: result?.error\n                        ? (0,_stringify_js__WEBPACK_IMPORTED_MODULE_7__.stringify)(result.error)\n                        : response.statusText,\n                    headers: response.headers,\n                    status: response.status,\n                    url,\n                });\n                continue;\n            }\n            if (!(0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_8__.isHex)(result)) {\n                error = new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupResponseMalformedError({\n                    result,\n                    url,\n                });\n                continue;\n            }\n            return result;\n        }\n        catch (err) {\n            error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_6__.HttpRequestError({\n                body,\n                details: err.message,\n                url,\n            });\n        }\n    }\n    throw error;\n}\n//# sourceMappingURL=ccip.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/utils/ccip.js?");

/***/ })

}]);