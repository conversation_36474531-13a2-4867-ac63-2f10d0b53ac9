"use strict";(self.webpackChunkfula_webui=self.webpackChunkfula_webui||[]).push([[826],{45238:(e,r,t)=>{t.d(r,{K:()=>a});var n=t(75930);function a(e=0){return null!=globalThis.Buffer&&null!=globalThis.Buffer.allocUnsafe?(0,n.o)(globalThis.Buffer.allocUnsafe(e)):new Uint8Array(e)}},44117:(e,r,t)=>{t.d(r,{fromString:()=>o});var n=t(55821),a=t(75930);function o(e,r="utf8"){const t=n.A[r];if(!t)throw new Error(`Unsupported encoding "${r}"`);return"utf8"!==r&&"utf-8"!==r||null==globalThis.Buffer||null==globalThis.Buffer.from?t.decoder.decode(`${t.prefix}${e}`):(0,a.o)(globalThis.Buffer.from(e,"utf-8"))}},27302:(e,r,t)=>{t.d(r,{toString:()=>a});var n=t(55821);function a(e,r="utf8"){const t=n.A[r];if(!t)throw new Error(`Unsupported encoding "${r}"`);return"utf8"!==r&&"utf-8"!==r||null==globalThis.Buffer||null==globalThis.Buffer.from?t.encoder.encode(e).substring(1):globalThis.Buffer.from(e.buffer,e.byteOffset,e.byteLength).toString("utf8")}},75930:(e,r,t)=>{function n(e){return null!=globalThis.Buffer?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e}t.d(r,{o:()=>n})},55821:(e,r,t)=>{t.d(r,{A:()=>Ve});var n={};t.r(n),t.d(n,{identity:()=>T});var a={};t.r(a),t.d(a,{base2:()=>S});var o={};t.r(o),t.d(o,{base8:()=>B});var i={};t.r(i),t.d(i,{base10:()=>M});var s={};t.r(s),t.d(s,{base16:()=>k,base16upper:()=>N});var f={};t.r(f),t.d(f,{base32:()=>j,base32hex:()=>L,base32hexpad:()=>K,base32hexpadupper:()=>V,base32hexupper:()=>J,base32pad:()=>O,base32padupper:()=>z,base32upper:()=>D,base32z:()=>$});var c={};t.r(c),t.d(c,{base36:()=>F,base36upper:()=>H});var d={};t.r(d),t.d(d,{base58btc:()=>q,base58flickr:()=>R});var h={};t.r(h),t.d(h,{base64:()=>G,base64pad:()=>I,base64url:()=>Q,base64urlpad:()=>Z});var b={};t.r(b),t.d(b,{base256emoji:()=>_});var p={};t.r(p),t.d(p,{sha256:()=>Ae,sha512:()=>ve});var u={};t.r(u),t.d(u,{identity:()=>Ce});var l={};t.r(l),t.d(l,{code:()=>Pe,decode:()=>Se,encode:()=>Te,name:()=>Ee});var w={};t.r(w),t.d(w,{code:()=>Ne,decode:()=>De,encode:()=>je,name:()=>ke});const y=function(e,r){if(e.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),n=0;n<t.length;n++)t[n]=255;for(var a=0;a<e.length;a++){var o=e.charAt(a),i=o.charCodeAt(0);if(255!==t[i])throw new TypeError(o+" is ambiguous");t[i]=a}var s=e.length,f=e.charAt(0),c=Math.log(s)/Math.log(256),d=Math.log(256)/Math.log(s);function h(e){if("string"!=typeof e)throw new TypeError("Expected String");if(0===e.length)return new Uint8Array;var r=0;if(" "!==e[r]){for(var n=0,a=0;e[r]===f;)n++,r++;for(var o=(e.length-r)*c+1>>>0,i=new Uint8Array(o);e[r];){var d=t[e.charCodeAt(r)];if(255===d)return;for(var h=0,b=o-1;(0!==d||h<a)&&-1!==b;b--,h++)d+=s*i[b]>>>0,i[b]=d%256>>>0,d=d/256>>>0;if(0!==d)throw new Error("Non-zero carry");a=h,r++}if(" "!==e[r]){for(var p=o-a;p!==o&&0===i[p];)p++;for(var u=new Uint8Array(n+(o-p)),l=n;p!==o;)u[l++]=i[p++];return u}}}return{encode:function(r){if(r instanceof Uint8Array||(ArrayBuffer.isView(r)?r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength):Array.isArray(r)&&(r=Uint8Array.from(r))),!(r instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===r.length)return"";for(var t=0,n=0,a=0,o=r.length;a!==o&&0===r[a];)a++,t++;for(var i=(o-a)*d+1>>>0,c=new Uint8Array(i);a!==o;){for(var h=r[a],b=0,p=i-1;(0!==h||b<n)&&-1!==p;p--,b++)h+=256*c[p]>>>0,c[p]=h%s>>>0,h=h/s>>>0;if(0!==h)throw new Error("Non-zero carry");n=b,a++}for(var u=i-n;u!==i&&0===c[u];)u++;for(var l=f.repeat(t);u<i;++u)l+=e.charAt(c[u]);return l},decodeUnsafe:h,decode:function(e){var t=h(e);if(t)return t;throw new Error(`Non-${r} character`)}}},m=(new Uint8Array(0),e=>{if(e instanceof Uint8Array&&"Uint8Array"===e.constructor.name)return e;if(e instanceof ArrayBuffer)return new Uint8Array(e);if(ArrayBuffer.isView(e))return new Uint8Array(e.buffer,e.byteOffset,e.byteLength);throw new Error("Unknown type, must be binary type")});class g{constructor(e,r,t){this.name=e,this.prefix=r,this.baseEncode=t}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class x{constructor(e,r,t){if(this.name=e,this.prefix=r,void 0===r.codePointAt(0))throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=t}decode(e){if("string"==typeof e){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}throw Error("Can only multibase decode strings")}or(e){return v(this,e)}}class A{constructor(e){this.decoders=e}or(e){return v(this,e)}decode(e){const r=e[0],t=this.decoders[r];if(t)return t.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const v=(e,r)=>new A({...e.decoders||{[e.prefix]:e},...r.decoders||{[r.prefix]:r}});class U{constructor(e,r,t,n){this.name=e,this.prefix=r,this.baseEncode=t,this.baseDecode=n,this.encoder=new g(e,r,t),this.decoder=new x(e,r,n)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const C=({name:e,prefix:r,encode:t,decode:n})=>new U(e,r,t,n),E=({prefix:e,name:r,alphabet:t})=>{const{encode:n,decode:a}=y(t,r);return C({prefix:e,name:r,encode:n,decode:e=>m(a(e))})},P=({name:e,prefix:r,bitsPerChar:t,alphabet:n})=>C({prefix:r,name:e,encode:e=>((e,r,t)=>{const n="="===r[r.length-1],a=(1<<t)-1;let o="",i=0,s=0;for(let n=0;n<e.length;++n)for(s=s<<8|e[n],i+=8;i>t;)i-=t,o+=r[a&s>>i];if(i&&(o+=r[a&s<<t-i]),n)for(;o.length*t&7;)o+="=";return o})(e,n,t),decode:r=>((e,r,t,n)=>{const a={};for(let e=0;e<r.length;++e)a[r[e]]=e;let o=e.length;for(;"="===e[o-1];)--o;const i=new Uint8Array(o*t/8|0);let s=0,f=0,c=0;for(let r=0;r<o;++r){const o=a[e[r]];if(void 0===o)throw new SyntaxError(`Non-${n} character`);f=f<<t|o,s+=t,s>=8&&(s-=8,i[c++]=255&f>>s)}if(s>=t||255&f<<8-s)throw new SyntaxError("Unexpected end of data");return i})(r,n,t,e)}),T=C({prefix:"\0",name:"identity",encode:e=>{return r=e,(new TextDecoder).decode(r);var r},decode:e=>(e=>(new TextEncoder).encode(e))(e)}),S=P({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1}),B=P({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3}),M=E({prefix:"9",name:"base10",alphabet:"0123456789"}),k=P({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),N=P({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4}),j=P({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),D=P({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),O=P({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),z=P({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),L=P({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),J=P({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),K=P({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),V=P({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),$=P({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5}),F=E({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),H=E({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"}),q=E({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),R=E({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"}),G=P({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),I=P({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),Q=P({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),Z=P({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6}),W=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),X=W.reduce(((e,r,t)=>(e[t]=r,e)),[]),Y=W.reduce(((e,r,t)=>(e[r.codePointAt(0)]=t,e)),[]),_=C({prefix:"🚀",name:"base256emoji",encode:function(e){return e.reduce(((e,r)=>e+X[r]),"")},decode:function(e){const r=[];for(const t of e){const e=Y[t.codePointAt(0)];if(void 0===e)throw new Error(`Non-base256emoji character: ${t}`);r.push(e)}return new Uint8Array(r)}});var ee=128,re=-128,te=Math.pow(2,31),ne=Math.pow(2,7),ae=Math.pow(2,14),oe=Math.pow(2,21),ie=Math.pow(2,28),se=Math.pow(2,35),fe=Math.pow(2,42),ce=Math.pow(2,49),de=Math.pow(2,56),he=Math.pow(2,63);const be=function e(r,t,n){t=t||[];for(var a=n=n||0;r>=te;)t[n++]=255&r|ee,r/=128;for(;r&re;)t[n++]=255&r|ee,r>>>=7;return t[n]=0|r,e.bytes=n-a+1,t},pe=function(e){return e<ne?1:e<ae?2:e<oe?3:e<ie?4:e<se?5:e<fe?6:e<ce?7:e<de?8:e<he?9:10},ue=(e,r,t=0)=>(be(e,r,t),r),le=e=>pe(e),we=(e,r)=>{const t=r.byteLength,n=le(e),a=n+le(t),o=new Uint8Array(a+t);return ue(e,o,0),ue(t,o,n),o.set(r,a),new ye(e,t,r,o)};class ye{constructor(e,r,t,n){this.code=e,this.size=r,this.digest=t,this.bytes=n}}const me=({name:e,code:r,encode:t})=>new ge(e,r,t);class ge{constructor(e,r,t){this.name=e,this.code=r,this.encode=t}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?we(this.code,r):r.then((e=>we(this.code,e)))}throw Error("Unknown type, must be binary type")}}const xe=e=>async r=>new Uint8Array(await crypto.subtle.digest(e,r)),Ae=me({name:"sha2-256",code:18,encode:xe("SHA-256")}),ve=me({name:"sha2-512",code:19,encode:xe("SHA-512")}),Ue=m,Ce={code:0,name:"identity",encode:Ue,digest:e=>we(0,Ue(e))},Ee="raw",Pe=85,Te=e=>m(e),Se=e=>m(e),Be=new TextEncoder,Me=new TextDecoder,ke="json",Ne=512,je=e=>Be.encode(JSON.stringify(e)),De=e=>JSON.parse(Me.decode(e));Symbol.toStringTag,Symbol.for("nodejs.util.inspect.custom"),Symbol.for("@ipld/js-cid/CID");const Oe={...n,...a,...o,...i,...s,...f,...c,...d,...h,...b};var ze=t(45238);function Le(e,r,t,n){return{name:e,prefix:r,encoder:{name:e,prefix:r,encode:t},decoder:{decode:n}}}const Je=Le("utf8","u",(e=>"u"+new TextDecoder("utf8").decode(e)),(e=>(new TextEncoder).encode(e.substring(1)))),Ke=Le("ascii","a",(e=>{let r="a";for(let t=0;t<e.length;t++)r+=String.fromCharCode(e[t]);return r}),(e=>{e=e.substring(1);const r=(0,ze.K)(e.length);for(let t=0;t<e.length;t++)r[t]=e.charCodeAt(t);return r})),Ve={utf8:Je,"utf-8":Je,hex:Oe.base16,latin1:Ke,ascii:Ke,binary:Ke,...Oe}}}]);