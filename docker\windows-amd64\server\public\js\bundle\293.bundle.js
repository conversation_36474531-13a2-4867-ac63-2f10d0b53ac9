(self.webpackChunkfula_webui=self.webpackChunkfula_webui||[]).push([[293],{7140:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(37007);function s(e,t,r){try{Reflect.apply(e,t,r)}catch(e){setTimeout((()=>{throw e}))}}class i extends n.EventEmitter{emit(e,...t){let r="error"===e;const n=this._events;if(void 0!==n)r=r&&void 0===n.error;else if(!r)return!1;if(r){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const r=new Error("Unhandled error."+(e?` (${e.message})`:""));throw r.context=e,r}const i=n[e];if(void 0===i)return!1;if("function"==typeof i)s(i,this,t);else{const e=i.length,r=function(e){const t=e.length,r=new Array(t);for(let n=0;n<t;n+=1)r[n]=e[n];return r}(i);for(let n=0;n<e;n+=1)s(r[n],this,t)}return!0}}t.default=i},47406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(31635),s=r(42919),i=function(){function e(){this._semaphore=new s.default(1)}return e.prototype.acquire=function(){return n.__awaiter(this,void 0,void 0,(function(){return n.__generator(this,(function(e){switch(e.label){case 0:return[4,this._semaphore.acquire()];case 1:return[2,e.sent()[1]]}}))}))},e.prototype.runExclusive=function(e){return this._semaphore.runExclusive((function(){return e()}))},e.prototype.isLocked=function(){return this._semaphore.isLocked()},e.prototype.release=function(){this._semaphore.release()},e}();t.default=i},42919:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(31635),s=function(){function e(e){if(this._maxConcurrency=e,this._queue=[],e<=0)throw new Error("semaphore must be initialized to a positive value");this._value=e}return e.prototype.acquire=function(){var e=this,t=this.isLocked(),r=new Promise((function(t){return e._queue.push(t)}));return t||this._dispatch(),r},e.prototype.runExclusive=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r,s;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.acquire()];case 1:t=n.sent(),r=t[0],s=t[1],n.label=2;case 2:return n.trys.push([2,,4,5]),[4,e(r)];case 3:return[2,n.sent()];case 4:return s(),[7];case 5:return[2]}}))}))},e.prototype.isLocked=function(){return this._value<=0},e.prototype.release=function(){if(this._maxConcurrency>1)throw new Error("this method is unavailabel on semaphores with concurrency > 1; use the scoped release returned by acquire instead");if(this._currentReleaser){var e=this._currentReleaser;this._currentReleaser=void 0,e()}},e.prototype._dispatch=function(){var e=this,t=this._queue.shift();if(t){var r=!1;this._currentReleaser=function(){r||(r=!0,e._value++,e._dispatch())},t([this._value--,this._currentReleaser])}},e}();t.default=s},56693:(e,t,r)=>{"use strict";t.eu=void 0;var n=r(47406);Object.defineProperty(t,"eu",{enumerable:!0,get:function(){return n.default}});r(42919),r(92646)},92646:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.withTimeout=void 0;var n=r(31635);t.withTimeout=function(e,t,r){var s=this;return void 0===r&&(r=new Error("timeout")),{acquire:function(){return new Promise((function(i,o){return n.__awaiter(s,void 0,void 0,(function(){var s,a;return n.__generator(this,(function(n){switch(n.label){case 0:return s=!1,setTimeout((function(){s=!0,o(r)}),t),[4,e.acquire()];case 1:return a=n.sent(),s?(Array.isArray(a)?a[1]:a)():i(a),[2]}}))}))}))},runExclusive:function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r;return n.__generator(this,(function(n){switch(n.label){case 0:t=function(){},n.label=1;case 1:return n.trys.push([1,,7,8]),[4,this.acquire()];case 2:return r=n.sent(),Array.isArray(r)?(t=r[1],[4,e(r[0])]):[3,4];case 3:return[2,n.sent()];case 4:return t=r,[4,e()];case 5:return[2,n.sent()];case 6:return[3,8];case 7:return t(),[7];case 8:return[2]}}))}))},release:function(){e.release()},isLocked:function(){return e.isLocked()}}}},39404:function(e,t,r){!function(e,t){"use strict";function n(e,t){if(!e)throw new Error(t||"Assertion failed")}function s(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}function i(e,t,r){if(i.isBN(e))return e;this.negative=0,this.words=null,this.length=0,this.red=null,null!==e&&("le"!==t&&"be"!==t||(r=t,t=10),this._init(e||0,t||10,r||"be"))}var o;"object"==typeof e?e.exports=i:t.BN=i,i.BN=i,i.wordSize=26;try{o="undefined"!=typeof window&&void 0!==window.Buffer?window.Buffer:r(47790).Buffer}catch(e){}function a(e,t){var r=e.charCodeAt(t);return r>=48&&r<=57?r-48:r>=65&&r<=70?r-55:r>=97&&r<=102?r-87:void n(!1,"Invalid character in "+e)}function c(e,t,r){var n=a(e,r);return r-1>=t&&(n|=a(e,r-1)<<4),n}function l(e,t,r,s){for(var i=0,o=0,a=Math.min(e.length,r),c=t;c<a;c++){var l=e.charCodeAt(c)-48;i*=s,o=l>=49?l-49+10:l>=17?l-17+10:l,n(l>=0&&o<s,"Invalid character"),i+=o}return i}function u(e,t){e.words=t.words,e.length=t.length,e.negative=t.negative,e.red=t.red}if(i.isBN=function(e){return e instanceof i||null!==e&&"object"==typeof e&&e.constructor.wordSize===i.wordSize&&Array.isArray(e.words)},i.max=function(e,t){return e.cmp(t)>0?e:t},i.min=function(e,t){return e.cmp(t)<0?e:t},i.prototype._init=function(e,t,r){if("number"==typeof e)return this._initNumber(e,t,r);if("object"==typeof e)return this._initArray(e,t,r);"hex"===t&&(t=16),n(t===(0|t)&&t>=2&&t<=36);var s=0;"-"===(e=e.toString().replace(/\s+/g,""))[0]&&(s++,this.negative=1),s<e.length&&(16===t?this._parseHex(e,s,r):(this._parseBase(e,t,s),"le"===r&&this._initArray(this.toArray(),t,r)))},i.prototype._initNumber=function(e,t,r){e<0&&(this.negative=1,e=-e),e<67108864?(this.words=[67108863&e],this.length=1):e<4503599627370496?(this.words=[67108863&e,e/67108864&67108863],this.length=2):(n(e<9007199254740992),this.words=[67108863&e,e/67108864&67108863,1],this.length=3),"le"===r&&this._initArray(this.toArray(),t,r)},i.prototype._initArray=function(e,t,r){if(n("number"==typeof e.length),e.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(e.length/3),this.words=new Array(this.length);for(var s=0;s<this.length;s++)this.words[s]=0;var i,o,a=0;if("be"===r)for(s=e.length-1,i=0;s>=0;s-=3)o=e[s]|e[s-1]<<8|e[s-2]<<16,this.words[i]|=o<<a&67108863,this.words[i+1]=o>>>26-a&67108863,(a+=24)>=26&&(a-=26,i++);else if("le"===r)for(s=0,i=0;s<e.length;s+=3)o=e[s]|e[s+1]<<8|e[s+2]<<16,this.words[i]|=o<<a&67108863,this.words[i+1]=o>>>26-a&67108863,(a+=24)>=26&&(a-=26,i++);return this._strip()},i.prototype._parseHex=function(e,t,r){this.length=Math.ceil((e.length-t)/6),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var s,i=0,o=0;if("be"===r)for(n=e.length-1;n>=t;n-=2)s=c(e,t,n)<<i,this.words[o]|=67108863&s,i>=18?(i-=18,o+=1,this.words[o]|=s>>>26):i+=8;else for(n=(e.length-t)%2==0?t+1:t;n<e.length;n+=2)s=c(e,t,n)<<i,this.words[o]|=67108863&s,i>=18?(i-=18,o+=1,this.words[o]|=s>>>26):i+=8;this._strip()},i.prototype._parseBase=function(e,t,r){this.words=[0],this.length=1;for(var n=0,s=1;s<=67108863;s*=t)n++;n--,s=s/t|0;for(var i=e.length-r,o=i%n,a=Math.min(i,i-o)+r,c=0,u=r;u<a;u+=n)c=l(e,u,u+n,t),this.imuln(s),this.words[0]+c<67108864?this.words[0]+=c:this._iaddn(c);if(0!==o){var h=1;for(c=l(e,u,e.length,t),u=0;u<o;u++)h*=t;this.imuln(h),this.words[0]+c<67108864?this.words[0]+=c:this._iaddn(c)}this._strip()},i.prototype.copy=function(e){e.words=new Array(this.length);for(var t=0;t<this.length;t++)e.words[t]=this.words[t];e.length=this.length,e.negative=this.negative,e.red=this.red},i.prototype._move=function(e){u(e,this)},i.prototype.clone=function(){var e=new i(null);return this.copy(e),e},i.prototype._expand=function(e){for(;this.length<e;)this.words[this.length++]=0;return this},i.prototype._strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},i.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},"undefined"!=typeof Symbol&&"function"==typeof Symbol.for)try{i.prototype[Symbol.for("nodejs.util.inspect.custom")]=h}catch(e){i.prototype.inspect=h}else i.prototype.inspect=h;function h(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var d=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],p=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],f=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function m(e,t,r){r.negative=t.negative^e.negative;var n=e.length+t.length|0;r.length=n,n=n-1|0;var s=0|e.words[0],i=0|t.words[0],o=s*i,a=67108863&o,c=o/67108864|0;r.words[0]=a;for(var l=1;l<n;l++){for(var u=c>>>26,h=67108863&c,d=Math.min(l,t.length-1),p=Math.max(0,l-e.length+1);p<=d;p++){var f=l-p|0;u+=(o=(s=0|e.words[f])*(i=0|t.words[p])+h)/67108864|0,h=67108863&o}r.words[l]=0|h,c=0|u}return 0!==c?r.words[l]=0|c:r.length--,r._strip()}i.prototype.toString=function(e,t){var r;if(t=0|t||1,16===(e=e||10)||"hex"===e){r="";for(var s=0,i=0,o=0;o<this.length;o++){var a=this.words[o],c=(16777215&(a<<s|i)).toString(16);i=a>>>24-s&16777215,(s+=2)>=26&&(s-=26,o--),r=0!==i||o!==this.length-1?d[6-c.length]+c+r:c+r}for(0!==i&&(r=i.toString(16)+r);r.length%t!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(e===(0|e)&&e>=2&&e<=36){var l=p[e],u=f[e];r="";var h=this.clone();for(h.negative=0;!h.isZero();){var m=h.modrn(u).toString(e);r=(h=h.idivn(u)).isZero()?m+r:d[l-m.length]+m+r}for(this.isZero()&&(r="0"+r);r.length%t!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}n(!1,"Base should be between 2 and 36")},i.prototype.toNumber=function(){var e=this.words[0];return 2===this.length?e+=67108864*this.words[1]:3===this.length&&1===this.words[2]?e+=4503599627370496+67108864*this.words[1]:this.length>2&&n(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-e:e},i.prototype.toJSON=function(){return this.toString(16,2)},o&&(i.prototype.toBuffer=function(e,t){return this.toArrayLike(o,e,t)}),i.prototype.toArray=function(e,t){return this.toArrayLike(Array,e,t)},i.prototype.toArrayLike=function(e,t,r){this._strip();var s=this.byteLength(),i=r||Math.max(1,s);n(s<=i,"byte array longer than desired length"),n(i>0,"Requested array length <= 0");var o=function(e,t){return e.allocUnsafe?e.allocUnsafe(t):new e(t)}(e,i);return this["_toArrayLike"+("le"===t?"LE":"BE")](o,s),o},i.prototype._toArrayLikeLE=function(e,t){for(var r=0,n=0,s=0,i=0;s<this.length;s++){var o=this.words[s]<<i|n;e[r++]=255&o,r<e.length&&(e[r++]=o>>8&255),r<e.length&&(e[r++]=o>>16&255),6===i?(r<e.length&&(e[r++]=o>>24&255),n=0,i=0):(n=o>>>24,i+=2)}if(r<e.length)for(e[r++]=n;r<e.length;)e[r++]=0},i.prototype._toArrayLikeBE=function(e,t){for(var r=e.length-1,n=0,s=0,i=0;s<this.length;s++){var o=this.words[s]<<i|n;e[r--]=255&o,r>=0&&(e[r--]=o>>8&255),r>=0&&(e[r--]=o>>16&255),6===i?(r>=0&&(e[r--]=o>>24&255),n=0,i=0):(n=o>>>24,i+=2)}if(r>=0)for(e[r--]=n;r>=0;)e[r--]=0},Math.clz32?i.prototype._countBits=function(e){return 32-Math.clz32(e)}:i.prototype._countBits=function(e){var t=e,r=0;return t>=4096&&(r+=13,t>>>=13),t>=64&&(r+=7,t>>>=7),t>=8&&(r+=4,t>>>=4),t>=2&&(r+=2,t>>>=2),r+t},i.prototype._zeroBits=function(e){if(0===e)return 26;var t=e,r=0;return 8191&t||(r+=13,t>>>=13),127&t||(r+=7,t>>>=7),15&t||(r+=4,t>>>=4),3&t||(r+=2,t>>>=2),1&t||r++,r},i.prototype.bitLength=function(){var e=this.words[this.length-1],t=this._countBits(e);return 26*(this.length-1)+t},i.prototype.zeroBits=function(){if(this.isZero())return 0;for(var e=0,t=0;t<this.length;t++){var r=this._zeroBits(this.words[t]);if(e+=r,26!==r)break}return e},i.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},i.prototype.toTwos=function(e){return 0!==this.negative?this.abs().inotn(e).iaddn(1):this.clone()},i.prototype.fromTwos=function(e){return this.testn(e-1)?this.notn(e).iaddn(1).ineg():this.clone()},i.prototype.isNeg=function(){return 0!==this.negative},i.prototype.neg=function(){return this.clone().ineg()},i.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},i.prototype.iuor=function(e){for(;this.length<e.length;)this.words[this.length++]=0;for(var t=0;t<e.length;t++)this.words[t]=this.words[t]|e.words[t];return this._strip()},i.prototype.ior=function(e){return n(!(this.negative|e.negative)),this.iuor(e)},i.prototype.or=function(e){return this.length>e.length?this.clone().ior(e):e.clone().ior(this)},i.prototype.uor=function(e){return this.length>e.length?this.clone().iuor(e):e.clone().iuor(this)},i.prototype.iuand=function(e){var t;t=this.length>e.length?e:this;for(var r=0;r<t.length;r++)this.words[r]=this.words[r]&e.words[r];return this.length=t.length,this._strip()},i.prototype.iand=function(e){return n(!(this.negative|e.negative)),this.iuand(e)},i.prototype.and=function(e){return this.length>e.length?this.clone().iand(e):e.clone().iand(this)},i.prototype.uand=function(e){return this.length>e.length?this.clone().iuand(e):e.clone().iuand(this)},i.prototype.iuxor=function(e){var t,r;this.length>e.length?(t=this,r=e):(t=e,r=this);for(var n=0;n<r.length;n++)this.words[n]=t.words[n]^r.words[n];if(this!==t)for(;n<t.length;n++)this.words[n]=t.words[n];return this.length=t.length,this._strip()},i.prototype.ixor=function(e){return n(!(this.negative|e.negative)),this.iuxor(e)},i.prototype.xor=function(e){return this.length>e.length?this.clone().ixor(e):e.clone().ixor(this)},i.prototype.uxor=function(e){return this.length>e.length?this.clone().iuxor(e):e.clone().iuxor(this)},i.prototype.inotn=function(e){n("number"==typeof e&&e>=0);var t=0|Math.ceil(e/26),r=e%26;this._expand(t),r>0&&t--;for(var s=0;s<t;s++)this.words[s]=67108863&~this.words[s];return r>0&&(this.words[s]=~this.words[s]&67108863>>26-r),this._strip()},i.prototype.notn=function(e){return this.clone().inotn(e)},i.prototype.setn=function(e,t){n("number"==typeof e&&e>=0);var r=e/26|0,s=e%26;return this._expand(r+1),this.words[r]=t?this.words[r]|1<<s:this.words[r]&~(1<<s),this._strip()},i.prototype.iadd=function(e){var t,r,n;if(0!==this.negative&&0===e.negative)return this.negative=0,t=this.isub(e),this.negative^=1,this._normSign();if(0===this.negative&&0!==e.negative)return e.negative=0,t=this.isub(e),e.negative=1,t._normSign();this.length>e.length?(r=this,n=e):(r=e,n=this);for(var s=0,i=0;i<n.length;i++)t=(0|r.words[i])+(0|n.words[i])+s,this.words[i]=67108863&t,s=t>>>26;for(;0!==s&&i<r.length;i++)t=(0|r.words[i])+s,this.words[i]=67108863&t,s=t>>>26;if(this.length=r.length,0!==s)this.words[this.length]=s,this.length++;else if(r!==this)for(;i<r.length;i++)this.words[i]=r.words[i];return this},i.prototype.add=function(e){var t;return 0!==e.negative&&0===this.negative?(e.negative=0,t=this.sub(e),e.negative^=1,t):0===e.negative&&0!==this.negative?(this.negative=0,t=e.sub(this),this.negative=1,t):this.length>e.length?this.clone().iadd(e):e.clone().iadd(this)},i.prototype.isub=function(e){if(0!==e.negative){e.negative=0;var t=this.iadd(e);return e.negative=1,t._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(e),this.negative=1,this._normSign();var r,n,s=this.cmp(e);if(0===s)return this.negative=0,this.length=1,this.words[0]=0,this;s>0?(r=this,n=e):(r=e,n=this);for(var i=0,o=0;o<n.length;o++)i=(t=(0|r.words[o])-(0|n.words[o])+i)>>26,this.words[o]=67108863&t;for(;0!==i&&o<r.length;o++)i=(t=(0|r.words[o])+i)>>26,this.words[o]=67108863&t;if(0===i&&o<r.length&&r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this.length=Math.max(this.length,o),r!==this&&(this.negative=1),this._strip()},i.prototype.sub=function(e){return this.clone().isub(e)};var g=function(e,t,r){var n,s,i,o=e.words,a=t.words,c=r.words,l=0,u=0|o[0],h=8191&u,d=u>>>13,p=0|o[1],f=8191&p,m=p>>>13,g=0|o[2],w=8191&g,y=g>>>13,v=0|o[3],b=8191&v,E=v>>>13,_=0|o[4],k=8191&_,M=_>>>13,S=0|o[5],C=8191&S,I=S>>>13,R=0|o[6],x=8191&R,A=R>>>13,N=0|o[7],L=8191&N,O=N>>>13,P=0|o[8],T=8191&P,j=P>>>13,B=0|o[9],F=8191&B,D=B>>>13,$=0|a[0],U=8191&$,H=$>>>13,V=0|a[1],q=8191&V,W=V>>>13,z=0|a[2],J=8191&z,G=z>>>13,Z=0|a[3],Q=8191&Z,Y=Z>>>13,K=0|a[4],X=8191&K,ee=K>>>13,te=0|a[5],re=8191&te,ne=te>>>13,se=0|a[6],ie=8191&se,oe=se>>>13,ae=0|a[7],ce=8191&ae,le=ae>>>13,ue=0|a[8],he=8191&ue,de=ue>>>13,pe=0|a[9],fe=8191&pe,me=pe>>>13;r.negative=e.negative^t.negative,r.length=19;var ge=(l+(n=Math.imul(h,U))|0)+((8191&(s=(s=Math.imul(h,H))+Math.imul(d,U)|0))<<13)|0;l=((i=Math.imul(d,H))+(s>>>13)|0)+(ge>>>26)|0,ge&=67108863,n=Math.imul(f,U),s=(s=Math.imul(f,H))+Math.imul(m,U)|0,i=Math.imul(m,H);var we=(l+(n=n+Math.imul(h,q)|0)|0)+((8191&(s=(s=s+Math.imul(h,W)|0)+Math.imul(d,q)|0))<<13)|0;l=((i=i+Math.imul(d,W)|0)+(s>>>13)|0)+(we>>>26)|0,we&=67108863,n=Math.imul(w,U),s=(s=Math.imul(w,H))+Math.imul(y,U)|0,i=Math.imul(y,H),n=n+Math.imul(f,q)|0,s=(s=s+Math.imul(f,W)|0)+Math.imul(m,q)|0,i=i+Math.imul(m,W)|0;var ye=(l+(n=n+Math.imul(h,J)|0)|0)+((8191&(s=(s=s+Math.imul(h,G)|0)+Math.imul(d,J)|0))<<13)|0;l=((i=i+Math.imul(d,G)|0)+(s>>>13)|0)+(ye>>>26)|0,ye&=67108863,n=Math.imul(b,U),s=(s=Math.imul(b,H))+Math.imul(E,U)|0,i=Math.imul(E,H),n=n+Math.imul(w,q)|0,s=(s=s+Math.imul(w,W)|0)+Math.imul(y,q)|0,i=i+Math.imul(y,W)|0,n=n+Math.imul(f,J)|0,s=(s=s+Math.imul(f,G)|0)+Math.imul(m,J)|0,i=i+Math.imul(m,G)|0;var ve=(l+(n=n+Math.imul(h,Q)|0)|0)+((8191&(s=(s=s+Math.imul(h,Y)|0)+Math.imul(d,Q)|0))<<13)|0;l=((i=i+Math.imul(d,Y)|0)+(s>>>13)|0)+(ve>>>26)|0,ve&=67108863,n=Math.imul(k,U),s=(s=Math.imul(k,H))+Math.imul(M,U)|0,i=Math.imul(M,H),n=n+Math.imul(b,q)|0,s=(s=s+Math.imul(b,W)|0)+Math.imul(E,q)|0,i=i+Math.imul(E,W)|0,n=n+Math.imul(w,J)|0,s=(s=s+Math.imul(w,G)|0)+Math.imul(y,J)|0,i=i+Math.imul(y,G)|0,n=n+Math.imul(f,Q)|0,s=(s=s+Math.imul(f,Y)|0)+Math.imul(m,Q)|0,i=i+Math.imul(m,Y)|0;var be=(l+(n=n+Math.imul(h,X)|0)|0)+((8191&(s=(s=s+Math.imul(h,ee)|0)+Math.imul(d,X)|0))<<13)|0;l=((i=i+Math.imul(d,ee)|0)+(s>>>13)|0)+(be>>>26)|0,be&=67108863,n=Math.imul(C,U),s=(s=Math.imul(C,H))+Math.imul(I,U)|0,i=Math.imul(I,H),n=n+Math.imul(k,q)|0,s=(s=s+Math.imul(k,W)|0)+Math.imul(M,q)|0,i=i+Math.imul(M,W)|0,n=n+Math.imul(b,J)|0,s=(s=s+Math.imul(b,G)|0)+Math.imul(E,J)|0,i=i+Math.imul(E,G)|0,n=n+Math.imul(w,Q)|0,s=(s=s+Math.imul(w,Y)|0)+Math.imul(y,Q)|0,i=i+Math.imul(y,Y)|0,n=n+Math.imul(f,X)|0,s=(s=s+Math.imul(f,ee)|0)+Math.imul(m,X)|0,i=i+Math.imul(m,ee)|0;var Ee=(l+(n=n+Math.imul(h,re)|0)|0)+((8191&(s=(s=s+Math.imul(h,ne)|0)+Math.imul(d,re)|0))<<13)|0;l=((i=i+Math.imul(d,ne)|0)+(s>>>13)|0)+(Ee>>>26)|0,Ee&=67108863,n=Math.imul(x,U),s=(s=Math.imul(x,H))+Math.imul(A,U)|0,i=Math.imul(A,H),n=n+Math.imul(C,q)|0,s=(s=s+Math.imul(C,W)|0)+Math.imul(I,q)|0,i=i+Math.imul(I,W)|0,n=n+Math.imul(k,J)|0,s=(s=s+Math.imul(k,G)|0)+Math.imul(M,J)|0,i=i+Math.imul(M,G)|0,n=n+Math.imul(b,Q)|0,s=(s=s+Math.imul(b,Y)|0)+Math.imul(E,Q)|0,i=i+Math.imul(E,Y)|0,n=n+Math.imul(w,X)|0,s=(s=s+Math.imul(w,ee)|0)+Math.imul(y,X)|0,i=i+Math.imul(y,ee)|0,n=n+Math.imul(f,re)|0,s=(s=s+Math.imul(f,ne)|0)+Math.imul(m,re)|0,i=i+Math.imul(m,ne)|0;var _e=(l+(n=n+Math.imul(h,ie)|0)|0)+((8191&(s=(s=s+Math.imul(h,oe)|0)+Math.imul(d,ie)|0))<<13)|0;l=((i=i+Math.imul(d,oe)|0)+(s>>>13)|0)+(_e>>>26)|0,_e&=67108863,n=Math.imul(L,U),s=(s=Math.imul(L,H))+Math.imul(O,U)|0,i=Math.imul(O,H),n=n+Math.imul(x,q)|0,s=(s=s+Math.imul(x,W)|0)+Math.imul(A,q)|0,i=i+Math.imul(A,W)|0,n=n+Math.imul(C,J)|0,s=(s=s+Math.imul(C,G)|0)+Math.imul(I,J)|0,i=i+Math.imul(I,G)|0,n=n+Math.imul(k,Q)|0,s=(s=s+Math.imul(k,Y)|0)+Math.imul(M,Q)|0,i=i+Math.imul(M,Y)|0,n=n+Math.imul(b,X)|0,s=(s=s+Math.imul(b,ee)|0)+Math.imul(E,X)|0,i=i+Math.imul(E,ee)|0,n=n+Math.imul(w,re)|0,s=(s=s+Math.imul(w,ne)|0)+Math.imul(y,re)|0,i=i+Math.imul(y,ne)|0,n=n+Math.imul(f,ie)|0,s=(s=s+Math.imul(f,oe)|0)+Math.imul(m,ie)|0,i=i+Math.imul(m,oe)|0;var ke=(l+(n=n+Math.imul(h,ce)|0)|0)+((8191&(s=(s=s+Math.imul(h,le)|0)+Math.imul(d,ce)|0))<<13)|0;l=((i=i+Math.imul(d,le)|0)+(s>>>13)|0)+(ke>>>26)|0,ke&=67108863,n=Math.imul(T,U),s=(s=Math.imul(T,H))+Math.imul(j,U)|0,i=Math.imul(j,H),n=n+Math.imul(L,q)|0,s=(s=s+Math.imul(L,W)|0)+Math.imul(O,q)|0,i=i+Math.imul(O,W)|0,n=n+Math.imul(x,J)|0,s=(s=s+Math.imul(x,G)|0)+Math.imul(A,J)|0,i=i+Math.imul(A,G)|0,n=n+Math.imul(C,Q)|0,s=(s=s+Math.imul(C,Y)|0)+Math.imul(I,Q)|0,i=i+Math.imul(I,Y)|0,n=n+Math.imul(k,X)|0,s=(s=s+Math.imul(k,ee)|0)+Math.imul(M,X)|0,i=i+Math.imul(M,ee)|0,n=n+Math.imul(b,re)|0,s=(s=s+Math.imul(b,ne)|0)+Math.imul(E,re)|0,i=i+Math.imul(E,ne)|0,n=n+Math.imul(w,ie)|0,s=(s=s+Math.imul(w,oe)|0)+Math.imul(y,ie)|0,i=i+Math.imul(y,oe)|0,n=n+Math.imul(f,ce)|0,s=(s=s+Math.imul(f,le)|0)+Math.imul(m,ce)|0,i=i+Math.imul(m,le)|0;var Me=(l+(n=n+Math.imul(h,he)|0)|0)+((8191&(s=(s=s+Math.imul(h,de)|0)+Math.imul(d,he)|0))<<13)|0;l=((i=i+Math.imul(d,de)|0)+(s>>>13)|0)+(Me>>>26)|0,Me&=67108863,n=Math.imul(F,U),s=(s=Math.imul(F,H))+Math.imul(D,U)|0,i=Math.imul(D,H),n=n+Math.imul(T,q)|0,s=(s=s+Math.imul(T,W)|0)+Math.imul(j,q)|0,i=i+Math.imul(j,W)|0,n=n+Math.imul(L,J)|0,s=(s=s+Math.imul(L,G)|0)+Math.imul(O,J)|0,i=i+Math.imul(O,G)|0,n=n+Math.imul(x,Q)|0,s=(s=s+Math.imul(x,Y)|0)+Math.imul(A,Q)|0,i=i+Math.imul(A,Y)|0,n=n+Math.imul(C,X)|0,s=(s=s+Math.imul(C,ee)|0)+Math.imul(I,X)|0,i=i+Math.imul(I,ee)|0,n=n+Math.imul(k,re)|0,s=(s=s+Math.imul(k,ne)|0)+Math.imul(M,re)|0,i=i+Math.imul(M,ne)|0,n=n+Math.imul(b,ie)|0,s=(s=s+Math.imul(b,oe)|0)+Math.imul(E,ie)|0,i=i+Math.imul(E,oe)|0,n=n+Math.imul(w,ce)|0,s=(s=s+Math.imul(w,le)|0)+Math.imul(y,ce)|0,i=i+Math.imul(y,le)|0,n=n+Math.imul(f,he)|0,s=(s=s+Math.imul(f,de)|0)+Math.imul(m,he)|0,i=i+Math.imul(m,de)|0;var Se=(l+(n=n+Math.imul(h,fe)|0)|0)+((8191&(s=(s=s+Math.imul(h,me)|0)+Math.imul(d,fe)|0))<<13)|0;l=((i=i+Math.imul(d,me)|0)+(s>>>13)|0)+(Se>>>26)|0,Se&=67108863,n=Math.imul(F,q),s=(s=Math.imul(F,W))+Math.imul(D,q)|0,i=Math.imul(D,W),n=n+Math.imul(T,J)|0,s=(s=s+Math.imul(T,G)|0)+Math.imul(j,J)|0,i=i+Math.imul(j,G)|0,n=n+Math.imul(L,Q)|0,s=(s=s+Math.imul(L,Y)|0)+Math.imul(O,Q)|0,i=i+Math.imul(O,Y)|0,n=n+Math.imul(x,X)|0,s=(s=s+Math.imul(x,ee)|0)+Math.imul(A,X)|0,i=i+Math.imul(A,ee)|0,n=n+Math.imul(C,re)|0,s=(s=s+Math.imul(C,ne)|0)+Math.imul(I,re)|0,i=i+Math.imul(I,ne)|0,n=n+Math.imul(k,ie)|0,s=(s=s+Math.imul(k,oe)|0)+Math.imul(M,ie)|0,i=i+Math.imul(M,oe)|0,n=n+Math.imul(b,ce)|0,s=(s=s+Math.imul(b,le)|0)+Math.imul(E,ce)|0,i=i+Math.imul(E,le)|0,n=n+Math.imul(w,he)|0,s=(s=s+Math.imul(w,de)|0)+Math.imul(y,he)|0,i=i+Math.imul(y,de)|0;var Ce=(l+(n=n+Math.imul(f,fe)|0)|0)+((8191&(s=(s=s+Math.imul(f,me)|0)+Math.imul(m,fe)|0))<<13)|0;l=((i=i+Math.imul(m,me)|0)+(s>>>13)|0)+(Ce>>>26)|0,Ce&=67108863,n=Math.imul(F,J),s=(s=Math.imul(F,G))+Math.imul(D,J)|0,i=Math.imul(D,G),n=n+Math.imul(T,Q)|0,s=(s=s+Math.imul(T,Y)|0)+Math.imul(j,Q)|0,i=i+Math.imul(j,Y)|0,n=n+Math.imul(L,X)|0,s=(s=s+Math.imul(L,ee)|0)+Math.imul(O,X)|0,i=i+Math.imul(O,ee)|0,n=n+Math.imul(x,re)|0,s=(s=s+Math.imul(x,ne)|0)+Math.imul(A,re)|0,i=i+Math.imul(A,ne)|0,n=n+Math.imul(C,ie)|0,s=(s=s+Math.imul(C,oe)|0)+Math.imul(I,ie)|0,i=i+Math.imul(I,oe)|0,n=n+Math.imul(k,ce)|0,s=(s=s+Math.imul(k,le)|0)+Math.imul(M,ce)|0,i=i+Math.imul(M,le)|0,n=n+Math.imul(b,he)|0,s=(s=s+Math.imul(b,de)|0)+Math.imul(E,he)|0,i=i+Math.imul(E,de)|0;var Ie=(l+(n=n+Math.imul(w,fe)|0)|0)+((8191&(s=(s=s+Math.imul(w,me)|0)+Math.imul(y,fe)|0))<<13)|0;l=((i=i+Math.imul(y,me)|0)+(s>>>13)|0)+(Ie>>>26)|0,Ie&=67108863,n=Math.imul(F,Q),s=(s=Math.imul(F,Y))+Math.imul(D,Q)|0,i=Math.imul(D,Y),n=n+Math.imul(T,X)|0,s=(s=s+Math.imul(T,ee)|0)+Math.imul(j,X)|0,i=i+Math.imul(j,ee)|0,n=n+Math.imul(L,re)|0,s=(s=s+Math.imul(L,ne)|0)+Math.imul(O,re)|0,i=i+Math.imul(O,ne)|0,n=n+Math.imul(x,ie)|0,s=(s=s+Math.imul(x,oe)|0)+Math.imul(A,ie)|0,i=i+Math.imul(A,oe)|0,n=n+Math.imul(C,ce)|0,s=(s=s+Math.imul(C,le)|0)+Math.imul(I,ce)|0,i=i+Math.imul(I,le)|0,n=n+Math.imul(k,he)|0,s=(s=s+Math.imul(k,de)|0)+Math.imul(M,he)|0,i=i+Math.imul(M,de)|0;var Re=(l+(n=n+Math.imul(b,fe)|0)|0)+((8191&(s=(s=s+Math.imul(b,me)|0)+Math.imul(E,fe)|0))<<13)|0;l=((i=i+Math.imul(E,me)|0)+(s>>>13)|0)+(Re>>>26)|0,Re&=67108863,n=Math.imul(F,X),s=(s=Math.imul(F,ee))+Math.imul(D,X)|0,i=Math.imul(D,ee),n=n+Math.imul(T,re)|0,s=(s=s+Math.imul(T,ne)|0)+Math.imul(j,re)|0,i=i+Math.imul(j,ne)|0,n=n+Math.imul(L,ie)|0,s=(s=s+Math.imul(L,oe)|0)+Math.imul(O,ie)|0,i=i+Math.imul(O,oe)|0,n=n+Math.imul(x,ce)|0,s=(s=s+Math.imul(x,le)|0)+Math.imul(A,ce)|0,i=i+Math.imul(A,le)|0,n=n+Math.imul(C,he)|0,s=(s=s+Math.imul(C,de)|0)+Math.imul(I,he)|0,i=i+Math.imul(I,de)|0;var xe=(l+(n=n+Math.imul(k,fe)|0)|0)+((8191&(s=(s=s+Math.imul(k,me)|0)+Math.imul(M,fe)|0))<<13)|0;l=((i=i+Math.imul(M,me)|0)+(s>>>13)|0)+(xe>>>26)|0,xe&=67108863,n=Math.imul(F,re),s=(s=Math.imul(F,ne))+Math.imul(D,re)|0,i=Math.imul(D,ne),n=n+Math.imul(T,ie)|0,s=(s=s+Math.imul(T,oe)|0)+Math.imul(j,ie)|0,i=i+Math.imul(j,oe)|0,n=n+Math.imul(L,ce)|0,s=(s=s+Math.imul(L,le)|0)+Math.imul(O,ce)|0,i=i+Math.imul(O,le)|0,n=n+Math.imul(x,he)|0,s=(s=s+Math.imul(x,de)|0)+Math.imul(A,he)|0,i=i+Math.imul(A,de)|0;var Ae=(l+(n=n+Math.imul(C,fe)|0)|0)+((8191&(s=(s=s+Math.imul(C,me)|0)+Math.imul(I,fe)|0))<<13)|0;l=((i=i+Math.imul(I,me)|0)+(s>>>13)|0)+(Ae>>>26)|0,Ae&=67108863,n=Math.imul(F,ie),s=(s=Math.imul(F,oe))+Math.imul(D,ie)|0,i=Math.imul(D,oe),n=n+Math.imul(T,ce)|0,s=(s=s+Math.imul(T,le)|0)+Math.imul(j,ce)|0,i=i+Math.imul(j,le)|0,n=n+Math.imul(L,he)|0,s=(s=s+Math.imul(L,de)|0)+Math.imul(O,he)|0,i=i+Math.imul(O,de)|0;var Ne=(l+(n=n+Math.imul(x,fe)|0)|0)+((8191&(s=(s=s+Math.imul(x,me)|0)+Math.imul(A,fe)|0))<<13)|0;l=((i=i+Math.imul(A,me)|0)+(s>>>13)|0)+(Ne>>>26)|0,Ne&=67108863,n=Math.imul(F,ce),s=(s=Math.imul(F,le))+Math.imul(D,ce)|0,i=Math.imul(D,le),n=n+Math.imul(T,he)|0,s=(s=s+Math.imul(T,de)|0)+Math.imul(j,he)|0,i=i+Math.imul(j,de)|0;var Le=(l+(n=n+Math.imul(L,fe)|0)|0)+((8191&(s=(s=s+Math.imul(L,me)|0)+Math.imul(O,fe)|0))<<13)|0;l=((i=i+Math.imul(O,me)|0)+(s>>>13)|0)+(Le>>>26)|0,Le&=67108863,n=Math.imul(F,he),s=(s=Math.imul(F,de))+Math.imul(D,he)|0,i=Math.imul(D,de);var Oe=(l+(n=n+Math.imul(T,fe)|0)|0)+((8191&(s=(s=s+Math.imul(T,me)|0)+Math.imul(j,fe)|0))<<13)|0;l=((i=i+Math.imul(j,me)|0)+(s>>>13)|0)+(Oe>>>26)|0,Oe&=67108863;var Pe=(l+(n=Math.imul(F,fe))|0)+((8191&(s=(s=Math.imul(F,me))+Math.imul(D,fe)|0))<<13)|0;return l=((i=Math.imul(D,me))+(s>>>13)|0)+(Pe>>>26)|0,Pe&=67108863,c[0]=ge,c[1]=we,c[2]=ye,c[3]=ve,c[4]=be,c[5]=Ee,c[6]=_e,c[7]=ke,c[8]=Me,c[9]=Se,c[10]=Ce,c[11]=Ie,c[12]=Re,c[13]=xe,c[14]=Ae,c[15]=Ne,c[16]=Le,c[17]=Oe,c[18]=Pe,0!==l&&(c[19]=l,r.length++),r};function w(e,t,r){r.negative=t.negative^e.negative,r.length=e.length+t.length;for(var n=0,s=0,i=0;i<r.length-1;i++){var o=s;s=0;for(var a=67108863&n,c=Math.min(i,t.length-1),l=Math.max(0,i-e.length+1);l<=c;l++){var u=i-l,h=(0|e.words[u])*(0|t.words[l]),d=67108863&h;a=67108863&(d=d+a|0),s+=(o=(o=o+(h/67108864|0)|0)+(d>>>26)|0)>>>26,o&=67108863}r.words[i]=a,n=o,o=s}return 0!==n?r.words[i]=n:r.length--,r._strip()}function y(e,t,r){return w(e,t,r)}function v(e,t){this.x=e,this.y=t}Math.imul||(g=m),i.prototype.mulTo=function(e,t){var r=this.length+e.length;return 10===this.length&&10===e.length?g(this,e,t):r<63?m(this,e,t):r<1024?w(this,e,t):y(this,e,t)},v.prototype.makeRBT=function(e){for(var t=new Array(e),r=i.prototype._countBits(e)-1,n=0;n<e;n++)t[n]=this.revBin(n,r,e);return t},v.prototype.revBin=function(e,t,r){if(0===e||e===r-1)return e;for(var n=0,s=0;s<t;s++)n|=(1&e)<<t-s-1,e>>=1;return n},v.prototype.permute=function(e,t,r,n,s,i){for(var o=0;o<i;o++)n[o]=t[e[o]],s[o]=r[e[o]]},v.prototype.transform=function(e,t,r,n,s,i){this.permute(i,e,t,r,n,s);for(var o=1;o<s;o<<=1)for(var a=o<<1,c=Math.cos(2*Math.PI/a),l=Math.sin(2*Math.PI/a),u=0;u<s;u+=a)for(var h=c,d=l,p=0;p<o;p++){var f=r[u+p],m=n[u+p],g=r[u+p+o],w=n[u+p+o],y=h*g-d*w;w=h*w+d*g,g=y,r[u+p]=f+g,n[u+p]=m+w,r[u+p+o]=f-g,n[u+p+o]=m-w,p!==a&&(y=c*h-l*d,d=c*d+l*h,h=y)}},v.prototype.guessLen13b=function(e,t){var r=1|Math.max(t,e),n=1&r,s=0;for(r=r/2|0;r;r>>>=1)s++;return 1<<s+1+n},v.prototype.conjugate=function(e,t,r){if(!(r<=1))for(var n=0;n<r/2;n++){var s=e[n];e[n]=e[r-n-1],e[r-n-1]=s,s=t[n],t[n]=-t[r-n-1],t[r-n-1]=-s}},v.prototype.normalize13b=function(e,t){for(var r=0,n=0;n<t/2;n++){var s=8192*Math.round(e[2*n+1]/t)+Math.round(e[2*n]/t)+r;e[n]=67108863&s,r=s<67108864?0:s/67108864|0}return e},v.prototype.convert13b=function(e,t,r,s){for(var i=0,o=0;o<t;o++)i+=0|e[o],r[2*o]=8191&i,i>>>=13,r[2*o+1]=8191&i,i>>>=13;for(o=2*t;o<s;++o)r[o]=0;n(0===i),n(!(-8192&i))},v.prototype.stub=function(e){for(var t=new Array(e),r=0;r<e;r++)t[r]=0;return t},v.prototype.mulp=function(e,t,r){var n=2*this.guessLen13b(e.length,t.length),s=this.makeRBT(n),i=this.stub(n),o=new Array(n),a=new Array(n),c=new Array(n),l=new Array(n),u=new Array(n),h=new Array(n),d=r.words;d.length=n,this.convert13b(e.words,e.length,o,n),this.convert13b(t.words,t.length,l,n),this.transform(o,i,a,c,n,s),this.transform(l,i,u,h,n,s);for(var p=0;p<n;p++){var f=a[p]*u[p]-c[p]*h[p];c[p]=a[p]*h[p]+c[p]*u[p],a[p]=f}return this.conjugate(a,c,n),this.transform(a,c,d,i,n,s),this.conjugate(d,i,n),this.normalize13b(d,n),r.negative=e.negative^t.negative,r.length=e.length+t.length,r._strip()},i.prototype.mul=function(e){var t=new i(null);return t.words=new Array(this.length+e.length),this.mulTo(e,t)},i.prototype.mulf=function(e){var t=new i(null);return t.words=new Array(this.length+e.length),y(this,e,t)},i.prototype.imul=function(e){return this.clone().mulTo(e,this)},i.prototype.imuln=function(e){var t=e<0;t&&(e=-e),n("number"==typeof e),n(e<67108864);for(var r=0,s=0;s<this.length;s++){var i=(0|this.words[s])*e,o=(67108863&i)+(67108863&r);r>>=26,r+=i/67108864|0,r+=o>>>26,this.words[s]=67108863&o}return 0!==r&&(this.words[s]=r,this.length++),t?this.ineg():this},i.prototype.muln=function(e){return this.clone().imuln(e)},i.prototype.sqr=function(){return this.mul(this)},i.prototype.isqr=function(){return this.imul(this.clone())},i.prototype.pow=function(e){var t=function(e){for(var t=new Array(e.bitLength()),r=0;r<t.length;r++){var n=r/26|0,s=r%26;t[r]=e.words[n]>>>s&1}return t}(e);if(0===t.length)return new i(1);for(var r=this,n=0;n<t.length&&0===t[n];n++,r=r.sqr());if(++n<t.length)for(var s=r.sqr();n<t.length;n++,s=s.sqr())0!==t[n]&&(r=r.mul(s));return r},i.prototype.iushln=function(e){n("number"==typeof e&&e>=0);var t,r=e%26,s=(e-r)/26,i=67108863>>>26-r<<26-r;if(0!==r){var o=0;for(t=0;t<this.length;t++){var a=this.words[t]&i,c=(0|this.words[t])-a<<r;this.words[t]=c|o,o=a>>>26-r}o&&(this.words[t]=o,this.length++)}if(0!==s){for(t=this.length-1;t>=0;t--)this.words[t+s]=this.words[t];for(t=0;t<s;t++)this.words[t]=0;this.length+=s}return this._strip()},i.prototype.ishln=function(e){return n(0===this.negative),this.iushln(e)},i.prototype.iushrn=function(e,t,r){var s;n("number"==typeof e&&e>=0),s=t?(t-t%26)/26:0;var i=e%26,o=Math.min((e-i)/26,this.length),a=67108863^67108863>>>i<<i,c=r;if(s-=o,s=Math.max(0,s),c){for(var l=0;l<o;l++)c.words[l]=this.words[l];c.length=o}if(0===o);else if(this.length>o)for(this.length-=o,l=0;l<this.length;l++)this.words[l]=this.words[l+o];else this.words[0]=0,this.length=1;var u=0;for(l=this.length-1;l>=0&&(0!==u||l>=s);l--){var h=0|this.words[l];this.words[l]=u<<26-i|h>>>i,u=h&a}return c&&0!==u&&(c.words[c.length++]=u),0===this.length&&(this.words[0]=0,this.length=1),this._strip()},i.prototype.ishrn=function(e,t,r){return n(0===this.negative),this.iushrn(e,t,r)},i.prototype.shln=function(e){return this.clone().ishln(e)},i.prototype.ushln=function(e){return this.clone().iushln(e)},i.prototype.shrn=function(e){return this.clone().ishrn(e)},i.prototype.ushrn=function(e){return this.clone().iushrn(e)},i.prototype.testn=function(e){n("number"==typeof e&&e>=0);var t=e%26,r=(e-t)/26,s=1<<t;return!(this.length<=r||!(this.words[r]&s))},i.prototype.imaskn=function(e){n("number"==typeof e&&e>=0);var t=e%26,r=(e-t)/26;if(n(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)return this;if(0!==t&&r++,this.length=Math.min(r,this.length),0!==t){var s=67108863^67108863>>>t<<t;this.words[this.length-1]&=s}return this._strip()},i.prototype.maskn=function(e){return this.clone().imaskn(e)},i.prototype.iaddn=function(e){return n("number"==typeof e),n(e<67108864),e<0?this.isubn(-e):0!==this.negative?1===this.length&&(0|this.words[0])<=e?(this.words[0]=e-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(e),this.negative=1,this):this._iaddn(e)},i.prototype._iaddn=function(e){this.words[0]+=e;for(var t=0;t<this.length&&this.words[t]>=67108864;t++)this.words[t]-=67108864,t===this.length-1?this.words[t+1]=1:this.words[t+1]++;return this.length=Math.max(this.length,t+1),this},i.prototype.isubn=function(e){if(n("number"==typeof e),n(e<67108864),e<0)return this.iaddn(-e);if(0!==this.negative)return this.negative=0,this.iaddn(e),this.negative=1,this;if(this.words[0]-=e,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var t=0;t<this.length&&this.words[t]<0;t++)this.words[t]+=67108864,this.words[t+1]-=1;return this._strip()},i.prototype.addn=function(e){return this.clone().iaddn(e)},i.prototype.subn=function(e){return this.clone().isubn(e)},i.prototype.iabs=function(){return this.negative=0,this},i.prototype.abs=function(){return this.clone().iabs()},i.prototype._ishlnsubmul=function(e,t,r){var s,i,o=e.length+r;this._expand(o);var a=0;for(s=0;s<e.length;s++){i=(0|this.words[s+r])+a;var c=(0|e.words[s])*t;a=((i-=67108863&c)>>26)-(c/67108864|0),this.words[s+r]=67108863&i}for(;s<this.length-r;s++)a=(i=(0|this.words[s+r])+a)>>26,this.words[s+r]=67108863&i;if(0===a)return this._strip();for(n(-1===a),a=0,s=0;s<this.length;s++)a=(i=-(0|this.words[s])+a)>>26,this.words[s]=67108863&i;return this.negative=1,this._strip()},i.prototype._wordDiv=function(e,t){var r=(this.length,e.length),n=this.clone(),s=e,o=0|s.words[s.length-1];0!=(r=26-this._countBits(o))&&(s=s.ushln(r),n.iushln(r),o=0|s.words[s.length-1]);var a,c=n.length-s.length;if("mod"!==t){(a=new i(null)).length=c+1,a.words=new Array(a.length);for(var l=0;l<a.length;l++)a.words[l]=0}var u=n.clone()._ishlnsubmul(s,1,c);0===u.negative&&(n=u,a&&(a.words[c]=1));for(var h=c-1;h>=0;h--){var d=67108864*(0|n.words[s.length+h])+(0|n.words[s.length+h-1]);for(d=Math.min(d/o|0,67108863),n._ishlnsubmul(s,d,h);0!==n.negative;)d--,n.negative=0,n._ishlnsubmul(s,1,h),n.isZero()||(n.negative^=1);a&&(a.words[h]=d)}return a&&a._strip(),n._strip(),"div"!==t&&0!==r&&n.iushrn(r),{div:a||null,mod:n}},i.prototype.divmod=function(e,t,r){return n(!e.isZero()),this.isZero()?{div:new i(0),mod:new i(0)}:0!==this.negative&&0===e.negative?(a=this.neg().divmod(e,t),"mod"!==t&&(s=a.div.neg()),"div"!==t&&(o=a.mod.neg(),r&&0!==o.negative&&o.iadd(e)),{div:s,mod:o}):0===this.negative&&0!==e.negative?(a=this.divmod(e.neg(),t),"mod"!==t&&(s=a.div.neg()),{div:s,mod:a.mod}):this.negative&e.negative?(a=this.neg().divmod(e.neg(),t),"div"!==t&&(o=a.mod.neg(),r&&0!==o.negative&&o.isub(e)),{div:a.div,mod:o}):e.length>this.length||this.cmp(e)<0?{div:new i(0),mod:this}:1===e.length?"div"===t?{div:this.divn(e.words[0]),mod:null}:"mod"===t?{div:null,mod:new i(this.modrn(e.words[0]))}:{div:this.divn(e.words[0]),mod:new i(this.modrn(e.words[0]))}:this._wordDiv(e,t);var s,o,a},i.prototype.div=function(e){return this.divmod(e,"div",!1).div},i.prototype.mod=function(e){return this.divmod(e,"mod",!1).mod},i.prototype.umod=function(e){return this.divmod(e,"mod",!0).mod},i.prototype.divRound=function(e){var t=this.divmod(e);if(t.mod.isZero())return t.div;var r=0!==t.div.negative?t.mod.isub(e):t.mod,n=e.ushrn(1),s=e.andln(1),i=r.cmp(n);return i<0||1===s&&0===i?t.div:0!==t.div.negative?t.div.isubn(1):t.div.iaddn(1)},i.prototype.modrn=function(e){var t=e<0;t&&(e=-e),n(e<=67108863);for(var r=(1<<26)%e,s=0,i=this.length-1;i>=0;i--)s=(r*s+(0|this.words[i]))%e;return t?-s:s},i.prototype.modn=function(e){return this.modrn(e)},i.prototype.idivn=function(e){var t=e<0;t&&(e=-e),n(e<=67108863);for(var r=0,s=this.length-1;s>=0;s--){var i=(0|this.words[s])+67108864*r;this.words[s]=i/e|0,r=i%e}return this._strip(),t?this.ineg():this},i.prototype.divn=function(e){return this.clone().idivn(e)},i.prototype.egcd=function(e){n(0===e.negative),n(!e.isZero());var t=this,r=e.clone();t=0!==t.negative?t.umod(e):t.clone();for(var s=new i(1),o=new i(0),a=new i(0),c=new i(1),l=0;t.isEven()&&r.isEven();)t.iushrn(1),r.iushrn(1),++l;for(var u=r.clone(),h=t.clone();!t.isZero();){for(var d=0,p=1;!(t.words[0]&p)&&d<26;++d,p<<=1);if(d>0)for(t.iushrn(d);d-- >0;)(s.isOdd()||o.isOdd())&&(s.iadd(u),o.isub(h)),s.iushrn(1),o.iushrn(1);for(var f=0,m=1;!(r.words[0]&m)&&f<26;++f,m<<=1);if(f>0)for(r.iushrn(f);f-- >0;)(a.isOdd()||c.isOdd())&&(a.iadd(u),c.isub(h)),a.iushrn(1),c.iushrn(1);t.cmp(r)>=0?(t.isub(r),s.isub(a),o.isub(c)):(r.isub(t),a.isub(s),c.isub(o))}return{a,b:c,gcd:r.iushln(l)}},i.prototype._invmp=function(e){n(0===e.negative),n(!e.isZero());var t=this,r=e.clone();t=0!==t.negative?t.umod(e):t.clone();for(var s,o=new i(1),a=new i(0),c=r.clone();t.cmpn(1)>0&&r.cmpn(1)>0;){for(var l=0,u=1;!(t.words[0]&u)&&l<26;++l,u<<=1);if(l>0)for(t.iushrn(l);l-- >0;)o.isOdd()&&o.iadd(c),o.iushrn(1);for(var h=0,d=1;!(r.words[0]&d)&&h<26;++h,d<<=1);if(h>0)for(r.iushrn(h);h-- >0;)a.isOdd()&&a.iadd(c),a.iushrn(1);t.cmp(r)>=0?(t.isub(r),o.isub(a)):(r.isub(t),a.isub(o))}return(s=0===t.cmpn(1)?o:a).cmpn(0)<0&&s.iadd(e),s},i.prototype.gcd=function(e){if(this.isZero())return e.abs();if(e.isZero())return this.abs();var t=this.clone(),r=e.clone();t.negative=0,r.negative=0;for(var n=0;t.isEven()&&r.isEven();n++)t.iushrn(1),r.iushrn(1);for(;;){for(;t.isEven();)t.iushrn(1);for(;r.isEven();)r.iushrn(1);var s=t.cmp(r);if(s<0){var i=t;t=r,r=i}else if(0===s||0===r.cmpn(1))break;t.isub(r)}return r.iushln(n)},i.prototype.invm=function(e){return this.egcd(e).a.umod(e)},i.prototype.isEven=function(){return!(1&this.words[0])},i.prototype.isOdd=function(){return!(1&~this.words[0])},i.prototype.andln=function(e){return this.words[0]&e},i.prototype.bincn=function(e){n("number"==typeof e);var t=e%26,r=(e-t)/26,s=1<<t;if(this.length<=r)return this._expand(r+1),this.words[r]|=s,this;for(var i=s,o=r;0!==i&&o<this.length;o++){var a=0|this.words[o];i=(a+=i)>>>26,a&=67108863,this.words[o]=a}return 0!==i&&(this.words[o]=i,this.length++),this},i.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},i.prototype.cmpn=function(e){var t,r=e<0;if(0!==this.negative&&!r)return-1;if(0===this.negative&&r)return 1;if(this._strip(),this.length>1)t=1;else{r&&(e=-e),n(e<=67108863,"Number is too big");var s=0|this.words[0];t=s===e?0:s<e?-1:1}return 0!==this.negative?0|-t:t},i.prototype.cmp=function(e){if(0!==this.negative&&0===e.negative)return-1;if(0===this.negative&&0!==e.negative)return 1;var t=this.ucmp(e);return 0!==this.negative?0|-t:t},i.prototype.ucmp=function(e){if(this.length>e.length)return 1;if(this.length<e.length)return-1;for(var t=0,r=this.length-1;r>=0;r--){var n=0|this.words[r],s=0|e.words[r];if(n!==s){n<s?t=-1:n>s&&(t=1);break}}return t},i.prototype.gtn=function(e){return 1===this.cmpn(e)},i.prototype.gt=function(e){return 1===this.cmp(e)},i.prototype.gten=function(e){return this.cmpn(e)>=0},i.prototype.gte=function(e){return this.cmp(e)>=0},i.prototype.ltn=function(e){return-1===this.cmpn(e)},i.prototype.lt=function(e){return-1===this.cmp(e)},i.prototype.lten=function(e){return this.cmpn(e)<=0},i.prototype.lte=function(e){return this.cmp(e)<=0},i.prototype.eqn=function(e){return 0===this.cmpn(e)},i.prototype.eq=function(e){return 0===this.cmp(e)},i.red=function(e){return new C(e)},i.prototype.toRed=function(e){return n(!this.red,"Already a number in reduction context"),n(0===this.negative,"red works only with positives"),e.convertTo(this)._forceRed(e)},i.prototype.fromRed=function(){return n(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},i.prototype._forceRed=function(e){return this.red=e,this},i.prototype.forceRed=function(e){return n(!this.red,"Already a number in reduction context"),this._forceRed(e)},i.prototype.redAdd=function(e){return n(this.red,"redAdd works only with red numbers"),this.red.add(this,e)},i.prototype.redIAdd=function(e){return n(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,e)},i.prototype.redSub=function(e){return n(this.red,"redSub works only with red numbers"),this.red.sub(this,e)},i.prototype.redISub=function(e){return n(this.red,"redISub works only with red numbers"),this.red.isub(this,e)},i.prototype.redShl=function(e){return n(this.red,"redShl works only with red numbers"),this.red.shl(this,e)},i.prototype.redMul=function(e){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,e),this.red.mul(this,e)},i.prototype.redIMul=function(e){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,e),this.red.imul(this,e)},i.prototype.redSqr=function(){return n(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},i.prototype.redISqr=function(){return n(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},i.prototype.redSqrt=function(){return n(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},i.prototype.redInvm=function(){return n(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},i.prototype.redNeg=function(){return n(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},i.prototype.redPow=function(e){return n(this.red&&!e.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,e)};var b={k256:null,p224:null,p192:null,p25519:null};function E(e,t){this.name=e,this.p=new i(t,16),this.n=this.p.bitLength(),this.k=new i(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function _(){E.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function k(){E.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function M(){E.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function S(){E.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function C(e){if("string"==typeof e){var t=i._prime(e);this.m=t.p,this.prime=t}else n(e.gtn(1),"modulus must be greater than 1"),this.m=e,this.prime=null}function I(e){C.call(this,e),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new i(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}E.prototype._tmp=function(){var e=new i(null);return e.words=new Array(Math.ceil(this.n/13)),e},E.prototype.ireduce=function(e){var t,r=e;do{this.split(r,this.tmp),t=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(t>this.n);var n=t<this.n?-1:r.ucmp(this.p);return 0===n?(r.words[0]=0,r.length=1):n>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},E.prototype.split=function(e,t){e.iushrn(this.n,0,t)},E.prototype.imulK=function(e){return e.imul(this.k)},s(_,E),_.prototype.split=function(e,t){for(var r=4194303,n=Math.min(e.length,9),s=0;s<n;s++)t.words[s]=e.words[s];if(t.length=n,e.length<=9)return e.words[0]=0,void(e.length=1);var i=e.words[9];for(t.words[t.length++]=i&r,s=10;s<e.length;s++){var o=0|e.words[s];e.words[s-10]=(o&r)<<4|i>>>22,i=o}i>>>=22,e.words[s-10]=i,0===i&&e.length>10?e.length-=10:e.length-=9},_.prototype.imulK=function(e){e.words[e.length]=0,e.words[e.length+1]=0,e.length+=2;for(var t=0,r=0;r<e.length;r++){var n=0|e.words[r];t+=977*n,e.words[r]=67108863&t,t=64*n+(t/67108864|0)}return 0===e.words[e.length-1]&&(e.length--,0===e.words[e.length-1]&&e.length--),e},s(k,E),s(M,E),s(S,E),S.prototype.imulK=function(e){for(var t=0,r=0;r<e.length;r++){var n=19*(0|e.words[r])+t,s=67108863&n;n>>>=26,e.words[r]=s,t=n}return 0!==t&&(e.words[e.length++]=t),e},i._prime=function(e){if(b[e])return b[e];var t;if("k256"===e)t=new _;else if("p224"===e)t=new k;else if("p192"===e)t=new M;else{if("p25519"!==e)throw new Error("Unknown prime "+e);t=new S}return b[e]=t,t},C.prototype._verify1=function(e){n(0===e.negative,"red works only with positives"),n(e.red,"red works only with red numbers")},C.prototype._verify2=function(e,t){n(!(e.negative|t.negative),"red works only with positives"),n(e.red&&e.red===t.red,"red works only with red numbers")},C.prototype.imod=function(e){return this.prime?this.prime.ireduce(e)._forceRed(this):(u(e,e.umod(this.m)._forceRed(this)),e)},C.prototype.neg=function(e){return e.isZero()?e.clone():this.m.sub(e)._forceRed(this)},C.prototype.add=function(e,t){this._verify2(e,t);var r=e.add(t);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},C.prototype.iadd=function(e,t){this._verify2(e,t);var r=e.iadd(t);return r.cmp(this.m)>=0&&r.isub(this.m),r},C.prototype.sub=function(e,t){this._verify2(e,t);var r=e.sub(t);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},C.prototype.isub=function(e,t){this._verify2(e,t);var r=e.isub(t);return r.cmpn(0)<0&&r.iadd(this.m),r},C.prototype.shl=function(e,t){return this._verify1(e),this.imod(e.ushln(t))},C.prototype.imul=function(e,t){return this._verify2(e,t),this.imod(e.imul(t))},C.prototype.mul=function(e,t){return this._verify2(e,t),this.imod(e.mul(t))},C.prototype.isqr=function(e){return this.imul(e,e.clone())},C.prototype.sqr=function(e){return this.mul(e,e)},C.prototype.sqrt=function(e){if(e.isZero())return e.clone();var t=this.m.andln(3);if(n(t%2==1),3===t){var r=this.m.add(new i(1)).iushrn(2);return this.pow(e,r)}for(var s=this.m.subn(1),o=0;!s.isZero()&&0===s.andln(1);)o++,s.iushrn(1);n(!s.isZero());var a=new i(1).toRed(this),c=a.redNeg(),l=this.m.subn(1).iushrn(1),u=this.m.bitLength();for(u=new i(2*u*u).toRed(this);0!==this.pow(u,l).cmp(c);)u.redIAdd(c);for(var h=this.pow(u,s),d=this.pow(e,s.addn(1).iushrn(1)),p=this.pow(e,s),f=o;0!==p.cmp(a);){for(var m=p,g=0;0!==m.cmp(a);g++)m=m.redSqr();n(g<f);var w=this.pow(h,new i(1).iushln(f-g-1));d=d.redMul(w),h=w.redSqr(),p=p.redMul(h),f=g}return d},C.prototype.invm=function(e){var t=e._invmp(this.m);return 0!==t.negative?(t.negative=0,this.imod(t).redNeg()):this.imod(t)},C.prototype.pow=function(e,t){if(t.isZero())return new i(1).toRed(this);if(0===t.cmpn(1))return e.clone();var r=new Array(16);r[0]=new i(1).toRed(this),r[1]=e;for(var n=2;n<r.length;n++)r[n]=this.mul(r[n-1],e);var s=r[0],o=0,a=0,c=t.bitLength()%26;for(0===c&&(c=26),n=t.length-1;n>=0;n--){for(var l=t.words[n],u=c-1;u>=0;u--){var h=l>>u&1;s!==r[0]&&(s=this.sqr(s)),0!==h||0!==o?(o<<=1,o|=h,(4==++a||0===n&&0===u)&&(s=this.mul(s,r[o]),a=0,o=0)):a=0}c=26}return s},C.prototype.convertTo=function(e){var t=e.umod(this.m);return t===e?t.clone():t},C.prototype.convertFrom=function(e){var t=e.clone();return t.red=null,t},i.mont=function(e){return new I(e)},s(I,C),I.prototype.convertTo=function(e){return this.imod(e.ushln(this.shift))},I.prototype.convertFrom=function(e){var t=this.imod(e.mul(this.rinv));return t.red=null,t},I.prototype.imul=function(e,t){if(e.isZero()||t.isZero())return e.words[0]=0,e.length=1,e;var r=e.imul(t),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),s=r.isub(n).iushrn(this.shift),i=s;return s.cmp(this.m)>=0?i=s.isub(this.m):s.cmpn(0)<0&&(i=s.iadd(this.m)),i._forceRed(this)},I.prototype.mul=function(e,t){if(e.isZero()||t.isZero())return new i(0)._forceRed(this);var r=e.mul(t),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),s=r.isub(n).iushrn(this.shift),o=s;return s.cmp(this.m)>=0?o=s.isub(this.m):s.cmpn(0)<0&&(o=s.iadd(this.m)),o._forceRed(this)},I.prototype.invm=function(e){return this.imod(e._invmp(this.m).mul(this.r2))._forceRed(this)}}(e=r.nmd(e),this)},80166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CoinbaseWalletSDK=void 0;const n=r(59120),s=r(77920),i=r(97),o=r(47637),a=r(92071),c=r(31625),l=r(73647),u=r(32261),h=r(70764),d=r(58315),p=r(55405);class f{constructor(e){var t,r,n;this._appName="",this._appLogoUrl=null,this._relay=null,this._relayEventManager=null;const a=e.linkAPIUrl||s.LINK_API_URL;void 0===e.overrideIsMetaMask?this._overrideIsMetaMask=!1:this._overrideIsMetaMask=e.overrideIsMetaMask,this._overrideIsCoinbaseWallet=null===(t=e.overrideIsCoinbaseWallet)||void 0===t||t,this._overrideIsCoinbaseBrowser=null!==(r=e.overrideIsCoinbaseBrowser)&&void 0!==r&&r,this._diagnosticLogger=e.diagnosticLogger,this._reloadOnDisconnect=null===(n=e.reloadOnDisconnect)||void 0===n||n;const m=new URL(a),g=`${m.protocol}//${m.host}`;if(this._storage=new o.ScopedLocalStorage(`-walletlink:${g}`),this._storage.setItem("version",f.VERSION),this.walletExtension||this.coinbaseBrowser)return;this._relayEventManager=new u.RelayEventManager;const w=(0,i.isMobileWeb)(),y=e.uiConstructor||(e=>w?new l.MobileRelayUI(e):new h.WalletLinkRelayUI(e)),v={linkAPIUrl:a,version:p.LIB_VERSION,darkMode:!!e.darkMode,headlessMode:!!e.headlessMode,uiConstructor:y,storage:this._storage,relayEventManager:this._relayEventManager,diagnosticLogger:this._diagnosticLogger,reloadOnDisconnect:this._reloadOnDisconnect,enableMobileWalletLink:e.enableMobileWalletLink};this._relay=w?new c.MobileRelay(v):new d.WalletLinkRelay(v),this.setAppInfo(e.appName,e.appLogoUrl),e.headlessMode||this._relay.attachUI()}makeWeb3Provider(e="",t=1){const r=this.walletExtension;if(r)return this.isCipherProvider(r)||r.setProviderInfo(e,t),!1===this._reloadOnDisconnect&&"function"==typeof r.disableReloadOnDisconnect&&r.disableReloadOnDisconnect(),r;const n=this.coinbaseBrowser;if(n)return n;const s=this._relay;if(!s||!this._relayEventManager||!this._storage)throw new Error("Relay not initialized, should never happen");return e||s.setConnectDisabled(!0),new a.CoinbaseWalletProvider({relayProvider:()=>Promise.resolve(s),relayEventManager:this._relayEventManager,storage:this._storage,jsonRpcUrl:e,chainId:t,qrUrl:this.getQrUrl(),diagnosticLogger:this._diagnosticLogger,overrideIsMetaMask:this._overrideIsMetaMask,overrideIsCoinbaseWallet:this._overrideIsCoinbaseWallet,overrideIsCoinbaseBrowser:this._overrideIsCoinbaseBrowser})}setAppInfo(e,t){var r;this._appName=e||"DApp",this._appLogoUrl=t||(0,i.getFavicon)();const n=this.walletExtension;n?this.isCipherProvider(n)||n.setAppInfo(this._appName,this._appLogoUrl):null===(r=this._relay)||void 0===r||r.setAppInfo(this._appName,this._appLogoUrl)}disconnect(){var e;const t=null==this?void 0:this.walletExtension;t?t.close():null===(e=this._relay)||void 0===e||e.resetAndReload()}getQrUrl(){var e,t;return null!==(t=null===(e=this._relay)||void 0===e?void 0:e.getQRCodeUrl())&&void 0!==t?t:null}getCoinbaseWalletLogo(e,t=240){return(0,n.walletLogo)(e,t)}get walletExtension(){var e;return null!==(e=window.coinbaseWalletExtension)&&void 0!==e?e:window.walletLinkExtension}get coinbaseBrowser(){var e,t;try{const r=null!==(e=window.ethereum)&&void 0!==e?e:null===(t=window.top)||void 0===t?void 0:t.ethereum;if(!r)return;return"isCoinbaseBrowser"in r&&r.isCoinbaseBrowser?r:void 0}catch(e){return}}isCipherProvider(e){return"boolean"==typeof e.isCipher&&e.isCipher}}t.CoinbaseWalletSDK=f,f.VERSION=p.LIB_VERSION},59120:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.walletLogo=void 0,t.walletLogo=(e,t)=>{let r;switch(e){case"standard":default:return r=t,`data:image/svg+xml,%3Csvg width='${t}' height='${r}' viewBox='0 0 1024 1024' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Crect width='1024' height='1024' fill='%230052FF'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M152 512C152 710.823 313.177 872 512 872C710.823 872 872 710.823 872 512C872 313.177 710.823 152 512 152C313.177 152 152 313.177 152 512ZM420 396C406.745 396 396 406.745 396 420V604C396 617.255 406.745 628 420 628H604C617.255 628 628 617.255 628 604V420C628 406.745 617.255 396 604 396H420Z' fill='white'/%3E %3C/svg%3E `;case"circle":return r=t,`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${t}' height='${r}' viewBox='0 0 999.81 999.81'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052fe;%7D.cls-2%7Bfill:%23fefefe;%7D.cls-3%7Bfill:%230152fe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M655-115.9h56c.83,1.59,2.36.88,3.56,1a478,478,0,0,1,75.06,10.42C891.4-81.76,978.33-32.58,1049.19,44q116.7,126,131.94,297.61c.38,4.14-.34,8.53,1.78,12.45v59c-1.58.84-.91,2.35-1,3.56a482.05,482.05,0,0,1-10.38,74.05c-24,106.72-76.64,196.76-158.83,268.93s-178.18,112.82-287.2,122.6c-4.83.43-9.86-.25-14.51,1.77H654c-1-1.68-2.69-.91-4.06-1a496.89,496.89,0,0,1-105.9-18.59c-93.54-27.42-172.78-77.59-236.91-150.94Q199.34,590.1,184.87,426.58c-.47-5.19.25-10.56-1.77-15.59V355c1.68-1,.91-2.7,1-4.06a498.12,498.12,0,0,1,18.58-105.9c26-88.75,72.64-164.9,140.6-227.57q126-116.27,297.21-131.61C645.32-114.57,650.35-113.88,655-115.9Zm377.92,500c0-192.44-156.31-349.49-347.56-350.15-194.13-.68-350.94,155.13-352.29,347.42-1.37,194.55,155.51,352.1,348.56,352.47C876.15,734.23,1032.93,577.84,1032.93,384.11Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-2' d='M1032.93,384.11c0,193.73-156.78,350.12-351.29,349.74-193-.37-349.93-157.92-348.56-352.47C334.43,189.09,491.24,33.28,685.37,34,876.62,34.62,1032.94,191.67,1032.93,384.11ZM683,496.81q43.74,0,87.48,0c15.55,0,25.32-9.72,25.33-25.21q0-87.48,0-175c0-15.83-9.68-25.46-25.59-25.46H595.77c-15.88,0-25.57,9.64-25.58,25.46q0,87.23,0,174.45c0,16.18,9.59,25.7,25.84,25.71Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-3' d='M683,496.81H596c-16.25,0-25.84-9.53-25.84-25.71q0-87.23,0-174.45c0-15.82,9.7-25.46,25.58-25.46H770.22c15.91,0,25.59,9.63,25.59,25.46q0,87.47,0,175c0,15.49-9.78,25.2-25.33,25.21Q726.74,496.84,683,496.81Z' transform='translate(-183.1 115.9)'/%3E%3C/svg%3E`;case"text":return r=(.1*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;case"textWithLogo":return r=(.25*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`;case"textLight":return r=(.1*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;case"textWithLogoLight":return r=(.25*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${r}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`}}},77920:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LINK_API_URL=void 0,t.LINK_API_URL="https://www.walletlink.org"},33337:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.errorValues=t.standardErrorCodes=void 0,t.standardErrorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901,unsupportedChain:4902}},t.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."},4902:{standard:"EIP-3085",message:"Unrecognized chain ID."}}},55733:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.standardErrors=void 0;const n=r(33337),s=r(84637);function i(e,t){const[r,n]=a(t);return new c(e,r||(0,s.getMessageFromCode)(e),n)}function o(e,t){const[r,n]=a(t);return new l(e,r||(0,s.getMessageFromCode)(e),n)}function a(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t||void 0,r]}}return[]}t.standardErrors={rpc:{parse:e=>i(n.standardErrorCodes.rpc.parse,e),invalidRequest:e=>i(n.standardErrorCodes.rpc.invalidRequest,e),invalidParams:e=>i(n.standardErrorCodes.rpc.invalidParams,e),methodNotFound:e=>i(n.standardErrorCodes.rpc.methodNotFound,e),internal:e=>i(n.standardErrorCodes.rpc.internal,e),server:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return i(t,e)},invalidInput:e=>i(n.standardErrorCodes.rpc.invalidInput,e),resourceNotFound:e=>i(n.standardErrorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>i(n.standardErrorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>i(n.standardErrorCodes.rpc.transactionRejected,e),methodNotSupported:e=>i(n.standardErrorCodes.rpc.methodNotSupported,e),limitExceeded:e=>i(n.standardErrorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>o(n.standardErrorCodes.provider.userRejectedRequest,e),unauthorized:e=>o(n.standardErrorCodes.provider.unauthorized,e),unsupportedMethod:e=>o(n.standardErrorCodes.provider.unsupportedMethod,e),disconnected:e=>o(n.standardErrorCodes.provider.disconnected,e),chainDisconnected:e=>o(n.standardErrorCodes.provider.chainDisconnected,e),unsupportedChain:e=>o(n.standardErrorCodes.provider.unsupportedChain,e),custom:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:t,message:r,data:n}=e;if(!r||"string"!=typeof r)throw new Error('"message" must be a nonempty string');return new l(t,r,n)}}};class c extends Error{constructor(e,t,r){if(!Number.isInteger(e))throw new Error('"code" must be an integer.');if(!t||"string"!=typeof t)throw new Error('"message" must be a nonempty string.');super(t),this.code=e,void 0!==r&&(this.data=r)}}class l extends c{constructor(e,t,r){if(!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(e,t,r)}}},66548:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.standardErrors=t.standardErrorCodes=t.serializeError=t.getMessageFromCode=t.getErrorCode=void 0;const n=r(33337);Object.defineProperty(t,"standardErrorCodes",{enumerable:!0,get:function(){return n.standardErrorCodes}});const s=r(55733);Object.defineProperty(t,"standardErrors",{enumerable:!0,get:function(){return s.standardErrors}});const i=r(54590);Object.defineProperty(t,"serializeError",{enumerable:!0,get:function(){return i.serializeError}});const o=r(84637);Object.defineProperty(t,"getErrorCode",{enumerable:!0,get:function(){return o.getErrorCode}}),Object.defineProperty(t,"getMessageFromCode",{enumerable:!0,get:function(){return o.getMessageFromCode}})},54590:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.serializeError=void 0;const n=r(34040),s=r(55405),i=r(33337),o=r(84637);t.serializeError=function(e,t){const r=(0,o.serialize)(function(e){return"string"==typeof e?{message:e,code:i.standardErrorCodes.rpc.internal}:(0,n.isErrorResponse)(e)?Object.assign(Object.assign({},e),{message:e.errorMessage,code:e.errorCode,data:{method:e.method}}):e}(e),{shouldIncludeStack:!0}),a=new URL("https://docs.cloud.coinbase.com/wallet-sdk/docs/errors");a.searchParams.set("version",s.LIB_VERSION),a.searchParams.set("code",r.code.toString());const c=function(e,t){const r=null==e?void 0:e.method;return r||(void 0!==t?"string"==typeof t?t:Array.isArray(t)?t.length>0?t[0].method:void 0:t.method:void 0)}(r.data,t);return c&&a.searchParams.set("method",c),a.searchParams.set("message",r.message),Object.assign(Object.assign({},r),{docUrl:a.href})}},84637:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.serialize=t.getErrorCode=t.isValidCode=t.getMessageFromCode=t.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const n=r(33337),s="Unspecified error message.";function i(e,r=s){if(e&&Number.isInteger(e)){const r=e.toString();if(l(n.errorValues,r))return n.errorValues[r].message;if(a(e))return t.JSON_RPC_SERVER_ERROR_MESSAGE}return r}function o(e){if(!Number.isInteger(e))return!1;const t=e.toString();return!!n.errorValues[t]||!!a(e)}function a(e){return e>=-32099&&e<=-32e3}function c(e){return e&&"object"==typeof e&&!Array.isArray(e)?Object.assign({},e):e}function l(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function u(e,t){return"object"==typeof e&&null!==e&&t in e&&"string"==typeof e[t]}t.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.",t.getMessageFromCode=i,t.isValidCode=o,t.getErrorCode=function(e){var t;return"number"==typeof e?e:function(e){return"object"==typeof e&&null!==e&&("number"==typeof e.code||"number"==typeof e.errorCode)}(e)?null!==(t=e.code)&&void 0!==t?t:e.errorCode:void 0},t.serialize=function(e,{shouldIncludeStack:t=!1}={}){const r={};if(e&&"object"==typeof e&&!Array.isArray(e)&&l(e,"code")&&o(e.code)){const t=e;r.code=t.code,t.message&&"string"==typeof t.message?(r.message=t.message,l(t,"data")&&(r.data=t.data)):(r.message=i(r.code),r.data={originalError:c(e)})}else r.code=n.standardErrorCodes.rpc.internal,r.message=u(e,"message")?e.message:s,r.data={originalError:c(e)};return t&&(r.stack=u(e,"stack")?e.stack:void 0),r}},98957:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ProviderType=t.RegExpString=t.IntNumber=t.BigIntString=t.AddressString=t.HexString=t.OpaqueType=void 0,t.OpaqueType=function(){return e=>e},t.HexString=e=>e,t.AddressString=e=>e,t.BigIntString=e=>e,t.IntNumber=function(e){return Math.floor(e)},t.RegExpString=e=>e,function(e){e.CoinbaseWallet="CoinbaseWallet",e.MetaMask="MetaMask",e.Unselected=""}(r||(t.ProviderType=r={}))},97:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.isMobileWeb=t.getLocation=t.isInIFrame=t.createQrUrl=t.getFavicon=t.range=t.isBigNumber=t.ensureParsedJSONObject=t.ensureBN=t.ensureRegExpString=t.ensureIntNumber=t.ensureBuffer=t.ensureAddressString=t.ensureEvenLengthHexString=t.ensureHexString=t.isHexString=t.prepend0x=t.strip0x=t.has0xPrefix=t.hexStringFromIntNumber=t.intNumberFromHexString=t.bigIntStringFromBN=t.hexStringFromBuffer=t.hexStringToUint8Array=t.uint8ArrayToHex=t.randomBytesHex=void 0;const s=n(r(39404)),i=r(66548),o=r(98957),a=/^[0-9]*$/,c=/^[a-f0-9]*$/;function l(e){return[...e].map((e=>e.toString(16).padStart(2,"0"))).join("")}function u(e){return e.startsWith("0x")||e.startsWith("0X")}function h(e){return u(e)?e.slice(2):e}function d(e){return u(e)?`0x${e.slice(2)}`:`0x${e}`}function p(e){if("string"!=typeof e)return!1;const t=h(e).toLowerCase();return c.test(t)}function f(e,t=!1){if("string"==typeof e){const r=h(e).toLowerCase();if(c.test(r))return(0,o.HexString)(t?`0x${r}`:r)}throw i.standardErrors.rpc.invalidParams(`"${String(e)}" is not a hexadecimal string`)}function m(e,t=!1){let r=f(e,!1);return r.length%2==1&&(r=(0,o.HexString)(`0${r}`)),t?(0,o.HexString)(`0x${r}`):r}function g(e){if("number"==typeof e&&Number.isInteger(e))return(0,o.IntNumber)(e);if("string"==typeof e){if(a.test(e))return(0,o.IntNumber)(Number(e));if(p(e))return(0,o.IntNumber)(new s.default(m(e,!1),16).toNumber())}throw i.standardErrors.rpc.invalidParams(`Not an integer: ${String(e)}`)}function w(e){if(null==e||"function"!=typeof e.constructor)return!1;const{constructor:t}=e;return"function"==typeof t.config&&"number"==typeof t.EUCLID}function y(){try{return null!==window.frameElement}catch(e){return!1}}t.randomBytesHex=function(e){return l(crypto.getRandomValues(new Uint8Array(e)))},t.uint8ArrayToHex=l,t.hexStringToUint8Array=function(e){return new Uint8Array(e.match(/.{1,2}/g).map((e=>parseInt(e,16))))},t.hexStringFromBuffer=function(e,t=!1){const r=e.toString("hex");return(0,o.HexString)(t?`0x${r}`:r)},t.bigIntStringFromBN=function(e){return(0,o.BigIntString)(e.toString(10))},t.intNumberFromHexString=function(e){return(0,o.IntNumber)(new s.default(m(e,!1),16).toNumber())},t.hexStringFromIntNumber=function(e){return(0,o.HexString)(`0x${new s.default(e).toString(16)}`)},t.has0xPrefix=u,t.strip0x=h,t.prepend0x=d,t.isHexString=p,t.ensureHexString=f,t.ensureEvenLengthHexString=m,t.ensureAddressString=function(e){if("string"==typeof e){const t=h(e).toLowerCase();if(p(t)&&40===t.length)return(0,o.AddressString)(d(t))}throw i.standardErrors.rpc.invalidParams(`Invalid Ethereum address: ${String(e)}`)},t.ensureBuffer=function(e){if(Buffer.isBuffer(e))return e;if("string"==typeof e){if(p(e)){const t=m(e,!1);return Buffer.from(t,"hex")}return Buffer.from(e,"utf8")}throw i.standardErrors.rpc.invalidParams(`Not binary data: ${String(e)}`)},t.ensureIntNumber=g,t.ensureRegExpString=function(e){if(e instanceof RegExp)return(0,o.RegExpString)(e.toString());throw i.standardErrors.rpc.invalidParams(`Not a RegExp: ${String(e)}`)},t.ensureBN=function(e){if(null!==e&&(s.default.isBN(e)||w(e)))return new s.default(e.toString(10),10);if("number"==typeof e)return new s.default(g(e));if("string"==typeof e){if(a.test(e))return new s.default(e,10);if(p(e))return new s.default(m(e,!1),16)}throw i.standardErrors.rpc.invalidParams(`Not an integer: ${String(e)}`)},t.ensureParsedJSONObject=function(e){if("string"==typeof e)return JSON.parse(e);if("object"==typeof e)return e;throw i.standardErrors.rpc.invalidParams(`Not a JSON string or an object: ${String(e)}`)},t.isBigNumber=w,t.range=function(e,t){return Array.from({length:t-e},((t,r)=>e+r))},t.getFavicon=function(){const e=document.querySelector('link[sizes="192x192"]')||document.querySelector('link[sizes="180x180"]')||document.querySelector('link[rel="icon"]')||document.querySelector('link[rel="shortcut icon"]'),{protocol:t,host:r}=document.location,n=e?e.getAttribute("href"):null;return!n||n.startsWith("javascript:")||n.startsWith("vbscript:")?null:n.startsWith("http://")||n.startsWith("https://")||n.startsWith("data:")?n:n.startsWith("//")?t+n:`${t}//${r}${n}`},t.createQrUrl=function(e,t,r,n,s,i){const o=n?"parent-id":"id";return`${r}/#/link?${new URLSearchParams({[o]:e,secret:t,server:r,v:s,chainId:i.toString()}).toString()}`},t.isInIFrame=y,t.getLocation=function(){try{return y()&&window.top?window.top.location:window.location}catch(e){return window.location}},t.isMobileWeb=function(){var e;return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(null===(e=null===window||void 0===window?void 0:window.navigator)||void 0===e?void 0:e.userAgent)}},58293:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CoinbaseWalletProvider=t.CoinbaseWalletSDK=void 0;const n=r(80166),s=r(92071);var i=r(80166);Object.defineProperty(t,"CoinbaseWalletSDK",{enumerable:!0,get:function(){return i.CoinbaseWalletSDK}});var o=r(92071);Object.defineProperty(t,"CoinbaseWalletProvider",{enumerable:!0,get:function(){return o.CoinbaseWalletProvider}}),t.default=n.CoinbaseWalletSDK,"undefined"!=typeof window&&(window.CoinbaseWalletSDK=n.CoinbaseWalletSDK,window.CoinbaseWalletProvider=s.CoinbaseWalletProvider,window.WalletLink=n.CoinbaseWalletSDK,window.WalletLinkProvider=s.CoinbaseWalletProvider)},35814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Cipher=void 0;const n=r(97);t.Cipher=class{constructor(e){this.secret=e}async encrypt(e){const t=this.secret;if(64!==t.length)throw Error("secret must be 256 bits");const r=crypto.getRandomValues(new Uint8Array(12)),s=await crypto.subtle.importKey("raw",(0,n.hexStringToUint8Array)(t),{name:"aes-gcm"},!1,["encrypt","decrypt"]),i=new TextEncoder,o=await window.crypto.subtle.encrypt({name:"AES-GCM",iv:r},s,i.encode(e)),a=o.slice(o.byteLength-16),c=o.slice(0,o.byteLength-16),l=new Uint8Array(a),u=new Uint8Array(c),h=new Uint8Array([...r,...l,...u]);return(0,n.uint8ArrayToHex)(h)}async decrypt(e){const t=this.secret;if(64!==t.length)throw Error("secret must be 256 bits");return new Promise(((r,s)=>{!async function(){const i=await crypto.subtle.importKey("raw",(0,n.hexStringToUint8Array)(t),{name:"aes-gcm"},!1,["encrypt","decrypt"]),o=(0,n.hexStringToUint8Array)(e),a=o.slice(0,12),c=o.slice(12,28),l=o.slice(28),u=new Uint8Array([...l,...c]),h={name:"AES-GCM",iv:new Uint8Array(a)};try{const e=await window.crypto.subtle.decrypt(h,i,u),t=new TextDecoder;r(t.decode(e))}catch(e){s(e)}}()}))}}},47637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ScopedLocalStorage=void 0,t.ScopedLocalStorage=class{constructor(e){this.scope=e}setItem(e,t){localStorage.setItem(this.scopedKey(e),t)}getItem(e){return localStorage.getItem(this.scopedKey(e))}removeItem(e){localStorage.removeItem(this.scopedKey(e))}clear(){const e=this.scopedKey(""),t=[];for(let r=0;r<localStorage.length;r++){const n=localStorage.key(r);"string"==typeof n&&n.startsWith(e)&&t.push(n)}t.forEach((e=>localStorage.removeItem(e)))}scopedKey(e){return`${this.scope}:${e}`}}},48157:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default='@namespace svg "http://www.w3.org/2000/svg";.-cbwsdk-css-reset,.-cbwsdk-css-reset *{animation:none;animation-delay:0;animation-direction:normal;animation-duration:0;animation-fill-mode:none;animation-iteration-count:1;animation-name:none;animation-play-state:running;animation-timing-function:ease;backface-visibility:visible;background:0;background-attachment:scroll;background-clip:border-box;background-color:rgba(0,0,0,0);background-image:none;background-origin:padding-box;background-position:0 0;background-position-x:0;background-position-y:0;background-repeat:repeat;background-size:auto auto;border:0;border-style:none;border-width:medium;border-color:inherit;border-bottom:0;border-bottom-color:inherit;border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-style:none;border-bottom-width:medium;border-collapse:separate;border-image:none;border-left:0;border-left-color:inherit;border-left-style:none;border-left-width:medium;border-radius:0;border-right:0;border-right-color:inherit;border-right-style:none;border-right-width:medium;border-spacing:0;border-top:0;border-top-color:inherit;border-top-left-radius:0;border-top-right-radius:0;border-top-style:none;border-top-width:medium;box-shadow:none;box-sizing:border-box;caption-side:top;clear:none;clip:auto;color:inherit;columns:auto;column-count:auto;column-fill:balance;column-gap:normal;column-rule:medium none currentColor;column-rule-color:currentColor;column-rule-style:none;column-rule-width:none;column-span:1;column-width:auto;counter-increment:none;counter-reset:none;direction:ltr;empty-cells:show;float:none;font:normal;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;height:auto;hyphens:none;letter-spacing:normal;line-height:normal;list-style:none;list-style-image:none;list-style-position:outside;list-style-type:disc;margin:0;margin-bottom:0;margin-left:0;margin-right:0;margin-top:0;opacity:1;orphans:0;outline:0;outline-color:invert;outline-style:none;outline-width:medium;overflow:visible;overflow-x:visible;overflow-y:visible;padding:0;padding-bottom:0;padding-left:0;padding-right:0;padding-top:0;page-break-after:auto;page-break-before:auto;page-break-inside:auto;perspective:none;perspective-origin:50% 50%;pointer-events:auto;position:static;quotes:"\\201C" "\\201D" "\\2018" "\\2019";tab-size:8;table-layout:auto;text-align:inherit;text-align-last:auto;text-decoration:none;text-decoration-color:inherit;text-decoration-line:none;text-decoration-style:solid;text-indent:0;text-shadow:none;text-transform:none;transform:none;transform-style:flat;transition:none;transition-delay:0s;transition-duration:0s;transition-property:none;transition-timing-function:ease;unicode-bidi:normal;vertical-align:baseline;visibility:visible;white-space:normal;widows:0;word-spacing:normal;z-index:auto}.-cbwsdk-css-reset strong{font-weight:bold}.-cbwsdk-css-reset *{box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;line-height:1}.-cbwsdk-css-reset [class*=container]{margin:0;padding:0}.-cbwsdk-css-reset style{display:none}'},18679:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.injectCssReset=void 0;const s=n(r(48157));t.injectCssReset=function(){const e=document.createElement("style");e.type="text/css",e.appendChild(document.createTextNode(s.default)),document.documentElement.appendChild(e)}},92071:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.CoinbaseWalletProvider=void 0;const s=n(r(39404)),i=r(30228),o=r(66548),a=r(97),c=r(31625),l=r(13948),u=r(80947),h=r(34040),d=n(r(58262)),p=r(78176),f=r(95186),m=r(99531),g="DefaultChainId",w="DefaultJsonRpcUrl";class y extends i.EventEmitter{constructor(e){var t,r;super(),this._filterPolyfill=new f.FilterPolyfill(this),this._subscriptionManager=new m.SubscriptionManager(this),this._relay=null,this._addresses=[],this.hasMadeFirstChainChangedEmission=!1,this.setProviderInfo=this.setProviderInfo.bind(this),this.updateProviderInfo=this.updateProviderInfo.bind(this),this.getChainId=this.getChainId.bind(this),this.setAppInfo=this.setAppInfo.bind(this),this.enable=this.enable.bind(this),this.close=this.close.bind(this),this.send=this.send.bind(this),this.sendAsync=this.sendAsync.bind(this),this.request=this.request.bind(this),this._setAddresses=this._setAddresses.bind(this),this.scanQRCode=this.scanQRCode.bind(this),this.genericRequest=this.genericRequest.bind(this),this._chainIdFromOpts=e.chainId,this._jsonRpcUrlFromOpts=e.jsonRpcUrl,this._overrideIsMetaMask=e.overrideIsMetaMask,this._relayProvider=e.relayProvider,this._storage=e.storage,this._relayEventManager=e.relayEventManager,this.diagnostic=e.diagnosticLogger,this.reloadOnDisconnect=!0,this.isCoinbaseWallet=null===(t=e.overrideIsCoinbaseWallet)||void 0===t||t,this.isCoinbaseBrowser=null!==(r=e.overrideIsCoinbaseBrowser)&&void 0!==r&&r,this.qrUrl=e.qrUrl;const n=this.getChainId(),s=(0,a.prepend0x)(n.toString(16));this.emit("connect",{chainIdStr:s});const i=this._storage.getItem(l.LOCAL_STORAGE_ADDRESSES_KEY);if(i){const e=i.split(" ");""!==e[0]&&(this._addresses=e.map((e=>(0,a.ensureAddressString)(e))),this.emit("accountsChanged",e))}this._subscriptionManager.events.on("notification",(e=>{this.emit("message",{type:e.method,data:e.params})})),this._isAuthorized()&&this.initializeRelay(),window.addEventListener("message",(e=>{var t;if(e.origin===location.origin&&e.source===window&&"walletLinkMessage"===e.data.type&&"dappChainSwitched"===e.data.data.action){const r=e.data.data.chainId,n=null!==(t=e.data.data.jsonRpcUrl)&&void 0!==t?t:this.jsonRpcUrl;this.updateProviderInfo(n,Number(r))}}))}get selectedAddress(){return this._addresses[0]||void 0}get networkVersion(){return this.getChainId().toString(10)}get chainId(){return(0,a.prepend0x)(this.getChainId().toString(16))}get isWalletLink(){return!0}get isMetaMask(){return this._overrideIsMetaMask}get host(){return this.jsonRpcUrl}get connected(){return!0}isConnected(){return!0}get jsonRpcUrl(){var e;return null!==(e=this._storage.getItem(w))&&void 0!==e?e:this._jsonRpcUrlFromOpts}set jsonRpcUrl(e){this._storage.setItem(w,e)}disableReloadOnDisconnect(){this.reloadOnDisconnect=!1}setProviderInfo(e,t){this.isCoinbaseBrowser||(this._chainIdFromOpts=t,this._jsonRpcUrlFromOpts=e),this.updateProviderInfo(this.jsonRpcUrl,this.getChainId())}updateProviderInfo(e,t){this.jsonRpcUrl=e;const r=this.getChainId();this._storage.setItem(g,t.toString(10)),(0,a.ensureIntNumber)(t)===r&&this.hasMadeFirstChainChangedEmission||(this.emit("chainChanged",this.getChainId()),this.hasMadeFirstChainChangedEmission=!0)}async watchAsset(e,t,r,n,s,i){const o=await this.initializeRelay(),a=await o.watchAsset(e,t,r,n,s,null==i?void 0:i.toString()).promise;return!(0,h.isErrorResponse)(a)&&!!a.result}async addEthereumChain(e,t,r,n,s,i){var o,c;if((0,a.ensureIntNumber)(e)===this.getChainId())return!1;const l=await this.initializeRelay(),u=l.inlineAddEthereumChain(e.toString());this._isAuthorized()||u||await l.requestEthereumAccounts().promise;const d=await l.addEthereumChain(e.toString(),t,s,r,n,i).promise;return!(0,h.isErrorResponse)(d)&&(!0===(null===(o=d.result)||void 0===o?void 0:o.isApproved)&&this.updateProviderInfo(t[0],e),!0===(null===(c=d.result)||void 0===c?void 0:c.isApproved))}async switchEthereumChain(e){const t=await this.initializeRelay(),r=await t.switchEthereumChain(e.toString(10),this.selectedAddress||void 0).promise;if((0,h.isErrorResponse)(r)){if(!r.errorCode)return;throw r.errorCode===o.standardErrorCodes.provider.unsupportedChain?o.standardErrors.provider.unsupportedChain():o.standardErrors.provider.custom({message:r.errorMessage,code:r.errorCode})}const n=r.result;n.isApproved&&n.rpcUrl.length>0&&this.updateProviderInfo(n.rpcUrl,e)}setAppInfo(e,t){this.initializeRelay().then((r=>r.setAppInfo(e,t)))}async enable(){var e;return null===(e=this.diagnostic)||void 0===e||e.log(p.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::enable",addresses_length:this._addresses.length,sessionIdHash:this._relay?u.Session.hash(this._relay.session.id):void 0}),this._isAuthorized()?[...this._addresses]:await this.send("eth_requestAccounts")}async close(){(await this.initializeRelay()).resetAndReload()}send(e,t){try{const r=this._send(e,t);if(r instanceof Promise)return r.catch((t=>{throw(0,o.serializeError)(t,e)}))}catch(t){throw(0,o.serializeError)(t,e)}}_send(e,t){if("string"==typeof e){const r={jsonrpc:"2.0",id:0,method:e,params:Array.isArray(t)?t:void 0!==t?[t]:[]};return this._sendRequestAsync(r).then((e=>e.result))}if("function"==typeof t){const r=e,n=t;return this._sendAsync(r,n)}if(Array.isArray(e))return e.map((e=>this._sendRequest(e)));const r=e;return this._sendRequest(r)}async sendAsync(e,t){try{return this._sendAsync(e,t).catch((t=>{throw(0,o.serializeError)(t,e)}))}catch(t){return Promise.reject((0,o.serializeError)(t,e))}}async _sendAsync(e,t){if("function"!=typeof t)throw new Error("callback is required");if(Array.isArray(e)){const r=t;return void this._sendMultipleRequestsAsync(e).then((e=>r(null,e))).catch((e=>r(e,null)))}const r=t;return this._sendRequestAsync(e).then((e=>r(null,e))).catch((e=>r(e,null)))}async request(e){try{return this._request(e).catch((t=>{throw(0,o.serializeError)(t,e.method)}))}catch(t){return Promise.reject((0,o.serializeError)(t,e.method))}}async _request(e){if(!e||"object"!=typeof e||Array.isArray(e))throw o.standardErrors.rpc.invalidRequest({message:"Expected a single, non-array, object argument.",data:e});const{method:t,params:r}=e;if("string"!=typeof t||0===t.length)throw o.standardErrors.rpc.invalidRequest({message:"'args.method' must be a non-empty string.",data:e});if(void 0!==r&&!Array.isArray(r)&&("object"!=typeof r||null===r))throw o.standardErrors.rpc.invalidRequest({message:"'args.params' must be an object or array if provided.",data:e});const n=void 0===r?[]:r,s=this._relayEventManager.makeRequestId();return(await this._sendRequestAsync({method:t,params:n,jsonrpc:"2.0",id:s})).result}async scanQRCode(e){const t=await this.initializeRelay(),r=await t.scanQRCode((0,a.ensureRegExpString)(e)).promise;if((0,h.isErrorResponse)(r))throw(0,o.serializeError)(r.errorMessage,"scanQRCode");if("string"!=typeof r.result)throw(0,o.serializeError)("result was not a string","scanQRCode");return r.result}async genericRequest(e,t){const r=await this.initializeRelay(),n=await r.genericRequest(e,t).promise;if((0,h.isErrorResponse)(n))throw(0,o.serializeError)(n.errorMessage,"generic");if("string"!=typeof n.result)throw(0,o.serializeError)("result was not a string","generic");return n.result}async connectAndSignIn(e){var t;let r;null===(t=this.diagnostic)||void 0===t||t.log(p.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::connectAndSignIn",sessionIdHash:this._relay?u.Session.hash(this._relay.session.id):void 0});try{const t=await this.initializeRelay();if(!(t instanceof c.MobileRelay))throw new Error("connectAndSignIn is only supported on mobile");if(r=await t.connectAndSignIn(e).promise,(0,h.isErrorResponse)(r))throw new Error(r.errorMessage)}catch(e){if("string"==typeof e.message&&e.message.match(/(denied|rejected)/i))throw o.standardErrors.provider.userRejectedRequest("User denied account authorization");throw e}if(!r.result)throw new Error("accounts received is empty");const{accounts:n}=r.result;return this._setAddresses(n),this.isCoinbaseBrowser||await this.switchEthereumChain(this.getChainId()),r.result}async selectProvider(e){const t=await this.initializeRelay(),r=await t.selectProvider(e).promise;if((0,h.isErrorResponse)(r))throw(0,o.serializeError)(r.errorMessage,"selectProvider");if("string"!=typeof r.result)throw(0,o.serializeError)("result was not a string","selectProvider");return r.result}supportsSubscriptions(){return!1}subscribe(){throw new Error("Subscriptions are not supported")}unsubscribe(){throw new Error("Subscriptions are not supported")}disconnect(){return!0}_sendRequest(e){const t={jsonrpc:"2.0",id:e.id},{method:r}=e;if(t.result=this._handleSynchronousMethods(e),void 0===t.result)throw new Error(`Coinbase Wallet does not support calling ${r} synchronously without a callback. Please provide a callback parameter to call ${r} asynchronously.`);return t}_setAddresses(e,t){if(!Array.isArray(e))throw new Error("addresses is not an array");const r=e.map((e=>(0,a.ensureAddressString)(e)));JSON.stringify(r)!==JSON.stringify(this._addresses)&&(this._addresses=r,this.emit("accountsChanged",this._addresses),this._storage.setItem(l.LOCAL_STORAGE_ADDRESSES_KEY,r.join(" ")))}_sendRequestAsync(e){return new Promise(((t,r)=>{try{const n=this._handleSynchronousMethods(e);if(void 0!==n)return t({jsonrpc:"2.0",id:e.id,result:n});const s=this._handleAsynchronousFilterMethods(e);if(void 0!==s)return void s.then((r=>t(Object.assign(Object.assign({},r),{id:e.id})))).catch((e=>r(e)));const i=this._handleSubscriptionMethods(e);if(void 0!==i)return void i.then((r=>t({jsonrpc:"2.0",id:e.id,result:r.result}))).catch((e=>r(e)))}catch(e){return r(e)}this._handleAsynchronousMethods(e).then((r=>r&&t(Object.assign(Object.assign({},r),{id:e.id})))).catch((e=>r(e)))}))}_sendMultipleRequestsAsync(e){return Promise.all(e.map((e=>this._sendRequestAsync(e))))}_handleSynchronousMethods(e){const{method:t}=e,r=e.params||[];switch(t){case"eth_accounts":return this._eth_accounts();case"eth_coinbase":return this._eth_coinbase();case"eth_uninstallFilter":return this._eth_uninstallFilter(r);case"net_version":return this._net_version();case"eth_chainId":return this._eth_chainId();default:return}}async _handleAsynchronousMethods(e){const{method:t}=e,r=e.params||[];switch(t){case"eth_requestAccounts":return this._eth_requestAccounts();case"eth_sign":return this._eth_sign(r);case"eth_ecRecover":return this._eth_ecRecover(r);case"personal_sign":return this._personal_sign(r);case"personal_ecRecover":return this._personal_ecRecover(r);case"eth_signTransaction":return this._eth_signTransaction(r);case"eth_sendRawTransaction":return this._eth_sendRawTransaction(r);case"eth_sendTransaction":return this._eth_sendTransaction(r);case"eth_signTypedData_v1":return this._eth_signTypedData_v1(r);case"eth_signTypedData_v2":return this._throwUnsupportedMethodError();case"eth_signTypedData_v3":return this._eth_signTypedData_v3(r);case"eth_signTypedData_v4":case"eth_signTypedData":return this._eth_signTypedData_v4(r);case"cbWallet_arbitrary":return this._cbwallet_arbitrary(r);case"wallet_addEthereumChain":return this._wallet_addEthereumChain(r);case"wallet_switchEthereumChain":return this._wallet_switchEthereumChain(r);case"wallet_watchAsset":return this._wallet_watchAsset(r)}return(await this.initializeRelay()).makeEthereumJSONRPCRequest(e,this.jsonRpcUrl).catch((t=>{var r;throw t.code!==o.standardErrorCodes.rpc.methodNotFound&&t.code!==o.standardErrorCodes.rpc.methodNotSupported||null===(r=this.diagnostic)||void 0===r||r.log(p.EVENTS.METHOD_NOT_IMPLEMENTED,{method:e.method,sessionIdHash:this._relay?u.Session.hash(this._relay.session.id):void 0}),t}))}_handleAsynchronousFilterMethods(e){const{method:t}=e,r=e.params||[];switch(t){case"eth_newFilter":return this._eth_newFilter(r);case"eth_newBlockFilter":return this._eth_newBlockFilter();case"eth_newPendingTransactionFilter":return this._eth_newPendingTransactionFilter();case"eth_getFilterChanges":return this._eth_getFilterChanges(r);case"eth_getFilterLogs":return this._eth_getFilterLogs(r)}}_handleSubscriptionMethods(e){switch(e.method){case"eth_subscribe":case"eth_unsubscribe":return this._subscriptionManager.handleRequest(e)}}_isKnownAddress(e){try{const t=(0,a.ensureAddressString)(e);return this._addresses.map((e=>(0,a.ensureAddressString)(e))).includes(t)}catch(e){}return!1}_ensureKnownAddress(e){var t;if(!this._isKnownAddress(e))throw null===(t=this.diagnostic)||void 0===t||t.log(p.EVENTS.UNKNOWN_ADDRESS_ENCOUNTERED),new Error("Unknown Ethereum address")}_prepareTransactionParams(e){const t=e.from?(0,a.ensureAddressString)(e.from):this.selectedAddress;if(!t)throw new Error("Ethereum address is unavailable");return this._ensureKnownAddress(t),{fromAddress:t,toAddress:e.to?(0,a.ensureAddressString)(e.to):null,weiValue:null!=e.value?(0,a.ensureBN)(e.value):new s.default(0),data:e.data?(0,a.ensureBuffer)(e.data):Buffer.alloc(0),nonce:null!=e.nonce?(0,a.ensureIntNumber)(e.nonce):null,gasPriceInWei:null!=e.gasPrice?(0,a.ensureBN)(e.gasPrice):null,maxFeePerGas:null!=e.maxFeePerGas?(0,a.ensureBN)(e.maxFeePerGas):null,maxPriorityFeePerGas:null!=e.maxPriorityFeePerGas?(0,a.ensureBN)(e.maxPriorityFeePerGas):null,gasLimit:null!=e.gas?(0,a.ensureBN)(e.gas):null,chainId:e.chainId?(0,a.ensureIntNumber)(e.chainId):this.getChainId()}}_isAuthorized(){return this._addresses.length>0}_requireAuthorization(){if(!this._isAuthorized())throw o.standardErrors.provider.unauthorized({})}_throwUnsupportedMethodError(){throw o.standardErrors.provider.unsupportedMethod({})}async _signEthereumMessage(e,t,r,n){this._ensureKnownAddress(t);try{const s=await this.initializeRelay(),i=await s.signEthereumMessage(e,t,r,n).promise;if((0,h.isErrorResponse)(i))throw new Error(i.errorMessage);return{jsonrpc:"2.0",id:0,result:i.result}}catch(e){if("string"==typeof e.message&&e.message.match(/(denied|rejected)/i))throw o.standardErrors.provider.userRejectedRequest("User denied message signature");throw e}}async _ethereumAddressFromSignedMessage(e,t,r){const n=await this.initializeRelay(),s=await n.ethereumAddressFromSignedMessage(e,t,r).promise;if((0,h.isErrorResponse)(s))throw new Error(s.errorMessage);return{jsonrpc:"2.0",id:0,result:s.result}}_eth_accounts(){return[...this._addresses]}_eth_coinbase(){return this.selectedAddress||null}_net_version(){return this.getChainId().toString(10)}_eth_chainId(){return(0,a.hexStringFromIntNumber)(this.getChainId())}getChainId(){const e=this._storage.getItem(g);if(!e)return(0,a.ensureIntNumber)(this._chainIdFromOpts);const t=parseInt(e,10);return(0,a.ensureIntNumber)(t)}async _eth_requestAccounts(){var e;if(null===(e=this.diagnostic)||void 0===e||e.log(p.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::_eth_requestAccounts",addresses_length:this._addresses.length,sessionIdHash:this._relay?u.Session.hash(this._relay.session.id):void 0}),this._isAuthorized())return Promise.resolve({jsonrpc:"2.0",id:0,result:this._addresses});let t;try{const e=await this.initializeRelay();if(t=await e.requestEthereumAccounts().promise,(0,h.isErrorResponse)(t))throw new Error(t.errorMessage)}catch(e){if("string"==typeof e.message&&e.message.match(/(denied|rejected)/i))throw o.standardErrors.provider.userRejectedRequest("User denied account authorization");throw e}if(!t.result)throw new Error("accounts received is empty");return this._setAddresses(t.result),this.isCoinbaseBrowser||await this.switchEthereumChain(this.getChainId()),{jsonrpc:"2.0",id:0,result:this._addresses}}_eth_sign(e){this._requireAuthorization();const t=(0,a.ensureAddressString)(e[0]),r=(0,a.ensureBuffer)(e[1]);return this._signEthereumMessage(r,t,!1)}_eth_ecRecover(e){const t=(0,a.ensureBuffer)(e[0]),r=(0,a.ensureBuffer)(e[1]);return this._ethereumAddressFromSignedMessage(t,r,!1)}_personal_sign(e){this._requireAuthorization();const t=(0,a.ensureBuffer)(e[0]),r=(0,a.ensureAddressString)(e[1]);return this._signEthereumMessage(t,r,!0)}_personal_ecRecover(e){const t=(0,a.ensureBuffer)(e[0]),r=(0,a.ensureBuffer)(e[1]);return this._ethereumAddressFromSignedMessage(t,r,!0)}async _eth_signTransaction(e){this._requireAuthorization();const t=this._prepareTransactionParams(e[0]||{});try{const e=await this.initializeRelay(),r=await e.signEthereumTransaction(t).promise;if((0,h.isErrorResponse)(r))throw new Error(r.errorMessage);return{jsonrpc:"2.0",id:0,result:r.result}}catch(e){if("string"==typeof e.message&&e.message.match(/(denied|rejected)/i))throw o.standardErrors.provider.userRejectedRequest("User denied transaction signature");throw e}}async _eth_sendRawTransaction(e){const t=(0,a.ensureBuffer)(e[0]),r=await this.initializeRelay(),n=await r.submitEthereumTransaction(t,this.getChainId()).promise;if((0,h.isErrorResponse)(n))throw new Error(n.errorMessage);return{jsonrpc:"2.0",id:0,result:n.result}}async _eth_sendTransaction(e){this._requireAuthorization();const t=this._prepareTransactionParams(e[0]||{});try{const e=await this.initializeRelay(),r=await e.signAndSubmitEthereumTransaction(t).promise;if((0,h.isErrorResponse)(r))throw new Error(r.errorMessage);return{jsonrpc:"2.0",id:0,result:r.result}}catch(e){if("string"==typeof e.message&&e.message.match(/(denied|rejected)/i))throw o.standardErrors.provider.userRejectedRequest("User denied transaction signature");throw e}}async _eth_signTypedData_v1(e){this._requireAuthorization();const t=(0,a.ensureParsedJSONObject)(e[0]),r=(0,a.ensureAddressString)(e[1]);this._ensureKnownAddress(r);const n=d.default.hashForSignTypedDataLegacy({data:t}),s=JSON.stringify(t,null,2);return this._signEthereumMessage(n,r,!1,s)}async _eth_signTypedData_v3(e){this._requireAuthorization();const t=(0,a.ensureAddressString)(e[0]),r=(0,a.ensureParsedJSONObject)(e[1]);this._ensureKnownAddress(t);const n=d.default.hashForSignTypedData_v3({data:r}),s=JSON.stringify(r,null,2);return this._signEthereumMessage(n,t,!1,s)}async _eth_signTypedData_v4(e){this._requireAuthorization();const t=(0,a.ensureAddressString)(e[0]),r=(0,a.ensureParsedJSONObject)(e[1]);this._ensureKnownAddress(t);const n=d.default.hashForSignTypedData_v4({data:r}),s=JSON.stringify(r,null,2);return this._signEthereumMessage(n,t,!1,s)}async _cbwallet_arbitrary(e){const t=e[0],r=e[1];if("string"!=typeof r)throw new Error("parameter must be a string");if("object"!=typeof t||null===t)throw new Error("parameter must be an object");return{jsonrpc:"2.0",id:0,result:await this.genericRequest(t,r)}}async _wallet_addEthereumChain(e){var t,r,n,s;const i=e[0];if(0===(null===(t=i.rpcUrls)||void 0===t?void 0:t.length))return{jsonrpc:"2.0",id:0,error:{code:2,message:"please pass in at least 1 rpcUrl"}};if(!i.chainName||""===i.chainName.trim())throw o.standardErrors.rpc.invalidParams("chainName is a required field");if(!i.nativeCurrency)throw o.standardErrors.rpc.invalidParams("nativeCurrency is a required field");const a=parseInt(i.chainId,16);return await this.addEthereumChain(a,null!==(r=i.rpcUrls)&&void 0!==r?r:[],null!==(n=i.blockExplorerUrls)&&void 0!==n?n:[],i.chainName,null!==(s=i.iconUrls)&&void 0!==s?s:[],i.nativeCurrency)?{jsonrpc:"2.0",id:0,result:null}:{jsonrpc:"2.0",id:0,error:{code:2,message:"unable to add ethereum chain"}}}async _wallet_switchEthereumChain(e){const t=e[0];return await this.switchEthereumChain(parseInt(t.chainId,16)),{jsonrpc:"2.0",id:0,result:null}}async _wallet_watchAsset(e){const t=Array.isArray(e)?e[0]:e;if(!t.type)throw o.standardErrors.rpc.invalidParams("Type is required");if("ERC20"!==(null==t?void 0:t.type))throw o.standardErrors.rpc.invalidParams(`Asset of type '${t.type}' is not supported`);if(!(null==t?void 0:t.options))throw o.standardErrors.rpc.invalidParams("Options are required");if(!(null==t?void 0:t.options.address))throw o.standardErrors.rpc.invalidParams("Address is required");const r=this.getChainId(),{address:n,symbol:s,image:i,decimals:a}=t.options;return{jsonrpc:"2.0",id:0,result:await this.watchAsset(t.type,n,s,a,i,r)}}_eth_uninstallFilter(e){const t=(0,a.ensureHexString)(e[0]);return this._filterPolyfill.uninstallFilter(t)}async _eth_newFilter(e){const t=e[0];return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newFilter(t)}}async _eth_newBlockFilter(){return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newBlockFilter()}}async _eth_newPendingTransactionFilter(){return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newPendingTransactionFilter()}}_eth_getFilterChanges(e){const t=(0,a.ensureHexString)(e[0]);return this._filterPolyfill.getFilterChanges(t)}_eth_getFilterLogs(e){const t=(0,a.ensureHexString)(e[0]);return this._filterPolyfill.getFilterLogs(t)}initializeRelay(){return this._relay?Promise.resolve(this._relay):this._relayProvider().then((e=>(e.setAccountsCallback(((e,t)=>this._setAddresses(e,t))),e.setChainCallback(((e,t)=>{this.updateProviderInfo(t,parseInt(e,10))})),e.setDappDefaultChainCallback(this._chainIdFromOpts),this._relay=e,e)))}}t.CoinbaseWalletProvider=y},78176:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EVENTS=void 0,t.EVENTS={STARTED_CONNECTING:"walletlink_sdk.started.connecting",CONNECTED_STATE_CHANGE:"walletlink_sdk.connected",DISCONNECTED:"walletlink_sdk.disconnected",METADATA_DESTROYED:"walletlink_sdk_metadata_destroyed",LINKED:"walletlink_sdk.linked",FAILURE:"walletlink_sdk.generic_failure",SESSION_CONFIG_RECEIVED:"walletlink_sdk.session_config_event_received",ETH_ACCOUNTS_STATE:"walletlink_sdk.eth_accounts_state",SESSION_STATE_CHANGE:"walletlink_sdk.session_state_change",UNLINKED_ERROR_STATE:"walletlink_sdk.unlinked_error_state",SKIPPED_CLEARING_SESSION:"walletlink_sdk.skipped_clearing_session",GENERAL_ERROR:"walletlink_sdk.general_error",WEB3_REQUEST:"walletlink_sdk.web3.request",WEB3_REQUEST_PUBLISHED:"walletlink_sdk.web3.request_published",WEB3_RESPONSE:"walletlink_sdk.web3.response",METHOD_NOT_IMPLEMENTED:"walletlink_sdk.method_not_implemented",UNKNOWN_ADDRESS_ENCOUNTERED:"walletlink_sdk.unknown_address_encountered"}},95186:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.filterFromParam=t.FilterPolyfill=void 0;const n=r(98957),s=r(97),i={jsonrpc:"2.0",id:0};function o(e){return{fromBlock:c(e.fromBlock),toBlock:c(e.toBlock),addresses:void 0===e.address?null:Array.isArray(e.address)?e.address:[e.address],topics:e.topics||[]}}function a(e){const t={fromBlock:l(e.fromBlock),toBlock:l(e.toBlock),topics:e.topics};return null!==e.addresses&&(t.address=e.addresses),t}function c(e){if(void 0===e||"latest"===e||"pending"===e)return"latest";if("earliest"===e)return(0,n.IntNumber)(0);if((0,s.isHexString)(e))return(0,s.intNumberFromHexString)(e);throw new Error(`Invalid block option: ${String(e)}`)}function l(e){return"latest"===e?e:(0,s.hexStringFromIntNumber)(e)}function u(){return Object.assign(Object.assign({},i),{error:{code:-32e3,message:"filter not found"}})}function h(){return Object.assign(Object.assign({},i),{result:[]})}t.FilterPolyfill=class{constructor(e){this.logFilters=new Map,this.blockFilters=new Set,this.pendingTransactionFilters=new Set,this.cursors=new Map,this.timeouts=new Map,this.nextFilterId=(0,n.IntNumber)(1),this.REQUEST_THROTTLE_INTERVAL=1e3,this.lastFetchTimestamp=new Date(0),this.resolvers=[],this.provider=e}async newFilter(e){const t=o(e),r=this.makeFilterId(),n=await this.setInitialCursorPosition(r,t.fromBlock);return console.info(`Installing new log filter(${r}):`,t,"initial cursor position:",n),this.logFilters.set(r,t),this.setFilterTimeout(r),(0,s.hexStringFromIntNumber)(r)}async newBlockFilter(){const e=this.makeFilterId(),t=await this.setInitialCursorPosition(e,"latest");return console.info(`Installing new block filter (${e}) with initial cursor position:`,t),this.blockFilters.add(e),this.setFilterTimeout(e),(0,s.hexStringFromIntNumber)(e)}async newPendingTransactionFilter(){const e=this.makeFilterId(),t=await this.setInitialCursorPosition(e,"latest");return console.info(`Installing new block filter (${e}) with initial cursor position:`,t),this.pendingTransactionFilters.add(e),this.setFilterTimeout(e),(0,s.hexStringFromIntNumber)(e)}uninstallFilter(e){const t=(0,s.intNumberFromHexString)(e);return console.info(`Uninstalling filter (${t})`),this.deleteFilter(t),!0}getFilterChanges(e){const t=(0,s.intNumberFromHexString)(e);return this.timeouts.has(t)&&this.setFilterTimeout(t),this.logFilters.has(t)?this.getLogFilterChanges(t):this.blockFilters.has(t)?this.getBlockFilterChanges(t):this.pendingTransactionFilters.has(t)?this.getPendingTransactionFilterChanges(t):Promise.resolve(u())}async getFilterLogs(e){const t=(0,s.intNumberFromHexString)(e),r=this.logFilters.get(t);return r?this.sendAsyncPromise(Object.assign(Object.assign({},i),{method:"eth_getLogs",params:[a(r)]})):u()}makeFilterId(){return(0,n.IntNumber)(++this.nextFilterId)}sendAsyncPromise(e){return new Promise(((t,r)=>{this.provider.sendAsync(e,((e,n)=>e?r(e):Array.isArray(n)||null==n?r(new Error(`unexpected response received: ${JSON.stringify(n)}`)):void t(n)))}))}deleteFilter(e){console.info(`Deleting filter (${e})`),this.logFilters.delete(e),this.blockFilters.delete(e),this.pendingTransactionFilters.delete(e),this.cursors.delete(e),this.timeouts.delete(e)}async getLogFilterChanges(e){const t=this.logFilters.get(e),r=this.cursors.get(e);if(!r||!t)return u();const o=await this.getCurrentBlockHeight(),c="latest"===t.toBlock?o:t.toBlock;if(r>o)return h();if(r>Number(t.toBlock))return h();console.info(`Fetching logs from ${r} to ${c} for filter ${e}`);const l=await this.sendAsyncPromise(Object.assign(Object.assign({},i),{method:"eth_getLogs",params:[a(Object.assign(Object.assign({},t),{fromBlock:r,toBlock:c}))]}));if(Array.isArray(l.result)){const t=l.result.map((e=>(0,s.intNumberFromHexString)(e.blockNumber||"0x0"))),i=Math.max(...t);if(i&&i>r){const t=(0,n.IntNumber)(i+1);console.info(`Moving cursor position for filter (${e}) from ${r} to ${t}`),this.cursors.set(e,t)}}return l}async getBlockFilterChanges(e){const t=this.cursors.get(e);if(!t)return u();const r=await this.getCurrentBlockHeight();if(t>r)return h();console.info(`Fetching blocks from ${t} to ${r} for filter (${e})`);const o=(await Promise.all((0,s.range)(t,r+1).map((e=>this.getBlockHashByNumber((0,n.IntNumber)(e)))))).filter((e=>!!e)),a=(0,n.IntNumber)(t+o.length);return console.info(`Moving cursor position for filter (${e}) from ${t} to ${a}`),this.cursors.set(e,a),Object.assign(Object.assign({},i),{result:o})}async getPendingTransactionFilterChanges(e){return Promise.resolve(h())}async setInitialCursorPosition(e,t){const r=await this.getCurrentBlockHeight(),n="number"==typeof t&&t>r?t:r;return this.cursors.set(e,n),n}setFilterTimeout(e){const t=this.timeouts.get(e);t&&window.clearTimeout(t);const r=window.setTimeout((()=>{console.info(`Filter (${e}) timed out`),this.deleteFilter(e)}),3e5);this.timeouts.set(e,r)}async getCurrentBlockHeight(){const e=new Date;if(e.getTime()-this.lastFetchTimestamp.getTime()>this.REQUEST_THROTTLE_INTERVAL){this.lastFetchTimestamp=e;const t=await this._getCurrentBlockHeight();this.currentBlockHeight=t,this.resolvers.forEach((e=>e(t))),this.resolvers=[]}return this.currentBlockHeight?this.currentBlockHeight:new Promise((e=>this.resolvers.push(e)))}async _getCurrentBlockHeight(){const{result:e}=await this.sendAsyncPromise(Object.assign(Object.assign({},i),{method:"eth_blockNumber",params:[]}));return(0,s.intNumberFromHexString)((0,s.ensureHexString)(e))}async getBlockHashByNumber(e){const t=await this.sendAsyncPromise(Object.assign(Object.assign({},i),{method:"eth_getBlockByNumber",params:[(0,s.hexStringFromIntNumber)(e),!1]}));return t.result&&"string"==typeof t.result.hash?(0,s.ensureHexString)(t.result.hash):null}},t.filterFromParam=o},99531:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SubscriptionManager=void 0;const n=r(87454),s=r(96401),i=()=>{};t.SubscriptionManager=class{constructor(e){const t=new n.PollingBlockTracker({provider:e,pollingInterval:15e3,setSkipCacheFlag:!0}),{events:r,middleware:i}=s({blockTracker:t,provider:e});this.events=r,this.subscriptionMiddleware=i}async handleRequest(e){const t={};return await this.subscriptionMiddleware(e,t,i,i),t}destroy(){this.subscriptionMiddleware.destroy()}}},13948:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RelayAbstract=t.APP_VERSION_KEY=t.LOCAL_STORAGE_ADDRESSES_KEY=t.WALLET_USER_NAME_KEY=void 0;const n=r(66548);t.WALLET_USER_NAME_KEY="walletUsername",t.LOCAL_STORAGE_ADDRESSES_KEY="Addresses",t.APP_VERSION_KEY="AppVersion",t.RelayAbstract=class{async makeEthereumJSONRPCRequest(e,t){if(!t)throw new Error("Error: No jsonRpcUrl provided");return window.fetch(t,{method:"POST",body:JSON.stringify(e),mode:"cors",headers:{"Content-Type":"application/json"}}).then((e=>e.json())).then((t=>{if(!t)throw n.standardErrors.rpc.parse({});const r=t,{error:s}=r;if(s)throw(0,n.serializeError)(s,e.method);return r}))}}},32261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RelayEventManager=void 0;const n=r(97);t.RelayEventManager=class{constructor(){this._nextRequestId=0,this.callbacks=new Map}makeRequestId(){this._nextRequestId=(this._nextRequestId+1)%2147483647;const e=this._nextRequestId,t=(0,n.prepend0x)(e.toString(16));return this.callbacks.get(t)&&this.callbacks.delete(t),e}}},80947:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Session=void 0;const n=r(62802),s=r(97),i="session:id",o="session:secret",a="session:linked";class c{constructor(e,t,r,i){this._storage=e,this._id=t||(0,s.randomBytesHex)(16),this._secret=r||(0,s.randomBytesHex)(32),this._key=(new n.sha256).update(`${this._id}, ${this._secret} WalletLink`).digest("hex"),this._linked=!!i}static load(e){const t=e.getItem(i),r=e.getItem(a),n=e.getItem(o);return t&&n?new c(e,t,n,"1"===r):null}static hash(e){return(new n.sha256).update(e).digest("hex")}get id(){return this._id}get secret(){return this._secret}get key(){return this._key}get linked(){return this._linked}set linked(e){this._linked=e,this.persistLinked()}save(){return this._storage.setItem(i,this._id),this._storage.setItem(o,this._secret),this.persistLinked(),this}persistLinked(){this._storage.setItem(a,this._linked?"1":"0")}}t.Session=c},31625:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MobileRelay=void 0;const n=r(97),s=r(58315),i=r(73647);class o extends s.WalletLinkRelay{constructor(e){var t;super(e),this._enableMobileWalletLink=null!==(t=e.enableMobileWalletLink)&&void 0!==t&&t}requestEthereumAccounts(){return this._enableMobileWalletLink?super.requestEthereumAccounts():{promise:new Promise((()=>{const e=(0,n.getLocation)();e.href=`https://go.cb-w.com/dapp?cb_url=${encodeURIComponent(e.href)}`})),cancel:()=>{}}}publishWeb3RequestEvent(e,t){if(super.publishWeb3RequestEvent(e,t),!(this._enableMobileWalletLink&&this.ui instanceof i.MobileRelayUI))return;let r=!1;switch(t.method){case"requestEthereumAccounts":case"connectAndSignIn":r=!0,this.ui.openCoinbaseWalletDeeplink(this.getQRCodeUrl());break;case"switchEthereumChain":return;default:r=!0,this.ui.openCoinbaseWalletDeeplink()}r&&window.addEventListener("blur",(()=>{window.addEventListener("focus",(()=>{this.connection.checkUnseenEvents()}),{once:!0})}),{once:!0})}handleWeb3ResponseMessage(e){super.handleWeb3ResponseMessage(e)}connectAndSignIn(e){if(!this._enableMobileWalletLink)throw new Error("connectAndSignIn is supported only when enableMobileWalletLink is on");return this.sendRequest({method:"connectAndSignIn",params:{appName:this.appName,appLogoUrl:this.appLogoUrl,domain:window.location.hostname,aud:window.location.href,version:"1",type:"eip4361",nonce:e.nonce,iat:(new Date).toISOString(),chainId:`eip155:${this.dappDefaultChain}`,statement:e.statement,resources:e.resources}})}}t.MobileRelay=o},73647:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MobileRelayUI=void 0;const n=r(31976);t.MobileRelayUI=class{constructor(e){this.attached=!1,this.darkMode=!1,this.redirectDialog=new n.RedirectDialog,this.darkMode=e.darkMode}attach(){if(this.attached)throw new Error("Coinbase Wallet SDK UI is already attached");this.redirectDialog.attach(),this.attached=!0}setConnected(e){}redirectToCoinbaseWallet(e){const t=new URL("https://go.cb-w.com/walletlink");t.searchParams.append("redirect_url",window.location.href),e&&t.searchParams.append("wl_url",e);const r=document.createElement("a");r.target="cbw-opener",r.href=t.href,r.rel="noreferrer noopener",r.click()}openCoinbaseWalletDeeplink(e){this.redirectDialog.present({title:"Redirecting to Coinbase Wallet...",buttonText:"Open",darkMode:this.darkMode,onButtonClick:()=>{this.redirectToCoinbaseWallet(e)}}),setTimeout((()=>{this.redirectToCoinbaseWallet(e)}),99)}showConnecting(e){return()=>{this.redirectDialog.clear()}}hideRequestEthereumAccounts(){this.redirectDialog.clear()}requestEthereumAccounts(){}addEthereumChain(){}watchAsset(){}selectProvider(){}switchEthereumChain(){}signEthereumMessage(){}signEthereumTransaction(){}submitEthereumTransaction(){}ethereumAddressFromSignedMessage(){}reloadUI(){}setStandalone(){}setConnectDisabled(){}inlineAccountsResponse(){return!1}inlineAddEthereumChain(){return!1}inlineWatchAsset(){return!1}inlineSwitchEthereumChain(){return!1}isStandalone(){return!1}}},58315:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletLinkRelay=void 0;const n=r(66548),s=r(98957),i=r(97),o=r(78176),a=r(13948),c=r(80947),l=r(92757),u=r(34040),h=r(70764);class d extends a.RelayAbstract{constructor(e){var t;super(),this.accountsCallback=null,this.chainCallbackParams={chainId:"",jsonRpcUrl:""},this.chainCallback=null,this.dappDefaultChain=1,this.appName="",this.appLogoUrl=null,this.linkedUpdated=e=>{var t;this.isLinked=e;const r=this.storage.getItem(a.LOCAL_STORAGE_ADDRESSES_KEY);if(e&&(this.session.linked=e),this.isUnlinkedErrorState=!1,r){const n=r.split(" "),s="true"===this.storage.getItem("IsStandaloneSigning");if(""!==n[0]&&!e&&this.session.linked&&!s){this.isUnlinkedErrorState=!0;const e=this.getSessionIdHash();null===(t=this.diagnostic)||void 0===t||t.log(o.EVENTS.UNLINKED_ERROR_STATE,{sessionIdHash:e})}}},this.metadataUpdated=(e,t)=>{this.storage.setItem(e,t)},this.chainUpdated=(e,t)=>{this.chainCallbackParams.chainId===e&&this.chainCallbackParams.jsonRpcUrl===t||(this.chainCallbackParams={chainId:e,jsonRpcUrl:t},this.chainCallback&&this.chainCallback(e,t))},this.accountUpdated=e=>{this.accountsCallback&&this.accountsCallback([e]),d.accountRequestCallbackIds.size>0&&(Array.from(d.accountRequestCallbackIds.values()).forEach((t=>{const r={type:"WEB3_RESPONSE",id:t,response:{method:"requestEthereumAccounts",result:[e]}};this.invokeCallback(Object.assign(Object.assign({},r),{id:t}))})),d.accountRequestCallbackIds.clear())},this.connectedUpdated=e=>{this.ui.setConnected(e)},this.resetAndReload=this.resetAndReload.bind(this),this.linkAPIUrl=e.linkAPIUrl,this.storage=e.storage,this.options=e;const{session:r,ui:n,connection:s}=this.subscribe();this._session=r,this.connection=s,this.relayEventManager=e.relayEventManager,this.diagnostic=e.diagnosticLogger,this._reloadOnDisconnect=null===(t=e.reloadOnDisconnect)||void 0===t||t,this.ui=n}subscribe(){const e=c.Session.load(this.storage)||new c.Session(this.storage).save(),{linkAPIUrl:t,diagnostic:r}=this,n=new l.WalletLinkConnection({session:e,linkAPIUrl:t,diagnostic:r,listener:this}),{version:s,darkMode:i}=this.options,o=this.options.uiConstructor({linkAPIUrl:t,version:s,darkMode:i,session:e});return n.connect(),{session:e,ui:o,connection:n}}attachUI(){this.ui.attach()}resetAndReload(){Promise.race([this.connection.setSessionMetadata("__destroyed","1"),new Promise((e=>setTimeout((()=>e(null)),1e3)))]).then((()=>{var e,t;const r=this.ui.isStandalone();null===(e=this.diagnostic)||void 0===e||e.log(o.EVENTS.SESSION_STATE_CHANGE,{method:"relay::resetAndReload",sessionMetadataChange:"__destroyed, 1",sessionIdHash:this.getSessionIdHash()}),this.connection.destroy();const n=c.Session.load(this.storage);if((null==n?void 0:n.id)===this._session.id?this.storage.clear():n&&(null===(t=this.diagnostic)||void 0===t||t.log(o.EVENTS.SKIPPED_CLEARING_SESSION,{sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:c.Session.hash(n.id)})),this._reloadOnDisconnect)return void this.ui.reloadUI();this.accountsCallback&&this.accountsCallback([],!0);const{session:s,ui:i,connection:a}=this.subscribe();this._session=s,this.connection=a,this.ui=i,r&&this.ui.setStandalone&&this.ui.setStandalone(!0),this.options.headlessMode||this.attachUI()})).catch((e=>{var t;null===(t=this.diagnostic)||void 0===t||t.log(o.EVENTS.FAILURE,{method:"relay::resetAndReload",message:`failed to reset and reload with ${e}`,sessionIdHash:this.getSessionIdHash()})}))}setAppInfo(e,t){this.appName=e,this.appLogoUrl=t}getStorageItem(e){return this.storage.getItem(e)}get session(){return this._session}setStorageItem(e,t){this.storage.setItem(e,t)}signEthereumMessage(e,t,r,n){return this.sendRequest({method:"signEthereumMessage",params:{message:(0,i.hexStringFromBuffer)(e,!0),address:t,addPrefix:r,typedDataJson:n||null}})}ethereumAddressFromSignedMessage(e,t,r){return this.sendRequest({method:"ethereumAddressFromSignedMessage",params:{message:(0,i.hexStringFromBuffer)(e,!0),signature:(0,i.hexStringFromBuffer)(t,!0),addPrefix:r}})}signEthereumTransaction(e){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:e.fromAddress,toAddress:e.toAddress,weiValue:(0,i.bigIntStringFromBN)(e.weiValue),data:(0,i.hexStringFromBuffer)(e.data,!0),nonce:e.nonce,gasPriceInWei:e.gasPriceInWei?(0,i.bigIntStringFromBN)(e.gasPriceInWei):null,maxFeePerGas:e.gasPriceInWei?(0,i.bigIntStringFromBN)(e.gasPriceInWei):null,maxPriorityFeePerGas:e.gasPriceInWei?(0,i.bigIntStringFromBN)(e.gasPriceInWei):null,gasLimit:e.gasLimit?(0,i.bigIntStringFromBN)(e.gasLimit):null,chainId:e.chainId,shouldSubmit:!1}})}signAndSubmitEthereumTransaction(e){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:e.fromAddress,toAddress:e.toAddress,weiValue:(0,i.bigIntStringFromBN)(e.weiValue),data:(0,i.hexStringFromBuffer)(e.data,!0),nonce:e.nonce,gasPriceInWei:e.gasPriceInWei?(0,i.bigIntStringFromBN)(e.gasPriceInWei):null,maxFeePerGas:e.maxFeePerGas?(0,i.bigIntStringFromBN)(e.maxFeePerGas):null,maxPriorityFeePerGas:e.maxPriorityFeePerGas?(0,i.bigIntStringFromBN)(e.maxPriorityFeePerGas):null,gasLimit:e.gasLimit?(0,i.bigIntStringFromBN)(e.gasLimit):null,chainId:e.chainId,shouldSubmit:!0}})}submitEthereumTransaction(e,t){return this.sendRequest({method:"submitEthereumTransaction",params:{signedTransaction:(0,i.hexStringFromBuffer)(e,!0),chainId:t}})}scanQRCode(e){return this.sendRequest({method:"scanQRCode",params:{regExp:e}})}getQRCodeUrl(){return(0,i.createQrUrl)(this._session.id,this._session.secret,this.linkAPIUrl,!1,this.options.version,this.dappDefaultChain)}genericRequest(e,t){return this.sendRequest({method:"generic",params:{action:t,data:e}})}sendGenericMessage(e){return this.sendRequest(e)}sendRequest(e){let t=null;const r=(0,i.randomBytesHex)(8),n=n=>{this.publishWeb3RequestCanceledEvent(r),this.handleErrorResponse(r,e.method,n),null==t||t()};return{promise:new Promise(((s,i)=>{this.ui.isStandalone()||(t=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:n,onResetConnection:this.resetAndReload})),this.relayEventManager.callbacks.set(r,(e=>{if(null==t||t(),(0,u.isErrorResponse)(e))return i(new Error(e.errorMessage));s(e)})),this.ui.isStandalone()?this.sendRequestStandalone(r,e):this.publishWeb3RequestEvent(r,e)})),cancel:n}}setConnectDisabled(e){this.ui.setConnectDisabled(e)}setAccountsCallback(e){this.accountsCallback=e}setChainCallback(e){this.chainCallback=e}setDappDefaultChainCallback(e){this.dappDefaultChain=e,this.ui instanceof h.WalletLinkRelayUI&&this.ui.setChainId(e)}publishWeb3RequestEvent(e,t){var r;const n={type:"WEB3_REQUEST",id:e,request:t},s=c.Session.load(this.storage);null===(r=this.diagnostic)||void 0===r||r.log(o.EVENTS.WEB3_REQUEST,{eventId:n.id,method:`relay::${t.method}`,sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:s?c.Session.hash(s.id):"",isSessionMismatched:((null==s?void 0:s.id)!==this._session.id).toString()}),this.publishEvent("Web3Request",n,!0).then((e=>{var r;null===(r=this.diagnostic)||void 0===r||r.log(o.EVENTS.WEB3_REQUEST_PUBLISHED,{eventId:n.id,method:`relay::${t.method}`,sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:s?c.Session.hash(s.id):"",isSessionMismatched:((null==s?void 0:s.id)!==this._session.id).toString()})})).catch((e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:n.id,response:{method:t.method,errorMessage:e.message}})}))}publishWeb3RequestCanceledEvent(e){const t={type:"WEB3_REQUEST_CANCELED",id:e};this.publishEvent("Web3RequestCanceled",t,!1).then()}publishEvent(e,t,r){return this.connection.publishEvent(e,t,r)}handleWeb3ResponseMessage(e){var t;const{response:r}=e;if(null===(t=this.diagnostic)||void 0===t||t.log(o.EVENTS.WEB3_RESPONSE,{eventId:e.id,method:`relay::${r.method}`,sessionIdHash:this.getSessionIdHash()}),"requestEthereumAccounts"===r.method)return d.accountRequestCallbackIds.forEach((t=>this.invokeCallback(Object.assign(Object.assign({},e),{id:t})))),void d.accountRequestCallbackIds.clear();this.invokeCallback(e)}handleErrorResponse(e,t,r,s){var i;const o=null!==(i=null==r?void 0:r.message)&&void 0!==i?i:(0,n.getMessageFromCode)(s);this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:e,response:{method:t,errorMessage:o,errorCode:s}})}invokeCallback(e){const t=this.relayEventManager.callbacks.get(e.id);t&&(t(e.response),this.relayEventManager.callbacks.delete(e.id))}requestEthereumAccounts(){const e={method:"requestEthereumAccounts",params:{appName:this.appName,appLogoUrl:this.appLogoUrl||null}},t=(0,i.randomBytesHex)(8),r=r=>{this.publishWeb3RequestCanceledEvent(t),this.handleErrorResponse(t,e.method,r)};return{promise:new Promise(((s,i)=>{if(this.relayEventManager.callbacks.set(t,(e=>{if(this.ui.hideRequestEthereumAccounts(),(0,u.isErrorResponse)(e))return i(new Error(e.errorMessage));s(e)})),this.ui.inlineAccountsResponse()){const e=e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:t,response:{method:"requestEthereumAccounts",result:e}})};this.ui.requestEthereumAccounts({onCancel:r,onAccounts:e})}else{const e=n.standardErrors.provider.userRejectedRequest("User denied account authorization");this.ui.requestEthereumAccounts({onCancel:()=>r(e)})}d.accountRequestCallbackIds.add(t),this.ui.inlineAccountsResponse()||this.ui.isStandalone()||this.publishWeb3RequestEvent(t,e)})),cancel:r}}selectProvider(e){const t=(0,i.randomBytesHex)(8);return{cancel:e=>{this.publishWeb3RequestCanceledEvent(t),this.handleErrorResponse(t,"selectProvider",e)},promise:new Promise(((r,n)=>{this.relayEventManager.callbacks.set(t,(e=>{if((0,u.isErrorResponse)(e))return n(new Error(e.errorMessage));r(e)})),this.ui.selectProvider&&this.ui.selectProvider({onApprove:e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:t,response:{method:"selectProvider",result:e}})},onCancel:e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:t,response:{method:"selectProvider",result:s.ProviderType.Unselected}})},providerOptions:e})}))}}watchAsset(e,t,r,n,s,o){const a={method:"watchAsset",params:{type:e,options:{address:t,symbol:r,decimals:n,image:s},chainId:o}};let c=null;const l=(0,i.randomBytesHex)(8),h=e=>{this.publishWeb3RequestCanceledEvent(l),this.handleErrorResponse(l,a.method,e),null==c||c()};return this.ui.inlineWatchAsset()||(c=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:h,onResetConnection:this.resetAndReload})),{cancel:h,promise:new Promise(((i,h)=>{this.relayEventManager.callbacks.set(l,(e=>{if(null==c||c(),(0,u.isErrorResponse)(e))return h(new Error(e.errorMessage));i(e)}));this.ui.inlineWatchAsset()&&this.ui.watchAsset({onApprove:()=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:l,response:{method:"watchAsset",result:!0}})},onCancel:e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:l,response:{method:"watchAsset",result:!1}})},type:e,address:t,symbol:r,decimals:n,image:s,chainId:o}),this.ui.inlineWatchAsset()||this.ui.isStandalone()||this.publishWeb3RequestEvent(l,a)}))}}addEthereumChain(e,t,r,n,s,o){const a={method:"addEthereumChain",params:{chainId:e,rpcUrls:t,blockExplorerUrls:n,chainName:s,iconUrls:r,nativeCurrency:o}};let c=null;const l=(0,i.randomBytesHex)(8),h=e=>{this.publishWeb3RequestCanceledEvent(l),this.handleErrorResponse(l,a.method,e),null==c||c()};return this.ui.inlineAddEthereumChain(e)||(c=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:h,onResetConnection:this.resetAndReload})),{promise:new Promise(((t,r)=>{this.relayEventManager.callbacks.set(l,(e=>{if(null==c||c(),(0,u.isErrorResponse)(e))return r(new Error(e.errorMessage));t(e)}));this.ui.inlineAddEthereumChain(e)&&this.ui.addEthereumChain({onCancel:e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:l,response:{method:"addEthereumChain",result:{isApproved:!1,rpcUrl:""}}})},onApprove:e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:l,response:{method:"addEthereumChain",result:{isApproved:!0,rpcUrl:e}}})},chainId:a.params.chainId,rpcUrls:a.params.rpcUrls,blockExplorerUrls:a.params.blockExplorerUrls,chainName:a.params.chainName,iconUrls:a.params.iconUrls,nativeCurrency:a.params.nativeCurrency}),this.ui.inlineAddEthereumChain(e)||this.ui.isStandalone()||this.publishWeb3RequestEvent(l,a)})),cancel:h}}switchEthereumChain(e,t){const r={method:"switchEthereumChain",params:Object.assign({chainId:e},{address:t})},s=(0,i.randomBytesHex)(8);return{promise:new Promise(((t,i)=>{this.relayEventManager.callbacks.set(s,(e=>(0,u.isErrorResponse)(e)&&e.errorCode?i(n.standardErrors.provider.custom({code:e.errorCode,message:"Unrecognized chain ID. Try adding the chain using addEthereumChain first."})):(0,u.isErrorResponse)(e)?i(new Error(e.errorMessage)):void t(e))),this.ui.switchEthereumChain({onCancel:t=>{var r;if(t){const i=null!==(r=(0,n.getErrorCode)(t))&&void 0!==r?r:n.standardErrorCodes.provider.unsupportedChain;this.handleErrorResponse(s,"switchEthereumChain",t instanceof Error?t:n.standardErrors.provider.unsupportedChain(e),i)}else this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:s,response:{method:"switchEthereumChain",result:{isApproved:!1,rpcUrl:""}}})},onApprove:e=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:s,response:{method:"switchEthereumChain",result:{isApproved:!0,rpcUrl:e}}})},chainId:r.params.chainId,address:r.params.address}),this.ui.inlineSwitchEthereumChain()||this.ui.isStandalone()||this.publishWeb3RequestEvent(s,r)})),cancel:e=>{this.publishWeb3RequestCanceledEvent(s),this.handleErrorResponse(s,r.method,e)}}}inlineAddEthereumChain(e){return this.ui.inlineAddEthereumChain(e)}getSessionIdHash(){return c.Session.hash(this._session.id)}sendRequestStandalone(e,t){const r=r=>{this.handleErrorResponse(e,t.method,r)},n=t=>{this.handleWeb3ResponseMessage({type:"WEB3_RESPONSE",id:e,response:t})};switch(t.method){case"signEthereumMessage":this.ui.signEthereumMessage({request:t,onSuccess:n,onCancel:r});break;case"signEthereumTransaction":this.ui.signEthereumTransaction({request:t,onSuccess:n,onCancel:r});break;case"submitEthereumTransaction":this.ui.submitEthereumTransaction({request:t,onSuccess:n,onCancel:r});break;case"ethereumAddressFromSignedMessage":this.ui.ethereumAddressFromSignedMessage({request:t,onSuccess:n});break;default:r()}}}t.WalletLinkRelay=d,d.accountRequestCallbackIds=new Set},92757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletLinkConnection=void 0;const n=r(98957),s=r(35814),i=r(78176),o=r(13948),a=r(80947),c=r(47521),l=r(1274);t.WalletLinkConnection=class{constructor({session:e,linkAPIUrl:t,listener:r,diagnostic:u,WebSocketClass:h=WebSocket}){this.destroyed=!1,this.lastHeartbeatResponse=0,this.nextReqId=(0,n.IntNumber)(1),this._connected=!1,this._linked=!1,this.shouldFetchUnseenEventsOnConnect=!1,this.requestResolutions=new Map,this.handleSessionMetadataUpdated=e=>{e&&new Map([["__destroyed",this.handleDestroyed],["EthereumAddress",this.handleAccountUpdated],["WalletUsername",this.handleWalletUsernameUpdated],["AppVersion",this.handleAppVersionUpdated],["ChainId",t=>e.JsonRpcUrl&&this.handleChainUpdated(t,e.JsonRpcUrl)]]).forEach(((t,r)=>{const n=e[r];void 0!==n&&t(n)}))},this.handleDestroyed=e=>{var t,r;"1"===e&&(null===(t=this.listener)||void 0===t||t.resetAndReload(),null===(r=this.diagnostic)||void 0===r||r.log(i.EVENTS.METADATA_DESTROYED,{alreadyDestroyed:this.isDestroyed,sessionIdHash:a.Session.hash(this.session.id)}))},this.handleAccountUpdated=async e=>{var t,r;try{const r=await this.cipher.decrypt(e);null===(t=this.listener)||void 0===t||t.accountUpdated(r)}catch(e){null===(r=this.diagnostic)||void 0===r||r.log(i.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"selectedAddress"})}},this.handleMetadataUpdated=async(e,t)=>{var r,n;try{const n=await this.cipher.decrypt(t);null===(r=this.listener)||void 0===r||r.metadataUpdated(e,n)}catch(t){null===(n=this.diagnostic)||void 0===n||n.log(i.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:e})}},this.handleWalletUsernameUpdated=async e=>{this.handleMetadataUpdated(o.WALLET_USER_NAME_KEY,e)},this.handleAppVersionUpdated=async e=>{this.handleMetadataUpdated(o.APP_VERSION_KEY,e)},this.handleChainUpdated=async(e,t)=>{var r,n;try{const n=await this.cipher.decrypt(e),s=await this.cipher.decrypt(t);null===(r=this.listener)||void 0===r||r.chainUpdated(n,s)}catch(e){null===(n=this.diagnostic)||void 0===n||n.log(i.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"chainId|jsonRpcUrl"})}},this.session=e,this.cipher=new s.Cipher(e.secret),this.diagnostic=u,this.listener=r;const d=new l.WalletLinkWebSocket(`${t}/rpc`,h);d.setConnectionStateListener((async t=>{var r;null===(r=this.diagnostic)||void 0===r||r.log(i.EVENTS.CONNECTED_STATE_CHANGE,{state:t,sessionIdHash:a.Session.hash(e.id)});let n=!1;switch(t){case l.ConnectionState.DISCONNECTED:if(!this.destroyed){const e=async()=>{await new Promise((e=>setTimeout(e,5e3))),this.destroyed||d.connect().catch((()=>{e()}))};e()}break;case l.ConnectionState.CONNECTED:try{await this.authenticate(),this.sendIsLinked(),this.sendGetSessionConfig(),n=!0}catch(e){}this.updateLastHeartbeat(),setInterval((()=>{this.heartbeat()}),1e4),this.shouldFetchUnseenEventsOnConnect&&this.fetchUnseenEventsAPI();case l.ConnectionState.CONNECTING:}this.connected!==n&&(this.connected=n)})),d.setIncomingDataListener((t=>{var r,n,s;switch(t.type){case"Heartbeat":return void this.updateLastHeartbeat();case"IsLinkedOK":case"Linked":{const n="IsLinkedOK"===t.type?t.linked:void 0;null===(r=this.diagnostic)||void 0===r||r.log(i.EVENTS.LINKED,{sessionIdHash:a.Session.hash(e.id),linked:n,type:t.type,onlineGuests:t.onlineGuests}),this.linked=n||t.onlineGuests>0;break}case"GetSessionConfigOK":case"SessionConfigUpdated":null===(n=this.diagnostic)||void 0===n||n.log(i.EVENTS.SESSION_CONFIG_RECEIVED,{sessionIdHash:a.Session.hash(e.id),metadata_keys:t&&t.metadata?Object.keys(t.metadata):void 0}),this.handleSessionMetadataUpdated(t.metadata);break;case"Event":this.handleIncomingEvent(t)}void 0!==t.id&&(null===(s=this.requestResolutions.get(t.id))||void 0===s||s(t))})),this.ws=d,this.http=new c.WalletLinkHTTP(t,e.id,e.key)}connect(){var e;if(this.destroyed)throw new Error("instance is destroyed");null===(e=this.diagnostic)||void 0===e||e.log(i.EVENTS.STARTED_CONNECTING,{sessionIdHash:a.Session.hash(this.session.id)}),this.ws.connect()}destroy(){var e;this.destroyed=!0,this.ws.disconnect(),null===(e=this.diagnostic)||void 0===e||e.log(i.EVENTS.DISCONNECTED,{sessionIdHash:a.Session.hash(this.session.id)}),this.listener=void 0}get isDestroyed(){return this.destroyed}get connected(){return this._connected}set connected(e){var t,r;this._connected=e,e&&(null===(t=this.onceConnected)||void 0===t||t.call(this)),null===(r=this.listener)||void 0===r||r.connectedUpdated(e)}setOnceConnected(e){return new Promise((t=>{this.connected?e().then(t):this.onceConnected=()=>{e().then(t),this.onceConnected=void 0}}))}get linked(){return this._linked}set linked(e){var t,r;this._linked=e,e&&(null===(t=this.onceLinked)||void 0===t||t.call(this)),null===(r=this.listener)||void 0===r||r.linkedUpdated(e)}setOnceLinked(e){return new Promise((t=>{this.linked?e().then(t):this.onceLinked=()=>{e().then(t),this.onceLinked=void 0}}))}async handleIncomingEvent(e){var t,r;if("Event"===e.type&&"Web3Response"===e.event)try{const r=await this.cipher.decrypt(e.data),n=JSON.parse(r);if("WEB3_RESPONSE"!==n.type)return;null===(t=this.listener)||void 0===t||t.handleWeb3ResponseMessage(n)}catch(e){null===(r=this.diagnostic)||void 0===r||r.log(i.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"incomingEvent"})}}async checkUnseenEvents(){if(this.connected){await new Promise((e=>setTimeout(e,250)));try{await this.fetchUnseenEventsAPI()}catch(e){console.error("Unable to check for unseen events",e)}}else this.shouldFetchUnseenEventsOnConnect=!0}async fetchUnseenEventsAPI(){this.shouldFetchUnseenEventsOnConnect=!1,(await this.http.fetchUnseenEvents()).forEach((e=>this.handleIncomingEvent(e)))}async setSessionMetadata(e,t){const r={type:"SetSessionConfig",id:(0,n.IntNumber)(this.nextReqId++),sessionId:this.session.id,metadata:{[e]:t}};return this.setOnceConnected((async()=>{const e=await this.makeRequest(r);if("Fail"===e.type)throw new Error(e.error||"failed to set session metadata")}))}async publishEvent(e,t,r=!1){const s=await this.cipher.encrypt(JSON.stringify(Object.assign(Object.assign({},t),{origin:location.origin,relaySource:window.coinbaseWalletExtension?"injected_sdk":"sdk"}))),i={type:"PublishEvent",id:(0,n.IntNumber)(this.nextReqId++),sessionId:this.session.id,event:e,data:s,callWebhook:r};return this.setOnceLinked((async()=>{const e=await this.makeRequest(i);if("Fail"===e.type)throw new Error(e.error||"failed to publish event");return e.eventId}))}sendData(e){this.ws.sendData(JSON.stringify(e))}updateLastHeartbeat(){this.lastHeartbeatResponse=Date.now()}heartbeat(){if(Date.now()-this.lastHeartbeatResponse>2e4)this.ws.disconnect();else try{this.ws.sendData("h")}catch(e){}}async makeRequest(e,t=6e4){const r=e.id;let n;return this.sendData(e),Promise.race([new Promise(((e,s)=>{n=window.setTimeout((()=>{s(new Error(`request ${r} timed out`))}),t)})),new Promise((e=>{this.requestResolutions.set(r,(t=>{clearTimeout(n),e(t),this.requestResolutions.delete(r)}))}))])}async authenticate(){const e={type:"HostSession",id:(0,n.IntNumber)(this.nextReqId++),sessionId:this.session.id,sessionKey:this.session.key},t=await this.makeRequest(e);if("Fail"===t.type)throw new Error(t.error||"failed to authentcate")}sendIsLinked(){const e={type:"IsLinked",id:(0,n.IntNumber)(this.nextReqId++),sessionId:this.session.id};this.sendData(e)}sendGetSessionConfig(){const e={type:"GetSessionConfig",id:(0,n.IntNumber)(this.nextReqId++),sessionId:this.session.id};this.sendData(e)}}},47521:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletLinkHTTP=void 0,t.WalletLinkHTTP=class{constructor(e,t,r){this.linkAPIUrl=e,this.sessionId=t;const n=`${t}:${r}`;this.auth=`Basic ${btoa(n)}`}async markUnseenEventsAsSeen(e){return Promise.all(e.map((e=>fetch(`${this.linkAPIUrl}/events/${e.eventId}/seen`,{method:"POST",headers:{Authorization:this.auth}})))).catch((e=>console.error("Unabled to mark event as failed:",e)))}async fetchUnseenEvents(){var e;const t=await fetch(`${this.linkAPIUrl}/events?unseen=true`,{headers:{Authorization:this.auth}});if(t.ok){const{events:r,error:n}=await t.json();if(n)throw new Error(`Check unseen events failed: ${n}`);const s=null!==(e=null==r?void 0:r.filter((e=>"Web3Response"===e.event)).map((e=>({type:"Event",sessionId:this.sessionId,eventId:e.id,event:e.event,data:e.data}))))&&void 0!==e?e:[];return this.markUnseenEventsAsSeen(s),s}throw new Error(`Check unseen events failed: ${t.status}`)}}},1274:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.WalletLinkWebSocket=t.ConnectionState=void 0,function(e){e[e.DISCONNECTED=0]="DISCONNECTED",e[e.CONNECTING=1]="CONNECTING",e[e.CONNECTED=2]="CONNECTED"}(r||(t.ConnectionState=r={})),t.WalletLinkWebSocket=class{setConnectionStateListener(e){this.connectionStateListener=e}setIncomingDataListener(e){this.incomingDataListener=e}constructor(e,t=WebSocket){this.WebSocketClass=t,this.webSocket=null,this.pendingData=[],this.url=e.replace(/^http/,"ws")}async connect(){if(this.webSocket)throw new Error("webSocket object is not null");return new Promise(((e,t)=>{var n;let s;try{this.webSocket=s=new this.WebSocketClass(this.url)}catch(e){return void t(e)}null===(n=this.connectionStateListener)||void 0===n||n.call(this,r.CONNECTING),s.onclose=e=>{var n;this.clearWebSocket(),t(new Error(`websocket error ${e.code}: ${e.reason}`)),null===(n=this.connectionStateListener)||void 0===n||n.call(this,r.DISCONNECTED)},s.onopen=t=>{var n;e(),null===(n=this.connectionStateListener)||void 0===n||n.call(this,r.CONNECTED),this.pendingData.length>0&&([...this.pendingData].forEach((e=>this.sendData(e))),this.pendingData=[])},s.onmessage=e=>{var t,r;if("h"===e.data)null===(t=this.incomingDataListener)||void 0===t||t.call(this,{type:"Heartbeat"});else try{const t=JSON.parse(e.data);null===(r=this.incomingDataListener)||void 0===r||r.call(this,t)}catch(e){}}}))}disconnect(){var e;const{webSocket:t}=this;if(t){this.clearWebSocket(),null===(e=this.connectionStateListener)||void 0===e||e.call(this,r.DISCONNECTED),this.connectionStateListener=void 0,this.incomingDataListener=void 0;try{t.close()}catch(e){}}}sendData(e){const{webSocket:t}=this;if(!t)return this.pendingData.push(e),void this.connect();t.send(e)}clearWebSocket(){const{webSocket:e}=this;e&&(this.webSocket=null,e.onclose=null,e.onerror=null,e.onmessage=null,e.onopen=null)}}},34040:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isErrorResponse=void 0,t.isErrorResponse=function(e){return void 0!==e.errorMessage}},70764:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletLinkRelayUI=void 0;const n=r(18679),s=r(95284),i=r(20022);t.WalletLinkRelayUI=class{constructor(e){this.standalone=null,this.attached=!1,this.snackbar=new i.Snackbar({darkMode:e.darkMode}),this.linkFlow=new s.LinkFlow({darkMode:e.darkMode,version:e.version,sessionId:e.session.id,sessionSecret:e.session.secret,linkAPIUrl:e.linkAPIUrl,isParentConnection:!1})}attach(){if(this.attached)throw new Error("Coinbase Wallet SDK UI is already attached");const e=document.documentElement,t=document.createElement("div");t.className="-cbwsdk-css-reset",e.appendChild(t),this.linkFlow.attach(t),this.snackbar.attach(t),this.attached=!0,(0,n.injectCssReset)()}setConnected(e){this.linkFlow.setConnected(e)}setChainId(e){this.linkFlow.setChainId(e)}setConnectDisabled(e){this.linkFlow.setConnectDisabled(e)}addEthereumChain(){}watchAsset(){}switchEthereumChain(){}requestEthereumAccounts(e){this.linkFlow.open({onCancel:e.onCancel})}hideRequestEthereumAccounts(){this.linkFlow.close()}signEthereumMessage(){}signEthereumTransaction(){}submitEthereumTransaction(){}ethereumAddressFromSignedMessage(){}showConnecting(e){let t;return t=e.isUnlinkedErrorState?{autoExpand:!0,message:"Connection lost",menuItems:[{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:e.onResetConnection}]}:{message:"Confirm on phone",menuItems:[{isRed:!0,info:"Cancel transaction",svgWidth:"11",svgHeight:"11",path:"M10.3711 1.52346L9.21775 0.370117L5.37109 4.21022L1.52444 0.370117L0.371094 1.52346L4.2112 5.37012L0.371094 9.21677L1.52444 10.3701L5.37109 6.53001L9.21775 10.3701L10.3711 9.21677L6.53099 5.37012L10.3711 1.52346Z",defaultFillRule:"inherit",defaultClipRule:"inherit",onClick:e.onCancel},{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:e.onResetConnection}]},this.snackbar.presentItem(t)}reloadUI(){document.location.reload()}inlineAccountsResponse(){return!1}inlineAddEthereumChain(){return!1}inlineWatchAsset(){return!1}inlineSwitchEthereumChain(){return!1}setStandalone(e){this.standalone=e}isStandalone(){var e;return null!==(e=this.standalone)&&void 0!==e&&e}}},58676:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=".-cbwsdk-css-reset .-cbwsdk-connect-content{height:430px;width:700px;border-radius:12px;padding:30px}.-cbwsdk-css-reset .-cbwsdk-connect-content.light{background:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content.dark{background:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-connect-content-header{display:flex;align-items:center;justify-content:space-between;margin:0 0 30px}.-cbwsdk-css-reset .-cbwsdk-connect-content-heading{font-style:normal;font-weight:500;font-size:28px;line-height:36px;margin:0}.-cbwsdk-css-reset .-cbwsdk-connect-content-heading.light{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-connect-content-heading.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content-layout{display:flex;flex-direction:row}.-cbwsdk-css-reset .-cbwsdk-connect-content-column-left{margin-right:30px;display:flex;flex-direction:column;justify-content:space-between}.-cbwsdk-css-reset .-cbwsdk-connect-content-column-right{flex:25%;margin-right:34px}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-wrapper{width:220px;height:220px;border-radius:12px;display:flex;justify-content:center;align-items:center;background:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting{position:absolute;top:0;bottom:0;left:0;right:0;display:flex;flex-direction:column;align-items:center;justify-content:center}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.light{background-color:rgba(255,255,255,.95)}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.light>p{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.dark{background-color:rgba(10,11,13,.9)}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting.dark>p{color:#fff}.-cbwsdk-css-reset .-cbwsdk-connect-content-qr-connecting>p{font-size:12px;font-weight:bold;margin-top:16px}.-cbwsdk-css-reset .-cbwsdk-connect-content-update-app{border-radius:8px;font-size:14px;line-height:20px;padding:12px;width:339px}.-cbwsdk-css-reset .-cbwsdk-connect-content-update-app.light{background:#eef0f3;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-connect-content-update-app.dark{background:#1e2025;color:#8a919e}.-cbwsdk-css-reset .-cbwsdk-cancel-button{-webkit-appearance:none;border:none;background:none;cursor:pointer;padding:0;margin:0}.-cbwsdk-css-reset .-cbwsdk-cancel-button-x{position:relative;display:block;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-wallet-steps{padding:0 0 0 16px;margin:0;width:100%;list-style:decimal}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item{list-style-type:decimal;display:list-item;font-style:normal;font-weight:400;font-size:16px;line-height:24px;margin-top:20px}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item.light{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-item-wrapper{display:flex;align-items:center}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-pad-left{margin-left:6px}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon{display:flex;border-radius:50%;height:24px;width:24px}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon svg{margin:auto;display:block}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon.light{background:#0052ff}.-cbwsdk-css-reset .-cbwsdk-wallet-steps-icon.dark{background:#588af5}.-cbwsdk-css-reset .-cbwsdk-connect-item{align-items:center;display:flex;flex-direction:row;padding:16px 24px;gap:12px;cursor:pointer;border-radius:100px;font-weight:600}.-cbwsdk-css-reset .-cbwsdk-connect-item.light{background:#f5f8ff;color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-connect-item.dark{background:#001033;color:#588af5}.-cbwsdk-css-reset .-cbwsdk-connect-item-copy-wrapper{margin:0 4px 0 8px}.-cbwsdk-css-reset .-cbwsdk-connect-item-title{margin:0 0 0;font-size:16px;line-height:24px;font-weight:500}.-cbwsdk-css-reset .-cbwsdk-connect-item-description{font-weight:400;font-size:14px;line-height:20px;margin:0}"},11574:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.CoinbaseWalletSteps=t.ConnectContent=void 0;const s=n(r(20053)),i=r(50172),o=r(97),a=r(55405),c=r(35981),l=r(43775),u=r(33201),h=r(11),d=r(99582),p=n(r(58676)),f=w,m=e=>"light"===e?"#FFFFFF":"#0A0B0D";function g({title:e,description:t,theme:r}){return(0,i.h)("div",{className:(0,s.default)("-cbwsdk-connect-item",r)},(0,i.h)("div",null,(0,i.h)(l.CoinbaseWalletRound,null)),(0,i.h)("div",{className:"-cbwsdk-connect-item-copy-wrapper"},(0,i.h)("h3",{className:"-cbwsdk-connect-item-title"},e),(0,i.h)("p",{className:"-cbwsdk-connect-item-description"},t)))}function w({theme:e}){return(0,i.h)("ol",{className:"-cbwsdk-wallet-steps"},(0,i.h)("li",{className:(0,s.default)("-cbwsdk-wallet-steps-item",e)},(0,i.h)("div",{className:"-cbwsdk-wallet-steps-item-wrapper"},"Open Coinbase Wallet app")),(0,i.h)("li",{className:(0,s.default)("-cbwsdk-wallet-steps-item",e)},(0,i.h)("div",{className:"-cbwsdk-wallet-steps-item-wrapper"},(0,i.h)("span",null,"Tap ",(0,i.h)("strong",null,"Scan")," "),(0,i.h)("span",{className:(0,s.default)("-cbwsdk-wallet-steps-pad-left","-cbwsdk-wallet-steps-icon",e)},(0,i.h)(u.QRCodeIcon,{fill:m(e)})))))}t.ConnectContent=function(e){const{theme:t}=e,r=(0,o.createQrUrl)(e.sessionId,e.sessionSecret,e.linkAPIUrl,e.isParentConnection,e.version,e.chainId),n=f;return(0,i.h)("div",{"data-testid":"connect-content",className:(0,s.default)("-cbwsdk-connect-content",t)},(0,i.h)("style",null,p.default),(0,i.h)("div",{className:"-cbwsdk-connect-content-header"},(0,i.h)("h2",{className:(0,s.default)("-cbwsdk-connect-content-heading",t)},"Scan to connect with our mobile app"),e.onCancel&&(0,i.h)("button",{type:"button",className:"-cbwsdk-cancel-button",onClick:e.onCancel},(0,i.h)(c.CloseIcon,{fill:"light"===t?"#0A0B0D":"#FFFFFF"}))),(0,i.h)("div",{className:"-cbwsdk-connect-content-layout"},(0,i.h)("div",{className:"-cbwsdk-connect-content-column-left"},(0,i.h)(g,{title:"Coinbase Wallet app",description:"Connect with your self-custody wallet",theme:t})),(0,i.h)("div",{className:"-cbwsdk-connect-content-column-right"},(0,i.h)("div",{className:"-cbwsdk-connect-content-qr-wrapper"},(0,i.h)(h.QRCode,{content:r,width:200,height:200,fgColor:"#000",bgColor:"transparent"}),(0,i.h)("input",{type:"hidden",name:"cbw-cbwsdk-version",value:a.LIB_VERSION}),(0,i.h)("input",{type:"hidden",value:r})),(0,i.h)(n,{theme:t}),!e.isConnected&&(0,i.h)("div",{"data-testid":"connecting-spinner",className:(0,s.default)("-cbwsdk-connect-content-qr-connecting",t)},(0,i.h)(d.Spinner,{size:36,color:"dark"===t?"#FFF":"#000"}),(0,i.h)("p",null,"Connecting...")))))},t.CoinbaseWalletSteps=w},6416:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=".-cbwsdk-css-reset .-cbwsdk-connect-dialog{z-index:2147483647;position:fixed;top:0;left:0;right:0;bottom:0;display:flex;align-items:center;justify-content:center}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop{z-index:2147483647;position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop.light{background-color:rgba(0,0,0,.5)}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop.dark{background-color:rgba(50,53,61,.4)}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-box{display:flex;position:relative;flex-direction:column;transform:scale(1);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-box-hidden{opacity:0;transform:scale(0.85)}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-container{display:block}.-cbwsdk-css-reset .-cbwsdk-connect-dialog-container-hidden{display:none}"},2570:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ConnectDialog=void 0;const s=n(r(20053)),i=r(50172),o=r(45994),a=r(11574),c=r(95350),l=n(r(6416));t.ConnectDialog=e=>{const{isOpen:t,darkMode:r}=e,[n,u]=(0,o.useState)(!t),[h,d]=(0,o.useState)(!t);(0,o.useEffect)((()=>{const e=[window.setTimeout((()=>{d(!t)}),10)];return t?u(!1):e.push(window.setTimeout((()=>{u(!0)}),360)),()=>{e.forEach(window.clearTimeout)}}),[t]);const p=r?"dark":"light";return(0,i.h)("div",{class:(0,s.default)("-cbwsdk-connect-dialog-container",n&&"-cbwsdk-connect-dialog-container-hidden")},(0,i.h)("style",null,l.default),(0,i.h)("div",{class:(0,s.default)("-cbwsdk-connect-dialog-backdrop",p,h&&"-cbwsdk-connect-dialog-backdrop-hidden")}),(0,i.h)("div",{class:"-cbwsdk-connect-dialog"},(0,i.h)("div",{class:(0,s.default)("-cbwsdk-connect-dialog-box",h&&"-cbwsdk-connect-dialog-box-hidden")},e.connectDisabled?null:(0,i.h)(a.ConnectContent,{theme:p,version:e.version,sessionId:e.sessionId,sessionSecret:e.sessionSecret,linkAPIUrl:e.linkAPIUrl,isConnected:e.isConnected,isParentConnection:e.isParentConnection,chainId:e.chainId,onCancel:e.onCancel}),(0,i.h)(c.TryExtensionContent,{theme:p}))))}},95284:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LinkFlow=void 0;const n=r(50172),s=r(2570);t.LinkFlow=class{constructor(e){this.connected=!1,this.chainId=1,this.isOpen=!1,this.onCancel=null,this.root=null,this.connectDisabled=!1,this.darkMode=e.darkMode,this.version=e.version,this.sessionId=e.sessionId,this.sessionSecret=e.sessionSecret,this.linkAPIUrl=e.linkAPIUrl,this.isParentConnection=e.isParentConnection}attach(e){this.root=document.createElement("div"),this.root.className="-cbwsdk-link-flow-root",e.appendChild(this.root),this.render()}setConnected(e){this.connected!==e&&(this.connected=e,this.render())}setChainId(e){this.chainId!==e&&(this.chainId=e,this.render())}detach(){var e;this.root&&((0,n.render)(null,this.root),null===(e=this.root.parentElement)||void 0===e||e.removeChild(this.root))}setConnectDisabled(e){this.connectDisabled=e}open(e){this.isOpen=!0,this.onCancel=e.onCancel,this.render()}close(){this.isOpen=!1,this.onCancel=null,this.render()}render(){this.root&&(0,n.render)((0,n.h)(s.ConnectDialog,{darkMode:this.darkMode,version:this.version,sessionId:this.sessionId,sessionSecret:this.sessionSecret,linkAPIUrl:this.linkAPIUrl,isOpen:this.isOpen,isConnected:this.connected,isParentConnection:this.isParentConnection,chainId:this.chainId,onCancel:this.onCancel,connectDisabled:this.connectDisabled}),this.root)}}},11:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.QRCode=void 0;const s=r(50172),i=r(45994),o=n(r(3236));t.QRCode=e=>{const[t,r]=(0,i.useState)("");return(0,i.useEffect)((()=>{var t,n;const s=new o.default({content:e.content,background:e.bgColor||"#ffffff",color:e.fgColor||"#000000",container:"svg",ecl:"M",width:null!==(t=e.width)&&void 0!==t?t:256,height:null!==(n=e.height)&&void 0!==n?n:256,padding:0,image:e.image}),i=Buffer.from(s.svg(),"utf8").toString("base64");r(`data:image/svg+xml;base64,${i}`)}),[e.bgColor,e.content,e.fgColor,e.height,e.image,e.width]),t?(0,s.h)("img",{src:t,alt:"QR Code"}):null}},83666:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=".-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop{position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s;background-color:rgba(10,11,13,.5)}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box{display:block;position:fixed;top:50%;left:50%;transform:translate(-50%, -50%);padding:20px;border-radius:8px;background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box p{display:block;font-weight:400;font-size:14px;line-height:20px;padding-bottom:12px;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box button{appearance:none;border:none;background:none;color:#0052ff;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark{background-color:#0a0b0d;color:#fff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark button{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light{background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light button{color:#0052ff}"},31976:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.RedirectDialog=void 0;const s=n(r(20053)),i=r(50172),o=r(18679),a=r(11281),c=n(r(83666));t.RedirectDialog=class{constructor(){this.root=null}attach(){const e=document.documentElement;this.root=document.createElement("div"),this.root.className="-cbwsdk-css-reset",e.appendChild(this.root),(0,o.injectCssReset)()}present(e){this.render(e)}clear(){this.render(null)}render(e){this.root&&((0,i.render)(null,this.root),e&&(0,i.render)((0,i.h)(l,Object.assign({},e,{onDismiss:()=>{this.clear()}})),this.root))}};const l=({title:e,buttonText:t,darkMode:r,onButtonClick:n,onDismiss:o})=>{const l=r?"dark":"light";return(0,i.h)(a.SnackbarContainer,{darkMode:r},(0,i.h)("div",{class:"-cbwsdk-redirect-dialog"},(0,i.h)("style",null,c.default),(0,i.h)("div",{class:"-cbwsdk-redirect-dialog-backdrop",onClick:o}),(0,i.h)("div",{class:(0,s.default)("-cbwsdk-redirect-dialog-box",l)},(0,i.h)("p",null,e),(0,i.h)("button",{onClick:n},t))))}},27700:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=".-cbwsdk-css-reset .-gear-container{margin-left:16px !important;margin-right:9px !important;display:flex;align-items:center;justify-content:center;width:24px;height:24px;transition:opacity .25s}.-cbwsdk-css-reset .-gear-container *{user-select:none}.-cbwsdk-css-reset .-gear-container svg{opacity:0;position:absolute}.-cbwsdk-css-reset .-gear-icon{height:12px;width:12px;z-index:10000}.-cbwsdk-css-reset .-cbwsdk-snackbar{align-items:flex-end;display:flex;flex-direction:column;position:fixed;right:0;top:0;z-index:2147483647}.-cbwsdk-css-reset .-cbwsdk-snackbar *{user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance{display:flex;flex-direction:column;margin:8px 16px 0 16px;overflow:visible;text-align:left;transform:translateX(0);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header:hover .-gear-container svg{opacity:1}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header{display:flex;align-items:center;background:#fff;overflow:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-cblogo{margin:8px 8px 8px 8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-message{color:#000;font-size:13px;line-height:1.5;user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu{background:#fff;transition:opacity .25s ease-in-out,transform .25s linear,visibility 0s;visibility:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;opacity:0;flex-direction:column;padding-left:8px;padding-right:8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:last-child{margin-bottom:8px !important}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover{background:#f5f7f8;border-radius:6px;transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover span{color:#050f19;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover svg path{fill:#000;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item{visibility:inherit;height:35px;margin-top:8px;margin-bottom:0;display:flex;flex-direction:row;align-items:center;padding:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item *{visibility:inherit;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover{background:rgba(223,95,103,.2);transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover svg path{fill:#df5f67;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover span{color:#df5f67;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-info{color:#aaa;font-size:13px;margin:0 8px 0 32px;position:absolute}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-hidden{opacity:0;text-align:left;transform:translateX(25%);transition:opacity .5s linear}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-expanded .-cbwsdk-snackbar-instance-menu{opacity:1;display:flex;transform:translateY(8px);visibility:visible}"},20022:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.SnackbarInstance=t.SnackbarContainer=t.Snackbar=void 0;const s=n(r(20053)),i=r(50172),o=r(45994),a=n(r(27700));t.Snackbar=class{constructor(e){this.items=new Map,this.nextItemKey=0,this.root=null,this.darkMode=e.darkMode}attach(e){this.root=document.createElement("div"),this.root.className="-cbwsdk-snackbar-root",e.appendChild(this.root),this.render()}presentItem(e){const t=this.nextItemKey++;return this.items.set(t,e),this.render(),()=>{this.items.delete(t),this.render()}}clear(){this.items.clear(),this.render()}render(){this.root&&(0,i.render)((0,i.h)("div",null,(0,i.h)(t.SnackbarContainer,{darkMode:this.darkMode},Array.from(this.items.entries()).map((([e,r])=>(0,i.h)(t.SnackbarInstance,Object.assign({},r,{key:e})))))),this.root)}},t.SnackbarContainer=e=>(0,i.h)("div",{class:(0,s.default)("-cbwsdk-snackbar-container")},(0,i.h)("style",null,a.default),(0,i.h)("div",{class:"-cbwsdk-snackbar"},e.children)),t.SnackbarInstance=({autoExpand:e,message:t,menuItems:r})=>{const[n,a]=(0,o.useState)(!0),[c,l]=(0,o.useState)(null!=e&&e);return(0,o.useEffect)((()=>{const e=[window.setTimeout((()=>{a(!1)}),1),window.setTimeout((()=>{l(!0)}),1e4)];return()=>{e.forEach(window.clearTimeout)}})),(0,i.h)("div",{class:(0,s.default)("-cbwsdk-snackbar-instance",n&&"-cbwsdk-snackbar-instance-hidden",c&&"-cbwsdk-snackbar-instance-expanded")},(0,i.h)("div",{class:"-cbwsdk-snackbar-instance-header",onClick:()=>{l(!c)}},(0,i.h)("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDkyIDEwLjQxOWE4LjkzIDguOTMgMCAwMTguOTMtOC45M2gxMS4xNjNhOC45MyA4LjkzIDAgMDE4LjkzIDguOTN2MTEuMTYzYTguOTMgOC45MyAwIDAxLTguOTMgOC45M0gxMC40MjJhOC45MyA4LjkzIDAgMDEtOC45My04LjkzVjEwLjQxOXoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEwLjQxOSAwSDIxLjU4QzI3LjMzNSAwIDMyIDQuNjY1IDMyIDEwLjQxOVYyMS41OEMzMiAyNy4zMzUgMjcuMzM1IDMyIDIxLjU4MSAzMkgxMC40MkM0LjY2NSAzMiAwIDI3LjMzNSAwIDIxLjU4MVYxMC40MkMwIDQuNjY1IDQuNjY1IDAgMTAuNDE5IDB6bTAgMS40ODhhOC45MyA4LjkzIDAgMDAtOC45MyA4LjkzdjExLjE2M2E4LjkzIDguOTMgMCAwMDguOTMgOC45M0gyMS41OGE4LjkzIDguOTMgMCAwMDguOTMtOC45M1YxMC40MmE4LjkzIDguOTMgMCAwMC04LjkzLTguOTNIMTAuNDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS45OTggMjYuMDQ5Yy01LjU0OSAwLTEwLjA0Ny00LjQ5OC0xMC4wNDctMTAuMDQ3IDAtNS41NDggNC40OTgtMTAuMDQ2IDEwLjA0Ny0xMC4wNDYgNS41NDggMCAxMC4wNDYgNC40OTggMTAuMDQ2IDEwLjA0NiAwIDUuNTQ5LTQuNDk4IDEwLjA0Ny0xMC4wNDYgMTAuMDQ3eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xMi43NjIgMTQuMjU0YzAtLjgyMi42NjctMS40ODkgMS40ODktMS40ODloMy40OTdjLjgyMiAwIDEuNDg4LjY2NiAxLjQ4OCAxLjQ4OXYzLjQ5N2MwIC44MjItLjY2NiAxLjQ4OC0xLjQ4OCAxLjQ4OGgtMy40OTdhMS40ODggMS40ODggMCAwMS0xLjQ4OS0xLjQ4OHYtMy40OTh6IiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+",class:"-cbwsdk-snackbar-instance-header-cblogo"})," ",(0,i.h)("div",{class:"-cbwsdk-snackbar-instance-header-message"},t),(0,i.h)("div",{class:"-gear-container"},!c&&(0,i.h)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,i.h)("circle",{cx:"12",cy:"12",r:"12",fill:"#F5F7F8"})),(0,i.h)("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDYuNzV2LTEuNWwtMS43Mi0uNTdjLS4wOC0uMjctLjE5LS41Mi0uMzItLjc3bC44MS0xLjYyLTEuMDYtMS4wNi0xLjYyLjgxYy0uMjQtLjEzLS41LS4yNC0uNzctLjMyTDYuNzUgMGgtMS41bC0uNTcgMS43MmMtLjI3LjA4LS41My4xOS0uNzcuMzJsLTEuNjItLjgxLTEuMDYgMS4wNi44MSAxLjYyYy0uMTMuMjQtLjI0LjUtLjMyLjc3TDAgNS4yNXYxLjVsMS43Mi41N2MuMDguMjcuMTkuNTMuMzIuNzdsLS44MSAxLjYyIDEuMDYgMS4wNiAxLjYyLS44MWMuMjQuMTMuNS4yMy43Ny4zMkw1LjI1IDEyaDEuNWwuNTctMS43MmMuMjctLjA4LjUyLS4xOS43Ny0uMzJsMS42Mi44MSAxLjA2LTEuMDYtLjgxLTEuNjJjLjEzLS4yNC4yMy0uNS4zMi0uNzdMMTIgNi43NXpNNiA4LjVhMi41IDIuNSAwIDAxMC01IDIuNSAyLjUgMCAwMTAgNXoiIGZpbGw9IiMwNTBGMTkiLz48L3N2Zz4=",class:"-gear-icon",title:"Expand"}))),r&&r.length>0&&(0,i.h)("div",{class:"-cbwsdk-snackbar-instance-menu"},r.map(((e,t)=>(0,i.h)("div",{class:(0,s.default)("-cbwsdk-snackbar-instance-menu-item",e.isRed&&"-cbwsdk-snackbar-instance-menu-item-is-red"),onClick:e.onClick,key:t},(0,i.h)("svg",{width:e.svgWidth,height:e.svgHeight,viewBox:"0 0 10 11",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,i.h)("path",{"fill-rule":e.defaultFillRule,"clip-rule":e.defaultClipRule,d:e.path,fill:"#AAAAAA"})),(0,i.h)("span",{class:(0,s.default)("-cbwsdk-snackbar-instance-menu-item-info",e.isRed&&"-cbwsdk-snackbar-instance-menu-item-info-is-red")},e.info))))))}},11281:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(20022),t)},84940:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=".-cbwsdk-css-reset .-cbwsdk-spinner{display:inline-block}.-cbwsdk-css-reset .-cbwsdk-spinner svg{display:inline-block;animation:2s linear infinite -cbwsdk-spinner-svg}.-cbwsdk-css-reset .-cbwsdk-spinner svg circle{animation:1.9s ease-in-out infinite both -cbwsdk-spinner-circle;display:block;fill:rgba(0,0,0,0);stroke-dasharray:283;stroke-dashoffset:280;stroke-linecap:round;stroke-width:10px;transform-origin:50% 50%}@keyframes -cbwsdk-spinner-svg{0%{transform:rotateZ(0deg)}100%{transform:rotateZ(360deg)}}@keyframes -cbwsdk-spinner-circle{0%,25%{stroke-dashoffset:280;transform:rotate(0)}50%,75%{stroke-dashoffset:75;transform:rotate(45deg)}100%{stroke-dashoffset:280;transform:rotate(360deg)}}"},99582:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Spinner=void 0;const s=r(50172),i=n(r(84940));t.Spinner=e=>{var t;const r=null!==(t=e.size)&&void 0!==t?t:64,n=e.color||"#000";return(0,s.h)("div",{class:"-cbwsdk-spinner"},(0,s.h)("style",null,i.default),(0,s.h)("svg",{viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",style:{width:r,height:r}},(0,s.h)("circle",{style:{cx:50,cy:50,r:45,stroke:n}})))}},55412:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=".-cbwsdk-css-reset .-cbwsdk-try-extension{display:flex;margin-top:12px;height:202px;width:700px;border-radius:12px;padding:30px}.-cbwsdk-css-reset .-cbwsdk-try-extension.light{background:#fff}.-cbwsdk-css-reset .-cbwsdk-try-extension.dark{background:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-try-extension-column-half{flex:50%}.-cbwsdk-css-reset .-cbwsdk-try-extension-heading{font-style:normal;font-weight:500;font-size:25px;line-height:32px;margin:0;max-width:204px}.-cbwsdk-css-reset .-cbwsdk-try-extension-heading.light{color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-try-extension-heading.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta{appearance:none;border:none;background:none;color:#0052ff;cursor:pointer;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta.light{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta.dark{color:#588af5}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta-wrapper{display:flex;align-items:center;margin-top:12px}.-cbwsdk-css-reset .-cbwsdk-try-extension-cta-icon{display:block;margin-left:4px;height:14px}.-cbwsdk-css-reset .-cbwsdk-try-extension-list{display:flex;flex-direction:column;justify-content:center;align-items:center;margin:0;padding:0;list-style:none;height:100%}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item{display:flex;align-items:center;flex-flow:nowrap;margin-top:24px}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item:first-of-type{margin-top:0}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon-wrapper{display:block}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon{display:flex;height:32px;width:32px;border-radius:50%}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon svg{margin:auto;display:block}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon.light{background:#eef0f3}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-icon.dark{background:#1e2025}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-copy{display:block;font-weight:400;font-size:14px;line-height:20px;padding-left:12px}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-copy.light{color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-try-extension-list-item-copy.dark{color:#8a919e}"},95350:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.TryExtensionContent=void 0;const s=n(r(20053)),i=r(50172),o=r(45994),a=r(45397),c=r(75239),l=r(11034),u=n(r(55412));t.TryExtensionContent=function({theme:e}){const[t,r]=(0,o.useState)(!1),n=(0,o.useCallback)((()=>{window.open("https://api.wallet.coinbase.com/rpc/v2/desktop/chrome","_blank")}),[]),h=(0,o.useCallback)((()=>{t?window.location.reload():(n(),r(!0))}),[n,t]);return(0,i.h)("div",{class:(0,s.default)("-cbwsdk-try-extension",e)},(0,i.h)("style",null,u.default),(0,i.h)("div",{class:"-cbwsdk-try-extension-column-half"},(0,i.h)("h3",{class:(0,s.default)("-cbwsdk-try-extension-heading",e)},"Or try the Coinbase Wallet browser extension"),(0,i.h)("div",{class:"-cbwsdk-try-extension-cta-wrapper"},(0,i.h)("button",{class:(0,s.default)("-cbwsdk-try-extension-cta",e),onClick:h},t?"Refresh":"Install"),(0,i.h)("div",null,!t&&(0,i.h)(a.ArrowLeftIcon,{class:"-cbwsdk-try-extension-cta-icon",fill:"light"===e?"#0052FF":"#588AF5"})))),(0,i.h)("div",{class:"-cbwsdk-try-extension-column-half"},(0,i.h)("ul",{class:"-cbwsdk-try-extension-list"},(0,i.h)("li",{class:"-cbwsdk-try-extension-list-item"},(0,i.h)("div",{class:"-cbwsdk-try-extension-list-item-icon-wrapper"},(0,i.h)("span",{class:(0,s.default)("-cbwsdk-try-extension-list-item-icon",e)},(0,i.h)(c.LaptopIcon,{fill:"light"===e?"#0A0B0D":"#FFFFFF"}))),(0,i.h)("div",{class:(0,s.default)("-cbwsdk-try-extension-list-item-copy",e)},"Connect with dapps with just one click on your desktop browser")),(0,i.h)("li",{class:"-cbwsdk-try-extension-list-item"},(0,i.h)("div",{class:"-cbwsdk-try-extension-list-item-icon-wrapper"},(0,i.h)("span",{class:(0,s.default)("-cbwsdk-try-extension-list-item-icon",e)},(0,i.h)(l.SafeIcon,{fill:"light"===e?"#0A0B0D":"#FFFFFF"}))),(0,i.h)("div",{class:(0,s.default)("-cbwsdk-try-extension-list-item-copy",e)},"Add an additional layer of security by using a supported Ledger hardware wallet")))))}},45397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ArrowLeftIcon=void 0;const n=r(50172);t.ArrowLeftIcon=function(e){return(0,n.h)("svg",Object.assign({width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},e),(0,n.h)("path",{d:"M8.60675 0.155884L7.37816 1.28209L12.7723 7.16662H0V8.83328H12.6548L6.82149 14.6666L8 15.8451L15.8201 8.02501L8.60675 0.155884Z"}))}},35981:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CloseIcon=void 0;const n=r(50172);t.CloseIcon=function(e){return(0,n.h)("svg",Object.assign({width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,n.h)("path",{d:"M13.7677 13L12.3535 14.4142L18.3535 20.4142L12.3535 26.4142L13.7677 27.8284L19.7677 21.8284L25.7677 27.8284L27.1819 26.4142L21.1819 20.4142L27.1819 14.4142L25.7677 13L19.7677 19L13.7677 13Z"}))}},43775:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CoinbaseWalletRound=void 0;const n=r(50172);t.CoinbaseWalletRound=function(e){return(0,n.h)("svg",Object.assign({width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,n.h)("circle",{cx:"14",cy:"14",r:"14",fill:"#0052FF"}),(0,n.h)("path",{d:"M23.8521 14.0003C23.8521 19.455 19.455 23.8521 14.0003 23.8521C8.54559 23.8521 4.14844 19.455 4.14844 14.0003C4.14844 8.54559 8.54559 4.14844 14.0003 4.14844C19.455 4.14844 23.8521 8.54559 23.8521 14.0003Z",fill:"white"}),(0,n.h)("path",{d:"M11.1855 12.5042C11.1855 12.0477 11.1855 11.7942 11.2835 11.642C11.3814 11.4899 11.4793 11.3377 11.6261 11.287C11.8219 11.1855 12.0178 11.1855 12.5073 11.1855H15.4934C15.983 11.1855 16.1788 11.1855 16.3746 11.287C16.5215 11.3884 16.6683 11.4899 16.7173 11.642C16.8152 11.8449 16.8152 12.0477 16.8152 12.5042V15.4965C16.8152 15.953 16.8152 16.2066 16.7173 16.3587C16.6194 16.5109 16.5215 16.663 16.3746 16.7137C16.1788 16.8152 15.983 16.8152 15.4934 16.8152H12.5073C12.0178 16.8152 11.8219 16.8152 11.6261 16.7137C11.4793 16.6123 11.3324 16.5109 11.2835 16.3587C11.1855 16.1558 11.1855 15.953 11.1855 15.4965V12.5042Z",fill:"#0052FF"}))}},75239:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LaptopIcon=void 0;const n=r(50172);t.LaptopIcon=function(e){return(0,n.h)("svg",Object.assign({width:"14",height:"14",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg"},e),(0,n.h)("path",{d:"M1.8001 2.2002H12.2001V9.40019H1.8001V2.2002ZM3.4001 3.8002V7.80019H10.6001V3.8002H3.4001Z"}),(0,n.h)("path",{d:"M13.4001 10.2002H0.600098C0.600098 11.0838 1.31644 11.8002 2.2001 11.8002H11.8001C12.6838 11.8002 13.4001 11.0838 13.4001 10.2002Z"}))}},33201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.QRCodeIcon=void 0;const n=r(50172);t.QRCodeIcon=function(e){return(0,n.h)("svg",Object.assign({width:"18",height:"18",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),(0,n.h)("path",{d:"M3 3V8.99939L5 8.99996V5H9V3H3Z"}),(0,n.h)("path",{d:"M15 21L21 21V15.0006L19 15V19L15 19V21Z"}),(0,n.h)("path",{d:"M21 9H19V5H15.0006L15 3H21V9Z"}),(0,n.h)("path",{d:"M3 15V21H8.99939L8.99996 19H5L5 15H3Z"}))}},11034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SafeIcon=void 0;const n=r(50172);t.SafeIcon=function(e){return(0,n.h)("svg",Object.assign({width:"14",height:"14",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg"},e),(0,n.h)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.600098 0.600098V11.8001H13.4001V0.600098H0.600098ZM7.0001 9.2001C5.3441 9.2001 4.0001 7.8561 4.0001 6.2001C4.0001 4.5441 5.3441 3.2001 7.0001 3.2001C8.6561 3.2001 10.0001 4.5441 10.0001 6.2001C10.0001 7.8561 8.6561 9.2001 7.0001 9.2001ZM0.600098 12.6001H3.8001V13.4001H0.600098V12.6001ZM10.2001 12.6001H13.4001V13.4001H10.2001V12.6001ZM8.8001 6.2001C8.8001 7.19421 7.99421 8.0001 7.0001 8.0001C6.00598 8.0001 5.2001 7.19421 5.2001 6.2001C5.2001 5.20598 6.00598 4.4001 7.0001 4.4001C7.99421 4.4001 8.8001 5.20598 8.8001 6.2001Z"}))}},35136:(e,t,r)=>{const n=r(86248),s=r(39404);function i(e){return e.startsWith("int[")?"int256"+e.slice(3):"int"===e?"int256":e.startsWith("uint[")?"uint256"+e.slice(4):"uint"===e?"uint256":e.startsWith("fixed[")?"fixed128x128"+e.slice(5):"fixed"===e?"fixed128x128":e.startsWith("ufixed[")?"ufixed128x128"+e.slice(6):"ufixed"===e?"ufixed128x128":e}function o(e){return parseInt(/^\D+(\d+)$/.exec(e)[1],10)}function a(e){var t=/^\D+(\d+)x(\d+)$/.exec(e);return[parseInt(t[1],10),parseInt(t[2],10)]}function c(e){var t=e.match(/(.*)\[(.*?)\]$/);return t?""===t[2]?"dynamic":parseInt(t[2],10):null}function l(e){var t=typeof e;if("string"===t)return n.isHexString(e)?new s(n.stripHexPrefix(e),16):new s(e,10);if("number"===t)return new s(e);if(e.toArray)return e;throw new Error("Argument is not a number")}function u(e,t){var r,i,h,d;if("address"===e)return u("uint160",l(t));if("bool"===e)return u("uint8",t?1:0);if("string"===e)return u("bytes",new Buffer(t,"utf8"));if(function(e){return e.lastIndexOf("]")===e.length-1}(e)){if(void 0===t.length)throw new Error("Not an array?");if("dynamic"!==(r=c(e))&&0!==r&&t.length>r)throw new Error("Elements exceed array size: "+r);for(d in h=[],e=e.slice(0,e.lastIndexOf("[")),"string"==typeof t&&(t=JSON.parse(t)),t)h.push(u(e,t[d]));if("dynamic"===r){var p=u("uint256",t.length);h.unshift(p)}return Buffer.concat(h)}if("bytes"===e)return t=new Buffer(t),h=Buffer.concat([u("uint256",t.length),t]),t.length%32!=0&&(h=Buffer.concat([h,n.zeros(32-t.length%32)])),h;if(e.startsWith("bytes")){if((r=o(e))<1||r>32)throw new Error("Invalid bytes<N> width: "+r);return n.setLengthRight(t,32)}if(e.startsWith("uint")){if((r=o(e))%8||r<8||r>256)throw new Error("Invalid uint<N> width: "+r);if((i=l(t)).bitLength()>r)throw new Error("Supplied uint exceeds width: "+r+" vs "+i.bitLength());if(i<0)throw new Error("Supplied uint is negative");return i.toArrayLike(Buffer,"be",32)}if(e.startsWith("int")){if((r=o(e))%8||r<8||r>256)throw new Error("Invalid int<N> width: "+r);if((i=l(t)).bitLength()>r)throw new Error("Supplied int exceeds width: "+r+" vs "+i.bitLength());return i.toTwos(256).toArrayLike(Buffer,"be",32)}if(e.startsWith("ufixed")){if(r=a(e),(i=l(t))<0)throw new Error("Supplied ufixed is negative");return u("uint256",i.mul(new s(2).pow(new s(r[1]))))}if(e.startsWith("fixed"))return r=a(e),u("int256",l(t).mul(new s(2).pow(new s(r[1]))));throw new Error("Unsupported or invalid type: "+e)}function h(e){return"string"===e||"bytes"===e||"dynamic"===c(e)}function d(e,t){if(e.length!==t.length)throw new Error("Number of types are not matching the values");for(var r,s,a=[],c=0;c<e.length;c++){var u=i(e[c]),h=t[c];if("bytes"===u)a.push(h);else if("string"===u)a.push(new Buffer(h,"utf8"));else if("bool"===u)a.push(new Buffer(h?"01":"00","hex"));else if("address"===u)a.push(n.setLength(h,20));else if(u.startsWith("bytes")){if((r=o(u))<1||r>32)throw new Error("Invalid bytes<N> width: "+r);a.push(n.setLengthRight(h,r))}else if(u.startsWith("uint")){if((r=o(u))%8||r<8||r>256)throw new Error("Invalid uint<N> width: "+r);if((s=l(h)).bitLength()>r)throw new Error("Supplied uint exceeds width: "+r+" vs "+s.bitLength());a.push(s.toArrayLike(Buffer,"be",r/8))}else{if(!u.startsWith("int"))throw new Error("Unsupported or invalid type: "+u);if((r=o(u))%8||r<8||r>256)throw new Error("Invalid int<N> width: "+r);if((s=l(h)).bitLength()>r)throw new Error("Supplied int exceeds width: "+r+" vs "+s.bitLength());a.push(s.toTwos(r).toArrayLike(Buffer,"be",r/8))}}return Buffer.concat(a)}e.exports={rawEncode:function(e,t){var r=[],n=[],s=32*e.length;for(var o in e){var a=i(e[o]),c=u(a,t[o]);h(a)?(r.push(u("uint256",s)),n.push(c),s+=c.length):r.push(c)}return Buffer.concat(r.concat(n))},solidityPack:d,soliditySHA3:function(e,t){return n.keccak(d(e,t))}}},58262:(e,t,r)=>{const n=r(86248),s=r(35136),i={type:"object",properties:{types:{type:"object",additionalProperties:{type:"array",items:{type:"object",properties:{name:{type:"string"},type:{type:"string"}},required:["name","type"]}}},primaryType:{type:"string"},domain:{type:"object"},message:{type:"object"}},required:["types","primaryType","domain","message"]},o={encodeData(e,t,r,i=!0){const o=["bytes32"],a=[this.hashType(e,r)];if(i){const c=(e,t,o)=>{if(void 0!==r[t])return["bytes32",null==o?"0x0000000000000000000000000000000000000000000000000000000000000000":n.keccak(this.encodeData(t,o,r,i))];if(void 0===o)throw new Error(`missing value for field ${e} of type ${t}`);if("bytes"===t)return["bytes32",n.keccak(o)];if("string"===t)return"string"==typeof o&&(o=Buffer.from(o,"utf8")),["bytes32",n.keccak(o)];if(t.lastIndexOf("]")===t.length-1){const r=t.slice(0,t.lastIndexOf("[")),i=o.map((t=>c(e,r,t)));return["bytes32",n.keccak(s.rawEncode(i.map((([e])=>e)),i.map((([,e])=>e))))]}return[t,o]};for(const n of r[e]){const[e,r]=c(n.name,n.type,t[n.name]);o.push(e),a.push(r)}}else for(const s of r[e]){let e=t[s.name];if(void 0!==e)if("bytes"===s.type)o.push("bytes32"),e=n.keccak(e),a.push(e);else if("string"===s.type)o.push("bytes32"),"string"==typeof e&&(e=Buffer.from(e,"utf8")),e=n.keccak(e),a.push(e);else if(void 0!==r[s.type])o.push("bytes32"),e=n.keccak(this.encodeData(s.type,e,r,i)),a.push(e);else{if(s.type.lastIndexOf("]")===s.type.length-1)throw new Error("Arrays currently unimplemented in encodeData");o.push(s.type),a.push(e)}}return s.rawEncode(o,a)},encodeType(e,t){let r="",n=this.findTypeDependencies(e,t).filter((t=>t!==e));n=[e].concat(n.sort());for(const e of n){if(!t[e])throw new Error("No type definition specified: "+e);r+=e+"("+t[e].map((({name:e,type:t})=>t+" "+e)).join(",")+")"}return r},findTypeDependencies(e,t,r=[]){if(e=e.match(/^\w*/)[0],r.includes(e)||void 0===t[e])return r;r.push(e);for(const n of t[e])for(const e of this.findTypeDependencies(n.type,t,r))!r.includes(e)&&r.push(e);return r},hashStruct(e,t,r,s=!0){return n.keccak(this.encodeData(e,t,r,s))},hashType(e,t){return n.keccak(this.encodeType(e,t))},sanitizeData(e){const t={};for(const r in i.properties)e[r]&&(t[r]=e[r]);return t.types&&(t.types=Object.assign({EIP712Domain:[]},t.types)),t},hash(e,t=!0){const r=this.sanitizeData(e),s=[Buffer.from("1901","hex")];return s.push(this.hashStruct("EIP712Domain",r.domain,r.types,t)),"EIP712Domain"!==r.primaryType&&s.push(this.hashStruct(r.primaryType,r.message,r.types,t)),n.keccak(Buffer.concat(s))}};e.exports={TYPED_MESSAGE_SCHEMA:i,TypedDataUtils:o,hashForSignTypedDataLegacy:function(e){return function(e){const t=new Error("Expect argument to be non-empty array");if("object"!=typeof e||!e.length)throw t;const r=e.map((function(e){return"bytes"===e.type?n.toBuffer(e.value):e.value})),i=e.map((function(e){return e.type})),o=e.map((function(e){if(!e.name)throw t;return e.type+" "+e.name}));return s.soliditySHA3(["bytes32","bytes32"],[s.soliditySHA3(new Array(e.length).fill("string"),o),s.soliditySHA3(i,r)])}(e.data)},hashForSignTypedData_v3:function(e){return o.hash(e.data,!1)},hashForSignTypedData_v4:function(e){return o.hash(e.data)}}},86248:(e,t,r)=>{const n=r(95508),s=r(39404);function i(e){return Buffer.allocUnsafe(e).fill(0)}function o(e,t,r){const n=i(t);return e=a(e),r?e.length<t?(e.copy(n),n):e.slice(0,t):e.length<t?(e.copy(n,t-e.length),n):e.slice(-t)}function a(e){if(!Buffer.isBuffer(e))if(Array.isArray(e))e=Buffer.from(e);else if("string"==typeof e)e=c(e)?Buffer.from((t=l(e)).length%2?"0"+t:t,"hex"):Buffer.from(e);else if("number"==typeof e)e=intToBuffer(e);else if(null==e)e=Buffer.allocUnsafe(0);else if(s.isBN(e))e=e.toArrayLike(Buffer);else{if(!e.toArray)throw new Error("invalid type");e=Buffer.from(e.toArray())}var t;return e}function c(e){return"string"==typeof e&&e.match(/^0x[0-9A-Fa-f]*$/)}function l(e){return"string"==typeof e&&e.startsWith("0x")?e.slice(2):e}e.exports={zeros:i,setLength:o,setLengthRight:function(e,t){return o(e,t,!0)},isHexString:c,stripHexPrefix:l,toBuffer:a,bufferToHex:function(e){return"0x"+(e=a(e)).toString("hex")},keccak:function(e,t){return e=a(e),t||(t=256),n("keccak"+t).update(e).digest()}}},3236:e=>{function t(e){this.mode=n.MODE_8BIT_BYTE,this.data=e,this.parsedData=[];for(var t=0,r=this.data.length;t<r;t++){var s=[],i=this.data.charCodeAt(t);i>65536?(s[0]=240|(1835008&i)>>>18,s[1]=128|(258048&i)>>>12,s[2]=128|(4032&i)>>>6,s[3]=128|63&i):i>2048?(s[0]=224|(61440&i)>>>12,s[1]=128|(4032&i)>>>6,s[2]=128|63&i):i>128?(s[0]=192|(1984&i)>>>6,s[1]=128|63&i):s[0]=i,this.parsedData.push(s)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function r(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}t.prototype={getLength:function(e){return this.parsedData.length},write:function(e){for(var t=0,r=this.parsedData.length;t<r;t++)e.put(this.parsedData[t],8)}},r.prototype={addData:function(e){var r=new t(e);this.dataList.push(r),this.dataCache=null},isDark:function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(var s=0;s<this.moduleCount;s++)this.modules[n][s]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=r.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},setupPositionProbePattern:function(e,t){for(var r=-1;r<=7;r++)if(!(e+r<=-1||this.moduleCount<=e+r))for(var n=-1;n<=7;n++)t+n<=-1||this.moduleCount<=t+n||(this.modules[e+r][t+n]=0<=r&&r<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=n&&n<=4)},getBestMaskPattern:function(){for(var e=0,t=0,r=0;r<8;r++){this.makeImpl(!0,r);var n=s.getLostPoint(this);(0==r||e>n)&&(e=n,t=r)}return t},createMovieClip:function(e,t,r){var n=e.createEmptyMovieClip(t,r);this.make();for(var s=0;s<this.modules.length;s++)for(var i=1*s,o=0;o<this.modules[s].length;o++){var a=1*o;this.modules[s][o]&&(n.beginFill(0,100),n.moveTo(a,i),n.lineTo(a+1,i),n.lineTo(a+1,i+1),n.lineTo(a,i+1),n.endFill())}return n},setupTimingPattern:function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)},setupPositionAdjustPattern:function(){for(var e=s.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var r=0;r<e.length;r++){var n=e[t],i=e[r];if(null==this.modules[n][i])for(var o=-2;o<=2;o++)for(var a=-2;a<=2;a++)this.modules[n+o][i+a]=-2==o||2==o||-2==a||2==a||0==o&&0==a}},setupTypeNumber:function(e){for(var t=s.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var n=!e&&1==(t>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=n}for(r=0;r<18;r++)n=!e&&1==(t>>r&1),this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=n},setupTypeInfo:function(e,t){for(var r=this.errorCorrectLevel<<3|t,n=s.getBCHTypeInfo(r),i=0;i<15;i++){var o=!e&&1==(n>>i&1);i<6?this.modules[i][8]=o:i<8?this.modules[i+1][8]=o:this.modules[this.moduleCount-15+i][8]=o}for(i=0;i<15;i++)o=!e&&1==(n>>i&1),i<8?this.modules[8][this.moduleCount-i-1]=o:i<9?this.modules[8][15-i-1+1]=o:this.modules[8][15-i-1]=o;this.modules[this.moduleCount-8][8]=!e},mapData:function(e,t){for(var r=-1,n=this.moduleCount-1,i=7,o=0,a=this.moduleCount-1;a>0;a-=2)for(6==a&&a--;;){for(var c=0;c<2;c++)if(null==this.modules[n][a-c]){var l=!1;o<e.length&&(l=1==(e[o]>>>i&1)),s.getMask(t,n,a-c)&&(l=!l),this.modules[n][a-c]=l,-1==--i&&(o++,i=7)}if((n+=r)<0||this.moduleCount<=n){n-=r,r=-r;break}}}},r.PAD0=236,r.PAD1=17,r.createData=function(e,t,n){for(var i=c.getRSBlocks(e,t),o=new l,a=0;a<n.length;a++){var u=n[a];o.put(u.mode,4),o.put(u.getLength(),s.getLengthInBits(u.mode,e)),u.write(o)}var h=0;for(a=0;a<i.length;a++)h+=i[a].dataCount;if(o.getLengthInBits()>8*h)throw new Error("code length overflow. ("+o.getLengthInBits()+">"+8*h+")");for(o.getLengthInBits()+4<=8*h&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(!1);for(;!(o.getLengthInBits()>=8*h||(o.put(r.PAD0,8),o.getLengthInBits()>=8*h));)o.put(r.PAD1,8);return r.createBytes(o,i)},r.createBytes=function(e,t){for(var r=0,n=0,i=0,o=new Array(t.length),c=new Array(t.length),l=0;l<t.length;l++){var u=t[l].dataCount,h=t[l].totalCount-u;n=Math.max(n,u),i=Math.max(i,h),o[l]=new Array(u);for(var d=0;d<o[l].length;d++)o[l][d]=255&e.buffer[d+r];r+=u;var p=s.getErrorCorrectPolynomial(h),f=new a(o[l],p.getLength()-1).mod(p);for(c[l]=new Array(p.getLength()-1),d=0;d<c[l].length;d++){var m=d+f.getLength()-c[l].length;c[l][d]=m>=0?f.get(m):0}}var g=0;for(d=0;d<t.length;d++)g+=t[d].totalCount;var w=new Array(g),y=0;for(d=0;d<n;d++)for(l=0;l<t.length;l++)d<o[l].length&&(w[y++]=o[l][d]);for(d=0;d<i;d++)for(l=0;l<t.length;l++)d<c[l].length&&(w[y++]=c[l][d]);return w};for(var n={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},s={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;s.getBCHDigit(t)-s.getBCHDigit(s.G15)>=0;)t^=s.G15<<s.getBCHDigit(t)-s.getBCHDigit(s.G15);return(e<<10|t)^s.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;s.getBCHDigit(t)-s.getBCHDigit(s.G18)>=0;)t^=s.G18<<s.getBCHDigit(t)-s.getBCHDigit(s.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;0!=e;)t++,e>>>=1;return t},getPatternPosition:function(e){return s.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,r){switch(e){case 0:return(t+r)%2==0;case 1:return t%2==0;case 2:return r%3==0;case 3:return(t+r)%3==0;case 4:return(Math.floor(t/2)+Math.floor(r/3))%2==0;case 5:return t*r%2+t*r%3==0;case 6:return(t*r%2+t*r%3)%2==0;case 7:return(t*r%3+(t+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new a([1],0),r=0;r<e;r++)t=t.multiply(new a([1,i.gexp(r)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case n.MODE_NUMBER:return 10;case n.MODE_ALPHA_NUM:return 9;case n.MODE_8BIT_BYTE:case n.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case n.MODE_NUMBER:return 12;case n.MODE_ALPHA_NUM:return 11;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else{if(!(t<41))throw new Error("type:"+t);switch(e){case n.MODE_NUMBER:return 14;case n.MODE_ALPHA_NUM:return 13;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}}},getLostPoint:function(e){for(var t=e.getModuleCount(),r=0,n=0;n<t;n++)for(var s=0;s<t;s++){for(var i=0,o=e.isDark(n,s),a=-1;a<=1;a++)if(!(n+a<0||t<=n+a))for(var c=-1;c<=1;c++)s+c<0||t<=s+c||0==a&&0==c||o==e.isDark(n+a,s+c)&&i++;i>5&&(r+=3+i-5)}for(n=0;n<t-1;n++)for(s=0;s<t-1;s++){var l=0;e.isDark(n,s)&&l++,e.isDark(n+1,s)&&l++,e.isDark(n,s+1)&&l++,e.isDark(n+1,s+1)&&l++,0!=l&&4!=l||(r+=3)}for(n=0;n<t;n++)for(s=0;s<t-6;s++)e.isDark(n,s)&&!e.isDark(n,s+1)&&e.isDark(n,s+2)&&e.isDark(n,s+3)&&e.isDark(n,s+4)&&!e.isDark(n,s+5)&&e.isDark(n,s+6)&&(r+=40);for(s=0;s<t;s++)for(n=0;n<t-6;n++)e.isDark(n,s)&&!e.isDark(n+1,s)&&e.isDark(n+2,s)&&e.isDark(n+3,s)&&e.isDark(n+4,s)&&!e.isDark(n+5,s)&&e.isDark(n+6,s)&&(r+=40);var u=0;for(s=0;s<t;s++)for(n=0;n<t;n++)e.isDark(n,s)&&u++;return r+Math.abs(100*u/t/t-50)/5*10}},i={glog:function(e){if(e<1)throw new Error("glog("+e+")");return i.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return i.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},o=0;o<8;o++)i.EXP_TABLE[o]=1<<o;for(o=8;o<256;o++)i.EXP_TABLE[o]=i.EXP_TABLE[o-4]^i.EXP_TABLE[o-5]^i.EXP_TABLE[o-6]^i.EXP_TABLE[o-8];for(o=0;o<255;o++)i.LOG_TABLE[i.EXP_TABLE[o]]=o;function a(e,t){if(null==e.length)throw new Error(e.length+"/"+t);for(var r=0;r<e.length&&0==e[r];)r++;this.num=new Array(e.length-r+t);for(var n=0;n<e.length-r;n++)this.num[n]=e[n+r]}function c(e,t){this.totalCount=e,this.dataCount=t}function l(){this.buffer=[],this.length=0}a.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),r=0;r<this.getLength();r++)for(var n=0;n<e.getLength();n++)t[r+n]^=i.gexp(i.glog(this.get(r))+i.glog(e.get(n)));return new a(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=i.glog(this.get(0))-i.glog(e.get(0)),r=new Array(this.getLength()),n=0;n<this.getLength();n++)r[n]=this.get(n);for(n=0;n<e.getLength();n++)r[n]^=i.gexp(i.glog(e.get(n))+t);return new a(r,0).mod(e)}},c.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],c.getRSBlocks=function(e,t){var r=c.getRsBlockTable(e,t);if(null==r)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var n=r.length/3,s=[],i=0;i<n;i++)for(var o=r[3*i+0],a=r[3*i+1],l=r[3*i+2],u=0;u<o;u++)s.push(new c(a,l));return s},c.getRsBlockTable=function(e,t){switch(t){case 1:return c.RS_BLOCK_TABLE[4*(e-1)+0];case 0:return c.RS_BLOCK_TABLE[4*(e-1)+1];case 3:return c.RS_BLOCK_TABLE[4*(e-1)+2];case 2:return c.RS_BLOCK_TABLE[4*(e-1)+3];default:return}},l.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var r=0;r<t;r++)this.putBit(1==(e>>>t-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var u=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function h(e){if(this.options={padding:4,width:256,height:256,typeNumber:4,color:"#000000",background:"#ffffff",ecl:"M",image:{svg:"",width:0,height:0}},"string"==typeof e&&(e={content:e}),e)for(var t in e)this.options[t]=e[t];if("string"!=typeof this.options.content)throw new Error("Expected 'content' as string!");if(0===this.options.content.length)throw new Error("Expected 'content' to be non-empty!");if(!(this.options.padding>=0))throw new Error("Expected 'padding' value to be non-negative!");if(!(this.options.width>0&&this.options.height>0))throw new Error("Expected 'width' or 'height' value to be higher than zero!");var n=this.options.content,s=function(e,t){for(var r=function(e){var t=encodeURI(e).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return t.length+(t.length!=e?3:0)}(e),n=1,s=0,i=0,o=u.length;i<=o;i++){var a=u[i];if(!a)throw new Error("Content too long: expected "+s+" but got "+r);switch(t){case"L":s=a[0];break;case"M":s=a[1];break;case"Q":s=a[2];break;case"H":s=a[3];break;default:throw new Error("Unknwon error correction level: "+t)}if(r<=s)break;n++}if(n>u.length)throw new Error("Content too long");return n}(n,this.options.ecl),i=function(e){switch(e){case"L":return 1;case"M":return 0;case"Q":return 3;case"H":return 2;default:throw new Error("Unknwon error correction level: "+e)}}(this.options.ecl);this.qrcode=new r(s,i),this.qrcode.addData(n),this.qrcode.make()}h.prototype.svg=function(e){var t=this.options||{},r=this.qrcode.modules;void 0===e&&(e={container:t.container||"svg"});for(var n=void 0===t.pretty||!!t.pretty,s=n?"  ":"",i=n?"\r\n":"",o=t.width,a=t.height,c=r.length,l=o/(c+2*t.padding),u=a/(c+2*t.padding),h=void 0!==t.join&&!!t.join,d=void 0!==t.swap&&!!t.swap,p=void 0===t.xmlDeclaration||!!t.xmlDeclaration,f=void 0!==t.predefined&&!!t.predefined,m=f?s+'<defs><path id="qrmodule" d="M0 0 h'+u+" v"+l+' H0 z" style="fill:'+t.color+';shape-rendering:crispEdges;" /></defs>'+i:"",g=s+'<rect x="0" y="0" width="'+o+'" height="'+a+'" style="fill:'+t.background+';shape-rendering:crispEdges;"/>'+i,w="",y="",v=0;v<c;v++)for(var b=0;b<c;b++)if(r[b][v]){var E=b*l+t.padding*l,_=v*u+t.padding*u;if(d){var k=E;E=_,_=k}if(h){var M=l+E,S=u+_;E=Number.isInteger(E)?Number(E):E.toFixed(2),_=Number.isInteger(_)?Number(_):_.toFixed(2),M=Number.isInteger(M)?Number(M):M.toFixed(2),y+="M"+E+","+_+" V"+(S=Number.isInteger(S)?Number(S):S.toFixed(2))+" H"+M+" V"+_+" H"+E+" Z "}else w+=f?s+'<use x="'+E.toString()+'" y="'+_.toString()+'" href="#qrmodule" />'+i:s+'<rect x="'+E.toString()+'" y="'+_.toString()+'" width="'+l+'" height="'+u+'" style="fill:'+t.color+';shape-rendering:crispEdges;"/>'+i}h&&(w=s+'<path x="0" y="0" style="fill:'+t.color+';shape-rendering:crispEdges;" d="'+y+'" />');let C="";if(void 0!==this.options.image&&this.options.image.svg){const e=o*this.options.image.width/100,t=a*this.options.image.height/100;C+=`<svg x="${o/2-e/2}" y="${a/2-t/2}" width="${e}" height="${t}" viewBox="0 0 100 100" preserveAspectRatio="xMinYMin meet">`,C+=this.options.image.svg+i,C+="</svg>"}var I="";switch(e.container){case"svg":p&&(I+='<?xml version="1.0" standalone="yes"?>'+i),I+='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="'+o+'" height="'+a+'">'+i,I+=m+g+w,I+=C,I+="</svg>";break;case"svg-viewbox":p&&(I+='<?xml version="1.0" standalone="yes"?>'+i),I+='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 '+o+" "+a+'">'+i,I+=m+g+w,I+=C,I+="</svg>";break;case"g":I+='<g width="'+o+'" height="'+a+'">'+i,I+=m+g+w,I+=C,I+="</g>";break;default:I+=(m+g+w+C).replace(/^\s+/,"")}return I},e.exports=h},55405:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LIB_VERSION=void 0,t.LIB_VERSION="3.9.3"},17833:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,s=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(s=n))})),t.splice(s,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(40736)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},40736:(e,t,r)=>{e.exports=function(e){function t(e){let r,s,i,o=null;function a(...e){if(!a.enabled)return;const n=a,s=Number(new Date),i=s-(r||s);n.diff=i,n.prev=r,n.curr=s,r=s,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,s)=>{if("%%"===r)return"%";o++;const i=t.formatters[s];if("function"==typeof i){const t=e[o];r=i.call(n,t),e.splice(o,1),o--}return r})),t.formatArgs.call(n,e),(n.log||t.log).apply(n,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(s!==t.namespaces&&(s=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){const n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function s(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names.map(s),...t.skips.map(s).map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").split(/[\s,]+/),s=n.length;for(r=0;r<s;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let r,n;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(6585),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},31242:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.BaseBlockTracker=void 0;const s=n(r(7140)),i=(e,t)=>e+t,o=["sync","latest"];class a extends s.default{constructor(e){super(),this._blockResetDuration=e.blockResetDuration||2e4,this._usePastBlocks=e.usePastBlocks||!1,this._currentBlock=null,this._isRunning=!1,this._onNewListener=this._onNewListener.bind(this),this._onRemoveListener=this._onRemoveListener.bind(this),this._resetCurrentBlock=this._resetCurrentBlock.bind(this),this._setupInternalEvents()}async destroy(){this._cancelBlockResetTimeout(),await this._maybeEnd(),super.removeAllListeners()}isRunning(){return this._isRunning}getCurrentBlock(){return this._currentBlock}async getLatestBlock(){return this._currentBlock?this._currentBlock:await new Promise((e=>this.once("latest",e)))}removeAllListeners(e){return e?super.removeAllListeners(e):super.removeAllListeners(),this._setupInternalEvents(),this._onRemoveListener(),this}_setupInternalEvents(){this.removeListener("newListener",this._onNewListener),this.removeListener("removeListener",this._onRemoveListener),this.on("newListener",this._onNewListener),this.on("removeListener",this._onRemoveListener)}_onNewListener(e){o.includes(e)&&this._maybeStart()}_onRemoveListener(){this._getBlockTrackerEventCount()>0||this._maybeEnd()}async _maybeStart(){this._isRunning||(this._isRunning=!0,this._cancelBlockResetTimeout(),await this._start(),this.emit("_started"))}async _maybeEnd(){this._isRunning&&(this._isRunning=!1,this._setupBlockResetTimeout(),await this._end(),this.emit("_ended"))}_getBlockTrackerEventCount(){return o.map((e=>this.listenerCount(e))).reduce(i)}_shouldUseNewBlock(e){const t=this._currentBlock;if(!t)return!0;const r=c(e),n=c(t);return this._usePastBlocks&&r<n||r>n}_newPotentialLatest(e){this._shouldUseNewBlock(e)&&this._setCurrentBlock(e)}_setCurrentBlock(e){const t=this._currentBlock;this._currentBlock=e,this.emit("latest",e),this.emit("sync",{oldBlock:t,newBlock:e})}_setupBlockResetTimeout(){this._cancelBlockResetTimeout(),this._blockResetTimeout=setTimeout(this._resetCurrentBlock,this._blockResetDuration),this._blockResetTimeout.unref&&this._blockResetTimeout.unref()}_cancelBlockResetTimeout(){this._blockResetTimeout&&clearTimeout(this._blockResetTimeout)}_resetCurrentBlock(){this._currentBlock=null}}function c(e){return Number.parseInt(e,16)}t.BaseBlockTracker=a},72826:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PollingBlockTracker=void 0;const s=n(r(31819)),i=n(r(18227)),o=r(31242),a=r(68959),c=(0,a.createModuleLogger)(a.projectLogger,"polling-block-tracker"),l=(0,s.default)();class u extends o.BaseBlockTracker{constructor(e={}){var t;if(!e.provider)throw new Error("PollingBlockTracker - no provider specified.");super(Object.assign(Object.assign({},e),{blockResetDuration:null!==(t=e.blockResetDuration)&&void 0!==t?t:e.pollingInterval})),this._provider=e.provider,this._pollingInterval=e.pollingInterval||2e4,this._retryTimeout=e.retryTimeout||this._pollingInterval/10,this._keepEventLoopActive=void 0===e.keepEventLoopActive||e.keepEventLoopActive,this._setSkipCacheFlag=e.setSkipCacheFlag||!1}async checkForLatestBlock(){return await this._updateLatestBlock(),await this.getLatestBlock()}async _start(){this._synchronize()}async _end(){}async _synchronize(){for(var e;this._isRunning;)try{await this._updateLatestBlock();const e=h(this._pollingInterval,!this._keepEventLoopActive);this.emit("_waitingForNextIteration"),await e}catch(t){const r=new Error(`PollingBlockTracker - encountered an error while attempting to update latest block:\n${null!==(e=t.stack)&&void 0!==e?e:t}`);try{this.emit("error",r)}catch(e){console.error(r)}const n=h(this._retryTimeout,!this._keepEventLoopActive);this.emit("_waitingForNextIteration"),await n}}async _updateLatestBlock(){const e=await this._fetchLatestBlock();this._newPotentialLatest(e)}async _fetchLatestBlock(){const e={jsonrpc:"2.0",id:l(),method:"eth_blockNumber",params:[]};this._setSkipCacheFlag&&(e.skipCache=!0),c("Making request",e);const t=await(0,i.default)((t=>this._provider.sendAsync(e,t)))();if(c("Got response",t),t.error)throw new Error(`PollingBlockTracker - encountered error fetching block:\n${t.error.message}`);return t.result}}function h(e,t){return new Promise((r=>{const n=setTimeout(r,e);n.unref&&t&&n.unref()}))}t.PollingBlockTracker=u},59473:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.SubscribeBlockTracker=void 0;const s=n(r(31819)),i=r(31242),o=(0,s.default)();class a extends i.BaseBlockTracker{constructor(e={}){if(!e.provider)throw new Error("SubscribeBlockTracker - no provider specified.");super(e),this._provider=e.provider,this._subscriptionId=null}async checkForLatestBlock(){return await this.getLatestBlock()}async _start(){if(void 0===this._subscriptionId||null===this._subscriptionId)try{const e=await this._call("eth_blockNumber");this._subscriptionId=await this._call("eth_subscribe","newHeads"),this._provider.on("data",this._handleSubData.bind(this)),this._newPotentialLatest(e)}catch(e){this.emit("error",e)}}async _end(){if(null!==this._subscriptionId&&void 0!==this._subscriptionId)try{await this._call("eth_unsubscribe",this._subscriptionId),this._subscriptionId=null}catch(e){this.emit("error",e)}}_call(e,...t){return new Promise(((r,n)=>{this._provider.sendAsync({id:o(),method:e,params:t,jsonrpc:"2.0"},((e,t)=>{e?n(e):r(t.result)}))}))}_handleSubData(e,t){var r;"eth_subscription"===t.method&&(null===(r=t.params)||void 0===r?void 0:r.subscription)===this._subscriptionId&&this._newPotentialLatest(t.params.result.number)}}t.SubscribeBlockTracker=a},87454:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(72826),t),s(r(59473),t)},68959:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createModuleLogger=t.projectLogger=void 0;const n=r(44657);Object.defineProperty(t,"createModuleLogger",{enumerable:!0,get:function(){return n.createModuleLogger}}),t.projectLogger=(0,n.createProjectLogger)("eth-block-tracker")},74343:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertExhaustive=t.assertStruct=t.assert=t.AssertionError=void 0;const n=r(2150);function s(e,t){return r=e,Boolean("string"==typeof(null===(s=null===(n=null==r?void 0:r.prototype)||void 0===n?void 0:n.constructor)||void 0===s?void 0:s.name))?new e({message:t}):e({message:t});var r,n,s}class i extends Error{constructor(e){super(e.message),this.code="ERR_ASSERTION"}}t.AssertionError=i,t.assert=function(e,t="Assertion failed.",r=i){if(!e){if(t instanceof Error)throw t;throw s(r,t)}},t.assertStruct=function(e,t,r="Assertion failed",o=i){try{(0,n.assert)(e,t)}catch(e){throw s(o,`${r}: ${function(e){const t=function(e){return"object"==typeof e&&null!==e&&"message"in e}(e)?e.message:String(e);return t.endsWith(".")?t.slice(0,-1):t}(e)}.`)}},t.assertExhaustive=function(e){throw new Error("Invalid branch reached. Should be detected during compilation.")}},59398:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.base64=void 0;const n=r(2150),s=r(74343);t.base64=(e,t={})=>{var r,i;const o=null!==(r=t.paddingRequired)&&void 0!==r&&r,a=null!==(i=t.characterSet)&&void 0!==i?i:"base64";let c,l;return"base64"===a?c=String.raw`[A-Za-z0-9+\/]`:((0,s.assert)("base64url"===a),c=String.raw`[-_A-Za-z0-9]`),l=o?new RegExp(`^(?:${c}{4})*(?:${c}{3}=|${c}{2}==)?$`,"u"):new RegExp(`^(?:${c}{4})*(?:${c}{2,3}|${c}{3}=|${c}{2}==)?$`,"u"),(0,n.pattern)(e,l)}},49074:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createDataView=t.concatBytes=t.valueToBytes=t.stringToBytes=t.numberToBytes=t.signedBigIntToBytes=t.bigIntToBytes=t.hexToBytes=t.bytesToString=t.bytesToNumber=t.bytesToSignedBigInt=t.bytesToBigInt=t.bytesToHex=t.assertIsBytes=t.isBytes=void 0;const n=r(74343),s=r(19376),i=48,o=58,a=87,c=function(){const e=[];return()=>{if(0===e.length)for(let t=0;t<256;t++)e.push(t.toString(16).padStart(2,"0"));return e}}();function l(e){return e instanceof Uint8Array}function u(e){(0,n.assert)(l(e),"Value must be a Uint8Array.")}function h(e){if(u(e),0===e.length)return"0x";const t=c(),r=new Array(e.length);for(let n=0;n<e.length;n++)r[n]=t[e[n]];return(0,s.add0x)(r.join(""))}function d(e){u(e);const t=h(e);return BigInt(t)}function p(e){var t;if("0x"===(null===(t=null==e?void 0:e.toLowerCase)||void 0===t?void 0:t.call(e)))return new Uint8Array;(0,s.assertIsHexString)(e);const r=(0,s.remove0x)(e).toLowerCase(),n=r.length%2==0?r:`0${r}`,c=new Uint8Array(n.length/2);for(let e=0;e<c.length;e++){const t=n.charCodeAt(2*e),r=n.charCodeAt(2*e+1),s=t-(t<o?i:a),l=r-(r<o?i:a);c[e]=16*s+l}return c}function f(e){return(0,n.assert)("bigint"==typeof e,"Value must be a bigint."),(0,n.assert)(e>=BigInt(0),"Value must be a non-negative bigint."),p(e.toString(16))}function m(e){return(0,n.assert)("number"==typeof e,"Value must be a number."),(0,n.assert)(e>=0,"Value must be a non-negative number."),(0,n.assert)(Number.isSafeInteger(e),"Value is not a safe integer. Use `bigIntToBytes` instead."),p(e.toString(16))}function g(e){return(0,n.assert)("string"==typeof e,"Value must be a string."),(new TextEncoder).encode(e)}function w(e){if("bigint"==typeof e)return f(e);if("number"==typeof e)return m(e);if("string"==typeof e)return e.startsWith("0x")?p(e):g(e);if(l(e))return e;throw new TypeError(`Unsupported value type: "${typeof e}".`)}t.isBytes=l,t.assertIsBytes=u,t.bytesToHex=h,t.bytesToBigInt=d,t.bytesToSignedBigInt=function(e){u(e);let t=BigInt(0);for(const r of e)t=(t<<BigInt(8))+BigInt(r);return BigInt.asIntN(8*e.length,t)},t.bytesToNumber=function(e){u(e);const t=d(e);return(0,n.assert)(t<=BigInt(Number.MAX_SAFE_INTEGER),"Number is not a safe integer. Use `bytesToBigInt` instead."),Number(t)},t.bytesToString=function(e){return u(e),(new TextDecoder).decode(e)},t.hexToBytes=p,t.bigIntToBytes=f,t.signedBigIntToBytes=function(e,t){(0,n.assert)("bigint"==typeof e,"Value must be a bigint."),(0,n.assert)("number"==typeof t,"Byte length must be a number."),(0,n.assert)(t>0,"Byte length must be greater than 0."),(0,n.assert)(function(e,t){(0,n.assert)(t>0);const r=e>>BigInt(31);return!((~e&r)+(e&~r)>>BigInt(8*t-1))}(e,t),"Byte length is too small to represent the given value.");let r=e;const s=new Uint8Array(t);for(let e=0;e<s.length;e++)s[e]=Number(BigInt.asUintN(8,r)),r>>=BigInt(8);return s.reverse()},t.numberToBytes=m,t.stringToBytes=g,t.valueToBytes=w,t.concatBytes=function(e){const t=new Array(e.length);let r=0;for(let n=0;n<e.length;n++){const s=w(e[n]);t[n]=s,r+=s.length}const n=new Uint8Array(r);for(let e=0,r=0;e<t.length;e++)n.set(t[e],r),r+=t[e].length;return n},t.createDataView=function(e){if("undefined"!=typeof Buffer&&e instanceof Buffer){const t=e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength);return new DataView(t)}return new DataView(e.buffer,e.byteOffset,e.byteLength)}},11526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ChecksumStruct=void 0;const n=r(2150),s=r(59398);t.ChecksumStruct=(0,n.size)((0,s.base64)((0,n.string)(),{paddingRequired:!0}),44,44)},55707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createHex=t.createBytes=t.createBigInt=t.createNumber=void 0;const n=r(2150),s=r(74343),i=r(49074),o=r(19376),a=(0,n.union)([(0,n.number)(),(0,n.bigint)(),(0,n.string)(),o.StrictHexStruct]),c=(0,n.coerce)((0,n.number)(),a,Number),l=(0,n.coerce)((0,n.bigint)(),a,BigInt),u=((0,n.union)([o.StrictHexStruct,(0,n.instance)(Uint8Array)]),(0,n.coerce)((0,n.instance)(Uint8Array),(0,n.union)([o.StrictHexStruct]),i.hexToBytes)),h=(0,n.coerce)(o.StrictHexStruct,(0,n.instance)(Uint8Array),i.bytesToHex);t.createNumber=function(e){try{const t=(0,n.create)(e,c);return(0,s.assert)(Number.isFinite(t),`Expected a number-like value, got "${e}".`),t}catch(t){if(t instanceof n.StructError)throw new Error(`Expected a number-like value, got "${e}".`);throw t}},t.createBigInt=function(e){try{return(0,n.create)(e,l)}catch(e){if(e instanceof n.StructError)throw new Error(`Expected a number-like value, got "${String(e.value)}".`);throw e}},t.createBytes=function(e){if("string"==typeof e&&"0x"===e.toLowerCase())return new Uint8Array;try{return(0,n.create)(e,u)}catch(e){if(e instanceof n.StructError)throw new Error(`Expected a bytes-like value, got "${String(e.value)}".`);throw e}},t.createHex=function(e){if(e instanceof Uint8Array&&0===e.length||"string"==typeof e&&"0x"===e.toLowerCase())return"0x";try{return(0,n.create)(e,h)}catch(e){if(e instanceof n.StructError)throw new Error(`Expected a bytes-like value, got "${String(e.value)}".`);throw e}}},33736:function(e,t){"use strict";var r,n,s=this&&this.__classPrivateFieldSet||function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},i=this&&this.__classPrivateFieldGet||function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};Object.defineProperty(t,"__esModule",{value:!0}),t.FrozenSet=t.FrozenMap=void 0;class o{constructor(e){r.set(this,void 0),s(this,r,new Map(e),"f"),Object.freeze(this)}get size(){return i(this,r,"f").size}[(r=new WeakMap,Symbol.iterator)](){return i(this,r,"f")[Symbol.iterator]()}entries(){return i(this,r,"f").entries()}forEach(e,t){return i(this,r,"f").forEach(((r,n,s)=>e.call(t,r,n,this)))}get(e){return i(this,r,"f").get(e)}has(e){return i(this,r,"f").has(e)}keys(){return i(this,r,"f").keys()}values(){return i(this,r,"f").values()}toString(){return`FrozenMap(${this.size}) {${this.size>0?` ${[...this.entries()].map((([e,t])=>`${String(e)} => ${String(t)}`)).join(", ")} `:""}}`}}t.FrozenMap=o;class a{constructor(e){n.set(this,void 0),s(this,n,new Set(e),"f"),Object.freeze(this)}get size(){return i(this,n,"f").size}[(n=new WeakMap,Symbol.iterator)](){return i(this,n,"f")[Symbol.iterator]()}entries(){return i(this,n,"f").entries()}forEach(e,t){return i(this,n,"f").forEach(((r,n,s)=>e.call(t,r,n,this)))}has(e){return i(this,n,"f").has(e)}keys(){return i(this,n,"f").keys()}values(){return i(this,n,"f").values()}toString(){return`FrozenSet(${this.size}) {${this.size>0?` ${[...this.values()].map((e=>String(e))).join(", ")} `:""}}`}}t.FrozenSet=a,Object.freeze(o),Object.freeze(o.prototype),Object.freeze(a),Object.freeze(a.prototype)},64716:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},19376:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.remove0x=t.add0x=t.assertIsStrictHexString=t.assertIsHexString=t.isStrictHexString=t.isHexString=t.StrictHexStruct=t.HexStruct=void 0;const n=r(2150),s=r(74343);function i(e){return(0,n.is)(e,t.HexStruct)}function o(e){return(0,n.is)(e,t.StrictHexStruct)}t.HexStruct=(0,n.pattern)((0,n.string)(),/^(?:0x)?[0-9a-f]+$/iu),t.StrictHexStruct=(0,n.pattern)((0,n.string)(),/^0x[0-9a-f]+$/iu),t.isHexString=i,t.isStrictHexString=o,t.assertIsHexString=function(e){(0,s.assert)(i(e),"Value must be a hexadecimal string.")},t.assertIsStrictHexString=function(e){(0,s.assert)(o(e),'Value must be a hexadecimal string, starting with "0x".')},t.add0x=function(e){return e.startsWith("0x")?e:e.startsWith("0X")?`0x${e.substring(2)}`:`0x${e}`},t.remove0x=function(e){return e.startsWith("0x")||e.startsWith("0X")?e.substring(2):e}},44657:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(74343),t),s(r(59398),t),s(r(49074),t),s(r(11526),t),s(r(55707),t),s(r(33736),t),s(r(64716),t),s(r(19376),t),s(r(44195),t),s(r(50540),t),s(r(32608),t),s(r(30389),t),s(r(45452),t),s(r(94810),t),s(r(48640),t),s(r(9513),t),s(r(23108),t)},44195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getJsonRpcIdValidator=t.assertIsJsonRpcError=t.isJsonRpcError=t.assertIsJsonRpcFailure=t.isJsonRpcFailure=t.assertIsJsonRpcSuccess=t.isJsonRpcSuccess=t.assertIsJsonRpcResponse=t.isJsonRpcResponse=t.assertIsPendingJsonRpcResponse=t.isPendingJsonRpcResponse=t.JsonRpcResponseStruct=t.JsonRpcFailureStruct=t.JsonRpcSuccessStruct=t.PendingJsonRpcResponseStruct=t.assertIsJsonRpcRequest=t.isJsonRpcRequest=t.assertIsJsonRpcNotification=t.isJsonRpcNotification=t.JsonRpcNotificationStruct=t.JsonRpcRequestStruct=t.JsonRpcParamsStruct=t.JsonRpcErrorStruct=t.JsonRpcIdStruct=t.JsonRpcVersionStruct=t.jsonrpc2=t.getJsonSize=t.isValidJson=t.JsonStruct=t.UnsafeJsonStruct=void 0;const n=r(2150),s=r(74343);t.UnsafeJsonStruct=(0,n.union)([(0,n.literal)(null),(0,n.boolean)(),(0,n.define)("finite number",(e=>(0,n.is)(e,(0,n.number)())&&Number.isFinite(e))),(0,n.string)(),(0,n.array)((0,n.lazy)((()=>t.UnsafeJsonStruct))),(0,n.record)((0,n.string)(),(0,n.lazy)((()=>t.UnsafeJsonStruct)))]),t.JsonStruct=(0,n.define)("Json",((e,r)=>{function n(e,t){const n=[...t.validator(e,r)];return!(n.length>0)||n}try{const r=n(e,t.UnsafeJsonStruct);return!0!==r?r:n(JSON.parse(JSON.stringify(e)),t.UnsafeJsonStruct)}catch(e){return e instanceof RangeError&&"Circular reference detected"}})),t.isValidJson=function(e){return(0,n.is)(e,t.JsonStruct)},t.getJsonSize=function(e){(0,s.assertStruct)(e,t.JsonStruct,"Invalid JSON value");const r=JSON.stringify(e);return(new TextEncoder).encode(r).byteLength},t.jsonrpc2="2.0",t.JsonRpcVersionStruct=(0,n.literal)(t.jsonrpc2),t.JsonRpcIdStruct=(0,n.nullable)((0,n.union)([(0,n.number)(),(0,n.string)()])),t.JsonRpcErrorStruct=(0,n.object)({code:(0,n.integer)(),message:(0,n.string)(),data:(0,n.optional)(t.JsonStruct),stack:(0,n.optional)((0,n.string)())}),t.JsonRpcParamsStruct=(0,n.optional)((0,n.union)([(0,n.record)((0,n.string)(),t.JsonStruct),(0,n.array)(t.JsonStruct)])),t.JsonRpcRequestStruct=(0,n.object)({id:t.JsonRpcIdStruct,jsonrpc:t.JsonRpcVersionStruct,method:(0,n.string)(),params:t.JsonRpcParamsStruct}),t.JsonRpcNotificationStruct=(0,n.omit)(t.JsonRpcRequestStruct,["id"]),t.isJsonRpcNotification=function(e){return(0,n.is)(e,t.JsonRpcNotificationStruct)},t.assertIsJsonRpcNotification=function(e,r){(0,s.assertStruct)(e,t.JsonRpcNotificationStruct,"Invalid JSON-RPC notification",r)},t.isJsonRpcRequest=function(e){return(0,n.is)(e,t.JsonRpcRequestStruct)},t.assertIsJsonRpcRequest=function(e,r){(0,s.assertStruct)(e,t.JsonRpcRequestStruct,"Invalid JSON-RPC request",r)},t.PendingJsonRpcResponseStruct=(0,n.object)({id:t.JsonRpcIdStruct,jsonrpc:t.JsonRpcVersionStruct,result:(0,n.optional)((0,n.unknown)()),error:(0,n.optional)(t.JsonRpcErrorStruct)}),t.JsonRpcSuccessStruct=(0,n.object)({id:t.JsonRpcIdStruct,jsonrpc:t.JsonRpcVersionStruct,result:t.JsonStruct}),t.JsonRpcFailureStruct=(0,n.object)({id:t.JsonRpcIdStruct,jsonrpc:t.JsonRpcVersionStruct,error:t.JsonRpcErrorStruct}),t.JsonRpcResponseStruct=(0,n.union)([t.JsonRpcSuccessStruct,t.JsonRpcFailureStruct]),t.isPendingJsonRpcResponse=function(e){return(0,n.is)(e,t.PendingJsonRpcResponseStruct)},t.assertIsPendingJsonRpcResponse=function(e,r){(0,s.assertStruct)(e,t.PendingJsonRpcResponseStruct,"Invalid pending JSON-RPC response",r)},t.isJsonRpcResponse=function(e){return(0,n.is)(e,t.JsonRpcResponseStruct)},t.assertIsJsonRpcResponse=function(e,r){(0,s.assertStruct)(e,t.JsonRpcResponseStruct,"Invalid JSON-RPC response",r)},t.isJsonRpcSuccess=function(e){return(0,n.is)(e,t.JsonRpcSuccessStruct)},t.assertIsJsonRpcSuccess=function(e,r){(0,s.assertStruct)(e,t.JsonRpcSuccessStruct,"Invalid JSON-RPC success response",r)},t.isJsonRpcFailure=function(e){return(0,n.is)(e,t.JsonRpcFailureStruct)},t.assertIsJsonRpcFailure=function(e,r){(0,s.assertStruct)(e,t.JsonRpcFailureStruct,"Invalid JSON-RPC failure response",r)},t.isJsonRpcError=function(e){return(0,n.is)(e,t.JsonRpcErrorStruct)},t.assertIsJsonRpcError=function(e,r){(0,s.assertStruct)(e,t.JsonRpcErrorStruct,"Invalid JSON-RPC error",r)},t.getJsonRpcIdValidator=function(e){const{permitEmptyString:t,permitFractions:r,permitNull:n}=Object.assign({permitEmptyString:!0,permitFractions:!1,permitNull:!0},e);return e=>Boolean("number"==typeof e&&(r||Number.isInteger(e))||"string"==typeof e&&(t||e.length>0)||n&&null===e)}},50540:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},32608:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.createModuleLogger=t.createProjectLogger=void 0;const s=(0,n(r(17833)).default)("metamask");t.createProjectLogger=function(e){return s.extend(e)},t.createModuleLogger=function(e,t){return e.extend(t)}},30389:(e,t)=>{"use strict";function r(e){return e.charCodeAt(0)<=127}var n;Object.defineProperty(t,"__esModule",{value:!0}),t.calculateNumberSize=t.calculateStringSize=t.isASCII=t.isPlainObject=t.ESCAPE_CHARACTERS_REGEXP=t.JsonSize=t.hasProperty=t.isObject=t.isNullOrUndefined=t.isNonEmptyArray=void 0,t.isNonEmptyArray=function(e){return Array.isArray(e)&&e.length>0},t.isNullOrUndefined=function(e){return null==e},t.isObject=function(e){return Boolean(e)&&"object"==typeof e&&!Array.isArray(e)},t.hasProperty=(e,t)=>Object.hasOwnProperty.call(e,t),(n=t.JsonSize||(t.JsonSize={}))[n.Null=4]="Null",n[n.Comma=1]="Comma",n[n.Wrapper=1]="Wrapper",n[n.True=4]="True",n[n.False=5]="False",n[n.Quote=1]="Quote",n[n.Colon=1]="Colon",n[n.Date=24]="Date",t.ESCAPE_CHARACTERS_REGEXP=/"|\\|\n|\r|\t/gu,t.isPlainObject=function(e){if("object"!=typeof e||null===e)return!1;try{let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}catch(e){return!1}},t.isASCII=r,t.calculateStringSize=function(e){var n;return e.split("").reduce(((e,t)=>r(t)?e+1:e+2),0)+(null!==(n=e.match(t.ESCAPE_CHARACTERS_REGEXP))&&void 0!==n?n:[]).length},t.calculateNumberSize=function(e){return e.toString().length}},45452:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hexToBigInt=t.hexToNumber=t.bigIntToHex=t.numberToHex=void 0;const n=r(74343),s=r(19376);t.numberToHex=e=>((0,n.assert)("number"==typeof e,"Value must be a number."),(0,n.assert)(e>=0,"Value must be a non-negative number."),(0,n.assert)(Number.isSafeInteger(e),"Value is not a safe integer. Use `bigIntToHex` instead."),(0,s.add0x)(e.toString(16))),t.bigIntToHex=e=>((0,n.assert)("bigint"==typeof e,"Value must be a bigint."),(0,n.assert)(e>=0,"Value must be a non-negative bigint."),(0,s.add0x)(e.toString(16))),t.hexToNumber=e=>{(0,s.assertIsHexString)(e);const t=parseInt(e,16);return(0,n.assert)(Number.isSafeInteger(t),"Value is not a safe integer. Use `hexToBigInt` instead."),t},t.hexToBigInt=e=>((0,s.assertIsHexString)(e),BigInt((0,s.add0x)(e)))},94810:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},48640:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.timeSince=t.inMilliseconds=t.Duration=void 0,(r=t.Duration||(t.Duration={}))[r.Millisecond=1]="Millisecond",r[r.Second=1e3]="Second",r[r.Minute=6e4]="Minute",r[r.Hour=36e5]="Hour",r[r.Day=864e5]="Day",r[r.Week=6048e5]="Week",r[r.Year=31536e6]="Year";const n=(e,t)=>{if(!(e=>Number.isInteger(e)&&e>=0)(e))throw new Error(`"${t}" must be a non-negative integer. Received: "${e}".`)};t.inMilliseconds=function(e,t){return n(e,"count"),e*t},t.timeSince=function(e){return n(e,"timestamp"),Date.now()-e}},9513:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},23108:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.satisfiesVersionRange=t.gtRange=t.gtVersion=t.assertIsSemVerRange=t.assertIsSemVerVersion=t.isValidSemVerRange=t.isValidSemVerVersion=t.VersionRangeStruct=t.VersionStruct=void 0;const n=r(93493),s=r(2150),i=r(74343);t.VersionStruct=(0,s.refine)((0,s.string)(),"Version",(e=>null!==(0,n.valid)(e)||`Expected SemVer version, got "${e}"`)),t.VersionRangeStruct=(0,s.refine)((0,s.string)(),"Version range",(e=>null!==(0,n.validRange)(e)||`Expected SemVer range, got "${e}"`)),t.isValidSemVerVersion=function(e){return(0,s.is)(e,t.VersionStruct)},t.isValidSemVerRange=function(e){return(0,s.is)(e,t.VersionRangeStruct)},t.assertIsSemVerVersion=function(e){(0,i.assertStruct)(e,t.VersionStruct)},t.assertIsSemVerRange=function(e){(0,i.assertStruct)(e,t.VersionRangeStruct)},t.gtVersion=function(e,t){return(0,n.gt)(e,t)},t.gtRange=function(e,t){return(0,n.gtr)(e,t)},t.satisfiesVersionRange=function(e,t){return(0,n.satisfies)(e,t,{includePrerelease:!0})}},18227:e=>{"use strict";const t=(e,t)=>function(){const r=t.promiseModule,n=new Array(arguments.length);for(let e=0;e<arguments.length;e++)n[e]=arguments[e];return new r(((r,s)=>{t.errorFirst?n.push((function(e,n){if(t.multiArgs){const t=new Array(arguments.length-1);for(let e=1;e<arguments.length;e++)t[e-1]=arguments[e];e?(t.unshift(e),s(t)):r(t)}else e?s(e):r(n)})):n.push((function(e){if(t.multiArgs){const e=new Array(arguments.length-1);for(let t=0;t<arguments.length;t++)e[t]=arguments[t];r(e)}else r(e)})),e.apply(this,n)}))};e.exports=(e,r)=>{r=Object.assign({exclude:[/.+(Sync|Stream)$/],errorFirst:!0,promiseModule:Promise},r);const n=e=>{const t=t=>"string"==typeof t?e===t:t.test(e);return r.include?r.include.some(t):!r.exclude.some(t)};let s;s="function"==typeof e?function(){return r.excludeMain?e.apply(this,arguments):t(e,r).apply(this,arguments)}:Object.create(Object.getPrototypeOf(e));for(const i in e){const o=e[i];s[i]="function"==typeof o&&n(i)?t(o,r):o}return s}},61088:(e,t,r)=>{const n=Symbol("SemVer ANY");class s{static get ANY(){return n}constructor(e,t){if(t=i(t),e instanceof s){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),l("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,l("comp",this)}parse(e){const t=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],r=e.match(t);if(!r)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new u(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(l("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}return c(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new h(e.value,t).test(this.value):""===e.operator?""===e.value||new h(this.value,t).test(e.semver):!((t=i(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))||(!this.operator.startsWith(">")||!e.operator.startsWith(">"))&&(!this.operator.startsWith("<")||!e.operator.startsWith("<"))&&(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))&&!(c(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))&&!(c(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}}e.exports=s;const i=r(75467),{safeRe:o,t:a}=r(45782),c=r(36991),l=r(28744),u=r(55780),h=r(46135)},46135:(e,t,r)=>{const n=/\s+/g;class s{constructor(e,t){if(t=o(t),e instanceof s)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new s(e.raw,t);if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(n," "),this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length)),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter((e=>!w(e[0]))),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&y(e[0])){this.set=[e];break}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");const t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&m)|(this.options.loose&&g))+":"+e,r=i.get(t);if(r)return r;const n=this.options.loose,s=n?u[h.HYPHENRANGELOOSE]:u[h.HYPHENRANGE];e=e.replace(s,A(this.options.includePrerelease)),c("hyphen replace",e),e=e.replace(u[h.COMPARATORTRIM],d),c("comparator trim",e),e=e.replace(u[h.TILDETRIM],p),c("tilde trim",e),e=e.replace(u[h.CARETTRIM],f),c("caret trim",e);let o=e.split(" ").map((e=>b(e,this.options))).join(" ").split(/\s+/).map((e=>x(e,this.options)));n&&(o=o.filter((e=>(c("loose invalid filter",e,this.options),!!e.match(u[h.COMPARATORLOOSE]))))),c("range list",o);const l=new Map,y=o.map((e=>new a(e,this.options)));for(const e of y){if(w(e))return[e];l.set(e.value,e)}l.size>1&&l.has("")&&l.delete("");const v=[...l.values()];return i.set(t,v),v}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Range is required");return this.set.some((r=>v(r,t)&&e.set.some((e=>v(e,t)&&r.every((r=>e.every((e=>r.intersects(e,t)))))))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new l(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(N(this.set[t],e,this.options))return!0;return!1}}e.exports=s;const i=new(r(53674)),o=r(75467),a=r(61088),c=r(28744),l=r(55780),{safeRe:u,t:h,comparatorTrimReplace:d,tildeTrimReplace:p,caretTrimReplace:f}=r(45782),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:g}=r(71053),w=e=>"<0.0.0-0"===e.value,y=e=>""===e.value,v=(e,t)=>{let r=!0;const n=e.slice();let s=n.pop();for(;r&&n.length;)r=n.every((e=>s.intersects(e,t))),s=n.pop();return r},b=(e,t)=>(c("comp",e,t),e=M(e,t),c("caret",e),e=_(e,t),c("tildes",e),e=C(e,t),c("xrange",e),e=R(e,t),c("stars",e),e),E=e=>!e||"x"===e.toLowerCase()||"*"===e,_=(e,t)=>e.trim().split(/\s+/).map((e=>k(e,t))).join(" "),k=(e,t)=>{const r=t.loose?u[h.TILDELOOSE]:u[h.TILDE];return e.replace(r,((t,r,n,s,i)=>{let o;return c("tilde",e,t,r,n,s,i),E(r)?o="":E(n)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:E(s)?o=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:i?(c("replaceTilde pr",i),o=`>=${r}.${n}.${s}-${i} <${r}.${+n+1}.0-0`):o=`>=${r}.${n}.${s} <${r}.${+n+1}.0-0`,c("tilde return",o),o}))},M=(e,t)=>e.trim().split(/\s+/).map((e=>S(e,t))).join(" "),S=(e,t)=>{c("caret",e,t);const r=t.loose?u[h.CARETLOOSE]:u[h.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,((t,r,s,i,o)=>{let a;return c("caret",e,t,r,s,i,o),E(r)?a="":E(s)?a=`>=${r}.0.0${n} <${+r+1}.0.0-0`:E(i)?a="0"===r?`>=${r}.${s}.0${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${n} <${+r+1}.0.0-0`:o?(c("replaceCaret pr",o),a="0"===r?"0"===s?`>=${r}.${s}.${i}-${o} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}-${o} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i}-${o} <${+r+1}.0.0-0`):(c("no pr"),a="0"===r?"0"===s?`>=${r}.${s}.${i}${n} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i} <${+r+1}.0.0-0`),c("caret return",a),a}))},C=(e,t)=>(c("replaceXRanges",e,t),e.split(/\s+/).map((e=>I(e,t))).join(" ")),I=(e,t)=>{e=e.trim();const r=t.loose?u[h.XRANGELOOSE]:u[h.XRANGE];return e.replace(r,((r,n,s,i,o,a)=>{c("xRange",e,r,n,s,i,o,a);const l=E(s),u=l||E(i),h=u||E(o),d=h;return"="===n&&d&&(n=""),a=t.includePrerelease?"-0":"",l?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&d?(u&&(i=0),o=0,">"===n?(n=">=",u?(s=+s+1,i=0,o=0):(i=+i+1,o=0)):"<="===n&&(n="<",u?s=+s+1:i=+i+1),"<"===n&&(a="-0"),r=`${n+s}.${i}.${o}${a}`):u?r=`>=${s}.0.0${a} <${+s+1}.0.0-0`:h&&(r=`>=${s}.${i}.0${a} <${s}.${+i+1}.0-0`),c("xRange return",r),r}))},R=(e,t)=>(c("replaceStars",e,t),e.trim().replace(u[h.STAR],"")),x=(e,t)=>(c("replaceGTE0",e,t),e.trim().replace(u[t.includePrerelease?h.GTE0PRE:h.GTE0],"")),A=e=>(t,r,n,s,i,o,a,c,l,u,h,d)=>`${r=E(n)?"":E(s)?`>=${n}.0.0${e?"-0":""}`:E(i)?`>=${n}.${s}.0${e?"-0":""}`:o?`>=${r}`:`>=${r}${e?"-0":""}`} ${c=E(l)?"":E(u)?`<${+l+1}.0.0-0`:E(h)?`<${l}.${+u+1}.0-0`:d?`<=${l}.${u}.${h}-${d}`:e?`<${l}.${u}.${+h+1}-0`:`<=${c}`}`.trim(),N=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(c(e[r].semver),e[r].semver!==a.ANY&&e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},55780:(e,t,r)=>{const n=r(28744),{MAX_LENGTH:s,MAX_SAFE_INTEGER:i}=r(71053),{safeRe:o,t:a}=r(45782),c=r(75467),{compareIdentifiers:l}=r(62035);class u{constructor(e,t){if(t=c(t),e instanceof u){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw new TypeError(`version is longer than ${s} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?o[a.LOOSE]:o[a.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>i||this.major<0)throw new TypeError("Invalid major version");if(this.minor>i||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>i||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<i)return t}return e})):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof u)){if("string"==typeof e&&e===this.version)return 0;e=new u(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof u||(e=new u(e,this.options)),l(this.major,e.major)||l(this.minor,e.minor)||l(this.patch,e.patch)}comparePre(e){if(e instanceof u||(e=new u(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],s=e.prerelease[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return l(r,s)}while(++t)}compareBuild(e){e instanceof u||(e=new u(e,this.options));let t=0;do{const r=this.build[t],s=e.build[t];if(n("build compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return l(r,s)}while(++t)}inc(e,t,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(r)?1:0;if(!t&&!1===r)throw new Error("invalid increment argument: identifier is empty");if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===l(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=u},75318:(e,t,r)=>{const n=r(20720);e.exports=(e,t)=>{const r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},36991:(e,t,r)=>{const n=r(93441),s=r(33631),i=r(25068),o=r(78937),a=r(62531),c=r(78624);e.exports=(e,t,r,l)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,l);case"!=":return s(e,r,l);case">":return i(e,r,l);case">=":return o(e,r,l);case"<":return a(e,r,l);case"<=":return c(e,r,l);default:throw new TypeError(`Invalid operator: ${t}`)}}},13258:(e,t,r)=>{const n=r(55780),s=r(20720),{safeRe:i,t:o}=r(45782);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){const n=t.includePrerelease?i[o.COERCERTLFULL]:i[o.COERCERTL];let s;for(;(s=n.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&s.index+s[0].length===r.index+r[0].length||(r=s),n.lastIndex=s.index+s[1].length+s[2].length;n.lastIndex=-1}else r=e.match(t.includePrerelease?i[o.COERCEFULL]:i[o.COERCE]);if(null===r)return null;const a=r[2],c=r[3]||"0",l=r[4]||"0",u=t.includePrerelease&&r[5]?`-${r[5]}`:"",h=t.includePrerelease&&r[6]?`+${r[6]}`:"";return s(`${a}.${c}.${l}${u}${h}`,t)}},5885:(e,t,r)=>{const n=r(55780);e.exports=(e,t,r)=>{const s=new n(e,r),i=new n(t,r);return s.compare(i)||s.compareBuild(i)}},93827:(e,t,r)=>{const n=r(55696);e.exports=(e,t)=>n(e,t,!0)},55696:(e,t,r)=>{const n=r(55780);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},680:(e,t,r)=>{const n=r(20720);e.exports=(e,t)=>{const r=n(e,null,!0),s=n(t,null,!0),i=r.compare(s);if(0===i)return null;const o=i>0,a=o?r:s,c=o?s:r,l=!!a.prerelease.length;if(c.prerelease.length&&!l)return c.patch||c.minor?a.patch?"patch":a.minor?"minor":"major":"major";const u=l?"pre":"";return r.major!==s.major?u+"major":r.minor!==s.minor?u+"minor":r.patch!==s.patch?u+"patch":"prerelease"}},93441:(e,t,r)=>{const n=r(55696);e.exports=(e,t,r)=>0===n(e,t,r)},25068:(e,t,r)=>{const n=r(55696);e.exports=(e,t,r)=>n(e,t,r)>0},78937:(e,t,r)=>{const n=r(55696);e.exports=(e,t,r)=>n(e,t,r)>=0},84703:(e,t,r)=>{const n=r(55780);e.exports=(e,t,r,s,i)=>{"string"==typeof r&&(i=s,s=r,r=void 0);try{return new n(e instanceof n?e.version:e,r).inc(t,s,i).version}catch(e){return null}}},62531:(e,t,r)=>{const n=r(55696);e.exports=(e,t,r)=>n(e,t,r)<0},78624:(e,t,r)=>{const n=r(55696);e.exports=(e,t,r)=>n(e,t,r)<=0},86746:(e,t,r)=>{const n=r(55780);e.exports=(e,t)=>new n(e,t).major},71678:(e,t,r)=>{const n=r(55780);e.exports=(e,t)=>new n(e,t).minor},33631:(e,t,r)=>{const n=r(55696);e.exports=(e,t,r)=>0!==n(e,t,r)},20720:(e,t,r)=>{const n=r(55780);e.exports=(e,t,r=!1)=>{if(e instanceof n)return e;try{return new n(e,t)}catch(e){if(!r)return null;throw e}}},7581:(e,t,r)=>{const n=r(55780);e.exports=(e,t)=>new n(e,t).patch},50241:(e,t,r)=>{const n=r(20720);e.exports=(e,t)=>{const r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},48482:(e,t,r)=>{const n=r(55696);e.exports=(e,t,r)=>n(t,e,r)},14357:(e,t,r)=>{const n=r(5885);e.exports=(e,t)=>e.sort(((e,r)=>n(r,e,t)))},20982:(e,t,r)=>{const n=r(46135);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},74791:(e,t,r)=>{const n=r(5885);e.exports=(e,t)=>e.sort(((e,r)=>n(e,r,t)))},30441:(e,t,r)=>{const n=r(20720);e.exports=(e,t)=>{const r=n(e,t);return r?r.version:null}},93493:(e,t,r)=>{const n=r(45782),s=r(71053),i=r(55780),o=r(62035),a=r(20720),c=r(30441),l=r(75318),u=r(84703),h=r(680),d=r(86746),p=r(71678),f=r(7581),m=r(50241),g=r(55696),w=r(48482),y=r(93827),v=r(5885),b=r(74791),E=r(14357),_=r(25068),k=r(62531),M=r(93441),S=r(33631),C=r(78937),I=r(78624),R=r(36991),x=r(13258),A=r(61088),N=r(46135),L=r(20982),O=r(17839),P=r(46076),T=r(37758),j=r(31005),B=r(17794),F=r(11043),D=r(91683),$=r(5742),U=r(24572),H=r(47805),V=r(87816);e.exports={parse:a,valid:c,clean:l,inc:u,diff:h,major:d,minor:p,patch:f,prerelease:m,compare:g,rcompare:w,compareLoose:y,compareBuild:v,sort:b,rsort:E,gt:_,lt:k,eq:M,neq:S,gte:C,lte:I,cmp:R,coerce:x,Comparator:A,Range:N,satisfies:L,toComparators:O,maxSatisfying:P,minSatisfying:T,minVersion:j,validRange:B,outside:F,gtr:D,ltr:$,intersects:U,simplifyRange:H,subset:V,SemVer:i,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},71053:e=>{const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},28744:e=>{const t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},62035:e=>{const t=/^[0-9]+$/,r=(e,r)=>{const n=t.test(e),s=t.test(r);return n&&s&&(e=+e,r=+r),e===r?0:n&&!s?-1:s&&!n?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},53674:e=>{e.exports=class{constructor(){this.max=1e3,this.map=new Map}get(e){const t=this.map.get(e);return void 0===t?void 0:(this.map.delete(e),this.map.set(e,t),t)}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){const e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}},75467:e=>{const t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},45782:(e,t,r)=>{const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:i}=r(71053),o=r(28744),a=(t=e.exports={}).re=[],c=t.safeRe=[],l=t.src=[],u=t.t={};let h=0;const d="[a-zA-Z0-9-]",p=[["\\s",1],["\\d",i],[d,s]],f=(e,t,r)=>{const n=(e=>{for(const[t,r]of p)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e})(t),s=h++;o(e,s,t),u[e]=s,l[s]=t,a[s]=new RegExp(t,r?"g":void 0),c[s]=new RegExp(n,r?"g":void 0)};f("NUMERICIDENTIFIER","0|[1-9]\\d*"),f("NUMERICIDENTIFIERLOOSE","\\d+"),f("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${d}*`),f("MAINVERSION",`(${l[u.NUMERICIDENTIFIER]})\\.(${l[u.NUMERICIDENTIFIER]})\\.(${l[u.NUMERICIDENTIFIER]})`),f("MAINVERSIONLOOSE",`(${l[u.NUMERICIDENTIFIERLOOSE]})\\.(${l[u.NUMERICIDENTIFIERLOOSE]})\\.(${l[u.NUMERICIDENTIFIERLOOSE]})`),f("PRERELEASEIDENTIFIER",`(?:${l[u.NUMERICIDENTIFIER]}|${l[u.NONNUMERICIDENTIFIER]})`),f("PRERELEASEIDENTIFIERLOOSE",`(?:${l[u.NUMERICIDENTIFIERLOOSE]}|${l[u.NONNUMERICIDENTIFIER]})`),f("PRERELEASE",`(?:-(${l[u.PRERELEASEIDENTIFIER]}(?:\\.${l[u.PRERELEASEIDENTIFIER]})*))`),f("PRERELEASELOOSE",`(?:-?(${l[u.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${l[u.PRERELEASEIDENTIFIERLOOSE]})*))`),f("BUILDIDENTIFIER",`${d}+`),f("BUILD",`(?:\\+(${l[u.BUILDIDENTIFIER]}(?:\\.${l[u.BUILDIDENTIFIER]})*))`),f("FULLPLAIN",`v?${l[u.MAINVERSION]}${l[u.PRERELEASE]}?${l[u.BUILD]}?`),f("FULL",`^${l[u.FULLPLAIN]}$`),f("LOOSEPLAIN",`[v=\\s]*${l[u.MAINVERSIONLOOSE]}${l[u.PRERELEASELOOSE]}?${l[u.BUILD]}?`),f("LOOSE",`^${l[u.LOOSEPLAIN]}$`),f("GTLT","((?:<|>)?=?)"),f("XRANGEIDENTIFIERLOOSE",`${l[u.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),f("XRANGEIDENTIFIER",`${l[u.NUMERICIDENTIFIER]}|x|X|\\*`),f("XRANGEPLAIN",`[v=\\s]*(${l[u.XRANGEIDENTIFIER]})(?:\\.(${l[u.XRANGEIDENTIFIER]})(?:\\.(${l[u.XRANGEIDENTIFIER]})(?:${l[u.PRERELEASE]})?${l[u.BUILD]}?)?)?`),f("XRANGEPLAINLOOSE",`[v=\\s]*(${l[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[u.XRANGEIDENTIFIERLOOSE]})(?:${l[u.PRERELEASELOOSE]})?${l[u.BUILD]}?)?)?`),f("XRANGE",`^${l[u.GTLT]}\\s*${l[u.XRANGEPLAIN]}$`),f("XRANGELOOSE",`^${l[u.GTLT]}\\s*${l[u.XRANGEPLAINLOOSE]}$`),f("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),f("COERCE",`${l[u.COERCEPLAIN]}(?:$|[^\\d])`),f("COERCEFULL",l[u.COERCEPLAIN]+`(?:${l[u.PRERELEASE]})?`+`(?:${l[u.BUILD]})?(?:$|[^\\d])`),f("COERCERTL",l[u.COERCE],!0),f("COERCERTLFULL",l[u.COERCEFULL],!0),f("LONETILDE","(?:~>?)"),f("TILDETRIM",`(\\s*)${l[u.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",f("TILDE",`^${l[u.LONETILDE]}${l[u.XRANGEPLAIN]}$`),f("TILDELOOSE",`^${l[u.LONETILDE]}${l[u.XRANGEPLAINLOOSE]}$`),f("LONECARET","(?:\\^)"),f("CARETTRIM",`(\\s*)${l[u.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",f("CARET",`^${l[u.LONECARET]}${l[u.XRANGEPLAIN]}$`),f("CARETLOOSE",`^${l[u.LONECARET]}${l[u.XRANGEPLAINLOOSE]}$`),f("COMPARATORLOOSE",`^${l[u.GTLT]}\\s*(${l[u.LOOSEPLAIN]})$|^$`),f("COMPARATOR",`^${l[u.GTLT]}\\s*(${l[u.FULLPLAIN]})$|^$`),f("COMPARATORTRIM",`(\\s*)${l[u.GTLT]}\\s*(${l[u.LOOSEPLAIN]}|${l[u.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",f("HYPHENRANGE",`^\\s*(${l[u.XRANGEPLAIN]})\\s+-\\s+(${l[u.XRANGEPLAIN]})\\s*$`),f("HYPHENRANGELOOSE",`^\\s*(${l[u.XRANGEPLAINLOOSE]})\\s+-\\s+(${l[u.XRANGEPLAINLOOSE]})\\s*$`),f("STAR","(<|>)?=?\\s*\\*"),f("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),f("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},91683:(e,t,r)=>{const n=r(11043);e.exports=(e,t,r)=>n(e,t,">",r)},24572:(e,t,r)=>{const n=r(46135);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t,r))},5742:(e,t,r)=>{const n=r(11043);e.exports=(e,t,r)=>n(e,t,"<",r)},46076:(e,t,r)=>{const n=r(55780),s=r(46135);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach((e=>{a.test(e)&&(i&&-1!==o.compare(e)||(i=e,o=new n(i,r)))})),i}},37758:(e,t,r)=>{const n=r(55780),s=r(46135);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach((e=>{a.test(e)&&(i&&1!==o.compare(e)||(i=e,o=new n(i,r)))})),i}},31005:(e,t,r)=>{const n=r(55780),s=r(46135),i=r(25068);e.exports=(e,t)=>{e=new s(e,t);let r=new n("0.0.0");if(e.test(r))return r;if(r=new n("0.0.0-0"),e.test(r))return r;r=null;for(let t=0;t<e.set.length;++t){const s=e.set[t];let o=null;s.forEach((e=>{const t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":o&&!i(t,o)||(o=t);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}})),!o||r&&!i(r,o)||(r=o)}return r&&e.test(r)?r:null}},11043:(e,t,r)=>{const n=r(55780),s=r(61088),{ANY:i}=s,o=r(46135),a=r(20982),c=r(25068),l=r(62531),u=r(78624),h=r(78937);e.exports=(e,t,r,d)=>{let p,f,m,g,w;switch(e=new n(e,d),t=new o(t,d),r){case">":p=c,f=u,m=l,g=">",w=">=";break;case"<":p=l,f=h,m=c,g="<",w="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,d))return!1;for(let r=0;r<t.set.length;++r){const n=t.set[r];let o=null,a=null;if(n.forEach((e=>{e.semver===i&&(e=new s(">=0.0.0")),o=o||e,a=a||e,p(e.semver,o.semver,d)?o=e:m(e.semver,a.semver,d)&&(a=e)})),o.operator===g||o.operator===w)return!1;if((!a.operator||a.operator===g)&&f(e,a.semver))return!1;if(a.operator===w&&m(e,a.semver))return!1}return!0}},47805:(e,t,r)=>{const n=r(20982),s=r(55696);e.exports=(e,t,r)=>{const i=[];let o=null,a=null;const c=e.sort(((e,t)=>s(e,t,r)));for(const e of c)n(e,t,r)?(a=e,o||(o=e)):(a&&i.push([o,a]),a=null,o=null);o&&i.push([o,null]);const l=[];for(const[e,t]of i)e===t?l.push(e):t||e!==c[0]?t?e===c[0]?l.push(`<=${t}`):l.push(`${e} - ${t}`):l.push(`>=${e}`):l.push("*");const u=l.join(" || "),h="string"==typeof t.raw?t.raw:String(t);return u.length<h.length?u:t}},87816:(e,t,r)=>{const n=r(46135),s=r(61088),{ANY:i}=s,o=r(20982),a=r(55696),c=[new s(">=0.0.0-0")],l=[new s(">=0.0.0")],u=(e,t,r)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===i){if(1===t.length&&t[0].semver===i)return!0;e=r.includePrerelease?c:l}if(1===t.length&&t[0].semver===i){if(r.includePrerelease)return!0;t=l}const n=new Set;let s,u,p,f,m,g,w;for(const t of e)">"===t.operator||">="===t.operator?s=h(s,t,r):"<"===t.operator||"<="===t.operator?u=d(u,t,r):n.add(t.semver);if(n.size>1)return null;if(s&&u){if(p=a(s.semver,u.semver,r),p>0)return null;if(0===p&&(">="!==s.operator||"<="!==u.operator))return null}for(const e of n){if(s&&!o(e,String(s),r))return null;if(u&&!o(e,String(u),r))return null;for(const n of t)if(!o(e,String(n),r))return!1;return!0}let y=!(!u||r.includePrerelease||!u.semver.prerelease.length)&&u.semver,v=!(!s||r.includePrerelease||!s.semver.prerelease.length)&&s.semver;y&&1===y.prerelease.length&&"<"===u.operator&&0===y.prerelease[0]&&(y=!1);for(const e of t){if(w=w||">"===e.operator||">="===e.operator,g=g||"<"===e.operator||"<="===e.operator,s)if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if(f=h(s,e,r),f===e&&f!==s)return!1}else if(">="===s.operator&&!o(s.semver,String(e),r))return!1;if(u)if(y&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===y.major&&e.semver.minor===y.minor&&e.semver.patch===y.patch&&(y=!1),"<"===e.operator||"<="===e.operator){if(m=d(u,e,r),m===e&&m!==u)return!1}else if("<="===u.operator&&!o(u.semver,String(e),r))return!1;if(!e.operator&&(u||s)&&0!==p)return!1}return!(s&&g&&!u&&0!==p||u&&w&&!s&&0!==p||v||y)},h=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n>0?e:n<0||">"===t.operator&&">="===e.operator?t:e},d=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n<0?e:n>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let s=!1;e:for(const n of e.set){for(const e of t.set){const t=u(n,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},17839:(e,t,r)=>{const n=r(46135);e.exports=(e,t)=>new n(e,t).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")))},17794:(e,t,r)=>{const n=r(46135);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}},63042:(e,t,r)=>{const n=r(81303);e.exports=class extends n{constructor(){super(),this.allResults=[]}async update(){throw new Error("BaseFilterWithHistory - no update method specified")}addResults(e){this.allResults=this.allResults.concat(e),super.addResults(e)}addInitialResults(e){this.allResults=this.allResults.concat(e),super.addInitialResults(e)}getAllResults(){return this.allResults}}},81303:(e,t,r)=>{const n=r(7140).default;e.exports=class extends n{constructor(){super(),this.updates=[]}async initialize(){}async update(){throw new Error("BaseFilter - no update method specified")}addResults(e){this.updates=this.updates.concat(e),e.forEach((e=>this.emit("update",e)))}addInitialResults(e){}getChangesAndClear(){const e=this.updates;return this.updates=[],e}}},34287:(e,t,r)=>{const n=r(81303),s=r(60571),{incrementHexInt:i}=r(13067);e.exports=class extends n{constructor({provider:e,params:t}){super(),this.type="block",this.provider=e}async update({oldBlock:e,newBlock:t}){const r=t,n=i(e),o=(await s({provider:this.provider,fromBlock:n,toBlock:r})).map((e=>e.hash));this.addResults(o)}}},60571:e=>{function t(e){return null==e?e:Number.parseInt(e,16)}function r(e){return null==e?e:"0x"+e.toString(16)}function n(e,t){return new Promise(((r,n)=>{e.sendAsync(t,((e,t)=>{e?n(e):t.error?n(t.error):t.result?r(t.result):n(new Error("Result was empty"))}))}))}e.exports=async function({provider:e,fromBlock:s,toBlock:i}){s||(s=i);const o=t(s),a=t(i),c=Array(a-o+1).fill().map(((e,t)=>o+t)).map(r);let l=await Promise.all(c.map((t=>async function(e,t,r){for(let t=0;t<3;t++)try{return await n(e,{id:1,jsonrpc:"2.0",method:"eth_getBlockByNumber",params:r})}catch(e){console.error(`provider.sendAsync failed: ${e.stack||e.message||e}`)}return null}(e,0,[t,!1]))));return l=l.filter((e=>null!==e)),l}},13067:e=>{function t(e){return e.sort(((e,t)=>"latest"===e||"earliest"===t?1:"latest"===t||"earliest"===e?-1:r(e)-r(t)))}function r(e){return null==e?e:Number.parseInt(e,16)}function n(e){if(null==e)return e;let t=e.toString(16);return t.length%2&&(t="0"+t),"0x"+t}function s(){return Math.floor(16*Math.random()).toString(16)}e.exports={minBlockRef:function(...e){return t(e)[0]},maxBlockRef:function(...e){const r=t(e);return r[r.length-1]},sortBlockRefs:t,bnToHex:function(e){return"0x"+e.toString(16)},blockRefIsNumber:function(e){return e&&!["earliest","latest","pending"].includes(e)},hexToInt:r,incrementHexInt:function(e){return null==e?e:n(r(e)+1)},intToHex:n,unsafeRandomBytes:function(e){let t="0x";for(let r=0;r<e;r++)t+=s(),t+=s();return t}}},41713:(e,t,r)=>{const n=r(56693).eu,{createAsyncMiddleware:s,createScaffoldMiddleware:i}=r(76583),o=r(48210),a=r(34287),c=r(11560),{intToHex:l,hexToInt:u}=r(13067);function h(e){return d((async(...t)=>{const r=await e(...t);return l(r.id)}))}function d(e){return s((async(t,r)=>{const n=await e.apply(null,t.params);r.result=n}))}function p(e,t){const r=[];for(let t in e)r.push(e[t]);return r}e.exports=function({blockTracker:e,provider:t}){let r=0,s={};const f=new n,m=function({mutex:e}){return t=>async(r,n,s,i)=>{(await e.acquire())(),t(r,n,s,i)}}({mutex:f}),g=i({eth_newFilter:m(h(y)),eth_newBlockFilter:m(h(v)),eth_newPendingTransactionFilter:m(h(b)),eth_uninstallFilter:m(d(k)),eth_getFilterChanges:m(d(E)),eth_getFilterLogs:m(d(_))}),w=async({oldBlock:e,newBlock:t})=>{if(0===s.length)return;const r=await f.acquire();try{await Promise.all(p(s).map((async r=>{try{await r.update({oldBlock:e,newBlock:t})}catch(e){console.error(e)}})))}catch(e){console.error(e)}r()};return g.newLogFilter=y,g.newBlockFilter=v,g.newPendingTransactionFilter=b,g.uninstallFilter=k,g.getFilterChanges=E,g.getFilterLogs=_,g.destroy=()=>{!async function(){const e=p(s).length;s={},S({prevFilterCount:e,newFilterCount:0})}()},g;async function y(e){const r=new o({provider:t,params:e});return await M(r),r}async function v(){const e=new a({provider:t});return await M(e),e}async function b(){const e=new c({provider:t});return await M(e),e}async function E(e){const t=u(e),r=s[t];if(!r)throw new Error(`No filter for index "${t}"`);return r.getChangesAndClear()}async function _(e){const t=u(e),r=s[t];if(!r)throw new Error(`No filter for index "${t}"`);let n=[];return"log"===r.type&&(n=r.getAllResults()),n}async function k(e){const t=u(e),r=s[t],n=Boolean(r);return n&&await async function(e){const t=p(s).length;delete s[e];S({prevFilterCount:t,newFilterCount:p(s).length})}(t),n}async function M(t){const n=p(s).length,i=await e.getLatestBlock();return await t.initialize({currentBlock:i}),r++,s[r]=t,t.id=r,t.idHex=l(r),S({prevFilterCount:n,newFilterCount:p(s).length}),r}function S({prevFilterCount:t,newFilterCount:r}){0===t&&r>0?e.on("sync",w):t>0&&0===r&&e.removeListener("sync",w)}}},48210:(e,t,r)=>{const n=r(46897),s=r(27759),i=r(63042),{bnToHex:o,hexToInt:a,incrementHexInt:c,minBlockRef:l,blockRefIsNumber:u}=r(13067);e.exports=class extends i{constructor({provider:e,params:t}){super(),this.type="log",this.ethQuery=new n(e),this.params=Object.assign({fromBlock:"latest",toBlock:"latest",address:void 0,topics:[]},t),this.params.address&&(Array.isArray(this.params.address)||(this.params.address=[this.params.address]),this.params.address=this.params.address.map((e=>e.toLowerCase())))}async initialize({currentBlock:e}){let t=this.params.fromBlock;["latest","pending"].includes(t)&&(t=e),"earliest"===t&&(t="0x0"),this.params.fromBlock=t;const r=l(this.params.toBlock,e),n=Object.assign({},this.params,{toBlock:r}),s=await this._fetchLogs(n);this.addInitialResults(s)}async update({oldBlock:e,newBlock:t}){const r=t;let n;n=e?c(e):t;const s=Object.assign({},this.params,{fromBlock:n,toBlock:r}),i=(await this._fetchLogs(s)).filter((e=>this.matchLog(e)));this.addResults(i)}async _fetchLogs(e){return await s((t=>this.ethQuery.getLogs(e,t)))()}matchLog(e){if(a(this.params.fromBlock)>=a(e.blockNumber))return!1;if(u(this.params.toBlock)&&a(this.params.toBlock)<=a(e.blockNumber))return!1;const t=e.address&&e.address.toLowerCase();return!(this.params.address&&t&&!this.params.address.includes(t))&&this.params.topics.every(((t,r)=>{let n=e.topics[r];if(!n)return!1;n=n.toLowerCase();let s=Array.isArray(t)?t:[t];return!!s.includes(null)||(s=s.map((e=>e.toLowerCase())),s.includes(n))}))}}},27759:e=>{"use strict";const t=(e,t,r,n)=>function(...s){return new(0,t.promiseModule)(((i,o)=>{t.multiArgs?s.push(((...e)=>{t.errorFirst?e[0]?o(e):(e.shift(),i(e)):i(e)})):t.errorFirst?s.push(((e,t)=>{e?o(e):i(t)})):s.push(i);const a=this===r?n:this;Reflect.apply(e,a,s)}))},r=new WeakMap;e.exports=(e,n)=>{n={exclude:[/.+(?:Sync|Stream)$/],errorFirst:!0,promiseModule:Promise,...n};const s=typeof e;if(null===e||"object"!==s&&"function"!==s)throw new TypeError(`Expected \`input\` to be a \`Function\` or \`Object\`, got \`${null===e?"null":s}\``);const i=new WeakMap,o=new Proxy(e,{apply(e,r,s){const a=i.get(e);if(a)return Reflect.apply(a,r,s);const c=n.excludeMain?e:t(e,n,o,e);return i.set(e,c),Reflect.apply(c,r,s)},get(e,s){const a=e[s];if(!((e,t)=>{let s=r.get(e);if(s||(s={},r.set(e,s)),t in s)return s[t];const i=e=>"string"==typeof e||"symbol"==typeof t?t===e:e.test(t),o=Reflect.getOwnPropertyDescriptor(e,t),a=void 0===o||o.writable||o.configurable,c=(n.include?n.include.some(i):!n.exclude.some(i))&&a;return s[t]=c,c})(e,s)||a===Function.prototype[s])return a;const c=i.get(a);if(c)return c;if("function"==typeof a){const r=t(a,n,o,e);return i.set(a,r),r}return a}});return o}},96401:(e,t,r)=>{const n=r(7140).default,{createAsyncMiddleware:s,createScaffoldMiddleware:i}=r(76583),o=r(41713),{unsafeRandomBytes:a,incrementHexInt:c}=r(13067),l=r(60571);function u(e){return null==e?null:{hash:e.hash,parentHash:e.parentHash,sha3Uncles:e.sha3Uncles,miner:e.miner,stateRoot:e.stateRoot,transactionsRoot:e.transactionsRoot,receiptsRoot:e.receiptsRoot,logsBloom:e.logsBloom,difficulty:e.difficulty,number:e.number,gasLimit:e.gasLimit,gasUsed:e.gasUsed,nonce:e.nonce,mixHash:e.mixHash,timestamp:e.timestamp,extraData:e.extraData}}e.exports=function({blockTracker:e,provider:t}){const r={},h=o({blockTracker:e,provider:t});let d=!1;const p=new n,f=i({eth_subscribe:s((async function(n,s){if(d)throw new Error("SubscriptionManager - attempting to use after destroying");const i=n.params[0],o=a(16);let p;switch(i){case"newHeads":p=function({subId:r}){const n={type:i,destroy:async()=>{e.removeListener("sync",n.update)},update:async({oldBlock:e,newBlock:n})=>{const s=n,i=c(e);(await l({provider:t,fromBlock:i,toBlock:s})).map(u).filter((e=>null!==e)).forEach((e=>{m(r,e)}))}};return e.on("sync",n.update),n}({subId:o});break;case"logs":const r=n.params[1];p=function({subId:e,filter:t}){return t.on("update",(t=>m(e,t))),{type:i,destroy:async()=>await h.uninstallFilter(t.idHex)}}({subId:o,filter:await h.newLogFilter(r)});break;default:throw new Error(`SubscriptionManager - unsupported subscription type "${i}"`)}return r[o]=p,void(s.result=o)})),eth_unsubscribe:s((async function(e,t){if(d)throw new Error("SubscriptionManager - attempting to use after destroying");const n=e.params[0],s=r[n];s?(delete r[n],await s.destroy(),t.result=!0):t.result=!1}))});return f.destroy=function(){p.removeAllListeners();for(const e in r)r[e].destroy(),delete r[e];d=!0},{events:p,middleware:f};function m(e,t){p.emit("notification",{jsonrpc:"2.0",method:"eth_subscription",params:{subscription:e,result:t}})}}},11560:(e,t,r)=>{const n=r(81303),s=r(60571),{incrementHexInt:i}=r(13067);e.exports=class extends n{constructor({provider:e}){super(),this.type="tx",this.provider=e}async update({oldBlock:e}){const t=e,r=i(e),n=await s({provider:this.provider,fromBlock:r,toBlock:t}),o=[];for(const e of n)o.push(...e.transactions);this.addResults(o)}}},46897:(e,t,r)=>{const n=r(57510),s=r(31819)();function i(e){this.currentProvider=e}function o(e){return function(){var t=[].slice.call(arguments),r=t.pop();this.sendAsync({method:e,params:t},r)}}function a(e,t){return function(){var r=[].slice.call(arguments),n=r.pop();r.length<e&&r.push("latest"),this.sendAsync({method:t,params:r},n)}}e.exports=i,i.prototype.getBalance=a(2,"eth_getBalance"),i.prototype.getCode=a(2,"eth_getCode"),i.prototype.getTransactionCount=a(2,"eth_getTransactionCount"),i.prototype.getStorageAt=a(3,"eth_getStorageAt"),i.prototype.call=a(2,"eth_call"),i.prototype.protocolVersion=o("eth_protocolVersion"),i.prototype.syncing=o("eth_syncing"),i.prototype.coinbase=o("eth_coinbase"),i.prototype.mining=o("eth_mining"),i.prototype.hashrate=o("eth_hashrate"),i.prototype.gasPrice=o("eth_gasPrice"),i.prototype.accounts=o("eth_accounts"),i.prototype.blockNumber=o("eth_blockNumber"),i.prototype.getBlockTransactionCountByHash=o("eth_getBlockTransactionCountByHash"),i.prototype.getBlockTransactionCountByNumber=o("eth_getBlockTransactionCountByNumber"),i.prototype.getUncleCountByBlockHash=o("eth_getUncleCountByBlockHash"),i.prototype.getUncleCountByBlockNumber=o("eth_getUncleCountByBlockNumber"),i.prototype.sign=o("eth_sign"),i.prototype.sendTransaction=o("eth_sendTransaction"),i.prototype.sendRawTransaction=o("eth_sendRawTransaction"),i.prototype.estimateGas=o("eth_estimateGas"),i.prototype.getBlockByHash=o("eth_getBlockByHash"),i.prototype.getBlockByNumber=o("eth_getBlockByNumber"),i.prototype.getTransactionByHash=o("eth_getTransactionByHash"),i.prototype.getTransactionByBlockHashAndIndex=o("eth_getTransactionByBlockHashAndIndex"),i.prototype.getTransactionByBlockNumberAndIndex=o("eth_getTransactionByBlockNumberAndIndex"),i.prototype.getTransactionReceipt=o("eth_getTransactionReceipt"),i.prototype.getUncleByBlockHashAndIndex=o("eth_getUncleByBlockHashAndIndex"),i.prototype.getUncleByBlockNumberAndIndex=o("eth_getUncleByBlockNumberAndIndex"),i.prototype.getCompilers=o("eth_getCompilers"),i.prototype.compileLLL=o("eth_compileLLL"),i.prototype.compileSolidity=o("eth_compileSolidity"),i.prototype.compileSerpent=o("eth_compileSerpent"),i.prototype.newFilter=o("eth_newFilter"),i.prototype.newBlockFilter=o("eth_newBlockFilter"),i.prototype.newPendingTransactionFilter=o("eth_newPendingTransactionFilter"),i.prototype.uninstallFilter=o("eth_uninstallFilter"),i.prototype.getFilterChanges=o("eth_getFilterChanges"),i.prototype.getFilterLogs=o("eth_getFilterLogs"),i.prototype.getLogs=o("eth_getLogs"),i.prototype.getWork=o("eth_getWork"),i.prototype.submitWork=o("eth_submitWork"),i.prototype.submitHashrate=o("eth_submitHashrate"),i.prototype.sendAsync=function(e,t){var r;this.currentProvider.sendAsync((r=e,n({id:s(),jsonrpc:"2.0",params:[]},r)),(function(e,r){if(!e&&r.error&&(e=new Error("EthQuery - RPC Error - "+r.error.message)),e)return t(e);t(null,r.result)}))}},32083:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EthereumProviderError=t.EthereumRpcError=void 0;const n=r(78463);class s extends Error{constructor(e,t,r){if(!Number.isInteger(e))throw new Error('"code" must be an integer.');if(!t||"string"!=typeof t)throw new Error('"message" must be a nonempty string.');super(t),this.code=e,void 0!==r&&(this.data=r)}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),this.stack&&(e.stack=this.stack),e}toString(){return n.default(this.serialize(),i,2)}}function i(e,t){if("[Circular]"!==t)return t}t.EthereumRpcError=s,t.EthereumProviderError=class extends s{constructor(e,t,r){if(!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(e,t,r)}}},23625:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.errorValues=t.errorCodes=void 0,t.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},t.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}}},87396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ethErrors=void 0;const n=r(32083),s=r(18326),i=r(23625);function o(e,t){const[r,i]=c(t);return new n.EthereumRpcError(e,r||s.getMessageFromCode(e),i)}function a(e,t){const[r,i]=c(t);return new n.EthereumProviderError(e,r||s.getMessageFromCode(e),i)}function c(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t||void 0,r]}}return[]}t.ethErrors={rpc:{parse:e=>o(i.errorCodes.rpc.parse,e),invalidRequest:e=>o(i.errorCodes.rpc.invalidRequest,e),invalidParams:e=>o(i.errorCodes.rpc.invalidParams,e),methodNotFound:e=>o(i.errorCodes.rpc.methodNotFound,e),internal:e=>o(i.errorCodes.rpc.internal,e),server:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return o(t,e)},invalidInput:e=>o(i.errorCodes.rpc.invalidInput,e),resourceNotFound:e=>o(i.errorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>o(i.errorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>o(i.errorCodes.rpc.transactionRejected,e),methodNotSupported:e=>o(i.errorCodes.rpc.methodNotSupported,e),limitExceeded:e=>o(i.errorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>a(i.errorCodes.provider.userRejectedRequest,e),unauthorized:e=>a(i.errorCodes.provider.unauthorized,e),unsupportedMethod:e=>a(i.errorCodes.provider.unsupportedMethod,e),disconnected:e=>a(i.errorCodes.provider.disconnected,e),chainDisconnected:e=>a(i.errorCodes.provider.chainDisconnected,e),custom:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:t,message:r,data:s}=e;if(!r||"string"!=typeof r)throw new Error('"message" must be a nonempty string');return new n.EthereumProviderError(t,r,s)}}}},12203:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getMessageFromCode=t.serializeError=t.EthereumProviderError=t.EthereumRpcError=t.ethErrors=t.errorCodes=void 0;const n=r(32083);Object.defineProperty(t,"EthereumRpcError",{enumerable:!0,get:function(){return n.EthereumRpcError}}),Object.defineProperty(t,"EthereumProviderError",{enumerable:!0,get:function(){return n.EthereumProviderError}});const s=r(18326);Object.defineProperty(t,"serializeError",{enumerable:!0,get:function(){return s.serializeError}}),Object.defineProperty(t,"getMessageFromCode",{enumerable:!0,get:function(){return s.getMessageFromCode}});const i=r(87396);Object.defineProperty(t,"ethErrors",{enumerable:!0,get:function(){return i.ethErrors}});const o=r(23625);Object.defineProperty(t,"errorCodes",{enumerable:!0,get:function(){return o.errorCodes}})},18326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.serializeError=t.isValidCode=t.getMessageFromCode=t.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const n=r(23625),s=r(32083),i=n.errorCodes.rpc.internal,o={code:i,message:a(i)};function a(e,r="Unspecified error message. This is a bug, please report it."){if(Number.isInteger(e)){const r=e.toString();if(h(n.errorValues,r))return n.errorValues[r].message;if(l(e))return t.JSON_RPC_SERVER_ERROR_MESSAGE}return r}function c(e){if(!Number.isInteger(e))return!1;const t=e.toString();return!!n.errorValues[t]||!!l(e)}function l(e){return e>=-32099&&e<=-32e3}function u(e){return e&&"object"==typeof e&&!Array.isArray(e)?Object.assign({},e):e}function h(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.",t.getMessageFromCode=a,t.isValidCode=c,t.serializeError=function(e,{fallbackError:t=o,shouldIncludeStack:r=!1}={}){var n,i;if(!t||!Number.isInteger(t.code)||"string"!=typeof t.message)throw new Error("Must provide fallback error with integer number code and string message.");if(e instanceof s.EthereumRpcError)return e.serialize();const l={};if(e&&"object"==typeof e&&!Array.isArray(e)&&h(e,"code")&&c(e.code)){const t=e;l.code=t.code,t.message&&"string"==typeof t.message?(l.message=t.message,h(t,"data")&&(l.data=t.data)):(l.message=a(l.code),l.data={originalError:u(e)})}else{l.code=t.code;const r=null===(n=e)||void 0===n?void 0:n.message;l.message=r&&"string"==typeof r?r:t.message,l.data={originalError:u(e)}}const d=null===(i=e)||void 0===i?void 0:i.stack;return r&&e&&d&&"string"==typeof d&&(l.stack=d),l}},78463:e=>{e.exports=o,o.default=o,o.stable=u,o.stableStringify=u;var t="[...]",r="[Circular]",n=[],s=[];function i(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(e,t,r,o){var a;void 0===o&&(o=i()),c(e,"",0,[],void 0,0,o);try{a=0===s.length?JSON.stringify(e,t,r):JSON.stringify(e,d(t),r)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==n.length;){var l=n.pop();4===l.length?Object.defineProperty(l[0],l[1],l[3]):l[0][l[1]]=l[2]}}return a}function a(e,t,r,i){var o=Object.getOwnPropertyDescriptor(i,r);void 0!==o.get?o.configurable?(Object.defineProperty(i,r,{value:e}),n.push([i,r,t,o])):s.push([t,r,e]):(i[r]=e,n.push([i,r,t]))}function c(e,n,s,i,o,l,u){var h;if(l+=1,"object"==typeof e&&null!==e){for(h=0;h<i.length;h++)if(i[h]===e)return void a(r,e,n,o);if(void 0!==u.depthLimit&&l>u.depthLimit)return void a(t,e,n,o);if(void 0!==u.edgesLimit&&s+1>u.edgesLimit)return void a(t,e,n,o);if(i.push(e),Array.isArray(e))for(h=0;h<e.length;h++)c(e[h],h,h,i,e,l,u);else{var d=Object.keys(e);for(h=0;h<d.length;h++){var p=d[h];c(e[p],p,h,i,e,l,u)}}i.pop()}}function l(e,t){return e<t?-1:e>t?1:0}function u(e,t,r,o){void 0===o&&(o=i());var a,c=h(e,"",0,[],void 0,0,o)||e;try{a=0===s.length?JSON.stringify(c,t,r):JSON.stringify(c,d(t),r)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==n.length;){var l=n.pop();4===l.length?Object.defineProperty(l[0],l[1],l[3]):l[0][l[1]]=l[2]}}return a}function h(e,s,i,o,c,u,d){var p;if(u+=1,"object"==typeof e&&null!==e){for(p=0;p<o.length;p++)if(o[p]===e)return void a(r,e,s,c);try{if("function"==typeof e.toJSON)return}catch(e){return}if(void 0!==d.depthLimit&&u>d.depthLimit)return void a(t,e,s,c);if(void 0!==d.edgesLimit&&i+1>d.edgesLimit)return void a(t,e,s,c);if(o.push(e),Array.isArray(e))for(p=0;p<e.length;p++)h(e[p],p,p,o,e,u,d);else{var f={},m=Object.keys(e).sort(l);for(p=0;p<m.length;p++){var g=m[p];h(e[g],g,p,o,e,u,d),f[g]=e[g]}if(void 0===c)return f;n.push([c,s,e]),c[s]=f}o.pop()}}function d(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(s.length>0)for(var n=0;n<s.length;n++){var i=s[n];if(i[1]===t&&i[0]===r){r=i[2],s.splice(n,1);break}}return e.call(this,t,r)}}},60712:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.JsonRpcEngine=void 0;const s=n(r(97119)),i=r(12203);class o extends s.default{constructor(){super(),this._middleware=[]}push(e){this._middleware.push(e)}handle(e,t){if(t&&"function"!=typeof t)throw new Error('"callback" must be a function if provided.');return Array.isArray(e)?t?this._handleBatch(e,t):this._handleBatch(e):t?this._handle(e,t):this._promiseHandle(e)}asMiddleware(){return async(e,t,r,n)=>{try{const[s,i,a]=await o._runAllMiddleware(e,t,this._middleware);return i?(await o._runReturnHandlers(a),n(s)):r((async e=>{try{await o._runReturnHandlers(a)}catch(t){return e(t)}return e()}))}catch(e){return n(e)}}}async _handleBatch(e,t){try{const r=await Promise.all(e.map(this._promiseHandle.bind(this)));return t?t(null,r):r}catch(e){if(t)return t(e);throw e}}_promiseHandle(e){return new Promise((t=>{this._handle(e,((e,r)=>{t(r)}))}))}async _handle(e,t){if(!e||Array.isArray(e)||"object"!=typeof e){const r=new i.EthereumRpcError(i.errorCodes.rpc.invalidRequest,"Requests must be plain objects. Received: "+typeof e,{request:e});return t(r,{id:void 0,jsonrpc:"2.0",error:r})}if("string"!=typeof e.method){const r=new i.EthereumRpcError(i.errorCodes.rpc.invalidRequest,"Must specify a string method. Received: "+typeof e.method,{request:e});return t(r,{id:e.id,jsonrpc:"2.0",error:r})}const r=Object.assign({},e),n={id:r.id,jsonrpc:r.jsonrpc};let s=null;try{await this._processRequest(r,n)}catch(e){s=e}return s&&(delete n.result,n.error||(n.error=i.serializeError(s))),t(s,n)}async _processRequest(e,t){const[r,n,s]=await o._runAllMiddleware(e,t,this._middleware);if(o._checkForCompletion(e,t,n),await o._runReturnHandlers(s),r)throw r}static async _runAllMiddleware(e,t,r){const n=[];let s=null,i=!1;for(const a of r)if([s,i]=await o._runMiddleware(e,t,a,n),i)break;return[s,i,n.reverse()]}static _runMiddleware(e,t,r,n){return new Promise((s=>{const o=e=>{const r=e||t.error;r&&(t.error=i.serializeError(r)),s([r,!0])},c=r=>{t.error?o(t.error):(r&&("function"!=typeof r&&o(new i.EthereumRpcError(i.errorCodes.rpc.internal,`JsonRpcEngine: "next" return handlers must be functions. Received "${typeof r}" for request:\n${a(e)}`,{request:e})),n.push(r)),s([null,!1]))};try{r(e,t,c,o)}catch(e){o(e)}}))}static async _runReturnHandlers(e){for(const t of e)await new Promise(((e,r)=>{t((t=>t?r(t):e()))}))}static _checkForCompletion(e,t,r){if(!("result"in t)&&!("error"in t))throw new i.EthereumRpcError(i.errorCodes.rpc.internal,`JsonRpcEngine: Response has no error or result for request:\n${a(e)}`,{request:e});if(!r)throw new i.EthereumRpcError(i.errorCodes.rpc.internal,`JsonRpcEngine: Nothing ended request:\n${a(e)}`,{request:e})}}function a(e){return JSON.stringify(e,null,2)}t.JsonRpcEngine=o},33179:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createAsyncMiddleware=void 0,t.createAsyncMiddleware=function(e){return async(t,r,n,s)=>{let i;const o=new Promise((e=>{i=e}));let a=null,c=!1;const l=async()=>{c=!0,n((e=>{a=e,i()})),await o};try{await e(t,r,l),c?(await o,a(null)):s(null)}catch(e){a?a(e):s(e)}}}},58009:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createScaffoldMiddleware=void 0,t.createScaffoldMiddleware=function(e){return(t,r,n,s)=>{const i=e[t.method];return void 0===i?n():"function"==typeof i?i(t,r,n,s):(r.result=i,s())}}},87709:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getUniqueId=void 0;const r=4294967295;let n=Math.floor(Math.random()*r);t.getUniqueId=function(){return n=(n+1)%r,n}},77477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createIdRemapMiddleware=void 0;const n=r(87709);t.createIdRemapMiddleware=function(){return(e,t,r,s)=>{const i=e.id,o=n.getUniqueId();e.id=o,t.id=o,r((r=>{e.id=i,t.id=i,r()}))}}},76583:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(77477),t),s(r(33179),t),s(r(58009),t),s(r(87709),t),s(r(60712),t),s(r(39455),t)},39455:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeMiddleware=void 0;const n=r(60712);t.mergeMiddleware=function(e){const t=new n.JsonRpcEngine;return e.forEach((e=>t.push(e))),t.asMiddleware()}},97119:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(37007);function s(e,t,r){try{Reflect.apply(e,t,r)}catch(e){setTimeout((()=>{throw e}))}}class i extends n.EventEmitter{emit(e,...t){let r="error"===e;const n=this._events;if(void 0!==n)r=r&&void 0===n.error;else if(!r)return!1;if(r){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const r=new Error("Unhandled error."+(e?` (${e.message})`:""));throw r.context=e,r}const i=n[e];if(void 0===i)return!1;if("function"==typeof i)s(i,this,t);else{const e=i.length,r=function(e){const t=e.length,r=new Array(t);for(let n=0;n<t;n+=1)r[n]=e[n];return r}(i);for(let n=0;n<e;n+=1)s(r[n],this,t)}return!0}}t.default=i},31819:e=>{e.exports=function(e){var t=(e=e||{}).max||Number.MAX_SAFE_INTEGER,r=void 0!==e.start?e.start:Math.floor(Math.random()*t);return function(){return r%=t,r++}}},6585:e=>{var t=1e3,r=60*t,n=60*r,s=24*n,i=7*s;function o(e,t,r,n){var s=t>=1.5*r;return Math.round(e/r)+" "+n+(s?"s":"")}e.exports=function(e,a){a=a||{};var c,l,u=typeof e;if("string"===u&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var o=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(o){var a=parseFloat(o[1]);switch((o[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return a*i;case"days":case"day":case"d":return a*s;case"hours":case"hour":case"hrs":case"hr":case"h":return a*n;case"minutes":case"minute":case"mins":case"min":case"m":return a*r;case"seconds":case"second":case"secs":case"sec":case"s":return a*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(e);if("number"===u&&isFinite(e))return a.long?(c=e,(l=Math.abs(c))>=s?o(c,l,s,"day"):l>=n?o(c,l,n,"hour"):l>=r?o(c,l,r,"minute"):l>=t?o(c,l,t,"second"):c+" ms"):function(e){var i=Math.abs(e);return i>=s?Math.round(e/s)+"d":i>=n?Math.round(e/n)+"h":i>=r?Math.round(e/r)+"m":i>=t?Math.round(e/t)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},57510:e=>{e.exports=function(){for(var e={},r=0;r<arguments.length;r++){var n=arguments[r];for(var s in n)t.call(n,s)&&(e[s]=n[s])}return e};var t=Object.prototype.hasOwnProperty},2150:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Struct:()=>u,StructError:()=>n,any:()=>S,array:()=>C,assert:()=>h,assign:()=>g,bigint:()=>I,boolean:()=>R,coerce:()=>Q,create:()=>d,date:()=>x,defaulted:()=>Y,define:()=>w,deprecated:()=>y,dynamic:()=>v,empty:()=>X,enums:()=>A,func:()=>N,instance:()=>L,integer:()=>O,intersection:()=>P,is:()=>f,lazy:()=>b,literal:()=>T,map:()=>j,mask:()=>p,max:()=>te,min:()=>re,never:()=>B,nonempty:()=>ne,nullable:()=>F,number:()=>D,object:()=>$,omit:()=>E,optional:()=>U,partial:()=>_,pattern:()=>se,pick:()=>k,record:()=>H,refine:()=>oe,regexp:()=>V,set:()=>q,size:()=>ie,string:()=>W,struct:()=>M,trimmed:()=>K,tuple:()=>z,type:()=>J,union:()=>G,unknown:()=>Z,validate:()=>m});class n extends TypeError{constructor(e,t){let r;const{message:n,explanation:s,...i}=e,{path:o}=e,a=0===o.length?n:`At path: ${o.join(".")} -- ${n}`;super(s??a),null!=s&&(this.cause=a),Object.assign(this,i),this.name=this.constructor.name,this.failures=()=>r??(r=[e,...t()])}}function s(e){return"object"==typeof e&&null!=e}function i(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function o(e){return"symbol"==typeof e?e.toString():"string"==typeof e?JSON.stringify(e):`${e}`}function a(e,t,r,n){if(!0===e)return;!1===e?e={}:"string"==typeof e&&(e={message:e});const{path:s,branch:i}=t,{type:a}=r,{refinement:c,message:l=`Expected a value of type \`${a}\`${c?` with refinement \`${c}\``:""}, but received: \`${o(n)}\``}=e;return{value:n,type:a,refinement:c,key:s[s.length-1],path:s,branch:i,...e,message:l}}function*c(e,t,r,n){var i;s(i=e)&&"function"==typeof i[Symbol.iterator]||(e=[e]);for(const s of e){const e=a(s,t,r,n);e&&(yield e)}}function*l(e,t,r={}){const{path:n=[],branch:i=[e],coerce:o=!1,mask:a=!1}=r,c={path:n,branch:i};if(o&&(e=t.coercer(e,c),a&&"type"!==t.type&&s(t.schema)&&s(e)&&!Array.isArray(e)))for(const r in e)void 0===t.schema[r]&&delete e[r];let u="valid";for(const n of t.validator(e,c))n.explanation=r.message,u="not_valid",yield[n,void 0];for(let[h,d,p]of t.entries(e,c)){const t=l(d,p,{path:void 0===h?n:[...n,h],branch:void 0===h?i:[...i,d],coerce:o,mask:a,message:r.message});for(const r of t)r[0]?(u=null!=r[0].refinement?"not_refined":"not_valid",yield[r[0],void 0]):o&&(d=r[1],void 0===h?e=d:e instanceof Map?e.set(h,d):e instanceof Set?e.add(d):s(e)&&(void 0!==d||h in e)&&(e[h]=d))}if("not_valid"!==u)for(const n of t.refiner(e,c))n.explanation=r.message,u="not_refined",yield[n,void 0];"valid"===u&&(yield[void 0,e])}class u{constructor(e){const{type:t,schema:r,validator:n,refiner:s,coercer:i=e=>e,entries:o=function*(){}}=e;this.type=t,this.schema=r,this.entries=o,this.coercer=i,this.validator=n?(e,t)=>c(n(e,t),t,this,e):()=>[],this.refiner=s?(e,t)=>c(s(e,t),t,this,e):()=>[]}assert(e,t){return h(e,this,t)}create(e,t){return d(e,this,t)}is(e){return f(e,this)}mask(e,t){return p(e,this,t)}validate(e,t={}){return m(e,this,t)}}function h(e,t,r){const n=m(e,t,{message:r});if(n[0])throw n[0]}function d(e,t,r){const n=m(e,t,{coerce:!0,message:r});if(n[0])throw n[0];return n[1]}function p(e,t,r){const n=m(e,t,{coerce:!0,mask:!0,message:r});if(n[0])throw n[0];return n[1]}function f(e,t){return!m(e,t)[0]}function m(e,t,r={}){const s=l(e,t,r),i=function(e){const{done:t,value:r}=e.next();return t?void 0:r}(s);return i[0]?[new n(i[0],(function*(){for(const e of s)e[0]&&(yield e[0])})),void 0]:[void 0,i[1]]}function g(...e){const t="type"===e[0].type,r=e.map((e=>e.schema)),n=Object.assign({},...r);return t?J(n):$(n)}function w(e,t){return new u({type:e,schema:null,validator:t})}function y(e,t){return new u({...e,refiner:(t,r)=>void 0===t||e.refiner(t,r),validator:(r,n)=>void 0===r||(t(r,n),e.validator(r,n))})}function v(e){return new u({type:"dynamic",schema:null,*entries(t,r){const n=e(t,r);yield*n.entries(t,r)},validator:(t,r)=>e(t,r).validator(t,r),coercer:(t,r)=>e(t,r).coercer(t,r),refiner:(t,r)=>e(t,r).refiner(t,r)})}function b(e){let t;return new u({type:"lazy",schema:null,*entries(r,n){t??(t=e()),yield*t.entries(r,n)},validator:(r,n)=>(t??(t=e()),t.validator(r,n)),coercer:(r,n)=>(t??(t=e()),t.coercer(r,n)),refiner:(r,n)=>(t??(t=e()),t.refiner(r,n))})}function E(e,t){const{schema:r}=e,n={...r};for(const e of t)delete n[e];return"type"===e.type?J(n):$(n)}function _(e){const t=e instanceof u,r=t?{...e.schema}:{...e};for(const e in r)r[e]=U(r[e]);return t&&"type"===e.type?J(r):$(r)}function k(e,t){const{schema:r}=e,n={};for(const e of t)n[e]=r[e];return"type"===e.type?J(n):$(n)}function M(e,t){return console.warn("superstruct@0.11 - The `struct` helper has been renamed to `define`."),w(e,t)}function S(){return w("any",(()=>!0))}function C(e){return new u({type:"array",schema:e,*entries(t){if(e&&Array.isArray(t))for(const[r,n]of t.entries())yield[r,n,e]},coercer:e=>Array.isArray(e)?e.slice():e,validator:e=>Array.isArray(e)||`Expected an array value, but received: ${o(e)}`})}function I(){return w("bigint",(e=>"bigint"==typeof e))}function R(){return w("boolean",(e=>"boolean"==typeof e))}function x(){return w("date",(e=>e instanceof Date&&!isNaN(e.getTime())||`Expected a valid \`Date\` object, but received: ${o(e)}`))}function A(e){const t={},r=e.map((e=>o(e))).join();for(const r of e)t[r]=r;return new u({type:"enums",schema:t,validator:t=>e.includes(t)||`Expected one of \`${r}\`, but received: ${o(t)}`})}function N(){return w("func",(e=>"function"==typeof e||`Expected a function, but received: ${o(e)}`))}function L(e){return w("instance",(t=>t instanceof e||`Expected a \`${e.name}\` instance, but received: ${o(t)}`))}function O(){return w("integer",(e=>"number"==typeof e&&!isNaN(e)&&Number.isInteger(e)||`Expected an integer, but received: ${o(e)}`))}function P(e){return new u({type:"intersection",schema:null,*entries(t,r){for(const n of e)yield*n.entries(t,r)},*validator(t,r){for(const n of e)yield*n.validator(t,r)},*refiner(t,r){for(const n of e)yield*n.refiner(t,r)}})}function T(e){const t=o(e),r=typeof e;return new u({type:"literal",schema:"string"===r||"number"===r||"boolean"===r?e:null,validator:r=>r===e||`Expected the literal \`${t}\`, but received: ${o(r)}`})}function j(e,t){return new u({type:"map",schema:null,*entries(r){if(e&&t&&r instanceof Map)for(const[n,s]of r.entries())yield[n,n,e],yield[n,s,t]},coercer:e=>e instanceof Map?new Map(e):e,validator:e=>e instanceof Map||`Expected a \`Map\` object, but received: ${o(e)}`})}function B(){return w("never",(()=>!1))}function F(e){return new u({...e,validator:(t,r)=>null===t||e.validator(t,r),refiner:(t,r)=>null===t||e.refiner(t,r)})}function D(){return w("number",(e=>"number"==typeof e&&!isNaN(e)||`Expected a number, but received: ${o(e)}`))}function $(e){const t=e?Object.keys(e):[],r=B();return new u({type:"object",schema:e||null,*entries(n){if(e&&s(n)){const s=new Set(Object.keys(n));for(const r of t)s.delete(r),yield[r,n[r],e[r]];for(const e of s)yield[e,n[e],r]}},validator:e=>s(e)||`Expected an object, but received: ${o(e)}`,coercer:e=>s(e)?{...e}:e})}function U(e){return new u({...e,validator:(t,r)=>void 0===t||e.validator(t,r),refiner:(t,r)=>void 0===t||e.refiner(t,r)})}function H(e,t){return new u({type:"record",schema:null,*entries(r){if(s(r))for(const n in r){const s=r[n];yield[n,n,e],yield[n,s,t]}},validator:e=>s(e)||`Expected an object, but received: ${o(e)}`})}function V(){return w("regexp",(e=>e instanceof RegExp))}function q(e){return new u({type:"set",schema:null,*entries(t){if(e&&t instanceof Set)for(const r of t)yield[r,r,e]},coercer:e=>e instanceof Set?new Set(e):e,validator:e=>e instanceof Set||`Expected a \`Set\` object, but received: ${o(e)}`})}function W(){return w("string",(e=>"string"==typeof e||`Expected a string, but received: ${o(e)}`))}function z(e){const t=B();return new u({type:"tuple",schema:null,*entries(r){if(Array.isArray(r)){const n=Math.max(e.length,r.length);for(let s=0;s<n;s++)yield[s,r[s],e[s]||t]}},validator:e=>Array.isArray(e)||`Expected an array, but received: ${o(e)}`})}function J(e){const t=Object.keys(e);return new u({type:"type",schema:e,*entries(r){if(s(r))for(const n of t)yield[n,r[n],e[n]]},validator:e=>s(e)||`Expected an object, but received: ${o(e)}`,coercer:e=>s(e)?{...e}:e})}function G(e){const t=e.map((e=>e.type)).join(" | ");return new u({type:"union",schema:null,coercer(t){for(const r of e){const[e,n]=r.validate(t,{coerce:!0});if(!e)return n}return t},validator(r,n){const s=[];for(const t of e){const[...e]=l(r,t,n),[i]=e;if(!i[0])return[];for(const[t]of e)t&&s.push(t)}return[`Expected the value to satisfy a union of \`${t}\`, but received: ${o(r)}`,...s]}})}function Z(){return w("unknown",(()=>!0))}function Q(e,t,r){return new u({...e,coercer:(n,s)=>f(n,t)?e.coercer(r(n,s),s):e.coercer(n,s)})}function Y(e,t,r={}){return Q(e,Z(),(e=>{const n="function"==typeof t?t():t;if(void 0===e)return n;if(!r.strict&&i(e)&&i(n)){const t={...e};let r=!1;for(const e in n)void 0===t[e]&&(t[e]=n[e],r=!0);if(r)return t}return e}))}function K(e){return Q(e,W(),(e=>e.trim()))}function X(e){return oe(e,"empty",(t=>{const r=ee(t);return 0===r||`Expected an empty ${e.type} but received one with a size of \`${r}\``}))}function ee(e){return e instanceof Map||e instanceof Set?e.size:e.length}function te(e,t,r={}){const{exclusive:n}=r;return oe(e,"max",(r=>n?r<t:r<=t||`Expected a ${e.type} less than ${n?"":"or equal to "}${t} but received \`${r}\``))}function re(e,t,r={}){const{exclusive:n}=r;return oe(e,"min",(r=>n?r>t:r>=t||`Expected a ${e.type} greater than ${n?"":"or equal to "}${t} but received \`${r}\``))}function ne(e){return oe(e,"nonempty",(t=>ee(t)>0||`Expected a nonempty ${e.type} but received an empty one`))}function se(e,t){return oe(e,"pattern",(r=>t.test(r)||`Expected a ${e.type} matching \`/${t.source}/\` but received "${r}"`))}function ie(e,t,r=t){const n=`Expected a ${e.type}`,s=t===r?`of \`${t}\``:`between \`${t}\` and \`${r}\``;return oe(e,"size",(e=>{if("number"==typeof e||e instanceof Date)return t<=e&&e<=r||`${n} ${s} but received \`${e}\``;if(e instanceof Map||e instanceof Set){const{size:i}=e;return t<=i&&i<=r||`${n} with a size ${s} but received one with a size of \`${i}\``}{const{length:i}=e;return t<=i&&i<=r||`${n} with a length ${s} but received one with a length of \`${i}\``}}))}function oe(e,t,r){return new u({...e,*refiner(n,s){yield*e.refiner(n,s);const i=c(r(n,s),s,e,n);for(const e of i)yield{...e,refinement:t}}})}},31635:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__addDisposableResource:()=>P,__assign:()=>i,__asyncDelegator:()=>S,__asyncGenerator:()=>M,__asyncValues:()=>C,__await:()=>k,__awaiter:()=>f,__classPrivateFieldGet:()=>N,__classPrivateFieldIn:()=>O,__classPrivateFieldSet:()=>L,__createBinding:()=>g,__decorate:()=>a,__disposeResources:()=>j,__esDecorate:()=>l,__exportStar:()=>w,__extends:()=>s,__generator:()=>m,__importDefault:()=>A,__importStar:()=>x,__makeTemplateObject:()=>I,__metadata:()=>p,__param:()=>c,__propKey:()=>h,__read:()=>v,__rest:()=>o,__runInitializers:()=>u,__setFunctionName:()=>d,__spread:()=>b,__spreadArray:()=>_,__spreadArrays:()=>E,__values:()=>y,default:()=>B});var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)};function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},i.apply(this,arguments)};function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]])}return r}function a(e,t,r,n){var s,i=arguments.length,o=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(o=(i<3?s(o):i>3?s(t,r,o):s(t,r))||o);return i>3&&o&&Object.defineProperty(t,r,o),o}function c(e,t){return function(r,n){t(r,n,e)}}function l(e,t,r,n,s,i){function o(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var a,c=n.kind,l="getter"===c?"get":"setter"===c?"set":"value",u=!t&&e?n.static?e:e.prototype:null,h=t||(u?Object.getOwnPropertyDescriptor(u,n.name):{}),d=!1,p=r.length-1;p>=0;p--){var f={};for(var m in n)f[m]="access"===m?{}:n[m];for(var m in n.access)f.access[m]=n.access[m];f.addInitializer=function(e){if(d)throw new TypeError("Cannot add initializers after decoration has completed");i.push(o(e||null))};var g=(0,r[p])("accessor"===c?{get:h.get,set:h.set}:h[l],f);if("accessor"===c){if(void 0===g)continue;if(null===g||"object"!=typeof g)throw new TypeError("Object expected");(a=o(g.get))&&(h.get=a),(a=o(g.set))&&(h.set=a),(a=o(g.init))&&s.unshift(a)}else(a=o(g))&&("field"===c?s.unshift(a):h[l]=a)}u&&Object.defineProperty(u,n.name,h),d=!0}function u(e,t,r){for(var n=arguments.length>2,s=0;s<t.length;s++)r=n?t[s].call(e,r):t[s].call(e);return n?r:void 0}function h(e){return"symbol"==typeof e?e:"".concat(e)}function d(e,t,r){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function f(e,t,r,n){return new(r||(r=Promise))((function(s,i){function o(e){try{c(n.next(e))}catch(e){i(e)}}function a(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?s(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((n=n.apply(e,t||[])).next())}))}function m(e,t){var r,n,s,i,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(o=0)),o;)try{if(r=1,n&&(s=2&a[0]?n.return:a[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,a[1])).done)return s;switch(n=0,s&&(a=[2&a[0],s.value]),a[0]){case 0:case 1:s=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!((s=(s=o.trys).length>0&&s[s.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!s||a[1]>s[0]&&a[1]<s[3])){o.label=a[1];break}if(6===a[0]&&o.label<s[1]){o.label=s[1],s=a;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(a);break}s[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],n=0}finally{r=s=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}var g=Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]};function w(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||g(t,e,r)}function y(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function v(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,s,i=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(e){s={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(s)throw s.error}}return o}function b(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(v(arguments[t]));return e}function E(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),s=0;for(t=0;t<r;t++)for(var i=arguments[t],o=0,a=i.length;o<a;o++,s++)n[s]=i[o];return n}function _(e,t,r){if(r||2===arguments.length)for(var n,s=0,i=t.length;s<i;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))}function k(e){return this instanceof k?(this.v=e,this):new k(e)}function M(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,s=r.apply(e,t||[]),i=[];return n={},o("next"),o("throw"),o("return",(function(e){return function(t){return Promise.resolve(t).then(e,l)}})),n[Symbol.asyncIterator]=function(){return this},n;function o(e,t){s[e]&&(n[e]=function(t){return new Promise((function(r,n){i.push([e,t,r,n])>1||a(e,t)}))},t&&(n[e]=t(n[e])))}function a(e,t){try{(r=s[e](t)).value instanceof k?Promise.resolve(r.value.v).then(c,l):u(i[0][2],r)}catch(e){u(i[0][3],e)}var r}function c(e){a("next",e)}function l(e){a("throw",e)}function u(e,t){e(t),i.shift(),i.length&&a(i[0][0],i[0][1])}}function S(e){var t,r;return t={},n("next"),n("throw",(function(e){throw e})),n("return"),t[Symbol.iterator]=function(){return this},t;function n(n,s){t[n]=e[n]?function(t){return(r=!r)?{value:k(e[n](t)),done:!1}:s?s(t):t}:s}}function C(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=y(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise((function(n,s){!function(e,t,r,n){Promise.resolve(n).then((function(t){e({value:t,done:r})}),t)}(n,s,(t=e[r](t)).done,t.value)}))}}}function I(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var R=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};function x(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&g(t,e,r);return R(t,e),t}function A(e){return e&&e.__esModule?e:{default:e}}function N(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function L(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r}function O(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function P(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var n,s;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(s=n)}if("function"!=typeof n)throw new TypeError("Object not disposable.");s&&(n=function(){try{s.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var T="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=new Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};function j(e){function t(t){e.error=e.hasError?new T(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}return function r(){for(;e.stack.length;){var n=e.stack.pop();try{var s=n.dispose&&n.dispose.call(n.value);if(n.async)return Promise.resolve(s).then(r,(function(e){return t(e),r()}))}catch(e){t(e)}}if(e.hasError)throw e.error}()}const B={__extends:s,__assign:i,__rest:o,__decorate:a,__param:c,__metadata:p,__awaiter:f,__generator:m,__createBinding:g,__exportStar:w,__values:y,__read:v,__spread:b,__spreadArrays:E,__spreadArray:_,__await:k,__asyncGenerator:M,__asyncDelegator:S,__asyncValues:C,__makeTemplateObject:I,__importStar:x,__importDefault:A,__classPrivateFieldGet:N,__classPrivateFieldSet:L,__classPrivateFieldIn:O,__addDisposableResource:P,__disposeResources:j}}}]);