{"name": "fula-webui", "version": "1.0.0", "description": "Fula Web UI for managing and connecting to the Fula blockchain.", "main": "app.js", "scripts": {"start": "electron-forge start", "bundle": "node bundle.js", "build": "webpack --mode production", "watch": "webpack --watch --mode development", "package": "electron-forge package", "make": "electron-forge make"}, "bin": "./app.js", "pkg": {"scripts": ["public/js/bundle/*.js", "routes/**/*.js", "app.js"], "assets": ["views/**/*", "public/css/*"], "targets": ["node18-win-x64"]}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@functionland/fula-sec-web": "^2.0.0", "@metamask/detect-provider": "^1.2.0", "@polkadot/api": "^12.2.1", "@polkadot/keyring": "^13.0.2", "@polkadot/util-crypto": "^13.0.2", "@tanstack/react-query": "^5.51.15", "@wagmi/connectors": "^5.1.1", "@wagmi/core": "^2.13.1", "@web3modal/wagmi": "^5.0.8", "axios": "^1.7.2", "base64-js": "^1.5.1", "body-parser": "^1.19.0", "buffer": "^6.0.3", "child_process": "^1.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "electron-squirrel-startup": "^1.0.1", "express": "^4.17.1", "multiformats": "^13.1.3", "multihashes": "^4.0.3", "path-browserify": "^1.0.1", "peer-id": "^0.16.0", "rfc4648": "^1.5.3", "tweetnacl": "^1.0.3", "viem": "^2.18.2", "wagmi": "^2.12.1", "web3": "^4.11.0"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/core": "^7.24.9", "@babel/preset-env": "^7.24.8", "@babel/preset-react": "^7.24.7", "@electron-forge/cli": "^7.4.0", "@electron-forge/maker-deb": "^7.4.0", "@electron-forge/maker-rpm": "^7.4.0", "@electron-forge/maker-squirrel": "^7.4.0", "@electron-forge/maker-zip": "^7.4.0", "@electron-forge/plugin-auto-unpack-natives": "^7.4.0", "@electron-forge/plugin-fuses": "^7.4.0", "@electron/fuses": "^1.8.0", "babel-loader": "^9.1.3", "babelify": "^10.0.0", "browserify": "^17.0.0", "electron": "31.2.1", "nodemon": "^2.0.15", "webpack": "^5.93.0", "webpack-cli": "^5.1.4"}}