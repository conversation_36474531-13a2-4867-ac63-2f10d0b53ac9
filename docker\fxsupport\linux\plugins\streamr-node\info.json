{"name": "streamr-node", "description": "Streamr (DATA) is an open source project and decentralized platform that relies on cryptography instead of trust. It is a P2P, real-time data infrastructure that houses a platform and tools for a new data economy. The technology stack includes a scalable real-time messaging network (pub/sub) hosted on computers worldwide, a marketplace for trading/selling data and a set of tools for working with real-time data.", "version": "101", "usage": {"storage": "none", "compute": "medium", "bandwidth": "high", "ram": "high", "gpu": "none"}, "rewards": [{"type": "token", "currency": "$DATA", "link": "https://coinmarketcap.com/currencies/streamr/"}], "socials": [{"telegram": "https://t.me/streamrofficial", "twitter": "https://x.com/streamr", "email": "", "website": "https://streamr.network/", "discord": ""}], "instructions": [{"order": 1, "description": "Open Port 32200 in your router", "url": ""}, {"order": 2, "description": "Skip if you chose default operator and no action is needed, otherwise Connect your wallet and Get Operator Contract Address and paste in the parameters input instead of default operator", "url": "https://streamr.network/hub/network/operators/"}, {"order": 3, "description": "Skip if you chose default operator and no action is needed, otherwise you need to fund your account with minimum 5K $DATA using the Fund button at the top of your profile", "url": "https://streamr.network/hub/network/operators/"}, {"order": 4, "description": "Click Install button", "url": ""}, {"order": 5, "description": "Node address appears after installation is done. Fund your node address with a 0.15 $MATIC for testnet or 0.15 $POL for mainnet.", "url": "", "paramId": 1}, {"order": 6, "description": "Skip if you chose default operator and no action is needed, otherwise Add your node to your operator by clicking on your operator and scroll down to Operator's node addresses", "url": "https://streamr.network/hub/network/operators/"}, {"order": 7, "description": "Skip if you chose default operator and no action is needed, otherwise join any sponsorship", "url": "https://streamr.network/hub/network/sponsorships/"}, {"order": 8, "description": "If you chose default operator, join Functionland pool, otherwise if you set up your own operator ignore this", "url": "https://fund.functionyard.fula.network/streamr", "paramId": 1}, {"order": 9, "description": "Add node to your metamask with the private key below (Polygon) to receive $DATA", "paramId": 2}], "requiredInputs": [{"name": "operator-contract-address", "instructions": "go to https://streamr.network/hub/network/operators/ and click on your operator (Or become a new operator) and copy the Operator Contract Address", "type": "string", "default": "0x53757949ffefd0f0763930b4b3ded8dfc81e9662"}, {"name": "network", "instructions": "testnet or mainnet.", "type": "string", "default": "mainnet"}], "outputs": [{"name": "node_addr", "id": 1}, {"name": "private_key", "id": 2}], "approved": true}