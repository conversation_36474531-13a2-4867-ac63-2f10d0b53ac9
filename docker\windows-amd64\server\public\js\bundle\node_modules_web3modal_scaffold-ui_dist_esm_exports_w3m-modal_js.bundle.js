"use strict";
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkfula_webui"] = self["webpackChunkfula_webui"] || []).push([["node_modules_web3modal_scaffold-ui_dist_esm_exports_w3m-modal_js"],{

/***/ "./node_modules/@web3modal/scaffold-ui/dist/esm/exports/w3m-modal.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@web3modal/scaffold-ui/dist/esm/exports/w3m-modal.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   W3mModal: () => (/* reexport safe */ _src_modal_w3m_modal_index_js__WEBPACK_IMPORTED_MODULE_0__.W3mModal)\n/* harmony export */ });\n/* harmony import */ var _src_modal_w3m_modal_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../src/modal/w3m-modal/index.js */ \"./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/index.js\");\n\n//# sourceMappingURL=w3m-modal.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@web3modal/scaffold-ui/dist/esm/exports/w3m-modal.js?");

/***/ }),

/***/ "./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/index.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   W3mModal: () => (/* binding */ W3mModal)\n/* harmony export */ });\n/* harmony import */ var _web3modal_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @web3modal/core */ \"./node_modules/@web3modal/core/dist/esm/index.js\");\n/* harmony import */ var _web3modal_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @web3modal/ui */ \"./node_modules/@web3modal/ui/dist/esm/index.js\");\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lit */ \"./node_modules/@web3modal/scaffold-ui/node_modules/lit/index.js\");\n/* harmony import */ var lit_decorators_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lit/decorators.js */ \"./node_modules/@web3modal/scaffold-ui/node_modules/lit/decorators.js\");\n/* harmony import */ var _styles_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./styles.js */ \"./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/styles.js\");\nvar __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\n\n\n\n\nconst SCROLL_LOCK = 'scroll-lock';\nlet W3mModal = class W3mModal extends lit__WEBPACK_IMPORTED_MODULE_2__.LitElement {\n    constructor() {\n        super();\n        this.unsubscribe = [];\n        this.abortController = undefined;\n        this.open = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalController.state.open;\n        this.caipAddress = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.AccountController.state.caipAddress;\n        this.isSiweEnabled = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.OptionsController.state.isSiweEnabled;\n        this.connected = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.AccountController.state.isConnected;\n        this.loading = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalController.state.loading;\n        this.initializeTheming();\n        _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ApiController.prefetch();\n        this.unsubscribe.push(_web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalController.subscribeKey('open', val => (val ? this.onOpen() : this.onClose())), _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalController.subscribeKey('loading', val => {\n            this.loading = val;\n            this.onNewAddress(_web3modal_core__WEBPACK_IMPORTED_MODULE_0__.AccountController.state.caipAddress);\n        }), _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.AccountController.subscribeKey('isConnected', val => (this.connected = val)), _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.AccountController.subscribeKey('caipAddress', val => this.onNewAddress(val)), _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.OptionsController.subscribeKey('isSiweEnabled', val => (this.isSiweEnabled = val)));\n        _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.EventsController.sendEvent({ type: 'track', event: 'MODAL_LOADED' });\n    }\n    disconnectedCallback() {\n        this.unsubscribe.forEach(unsubscribe => unsubscribe());\n        this.onRemoveKeyboardListener();\n    }\n    render() {\n        return this.open\n            ? (0,lit__WEBPACK_IMPORTED_MODULE_2__.html) `\n          <wui-flex @click=${this.onOverlayClick.bind(this)}>\n            <wui-card role=\"alertdialog\" aria-modal=\"true\" tabindex=\"0\">\n              <w3m-header></w3m-header>\n              <w3m-router></w3m-router>\n              <w3m-snackbar></w3m-snackbar>\n            </wui-card>\n          </wui-flex>\n          <w3m-tooltip></w3m-tooltip>\n        `\n            : null;\n    }\n    async onOverlayClick(event) {\n        if (event.target === event.currentTarget) {\n            await this.handleClose();\n        }\n    }\n    async handleClose() {\n        if (this.isSiweEnabled) {\n            const { SIWEController } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_web3modal_siwe_dist_esm_exports_index_js\"), __webpack_require__.e(\"_76bb\")]).then(__webpack_require__.bind(__webpack_require__, /*! @web3modal/siwe */ \"./node_modules/@web3modal/siwe/dist/esm/exports/index.js\"));\n            if (SIWEController.state.status !== 'success' && this.connected) {\n                await _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ConnectionController.disconnect();\n            }\n        }\n        _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalController.close();\n    }\n    initializeTheming() {\n        const { themeVariables, themeMode } = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ThemeController.state;\n        const defaultThemeMode = _web3modal_ui__WEBPACK_IMPORTED_MODULE_1__.UiHelperUtil.getColorTheme(themeMode);\n        (0,_web3modal_ui__WEBPACK_IMPORTED_MODULE_1__.initializeTheming)(themeVariables, defaultThemeMode);\n    }\n    onClose() {\n        this.open = false;\n        this.classList.remove('open');\n        this.onScrollUnlock();\n        _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.SnackController.hide();\n        this.onRemoveKeyboardListener();\n    }\n    onOpen() {\n        this.open = true;\n        this.classList.add('open');\n        this.onScrollLock();\n        this.onAddKeyboardListener();\n    }\n    onScrollLock() {\n        const styleTag = document.createElement('style');\n        styleTag.dataset['w3m'] = SCROLL_LOCK;\n        styleTag.textContent = `\n      body {\n        touch-action: none;\n        overflow: hidden;\n        overscroll-behavior: contain;\n      }\n      w3m-modal {\n        pointer-events: auto;\n      }\n    `;\n        document.head.appendChild(styleTag);\n    }\n    onScrollUnlock() {\n        const styleTag = document.head.querySelector(`style[data-w3m=\"${SCROLL_LOCK}\"]`);\n        if (styleTag) {\n            styleTag.remove();\n        }\n    }\n    onAddKeyboardListener() {\n        this.abortController = new AbortController();\n        const card = this.shadowRoot?.querySelector('wui-card');\n        card?.focus();\n        window.addEventListener('keydown', event => {\n            if (event.key === 'Escape') {\n                this.handleClose();\n            }\n            else if (event.key === 'Tab') {\n                const { tagName } = event.target;\n                if (tagName && !tagName.includes('W3M-') && !tagName.includes('WUI-')) {\n                    card?.focus();\n                }\n            }\n        }, this.abortController);\n    }\n    onRemoveKeyboardListener() {\n        this.abortController?.abort();\n        this.abortController = undefined;\n    }\n    async onNewAddress(caipAddress) {\n        if (!this.connected || this.loading) {\n            return;\n        }\n        const previousAddress = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.CoreHelperUtil.getPlainAddress(this.caipAddress);\n        const newAddress = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.CoreHelperUtil.getPlainAddress(caipAddress);\n        const previousNetworkId = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.CoreHelperUtil.getNetworkId(this.caipAddress);\n        const newNetworkId = _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.CoreHelperUtil.getNetworkId(caipAddress);\n        this.caipAddress = caipAddress;\n        if (this.isSiweEnabled) {\n            const { SIWEController } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_web3modal_siwe_dist_esm_exports_index_js\"), __webpack_require__.e(\"_76bb\")]).then(__webpack_require__.bind(__webpack_require__, /*! @web3modal/siwe */ \"./node_modules/@web3modal/siwe/dist/esm/exports/index.js\"));\n            const session = await SIWEController.getSession();\n            if (session && previousAddress && newAddress && previousAddress !== newAddress) {\n                if (SIWEController.state._client?.options.signOutOnAccountChange) {\n                    await SIWEController.signOut();\n                    this.onSiweNavigation();\n                }\n                return;\n            }\n            if (session && previousNetworkId && newNetworkId && previousNetworkId !== newNetworkId) {\n                if (SIWEController.state._client?.options.signOutOnNetworkChange) {\n                    await SIWEController.signOut();\n                    this.onSiweNavigation();\n                }\n                return;\n            }\n            this.onSiweNavigation();\n        }\n    }\n    onSiweNavigation() {\n        if (this.open) {\n            _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.RouterController.push('ConnectingSiwe');\n        }\n        else {\n            _web3modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalController.open({\n                view: 'ConnectingSiwe'\n            });\n        }\n    }\n};\nW3mModal.styles = _styles_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n__decorate([\n    (0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_3__.state)()\n], W3mModal.prototype, \"open\", void 0);\n__decorate([\n    (0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_3__.state)()\n], W3mModal.prototype, \"caipAddress\", void 0);\n__decorate([\n    (0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_3__.state)()\n], W3mModal.prototype, \"isSiweEnabled\", void 0);\n__decorate([\n    (0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_3__.state)()\n], W3mModal.prototype, \"connected\", void 0);\n__decorate([\n    (0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_3__.state)()\n], W3mModal.prototype, \"loading\", void 0);\nW3mModal = __decorate([\n    (0,_web3modal_ui__WEBPACK_IMPORTED_MODULE_1__.customElement)('w3m-modal')\n], W3mModal);\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/index.js?");

/***/ }),

/***/ "./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/styles.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/styles.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"./node_modules/@web3modal/scaffold-ui/node_modules/lit/index.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,lit__WEBPACK_IMPORTED_MODULE_0__.css) `\n  :host {\n    z-index: var(--w3m-z-index);\n    display: block;\n    backface-visibility: hidden;\n    will-change: opacity;\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    opacity: 0;\n    background-color: var(--wui-cover);\n    transition: opacity 0.2s var(--wui-ease-out-power-2);\n    will-change: opacity;\n  }\n\n  :host(.open) {\n    opacity: 1;\n  }\n\n  @keyframes zoom-in {\n    0% {\n      transform: scale(0.95) translateY(0);\n    }\n    100% {\n      transform: scale(1) translateY(0);\n    }\n  }\n\n  @keyframes slide-in {\n    0% {\n      transform: scale(1) translateY(50px);\n    }\n    100% {\n      transform: scale(1) translateY(0);\n    }\n  }\n\n  wui-card {\n    max-width: var(--w3m-modal-width);\n    width: 100%;\n    position: relative;\n    animation-duration: 0.2s;\n    animation-name: zoom-in;\n    animation-fill-mode: backwards;\n    animation-timing-function: var(--wui-ease-out-power-2);\n    outline: none;\n  }\n\n  wui-flex {\n    overflow-x: hidden;\n    overflow-y: auto;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n    height: 100%;\n  }\n\n  @media (max-height: 700px) and (min-width: 431px) {\n    wui-flex {\n      align-items: flex-start;\n    }\n\n    wui-card {\n      margin: var(--wui-spacing-xxl) 0px;\n    }\n  }\n\n  @media (max-width: 430px) {\n    wui-flex {\n      align-items: flex-end;\n    }\n\n    wui-card {\n      max-width: 100%;\n      border-bottom-left-radius: 0;\n      border-bottom-right-radius: 0;\n      border-bottom: none;\n      animation-name: slide-in;\n    }\n  }\n`);\n//# sourceMappingURL=styles.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@web3modal/scaffold-ui/dist/esm/src/modal/w3m-modal/styles.js?");

/***/ })

}]);