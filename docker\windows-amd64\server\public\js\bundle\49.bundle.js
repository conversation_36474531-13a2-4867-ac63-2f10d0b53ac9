/*! For license information please see 49.bundle.js.LICENSE.txt */
(self.webpackChunkfula_webui=self.webpackChunkfula_webui||[]).push([[49],{64974:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=r(63319),n=r(44911);e.DIGEST_LENGTH=64,e.BLOCK_SIZE=128;var s=function(){function t(){this.digestLength=e.DIGEST_LENGTH,this.blockSize=e.BLOCK_SIZE,this._stateHi=new Int32Array(8),this._stateLo=new Int32Array(8),this._tempHi=new Int32Array(16),this._tempLo=new Int32Array(16),this._buffer=new Uint8Array(256),this._bufferLength=0,this._bytesHashed=0,this._finished=!1,this.reset()}return t.prototype._initState=function(){this._stateHi[0]=**********,this._stateHi[1]=**********,this._stateHi[2]=**********,this._stateHi[3]=**********,this._stateHi[4]=**********,this._stateHi[5]=**********,this._stateHi[6]=528734635,this._stateHi[7]=**********,this._stateLo[0]=**********,this._stateLo[1]=**********,this._stateLo[2]=**********,this._stateLo[3]=**********,this._stateLo[4]=**********,this._stateLo[5]=725511199,this._stateLo[6]=4215389547,this._stateLo[7]=327033209},t.prototype.reset=function(){return this._initState(),this._bufferLength=0,this._bytesHashed=0,this._finished=!1,this},t.prototype.clean=function(){n.wipe(this._buffer),n.wipe(this._tempHi),n.wipe(this._tempLo),this.reset()},t.prototype.update=function(t,r){if(void 0===r&&(r=t.length),this._finished)throw new Error("SHA512: can't update because hash was finished.");var i=0;if(this._bytesHashed+=r,this._bufferLength>0){for(;this._bufferLength<e.BLOCK_SIZE&&r>0;)this._buffer[this._bufferLength++]=t[i++],r--;this._bufferLength===this.blockSize&&(a(this._tempHi,this._tempLo,this._stateHi,this._stateLo,this._buffer,0,this.blockSize),this._bufferLength=0)}for(r>=this.blockSize&&(i=a(this._tempHi,this._tempLo,this._stateHi,this._stateLo,t,i,r),r%=this.blockSize);r>0;)this._buffer[this._bufferLength++]=t[i++],r--;return this},t.prototype.finish=function(t){if(!this._finished){var e=this._bytesHashed,r=this._bufferLength,n=e/536870912|0,s=e<<3,o=e%128<112?128:256;this._buffer[r]=128;for(var c=r+1;c<o-8;c++)this._buffer[c]=0;i.writeUint32BE(n,this._buffer,o-8),i.writeUint32BE(s,this._buffer,o-4),a(this._tempHi,this._tempLo,this._stateHi,this._stateLo,this._buffer,0,o),this._finished=!0}for(c=0;c<this.digestLength/8;c++)i.writeUint32BE(this._stateHi[c],t,8*c),i.writeUint32BE(this._stateLo[c],t,8*c+4);return this},t.prototype.digest=function(){var t=new Uint8Array(this.digestLength);return this.finish(t),t},t.prototype.saveState=function(){if(this._finished)throw new Error("SHA256: cannot save finished state");return{stateHi:new Int32Array(this._stateHi),stateLo:new Int32Array(this._stateLo),buffer:this._bufferLength>0?new Uint8Array(this._buffer):void 0,bufferLength:this._bufferLength,bytesHashed:this._bytesHashed}},t.prototype.restoreState=function(t){return this._stateHi.set(t.stateHi),this._stateLo.set(t.stateLo),this._bufferLength=t.bufferLength,t.buffer&&this._buffer.set(t.buffer),this._bytesHashed=t.bytesHashed,this._finished=!1,this},t.prototype.cleanSavedState=function(t){n.wipe(t.stateHi),n.wipe(t.stateLo),t.buffer&&n.wipe(t.buffer),t.bufferLength=0,t.bytesHashed=0},t}();e.SHA512=s;var o=new Int32Array([1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591]);function a(t,e,r,n,s,a,c){for(var h,u,l,f,d,p,g,m,y=r[0],v=r[1],w=r[2],b=r[3],A=r[4],_=r[5],E=r[6],I=r[7],S=n[0],M=n[1],P=n[2],x=n[3],R=n[4],N=n[5],C=n[6],O=n[7];c>=128;){for(var U=0;U<16;U++){var T=8*U+a;t[U]=i.readUint32BE(s,T),e[U]=i.readUint32BE(s,T+4)}for(U=0;U<80;U++){var D,B,k=y,q=v,L=w,j=b,z=A,F=_,K=E,H=S,V=M,$=P,J=x,G=R,W=N,Q=C;if(d=65535&(u=O),p=u>>>16,g=65535&(h=I),m=h>>>16,d+=65535&(u=(R>>>14|A<<18)^(R>>>18|A<<14)^(A>>>9|R<<23)),p+=u>>>16,g+=65535&(h=(A>>>14|R<<18)^(A>>>18|R<<14)^(R>>>9|A<<23)),m+=h>>>16,d+=65535&(u=R&N^~R&C),p+=u>>>16,g+=65535&(h=A&_^~A&E),m+=h>>>16,h=o[2*U],d+=65535&(u=o[2*U+1]),p+=u>>>16,g+=65535&h,m+=h>>>16,h=t[U%16],p+=(u=e[U%16])>>>16,g+=65535&h,m+=h>>>16,g+=(p+=(d+=65535&u)>>>16)>>>16,d=65535&(u=f=65535&d|p<<16),p=u>>>16,g=65535&(h=l=65535&g|(m+=g>>>16)<<16),m=h>>>16,d+=65535&(u=(S>>>28|y<<4)^(y>>>2|S<<30)^(y>>>7|S<<25)),p+=u>>>16,g+=65535&(h=(y>>>28|S<<4)^(S>>>2|y<<30)^(S>>>7|y<<25)),m+=h>>>16,p+=(u=S&M^S&P^M&P)>>>16,g+=65535&(h=y&v^y&w^v&w),m+=h>>>16,D=65535&(g+=(p+=(d+=65535&u)>>>16)>>>16)|(m+=g>>>16)<<16,B=65535&d|p<<16,d=65535&(u=J),p=u>>>16,g=65535&(h=j),m=h>>>16,p+=(u=f)>>>16,g+=65535&(h=l),m+=h>>>16,v=k,w=q,b=L,A=j=65535&(g+=(p+=(d+=65535&u)>>>16)>>>16)|(m+=g>>>16)<<16,_=z,E=F,I=K,y=D,M=H,P=V,x=$,R=J=65535&d|p<<16,N=G,C=W,O=Q,S=B,U%16==15)for(T=0;T<16;T++)h=t[T],d=65535&(u=e[T]),p=u>>>16,g=65535&h,m=h>>>16,h=t[(T+9)%16],d+=65535&(u=e[(T+9)%16]),p+=u>>>16,g+=65535&h,m+=h>>>16,l=t[(T+1)%16],d+=65535&(u=((f=e[(T+1)%16])>>>1|l<<31)^(f>>>8|l<<24)^(f>>>7|l<<25)),p+=u>>>16,g+=65535&(h=(l>>>1|f<<31)^(l>>>8|f<<24)^l>>>7),m+=h>>>16,l=t[(T+14)%16],p+=(u=((f=e[(T+14)%16])>>>19|l<<13)^(l>>>29|f<<3)^(f>>>6|l<<26))>>>16,g+=65535&(h=(l>>>19|f<<13)^(f>>>29|l<<3)^l>>>6),m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,t[T]=65535&g|m<<16,e[T]=65535&d|p<<16}d=65535&(u=S),p=u>>>16,g=65535&(h=y),m=h>>>16,h=r[0],p+=(u=n[0])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[0]=y=65535&g|m<<16,n[0]=S=65535&d|p<<16,d=65535&(u=M),p=u>>>16,g=65535&(h=v),m=h>>>16,h=r[1],p+=(u=n[1])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[1]=v=65535&g|m<<16,n[1]=M=65535&d|p<<16,d=65535&(u=P),p=u>>>16,g=65535&(h=w),m=h>>>16,h=r[2],p+=(u=n[2])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[2]=w=65535&g|m<<16,n[2]=P=65535&d|p<<16,d=65535&(u=x),p=u>>>16,g=65535&(h=b),m=h>>>16,h=r[3],p+=(u=n[3])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[3]=b=65535&g|m<<16,n[3]=x=65535&d|p<<16,d=65535&(u=R),p=u>>>16,g=65535&(h=A),m=h>>>16,h=r[4],p+=(u=n[4])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[4]=A=65535&g|m<<16,n[4]=R=65535&d|p<<16,d=65535&(u=N),p=u>>>16,g=65535&(h=_),m=h>>>16,h=r[5],p+=(u=n[5])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[5]=_=65535&g|m<<16,n[5]=N=65535&d|p<<16,d=65535&(u=C),p=u>>>16,g=65535&(h=E),m=h>>>16,h=r[6],p+=(u=n[6])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[6]=E=65535&g|m<<16,n[6]=C=65535&d|p<<16,d=65535&(u=O),p=u>>>16,g=65535&(h=I),m=h>>>16,h=r[7],p+=(u=n[7])>>>16,g+=65535&h,m+=h>>>16,m+=(g+=(p+=(d+=65535&u)>>>16)>>>16)>>>16,r[7]=I=65535&g|m<<16,n[7]=O=65535&d|p<<16,a+=128,c-=128}return a}e.hash=function(t){var e=new s;e.update(t);var r=e.digest();return e.clean(),r}},63319:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=r(78139);function n(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>8,e[r+1]=t>>>0,e}function s(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e}function o(t,e){return void 0===e&&(e=0),t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}function a(t,e){return void 0===e&&(e=0),(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}function c(t,e){return void 0===e&&(e=0),t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e]}function h(t,e){return void 0===e&&(e=0),(t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e])>>>0}function u(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>24,e[r+1]=t>>>16,e[r+2]=t>>>8,e[r+3]=t>>>0,e}function l(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e[r+2]=t>>>16,e[r+3]=t>>>24,e}function f(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),u(t/4294967296>>>0,e,r),u(t>>>0,e,r+4),e}function d(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),l(t>>>0,e,r),l(t/4294967296>>>0,e,r+4),e}e.readInt16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])<<16>>16},e.readUint16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])>>>0},e.readInt16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])<<16>>16},e.readUint16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])>>>0},e.writeUint16BE=n,e.writeInt16BE=n,e.writeUint16LE=s,e.writeInt16LE=s,e.readInt32BE=o,e.readUint32BE=a,e.readInt32LE=c,e.readUint32LE=h,e.writeUint32BE=u,e.writeInt32BE=u,e.writeUint32LE=l,e.writeInt32LE=l,e.readInt64BE=function(t,e){void 0===e&&(e=0);var r=o(t,e),i=o(t,e+4);return 4294967296*r+i-4294967296*(i>>31)},e.readUint64BE=function(t,e){return void 0===e&&(e=0),4294967296*a(t,e)+a(t,e+4)},e.readInt64LE=function(t,e){void 0===e&&(e=0);var r=c(t,e);return 4294967296*c(t,e+4)+r-4294967296*(r>>31)},e.readUint64LE=function(t,e){void 0===e&&(e=0);var r=h(t,e);return 4294967296*h(t,e+4)+r},e.writeUint64BE=f,e.writeInt64BE=f,e.writeUint64LE=d,e.writeInt64LE=d,e.readUintBE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintBE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintBE: array is too short for the given bitLength");for(var i=0,n=1,s=t/8+r-1;s>=r;s--)i+=e[s]*n,n*=256;return i},e.readUintLE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintLE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintLE: array is too short for the given bitLength");for(var i=0,n=1,s=r;s<r+t/8;s++)i+=e[s]*n,n*=256;return i},e.writeUintBE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintBE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintBE value must be an integer");for(var s=1,o=t/8+n-1;o>=n;o--)r[o]=e/s&255,s*=256;return r},e.writeUintLE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintLE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintLE value must be an integer");for(var s=1,o=n;o<n+t/8;o++)r[o]=e/s&255,s*=256;return r},e.readFloat32BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e)},e.readFloat32LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e,!0)},e.readFloat64BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e)},e.readFloat64LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e,!0)},e.writeFloat32BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t),e},e.writeFloat32LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t,!0),e},e.writeFloat64BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t),e},e.writeFloat64LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t,!0),e}},78139:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.mul=Math.imul||function(t,e){var r=65535&t,i=65535&e;return r*i+((t>>>16&65535)*i+r*(e>>>16&65535)<<16>>>0)|0},e.add=function(t,e){return t+e|0},e.sub=function(t,e){return t-e|0},e.rotl=function(t,e){return t<<e|t>>>32-e},e.rotr=function(t,e){return t<<32-e|t>>>e},e.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},e.MAX_SAFE_INTEGER=9007199254740991,e.isSafeInteger=function(t){return e.isInteger(t)&&t>=-e.MAX_SAFE_INTEGER&&t<=e.MAX_SAFE_INTEGER}},44911:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.wipe=function(t){for(var e=0;e<t.length;e++)t[e]=0;return t}},97049:(t,e,r)=>{"use strict";r.d(e,{EthereumProvider:()=>Qp});var i={};r.r(i),r.d(i,{identity:()=>lt});var n={};r.r(n),r.d(n,{base2:()=>ft});var s={};r.r(s),r.d(s,{base8:()=>dt});var o={};r.r(o),r.d(o,{base10:()=>pt});var a={};r.r(a),r.d(a,{base16:()=>gt,base16upper:()=>mt});var c={};r.r(c),r.d(c,{base32:()=>yt,base32hex:()=>At,base32hexpad:()=>Et,base32hexpadupper:()=>It,base32hexupper:()=>_t,base32pad:()=>wt,base32padupper:()=>bt,base32upper:()=>vt,base32z:()=>St});var h={};r.r(h),r.d(h,{base36:()=>Mt,base36upper:()=>Pt});var u={};r.r(u),r.d(u,{base58btc:()=>xt,base58flickr:()=>Rt});var l={};r.r(l),r.d(l,{base64:()=>Nt,base64pad:()=>Ct,base64url:()=>Ot,base64urlpad:()=>Ut});var f={};r.r(f),r.d(f,{base256emoji:()=>kt});var d={};r.r(d),r.d(d,{sha256:()=>se,sha512:()=>oe});var p={};r.r(p),r.d(p,{identity:()=>ce});var g={};r.r(g),r.d(g,{code:()=>ue,decode:()=>fe,encode:()=>le,name:()=>he});var m={};r.r(m),r.d(m,{code:()=>me,decode:()=>ve,encode:()=>ye,name:()=>ge});var y={};r.r(y),r.d(y,{identity:()=>th});var v={};r.r(v),r.d(v,{base2:()=>eh});var w={};r.r(w),r.d(w,{base8:()=>rh});var b={};r.r(b),r.d(b,{base10:()=>ih});var A={};r.r(A),r.d(A,{base16:()=>nh,base16upper:()=>sh});var _={};r.r(_),r.d(_,{base32:()=>oh,base32hex:()=>uh,base32hexpad:()=>fh,base32hexpadupper:()=>dh,base32hexupper:()=>lh,base32pad:()=>ch,base32padupper:()=>hh,base32upper:()=>ah,base32z:()=>ph});var E={};r.r(E),r.d(E,{base36:()=>gh,base36upper:()=>mh});var I={};r.r(I),r.d(I,{base58btc:()=>yh,base58flickr:()=>vh});var S={};r.r(S),r.d(S,{base64:()=>wh,base64pad:()=>bh,base64url:()=>Ah,base64urlpad:()=>_h});var M={};r.r(M),r.d(M,{base256emoji:()=>Mh});var P={};r.r(P),r.d(P,{sha256:()=>Gh,sha512:()=>Wh});var x={};r.r(x),r.d(x,{identity:()=>Yh});var R={};r.r(R),r.d(R,{code:()=>Zh,decode:()=>eu,encode:()=>tu,name:()=>Xh});var N={};r.r(N),r.d(N,{code:()=>su,decode:()=>au,encode:()=>ou,name:()=>nu});var C=r(37007),O=r.n(C),U=function(t,e,r){if(r||2===arguments.length)for(var i,n=0,s=e.length;n<s;n++)!i&&n in e||(i||(i=Array.prototype.slice.call(e,0,n)),i[n]=e[n]);return t.concat(i||Array.prototype.slice.call(e))},T=function(t,e,r){this.name=t,this.version=e,this.os=r,this.type="browser"},D=function(t){this.version=t,this.type="node",this.name="node",this.os=process.platform},B=function(t,e,r,i){this.name=t,this.version=e,this.os=r,this.bot=i,this.type="bot-device"},k=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},q=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},L=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,j=3,z=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],F=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function K(t){var e=function(t){return""!==t&&z.reduce((function(e,r){var i=r[0],n=r[1];if(e)return e;var s=n.exec(t);return!!s&&[i,s]}),!1)}(t);if(!e)return null;var r=e[0],i=e[1];if("searchbot"===r)return new k;var n=i[1]&&i[1].split(".").join("_").split("_").slice(0,3);n?n.length<j&&(n=U(U([],n,!0),function(t){for(var e=[],r=0;r<t;r++)e.push("0");return e}(j-n.length),!0)):n=[];var s=n.join("."),o=function(t){for(var e=0,r=F.length;e<r;e++){var i=F[e],n=i[0];if(i[1].exec(t))return n}return null}(t),a=L.exec(t);return a&&a[1]?new B(r,s,o,a[1]):new T(r,s,o)}var H=r(88900),V=r(38196),$=r(42063),J=r(86663),G=r(51612),W=r(16804),Q=r(18359),Y=r(50204),X=r(774);function Z(t=0){return null!=globalThis.Buffer&&null!=globalThis.Buffer.allocUnsafe?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function tt(t,e){e||(e=t.reduce(((t,e)=>t+e.length),0));const r=Z(e);let i=0;for(const e of t)r.set(e,i),i+=e.length;return r}const et=function(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),i=0;i<r.length;i++)r[i]=255;for(var n=0;n<t.length;n++){var s=t.charAt(n),o=s.charCodeAt(0);if(255!==r[o])throw new TypeError(s+" is ambiguous");r[o]=n}var a=t.length,c=t.charAt(0),h=Math.log(a)/Math.log(256),u=Math.log(256)/Math.log(a);function l(t){if("string"!=typeof t)throw new TypeError("Expected String");if(0===t.length)return new Uint8Array;var e=0;if(" "!==t[e]){for(var i=0,n=0;t[e]===c;)i++,e++;for(var s=(t.length-e)*h+1>>>0,o=new Uint8Array(s);t[e];){var u=r[t.charCodeAt(e)];if(255===u)return;for(var l=0,f=s-1;(0!==u||l<n)&&-1!==f;f--,l++)u+=a*o[f]>>>0,o[f]=u%256>>>0,u=u/256>>>0;if(0!==u)throw new Error("Non-zero carry");n=l,e++}if(" "!==t[e]){for(var d=s-n;d!==s&&0===o[d];)d++;for(var p=new Uint8Array(i+(s-d)),g=i;d!==s;)p[g++]=o[d++];return p}}}return{encode:function(e){if(e instanceof Uint8Array||(ArrayBuffer.isView(e)?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):Array.isArray(e)&&(e=Uint8Array.from(e))),!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===e.length)return"";for(var r=0,i=0,n=0,s=e.length;n!==s&&0===e[n];)n++,r++;for(var o=(s-n)*u+1>>>0,h=new Uint8Array(o);n!==s;){for(var l=e[n],f=0,d=o-1;(0!==l||f<i)&&-1!==d;d--,f++)l+=256*h[d]>>>0,h[d]=l%a>>>0,l=l/a>>>0;if(0!==l)throw new Error("Non-zero carry");i=f,n++}for(var p=o-i;p!==o&&0===h[p];)p++;for(var g=c.repeat(r);p<o;++p)g+=t.charAt(h[p]);return g},decodeUnsafe:l,decode:function(t){var r=l(t);if(r)return r;throw new Error(`Non-${e} character`)}}},rt=(new Uint8Array(0),t=>{if(t instanceof Uint8Array&&"Uint8Array"===t.constructor.name)return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")});class it{constructor(t,e,r){this.name=t,this.prefix=e,this.baseEncode=r}encode(t){if(t instanceof Uint8Array)return`${this.prefix}${this.baseEncode(t)}`;throw Error("Unknown type, must be binary type")}}class nt{constructor(t,e,r){if(this.name=t,this.prefix=e,void 0===e.codePointAt(0))throw new Error("Invalid prefix character");this.prefixCodePoint=e.codePointAt(0),this.baseDecode=r}decode(t){if("string"==typeof t){if(t.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(t)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(t.slice(this.prefix.length))}throw Error("Can only multibase decode strings")}or(t){return ot(this,t)}}class st{constructor(t){this.decoders=t}or(t){return ot(this,t)}decode(t){const e=t[0],r=this.decoders[e];if(r)return r.decode(t);throw RangeError(`Unable to decode multibase string ${JSON.stringify(t)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const ot=(t,e)=>new st({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class at{constructor(t,e,r,i){this.name=t,this.prefix=e,this.baseEncode=r,this.baseDecode=i,this.encoder=new it(t,e,r),this.decoder=new nt(t,e,i)}encode(t){return this.encoder.encode(t)}decode(t){return this.decoder.decode(t)}}const ct=({name:t,prefix:e,encode:r,decode:i})=>new at(t,e,r,i),ht=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:n}=et(r,e);return ct({prefix:t,name:e,encode:i,decode:t=>rt(n(t))})},ut=({name:t,prefix:e,bitsPerChar:r,alphabet:i})=>ct({prefix:e,name:t,encode:t=>((t,e,r)=>{const i="="===e[e.length-1],n=(1<<r)-1;let s="",o=0,a=0;for(let i=0;i<t.length;++i)for(a=a<<8|t[i],o+=8;o>r;)o-=r,s+=e[n&a>>o];if(o&&(s+=e[n&a<<r-o]),i)for(;s.length*r&7;)s+="=";return s})(t,i,r),decode:e=>((t,e,r,i)=>{const n={};for(let t=0;t<e.length;++t)n[e[t]]=t;let s=t.length;for(;"="===t[s-1];)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,h=0;for(let e=0;e<s;++e){const s=n[t[e]];if(void 0===s)throw new SyntaxError(`Non-${i} character`);c=c<<r|s,a+=r,a>=8&&(a-=8,o[h++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o})(e,i,r,t)}),lt=ct({prefix:"\0",name:"identity",encode:t=>(t=>(new TextDecoder).decode(t))(t),decode:t=>(t=>(new TextEncoder).encode(t))(t)}),ft=ut({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1}),dt=ut({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3}),pt=ht({prefix:"9",name:"base10",alphabet:"0123456789"}),gt=ut({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),mt=ut({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4}),yt=ut({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),vt=ut({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),wt=ut({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),bt=ut({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),At=ut({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),_t=ut({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),Et=ut({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),It=ut({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),St=ut({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5}),Mt=ht({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),Pt=ht({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"}),xt=ht({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),Rt=ht({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"}),Nt=ut({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),Ct=ut({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),Ot=ut({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),Ut=ut({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6}),Tt=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),Dt=Tt.reduce(((t,e,r)=>(t[r]=e,t)),[]),Bt=Tt.reduce(((t,e,r)=>(t[e.codePointAt(0)]=r,t)),[]),kt=ct({prefix:"🚀",name:"base256emoji",encode:function(t){return t.reduce(((t,e)=>t+Dt[e]),"")},decode:function(t){const e=[];for(const r of t){const t=Bt[r.codePointAt(0)];if(void 0===t)throw new Error(`Non-base256emoji character: ${r}`);e.push(t)}return new Uint8Array(e)}});var qt=128,Lt=-128,jt=Math.pow(2,31),zt=Math.pow(2,7),Ft=Math.pow(2,14),Kt=Math.pow(2,21),Ht=Math.pow(2,28),Vt=Math.pow(2,35),$t=Math.pow(2,42),Jt=Math.pow(2,49),Gt=Math.pow(2,56),Wt=Math.pow(2,63);const Qt=function t(e,r,i){r=r||[];for(var n=i=i||0;e>=jt;)r[i++]=255&e|qt,e/=128;for(;e&Lt;)r[i++]=255&e|qt,e>>>=7;return r[i]=0|e,t.bytes=i-n+1,r},Yt=function(t){return t<zt?1:t<Ft?2:t<Kt?3:t<Ht?4:t<Vt?5:t<$t?6:t<Jt?7:t<Gt?8:t<Wt?9:10},Xt=(t,e,r=0)=>(Qt(t,e,r),e),Zt=t=>Yt(t),te=(t,e)=>{const r=e.byteLength,i=Zt(t),n=i+Zt(r),s=new Uint8Array(n+r);return Xt(t,s,0),Xt(r,s,i),s.set(e,n),new ee(t,r,e,s)};class ee{constructor(t,e,r,i){this.code=t,this.size=e,this.digest=r,this.bytes=i}}const re=({name:t,code:e,encode:r})=>new ie(t,e,r);class ie{constructor(t,e,r){this.name=t,this.code=e,this.encode=r}digest(t){if(t instanceof Uint8Array){const e=this.encode(t);return e instanceof Uint8Array?te(this.code,e):e.then((t=>te(this.code,t)))}throw Error("Unknown type, must be binary type")}}const ne=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),se=re({name:"sha2-256",code:18,encode:ne("SHA-256")}),oe=re({name:"sha2-512",code:19,encode:ne("SHA-512")}),ae=rt,ce={code:0,name:"identity",encode:ae,digest:t=>te(0,ae(t))},he="raw",ue=85,le=t=>rt(t),fe=t=>rt(t),de=new TextEncoder,pe=new TextDecoder,ge="json",me=512,ye=t=>de.encode(JSON.stringify(t)),ve=t=>JSON.parse(pe.decode(t));Symbol.toStringTag,Symbol.for("nodejs.util.inspect.custom"),Symbol.for("@ipld/js-cid/CID");const we={...i,...n,...s,...o,...a,...c,...h,...u,...l,...f};function be(t,e,r,i){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:i}}}const Ae=be("utf8","u",(t=>"u"+new TextDecoder("utf8").decode(t)),(t=>(new TextEncoder).encode(t.substring(1)))),_e=be("ascii","a",(t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}),(t=>{const e=Z((t=t.substring(1)).length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e})),Ee={utf8:Ae,"utf-8":Ae,hex:we.base16,latin1:_e,ascii:_e,binary:_e,...we};function Ie(t,e="utf8"){const r=Ee[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return"utf8"!==e&&"utf-8"!==e||null==globalThis.Buffer||null==globalThis.Buffer.from?r.decoder.decode(`${r.prefix}${t}`):globalThis.Buffer.from(t,"utf8")}function Se(t,e="utf8"){const r=Ee[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return"utf8"!==e&&"utf-8"!==e||null==globalThis.Buffer||null==globalThis.Buffer.from?r.encoder.encode(t).substring(1):globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8")}var Me=r(43228);const Pe=":";function xe(t){const[e,r]=t.split(Pe);return{namespace:e,reference:r}}function Re(t,e=[]){const r=[];return Object.keys(t).forEach((i=>{if(e.length&&!e.includes(i))return;const n=t[i];r.push(...n.accounts)})),r}function Ne(t,e){return t.includes(":")?[t]:e.chains||[]}var Ce=Object.defineProperty,Oe=Object.getOwnPropertySymbols,Ue=Object.prototype.hasOwnProperty,Te=Object.prototype.propertyIsEnumerable,De=(t,e,r)=>e in t?Ce(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Be=(t,e)=>{for(var r in e||(e={}))Ue.call(e,r)&&De(t,r,e[r]);if(Oe)for(var r of Oe(e))Te.call(e,r)&&De(t,r,e[r]);return t};const ke="ReactNative",qe={reactNative:"react-native",node:"node",browser:"browser",unknown:"unknown"},Le="js";function je(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}function ze(){return!(0,V.getDocument)()&&!!(0,V.getNavigator)()&&navigator.product===ke}function Fe(){return!je()&&!!(0,V.getNavigator)()&&!!(0,V.getDocument)()}function Ke(){return ze()?qe.reactNative:je()?qe.node:Fe()?qe.browser:qe.unknown}function He(t,e,i){const n=function(){if(Ke()===qe.reactNative&&typeof r.g<"u"&&typeof(null==r.g?void 0:r.g.Platform)<"u"){const{OS:t,Version:e}=r.g.Platform;return[t,e].join("-")}const t=e?K(e):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new q:"undefined"!=typeof navigator?K(navigator.userAgent):"undefined"!=typeof process&&process.version?new D(process.version.slice(1)):null;var e;if(null===t)return"unknown";const i=t.os?t.os.replace(" ","").toLowerCase():"unknown";return"browser"===t.type?[i,t.name,t.version].join("-"):[i,t.version].join("-")}(),s=function(){var t;const e=Ke();return e===qe.browser?[e,(null==(t=(0,V.getLocation)())?void 0:t.host)||"unknown"].join(":"):e}();return[[t,e].join("-"),[Le,i].join("-"),n,s].join("/")}function Ve(t,e){return t.filter((t=>e.includes(t))).length===t.length}function $e(t){return Object.fromEntries(t.entries())}function Je(t){return new Map(Object.entries(t))}function Ge(t=H.FIVE_MINUTES,e){const r=(0,H.toMiliseconds)(t||H.FIVE_MINUTES);let i,n,s;return{resolve:t=>{s&&i&&(clearTimeout(s),i(t))},reject:t=>{s&&n&&(clearTimeout(s),n(t))},done:()=>new Promise(((t,o)=>{s=setTimeout((()=>{o(new Error(e))}),r),i=t,n=o}))}}function We(t,e,r){return new Promise((async(i,n)=>{const s=setTimeout((()=>n(new Error(r))),e);try{i(await t)}catch(t){n(t)}clearTimeout(s)}))}function Qe(t,e){if("string"==typeof e&&e.startsWith(`${t}:`))return e;if("topic"===t.toLowerCase()){if("string"!=typeof e)throw new Error('Value must be "string" for expirer target type: topic');return`topic:${e}`}if("id"===t.toLowerCase()){if("number"!=typeof e)throw new Error('Value must be "number" for expirer target type: id');return`id:${e}`}throw new Error(`Unknown expirer target type: ${t}`)}function Ye(t){const[e,r]=t.split(":"),i={id:void 0,topic:void 0};if("topic"===e&&"string"==typeof r)i.topic=r;else{if("id"!==e||!Number.isInteger(Number(r)))throw new Error(`Invalid target, expected id:number or topic:string, got ${e}:${r}`);i.id=Number(r)}return i}function Xe(t,e){return(0,H.fromMiliseconds)((e||Date.now())+(0,H.toMiliseconds)(t))}function Ze(t){return Date.now()>=(0,H.toMiliseconds)(t)}function tr(t,e){return`${t}${e?`:${e}`:""}`}function er(t=[],e=[]){return[...new Set([...t,...e])]}var rr,ir=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof r.g<"u"?r.g:typeof self<"u"?self:{},nr={exports:{}};rr=nr,function(){var t="input is invalid type",e="object"==typeof window,r=e?window:{};r.JS_SHA3_NO_WINDOW&&(e=!1);var i=!e&&"object"==typeof self;!r.JS_SHA3_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node?r=ir:i&&(r=self);var n=!r.JS_SHA3_NO_COMMON_JS&&rr.exports,s=!r.JS_SHA3_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",o="0123456789abcdef".split(""),a=[4,1024,262144,67108864],c=[0,8,16,24],h=[1,0,32898,0,32906,2147483648,2147516416,2147483648,32907,0,2147483649,0,2147516545,2147483648,32777,2147483648,138,0,136,0,2147516425,0,2147483658,0,2147516555,0,139,2147483648,32905,2147483648,32771,2147483648,32770,2147483648,128,2147483648,32778,0,2147483658,2147483648,2147516545,2147483648,32896,2147483648,2147483649,0,2147516424,2147483648],u=[224,256,384,512],l=[128,256],f=["hex","buffer","arrayBuffer","array","digest"],d={128:168,256:136};(r.JS_SHA3_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),s&&(r.JS_SHA3_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});for(var p=function(t,e,r){return function(i){return new R(t,e,t).update(i)[r]()}},g=function(t,e,r){return function(i,n){return new R(t,e,n).update(i)[r]()}},m=function(t,e,r){return function(e,i,n,s){return A["cshake"+t].update(e,i,n,s)[r]()}},y=function(t,e,r){return function(e,i,n,s){return A["kmac"+t].update(e,i,n,s)[r]()}},v=function(t,e,r,i){for(var n=0;n<f.length;++n){var s=f[n];t[s]=e(r,i,s)}return t},w=function(t,e){var r=p(t,e,"hex");return r.create=function(){return new R(t,e,t)},r.update=function(t){return r.create().update(t)},v(r,p,t,e)},b=[{name:"keccak",padding:[1,256,65536,16777216],bits:u,createMethod:w},{name:"sha3",padding:[6,1536,393216,100663296],bits:u,createMethod:w},{name:"shake",padding:[31,7936,2031616,520093696],bits:l,createMethod:function(t,e){var r=g(t,e,"hex");return r.create=function(r){return new R(t,e,r)},r.update=function(t,e){return r.create(e).update(t)},v(r,g,t,e)}},{name:"cshake",padding:a,bits:l,createMethod:function(t,e){var r=d[t],i=m(t,0,"hex");return i.create=function(i,n,s){return n||s?new R(t,e,i).bytepad([n,s],r):A["shake"+t].create(i)},i.update=function(t,e,r,n){return i.create(e,r,n).update(t)},v(i,m,t,e)}},{name:"kmac",padding:a,bits:l,createMethod:function(t,e){var r=d[t],i=y(t,0,"hex");return i.create=function(i,n,s){return new N(t,e,n).bytepad(["KMAC",s],r).bytepad([i],r)},i.update=function(t,e,r,n){return i.create(t,r,n).update(e)},v(i,y,t,e)}}],A={},_=[],E=0;E<b.length;++E)for(var I=b[E],S=I.bits,M=0;M<S.length;++M){var P=I.name+"_"+S[M];if(_.push(P),A[P]=I.createMethod(S[M],I.padding),"sha3"!==I.name){var x=I.name+S[M];_.push(x),A[x]=A[P]}}function R(t,e,r){this.blocks=[],this.s=[],this.padding=e,this.outputBits=r,this.reset=!0,this.finalized=!1,this.block=0,this.start=0,this.blockCount=1600-(t<<1)>>5,this.byteCount=this.blockCount<<2,this.outputBlocks=r>>5,this.extraBytes=(31&r)>>3;for(var i=0;i<50;++i)this.s[i]=0}function N(t,e,r){R.call(this,t,e,r)}R.prototype.update=function(e){if(this.finalized)throw new Error("finalize already called");var r,i=typeof e;if("string"!==i){if("object"!==i)throw new Error(t);if(null===e)throw new Error(t);if(s&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!(Array.isArray(e)||s&&ArrayBuffer.isView(e)))throw new Error(t);r=!0}for(var n,o,a=this.blocks,h=this.byteCount,u=e.length,l=this.blockCount,f=0,d=this.s;f<u;){if(this.reset)for(this.reset=!1,a[0]=this.block,n=1;n<l+1;++n)a[n]=0;if(r)for(n=this.start;f<u&&n<h;++f)a[n>>2]|=e[f]<<c[3&n++];else for(n=this.start;f<u&&n<h;++f)(o=e.charCodeAt(f))<128?a[n>>2]|=o<<c[3&n++]:o<2048?(a[n>>2]|=(192|o>>6)<<c[3&n++],a[n>>2]|=(128|63&o)<<c[3&n++]):o<55296||o>=57344?(a[n>>2]|=(224|o>>12)<<c[3&n++],a[n>>2]|=(128|o>>6&63)<<c[3&n++],a[n>>2]|=(128|63&o)<<c[3&n++]):(o=65536+((1023&o)<<10|1023&e.charCodeAt(++f)),a[n>>2]|=(240|o>>18)<<c[3&n++],a[n>>2]|=(128|o>>12&63)<<c[3&n++],a[n>>2]|=(128|o>>6&63)<<c[3&n++],a[n>>2]|=(128|63&o)<<c[3&n++]);if(this.lastByteIndex=n,n>=h){for(this.start=n-h,this.block=a[l],n=0;n<l;++n)d[n]^=a[n];C(d),this.reset=!0}else this.start=n}return this},R.prototype.encode=function(t,e){var r=255&t,i=1,n=[r];for(r=255&(t>>=8);r>0;)n.unshift(r),r=255&(t>>=8),++i;return e?n.push(i):n.unshift(i),this.update(n),n.length},R.prototype.encodeString=function(e){var r,i=typeof e;if("string"!==i){if("object"!==i)throw new Error(t);if(null===e)throw new Error(t);if(s&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!(Array.isArray(e)||s&&ArrayBuffer.isView(e)))throw new Error(t);r=!0}var n=0,o=e.length;if(r)n=o;else for(var a=0;a<e.length;++a){var c=e.charCodeAt(a);c<128?n+=1:c<2048?n+=2:c<55296||c>=57344?n+=3:(c=65536+((1023&c)<<10|1023&e.charCodeAt(++a)),n+=4)}return n+=this.encode(8*n),this.update(e),n},R.prototype.bytepad=function(t,e){for(var r=this.encode(e),i=0;i<t.length;++i)r+=this.encodeString(t[i]);var n=e-r%e,s=[];return s.length=n,this.update(s),this},R.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex,r=this.blockCount,i=this.s;if(t[e>>2]|=this.padding[3&e],this.lastByteIndex===this.byteCount)for(t[0]=t[r],e=1;e<r+1;++e)t[e]=0;for(t[r-1]|=2147483648,e=0;e<r;++e)i[e]^=t[e];C(i)}},R.prototype.toString=R.prototype.hex=function(){this.finalize();for(var t,e=this.blockCount,r=this.s,i=this.outputBlocks,n=this.extraBytes,s=0,a=0,c="";a<i;){for(s=0;s<e&&a<i;++s,++a)t=r[s],c+=o[t>>4&15]+o[15&t]+o[t>>12&15]+o[t>>8&15]+o[t>>20&15]+o[t>>16&15]+o[t>>28&15]+o[t>>24&15];a%e==0&&(C(r),s=0)}return n&&(t=r[s],c+=o[t>>4&15]+o[15&t],n>1&&(c+=o[t>>12&15]+o[t>>8&15]),n>2&&(c+=o[t>>20&15]+o[t>>16&15])),c},R.prototype.arrayBuffer=function(){this.finalize();var t,e=this.blockCount,r=this.s,i=this.outputBlocks,n=this.extraBytes,s=0,o=0,a=this.outputBits>>3;t=n?new ArrayBuffer(i+1<<2):new ArrayBuffer(a);for(var c=new Uint32Array(t);o<i;){for(s=0;s<e&&o<i;++s,++o)c[o]=r[s];o%e==0&&C(r)}return n&&(c[s]=r[s],t=t.slice(0,a)),t},R.prototype.buffer=R.prototype.arrayBuffer,R.prototype.digest=R.prototype.array=function(){this.finalize();for(var t,e,r=this.blockCount,i=this.s,n=this.outputBlocks,s=this.extraBytes,o=0,a=0,c=[];a<n;){for(o=0;o<r&&a<n;++o,++a)t=a<<2,e=i[o],c[t]=255&e,c[t+1]=e>>8&255,c[t+2]=e>>16&255,c[t+3]=e>>24&255;a%r==0&&C(i)}return s&&(t=a<<2,e=i[o],c[t]=255&e,s>1&&(c[t+1]=e>>8&255),s>2&&(c[t+2]=e>>16&255)),c},N.prototype=new R,N.prototype.finalize=function(){return this.encode(this.outputBits,!0),R.prototype.finalize.call(this)};var C=function(t){var e,r,i,n,s,o,a,c,u,l,f,d,p,g,m,y,v,w,b,A,_,E,I,S,M,P,x,R,N,C,O,U,T,D,B,k,q,L,j,z,F,K,H,V,$,J,G,W,Q,Y,X,Z,tt,et,rt,it,nt,st,ot,at,ct,ht,ut;for(i=0;i<48;i+=2)n=t[0]^t[10]^t[20]^t[30]^t[40],s=t[1]^t[11]^t[21]^t[31]^t[41],o=t[2]^t[12]^t[22]^t[32]^t[42],a=t[3]^t[13]^t[23]^t[33]^t[43],c=t[4]^t[14]^t[24]^t[34]^t[44],u=t[5]^t[15]^t[25]^t[35]^t[45],l=t[6]^t[16]^t[26]^t[36]^t[46],f=t[7]^t[17]^t[27]^t[37]^t[47],e=(d=t[8]^t[18]^t[28]^t[38]^t[48])^(o<<1|a>>>31),r=(p=t[9]^t[19]^t[29]^t[39]^t[49])^(a<<1|o>>>31),t[0]^=e,t[1]^=r,t[10]^=e,t[11]^=r,t[20]^=e,t[21]^=r,t[30]^=e,t[31]^=r,t[40]^=e,t[41]^=r,e=n^(c<<1|u>>>31),r=s^(u<<1|c>>>31),t[2]^=e,t[3]^=r,t[12]^=e,t[13]^=r,t[22]^=e,t[23]^=r,t[32]^=e,t[33]^=r,t[42]^=e,t[43]^=r,e=o^(l<<1|f>>>31),r=a^(f<<1|l>>>31),t[4]^=e,t[5]^=r,t[14]^=e,t[15]^=r,t[24]^=e,t[25]^=r,t[34]^=e,t[35]^=r,t[44]^=e,t[45]^=r,e=c^(d<<1|p>>>31),r=u^(p<<1|d>>>31),t[6]^=e,t[7]^=r,t[16]^=e,t[17]^=r,t[26]^=e,t[27]^=r,t[36]^=e,t[37]^=r,t[46]^=e,t[47]^=r,e=l^(n<<1|s>>>31),r=f^(s<<1|n>>>31),t[8]^=e,t[9]^=r,t[18]^=e,t[19]^=r,t[28]^=e,t[29]^=r,t[38]^=e,t[39]^=r,t[48]^=e,t[49]^=r,g=t[0],m=t[1],J=t[11]<<4|t[10]>>>28,G=t[10]<<4|t[11]>>>28,R=t[20]<<3|t[21]>>>29,N=t[21]<<3|t[20]>>>29,at=t[31]<<9|t[30]>>>23,ct=t[30]<<9|t[31]>>>23,K=t[40]<<18|t[41]>>>14,H=t[41]<<18|t[40]>>>14,D=t[2]<<1|t[3]>>>31,B=t[3]<<1|t[2]>>>31,y=t[13]<<12|t[12]>>>20,v=t[12]<<12|t[13]>>>20,W=t[22]<<10|t[23]>>>22,Q=t[23]<<10|t[22]>>>22,C=t[33]<<13|t[32]>>>19,O=t[32]<<13|t[33]>>>19,ht=t[42]<<2|t[43]>>>30,ut=t[43]<<2|t[42]>>>30,et=t[5]<<30|t[4]>>>2,rt=t[4]<<30|t[5]>>>2,k=t[14]<<6|t[15]>>>26,q=t[15]<<6|t[14]>>>26,w=t[25]<<11|t[24]>>>21,b=t[24]<<11|t[25]>>>21,Y=t[34]<<15|t[35]>>>17,X=t[35]<<15|t[34]>>>17,U=t[45]<<29|t[44]>>>3,T=t[44]<<29|t[45]>>>3,S=t[6]<<28|t[7]>>>4,M=t[7]<<28|t[6]>>>4,it=t[17]<<23|t[16]>>>9,nt=t[16]<<23|t[17]>>>9,L=t[26]<<25|t[27]>>>7,j=t[27]<<25|t[26]>>>7,A=t[36]<<21|t[37]>>>11,_=t[37]<<21|t[36]>>>11,Z=t[47]<<24|t[46]>>>8,tt=t[46]<<24|t[47]>>>8,V=t[8]<<27|t[9]>>>5,$=t[9]<<27|t[8]>>>5,P=t[18]<<20|t[19]>>>12,x=t[19]<<20|t[18]>>>12,st=t[29]<<7|t[28]>>>25,ot=t[28]<<7|t[29]>>>25,z=t[38]<<8|t[39]>>>24,F=t[39]<<8|t[38]>>>24,E=t[48]<<14|t[49]>>>18,I=t[49]<<14|t[48]>>>18,t[0]=g^~y&w,t[1]=m^~v&b,t[10]=S^~P&R,t[11]=M^~x&N,t[20]=D^~k&L,t[21]=B^~q&j,t[30]=V^~J&W,t[31]=$^~G&Q,t[40]=et^~it&st,t[41]=rt^~nt&ot,t[2]=y^~w&A,t[3]=v^~b&_,t[12]=P^~R&C,t[13]=x^~N&O,t[22]=k^~L&z,t[23]=q^~j&F,t[32]=J^~W&Y,t[33]=G^~Q&X,t[42]=it^~st&at,t[43]=nt^~ot&ct,t[4]=w^~A&E,t[5]=b^~_&I,t[14]=R^~C&U,t[15]=N^~O&T,t[24]=L^~z&K,t[25]=j^~F&H,t[34]=W^~Y&Z,t[35]=Q^~X&tt,t[44]=st^~at&ht,t[45]=ot^~ct&ut,t[6]=A^~E&g,t[7]=_^~I&m,t[16]=C^~U&S,t[17]=O^~T&M,t[26]=z^~K&D,t[27]=F^~H&B,t[36]=Y^~Z&V,t[37]=X^~tt&$,t[46]=at^~ht&et,t[47]=ct^~ut&rt,t[8]=E^~g&y,t[9]=I^~m&v,t[18]=U^~S&P,t[19]=T^~M&x,t[28]=K^~D&k,t[29]=H^~B&q,t[38]=Z^~V&J,t[39]=tt^~$&G,t[48]=ht^~et&it,t[49]=ut^~rt&nt,t[0]^=h[i],t[1]^=h[i+1]};if(n)rr.exports=A;else for(E=0;E<_.length;++E)r[_[E]]=A[_[E]]}();var sr=nr.exports;let or=!1,ar=!1;const cr={debug:1,default:2,info:2,warning:3,error:4,off:5};let hr=cr.default,ur=null;const lr=function(){try{const t=[];if(["NFD","NFC","NFKD","NFKC"].forEach((e=>{try{if("test"!=="test".normalize(e))throw new Error("bad normalize")}catch{t.push(e)}})),t.length)throw new Error("missing "+t.join(", "));if(String.fromCharCode(233).normalize("NFD")!==String.fromCharCode(101,769))throw new Error("broken implementation")}catch(t){return t.message}return null}();var fr,dr;!function(t){t.DEBUG="DEBUG",t.INFO="INFO",t.WARNING="WARNING",t.ERROR="ERROR",t.OFF="OFF"}(fr||(fr={})),function(t){t.UNKNOWN_ERROR="UNKNOWN_ERROR",t.NOT_IMPLEMENTED="NOT_IMPLEMENTED",t.UNSUPPORTED_OPERATION="UNSUPPORTED_OPERATION",t.NETWORK_ERROR="NETWORK_ERROR",t.SERVER_ERROR="SERVER_ERROR",t.TIMEOUT="TIMEOUT",t.BUFFER_OVERRUN="BUFFER_OVERRUN",t.NUMERIC_FAULT="NUMERIC_FAULT",t.MISSING_NEW="MISSING_NEW",t.INVALID_ARGUMENT="INVALID_ARGUMENT",t.MISSING_ARGUMENT="MISSING_ARGUMENT",t.UNEXPECTED_ARGUMENT="UNEXPECTED_ARGUMENT",t.CALL_EXCEPTION="CALL_EXCEPTION",t.INSUFFICIENT_FUNDS="INSUFFICIENT_FUNDS",t.NONCE_EXPIRED="NONCE_EXPIRED",t.REPLACEMENT_UNDERPRICED="REPLACEMENT_UNDERPRICED",t.UNPREDICTABLE_GAS_LIMIT="UNPREDICTABLE_GAS_LIMIT",t.TRANSACTION_REPLACED="TRANSACTION_REPLACED",t.ACTION_REJECTED="ACTION_REJECTED"}(dr||(dr={}));const pr="0123456789abcdef";class gr{constructor(t){Object.defineProperty(this,"version",{enumerable:!0,value:t,writable:!1})}_log(t,e){const r=t.toLowerCase();null==cr[r]&&this.throwArgumentError("invalid log level name","logLevel",t),!(hr>cr[r])&&console.log.apply(console,e)}debug(...t){this._log(gr.levels.DEBUG,t)}info(...t){this._log(gr.levels.INFO,t)}warn(...t){this._log(gr.levels.WARNING,t)}makeError(t,e,r){if(ar)return this.makeError("censored error",e,{});e||(e=gr.errors.UNKNOWN_ERROR),r||(r={});const i=[];Object.keys(r).forEach((t=>{const e=r[t];try{if(e instanceof Uint8Array){let r="";for(let t=0;t<e.length;t++)r+=pr[e[t]>>4],r+=pr[15&e[t]];i.push(t+"=Uint8Array(0x"+r+")")}else i.push(t+"="+JSON.stringify(e))}catch{i.push(t+"="+JSON.stringify(r[t].toString()))}})),i.push(`code=${e}`),i.push(`version=${this.version}`);const n=t;let s="";switch(e){case dr.NUMERIC_FAULT:{s="NUMERIC_FAULT";const e=t;switch(e){case"overflow":case"underflow":case"division-by-zero":s+="-"+e;break;case"negative-power":case"negative-width":s+="-unsupported";break;case"unbound-bitwise-result":s+="-unbound-result"}break}case dr.CALL_EXCEPTION:case dr.INSUFFICIENT_FUNDS:case dr.MISSING_NEW:case dr.NONCE_EXPIRED:case dr.REPLACEMENT_UNDERPRICED:case dr.TRANSACTION_REPLACED:case dr.UNPREDICTABLE_GAS_LIMIT:s=e}s&&(t+=" [ See: https://links.ethers.org/v5-errors-"+s+" ]"),i.length&&(t+=" ("+i.join(", ")+")");const o=new Error(t);return o.reason=n,o.code=e,Object.keys(r).forEach((function(t){o[t]=r[t]})),o}throwError(t,e,r){throw this.makeError(t,e,r)}throwArgumentError(t,e,r){return this.throwError(t,gr.errors.INVALID_ARGUMENT,{argument:e,value:r})}assert(t,e,r,i){t||this.throwError(e,r,i)}assertArgument(t,e,r,i){t||this.throwArgumentError(e,r,i)}checkNormalize(t){lr&&this.throwError("platform missing String.prototype.normalize",gr.errors.UNSUPPORTED_OPERATION,{operation:"String.prototype.normalize",form:lr})}checkSafeUint53(t,e){"number"==typeof t&&(null==e&&(e="value not safe"),(t<0||t>=9007199254740991)&&this.throwError(e,gr.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"out-of-safe-range",value:t}),t%1&&this.throwError(e,gr.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"non-integer",value:t}))}checkArgumentCount(t,e,r){r=r?": "+r:"",t<e&&this.throwError("missing argument"+r,gr.errors.MISSING_ARGUMENT,{count:t,expectedCount:e}),t>e&&this.throwError("too many arguments"+r,gr.errors.UNEXPECTED_ARGUMENT,{count:t,expectedCount:e})}checkNew(t,e){(t===Object||null==t)&&this.throwError("missing new",gr.errors.MISSING_NEW,{name:e.name})}checkAbstract(t,e){t===e?this.throwError("cannot instantiate abstract class "+JSON.stringify(e.name)+" directly; use a sub-class",gr.errors.UNSUPPORTED_OPERATION,{name:t.name,operation:"new"}):(t===Object||null==t)&&this.throwError("missing new",gr.errors.MISSING_NEW,{name:e.name})}static globalLogger(){return ur||(ur=new gr("logger/5.7.0")),ur}static setCensorship(t,e){if(!t&&e&&this.globalLogger().throwError("cannot permanently disable censorship",gr.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"}),or){if(!t)return;this.globalLogger().throwError("error censorship permanent",gr.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"})}ar=!!t,or=!!e}static setLogLevel(t){const e=cr[t.toLowerCase()];null!=e?hr=e:gr.globalLogger().warn("invalid log level - "+t)}static from(t){return new gr(t)}}gr.errors=dr,gr.levels=fr;const mr=new gr("bytes/5.7.0");function yr(t){return!!t.toHexString}function vr(t){return t.slice||(t.slice=function(){const e=Array.prototype.slice.call(arguments);return vr(new Uint8Array(Array.prototype.slice.apply(t,e)))}),t}function wr(t){return"number"==typeof t&&t==t&&t%1==0}function br(t){if(null==t)return!1;if(t.constructor===Uint8Array)return!0;if("string"==typeof t||!wr(t.length)||t.length<0)return!1;for(let e=0;e<t.length;e++){const r=t[e];if(!wr(r)||r<0||r>=256)return!1}return!0}function Ar(t,e){if(e||(e={}),"number"==typeof t){mr.checkSafeUint53(t,"invalid arrayify value");const e=[];for(;t;)e.unshift(255&t),t=parseInt(String(t/256));return 0===e.length&&e.push(0),vr(new Uint8Array(e))}if(e.allowMissingPrefix&&"string"==typeof t&&"0x"!==t.substring(0,2)&&(t="0x"+t),yr(t)&&(t=t.toHexString()),_r(t)){let r=t.substring(2);r.length%2&&("left"===e.hexPad?r="0"+r:"right"===e.hexPad?r+="0":mr.throwArgumentError("hex data is odd-length","value",t));const i=[];for(let t=0;t<r.length;t+=2)i.push(parseInt(r.substring(t,t+2),16));return vr(new Uint8Array(i))}return br(t)?vr(new Uint8Array(t)):mr.throwArgumentError("invalid arrayify value","value",t)}function _r(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/)||e&&t.length!==2+2*e)}const Er="0123456789abcdef";function Ir(t,e){if(e||(e={}),"number"==typeof t){mr.checkSafeUint53(t,"invalid hexlify value");let e="";for(;t;)e=Er[15&t]+e,t=Math.floor(t/16);return e.length?(e.length%2&&(e="0"+e),"0x"+e):"0x00"}if("bigint"==typeof t)return(t=t.toString(16)).length%2?"0x0"+t:"0x"+t;if(e.allowMissingPrefix&&"string"==typeof t&&"0x"!==t.substring(0,2)&&(t="0x"+t),yr(t))return t.toHexString();if(_r(t))return t.length%2&&("left"===e.hexPad?t="0x0"+t.substring(2):"right"===e.hexPad?t+="0":mr.throwArgumentError("hex data is odd-length","value",t)),t.toLowerCase();if(br(t)){let e="0x";for(let r=0;r<t.length;r++){let i=t[r];e+=Er[(240&i)>>4]+Er[15&i]}return e}return mr.throwArgumentError("invalid hexlify value","value",t)}function Sr(t,e,r){return"string"!=typeof t?t=Ir(t):(!_r(t)||t.length%2)&&mr.throwArgumentError("invalid hexData","value",t),e=2+2*e,null!=r?"0x"+t.substring(e,2+2*r):"0x"+t.substring(e)}function Mr(t,e){for("string"!=typeof t?t=Ir(t):_r(t)||mr.throwArgumentError("invalid hex string","value",t),t.length>2*e+2&&mr.throwArgumentError("value out of range","value",arguments[1]);t.length<2*e+2;)t="0x0"+t.substring(2);return t}function Pr(t){const e={r:"0x",s:"0x",_vs:"0x",recoveryParam:0,v:0,yParityAndS:"0x",compact:"0x"};if(function(t){return _r(t)&&!(t.length%2)||br(t)}(t)){let r=Ar(t);64===r.length?(e.v=27+(r[32]>>7),r[32]&=127,e.r=Ir(r.slice(0,32)),e.s=Ir(r.slice(32,64))):65===r.length?(e.r=Ir(r.slice(0,32)),e.s=Ir(r.slice(32,64)),e.v=r[64]):mr.throwArgumentError("invalid signature string","signature",t),e.v<27&&(0===e.v||1===e.v?e.v+=27:mr.throwArgumentError("signature invalid v byte","signature",t)),e.recoveryParam=1-e.v%2,e.recoveryParam&&(r[32]|=128),e._vs=Ir(r.slice(32,64))}else{if(e.r=t.r,e.s=t.s,e.v=t.v,e.recoveryParam=t.recoveryParam,e._vs=t._vs,null!=e._vs){const r=function(t,e){(t=Ar(t)).length>e&&mr.throwArgumentError("value out of range","value",arguments[0]);const r=new Uint8Array(e);return r.set(t,e-t.length),vr(r)}(Ar(e._vs),32);e._vs=Ir(r);const i=r[0]>=128?1:0;null==e.recoveryParam?e.recoveryParam=i:e.recoveryParam!==i&&mr.throwArgumentError("signature recoveryParam mismatch _vs","signature",t),r[0]&=127;const n=Ir(r);null==e.s?e.s=n:e.s!==n&&mr.throwArgumentError("signature v mismatch _vs","signature",t)}if(null==e.recoveryParam)null==e.v?mr.throwArgumentError("signature missing v and recoveryParam","signature",t):0===e.v||1===e.v?e.recoveryParam=e.v:e.recoveryParam=1-e.v%2;else if(null==e.v)e.v=27+e.recoveryParam;else{const r=0===e.v||1===e.v?e.v:1-e.v%2;e.recoveryParam!==r&&mr.throwArgumentError("signature recoveryParam mismatch v","signature",t)}null!=e.r&&_r(e.r)?e.r=Mr(e.r,32):mr.throwArgumentError("signature missing or invalid r","signature",t),null!=e.s&&_r(e.s)?e.s=Mr(e.s,32):mr.throwArgumentError("signature missing or invalid s","signature",t);const r=Ar(e.s);r[0]>=128&&mr.throwArgumentError("signature s out of range","signature",t),e.recoveryParam&&(r[0]|=128);const i=Ir(r);e._vs&&(_r(e._vs)||mr.throwArgumentError("signature invalid _vs","signature",t),e._vs=Mr(e._vs,32)),null==e._vs?e._vs=i:e._vs!==i&&mr.throwArgumentError("signature _vs mismatch v and s","signature",t)}return e.yParityAndS=e._vs,e.compact=e.r+e.yParityAndS.substring(2),e}function xr(t){return"0x"+sr.keccak_256(Ar(t))}var Rr={exports:{}},Nr=function(t){var e=t.default;if("function"==typeof e){var r=function(){return e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var i=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(r,e,i.get?i:{enumerable:!0,get:function(){return t[e]}})})),r}(Object.freeze({__proto__:null,default:{}}));!function(t){!function(t,e){function r(t,e){if(!t)throw new Error(e||"Assertion failed")}function i(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}function n(t,e,r){if(n.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&(("le"===e||"be"===e)&&(r=e,e=10),this._init(t||0,e||10,r||"be"))}var s;"object"==typeof t?t.exports=n:e.BN=n,n.BN=n,n.wordSize=26;try{s=typeof window<"u"&&typeof window.Buffer<"u"?window.Buffer:Nr.Buffer}catch{}function o(t,e){var i=t.charCodeAt(e);return i>=48&&i<=57?i-48:i>=65&&i<=70?i-55:i>=97&&i<=102?i-87:void r(!1,"Invalid character in "+t)}function a(t,e,r){var i=o(t,r);return r-1>=e&&(i|=o(t,r-1)<<4),i}function c(t,e,i,n){for(var s=0,o=0,a=Math.min(t.length,i),c=e;c<a;c++){var h=t.charCodeAt(c)-48;s*=n,o=h>=49?h-49+10:h>=17?h-17+10:h,r(h>=0&&o<n,"Invalid character"),s+=o}return s}function h(t,e){t.words=e.words,t.length=e.length,t.negative=e.negative,t.red=e.red}if(n.isBN=function(t){return t instanceof n||null!==t&&"object"==typeof t&&t.constructor.wordSize===n.wordSize&&Array.isArray(t.words)},n.max=function(t,e){return t.cmp(e)>0?t:e},n.min=function(t,e){return t.cmp(e)<0?t:e},n.prototype._init=function(t,e,i){if("number"==typeof t)return this._initNumber(t,e,i);if("object"==typeof t)return this._initArray(t,e,i);"hex"===e&&(e=16),r(e===(0|e)&&e>=2&&e<=36);var n=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&(n++,this.negative=1),n<t.length&&(16===e?this._parseHex(t,n,i):(this._parseBase(t,e,n),"le"===i&&this._initArray(this.toArray(),e,i)))},n.prototype._initNumber=function(t,e,i){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(r(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===i&&this._initArray(this.toArray(),e,i)},n.prototype._initArray=function(t,e,i){if(r("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var s,o,a=0;if("be"===i)for(n=t.length-1,s=0;n>=0;n-=3)o=t[n]|t[n-1]<<8|t[n-2]<<16,this.words[s]|=o<<a&67108863,this.words[s+1]=o>>>26-a&67108863,(a+=24)>=26&&(a-=26,s++);else if("le"===i)for(n=0,s=0;n<t.length;n+=3)o=t[n]|t[n+1]<<8|t[n+2]<<16,this.words[s]|=o<<a&67108863,this.words[s+1]=o>>>26-a&67108863,(a+=24)>=26&&(a-=26,s++);return this._strip()},n.prototype._parseHex=function(t,e,r){this.length=Math.ceil((t.length-e)/6),this.words=new Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var n,s=0,o=0;if("be"===r)for(i=t.length-1;i>=e;i-=2)n=a(t,e,i)<<s,this.words[o]|=67108863&n,s>=18?(s-=18,o+=1,this.words[o]|=n>>>26):s+=8;else for(i=(t.length-e)%2==0?e+1:e;i<t.length;i+=2)n=a(t,e,i)<<s,this.words[o]|=67108863&n,s>=18?(s-=18,o+=1,this.words[o]|=n>>>26):s+=8;this._strip()},n.prototype._parseBase=function(t,e,r){this.words=[0],this.length=1;for(var i=0,n=1;n<=67108863;n*=e)i++;i--,n=n/e|0;for(var s=t.length-r,o=s%i,a=Math.min(s,s-o)+r,h=0,u=r;u<a;u+=i)h=c(t,u,u+i,e),this.imuln(n),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h);if(0!==o){var l=1;for(h=c(t,u,t.length,e),u=0;u<o;u++)l*=e;this.imuln(l),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h)}this._strip()},n.prototype.copy=function(t){t.words=new Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},n.prototype._move=function(t){h(t,this)},n.prototype.clone=function(){var t=new n(null);return this.copy(t),t},n.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},n.prototype._strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},n.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},typeof Symbol<"u"&&"function"==typeof Symbol.for)try{n.prototype[Symbol.for("nodejs.util.inspect.custom")]=u}catch{n.prototype.inspect=u}else n.prototype.inspect=u;function u(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var l=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],f=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],d=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function p(t,e,r){r.negative=e.negative^t.negative;var i=t.length+e.length|0;r.length=i,i=i-1|0;var n=0|t.words[0],s=0|e.words[0],o=n*s,a=67108863&o,c=o/67108864|0;r.words[0]=a;for(var h=1;h<i;h++){for(var u=c>>>26,l=67108863&c,f=Math.min(h,e.length-1),d=Math.max(0,h-t.length+1);d<=f;d++){var p=h-d|0;u+=(o=(n=0|t.words[p])*(s=0|e.words[d])+l)/67108864|0,l=67108863&o}r.words[h]=0|l,c=0|u}return 0!==c?r.words[h]=0|c:r.length--,r._strip()}n.prototype.toString=function(t,e){var i;if(e=0|e||1,16===(t=t||10)||"hex"===t){i="";for(var n=0,s=0,o=0;o<this.length;o++){var a=this.words[o],c=(16777215&(a<<n|s)).toString(16);s=a>>>24-n&16777215,(n+=2)>=26&&(n-=26,o--),i=0!==s||o!==this.length-1?l[6-c.length]+c+i:c+i}for(0!==s&&(i=s.toString(16)+i);i.length%e!=0;)i="0"+i;return 0!==this.negative&&(i="-"+i),i}if(t===(0|t)&&t>=2&&t<=36){var h=f[t],u=d[t];i="";var p=this.clone();for(p.negative=0;!p.isZero();){var g=p.modrn(u).toString(t);i=(p=p.idivn(u)).isZero()?g+i:l[h-g.length]+g+i}for(this.isZero()&&(i="0"+i);i.length%e!=0;)i="0"+i;return 0!==this.negative&&(i="-"+i),i}r(!1,"Base should be between 2 and 36")},n.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&r(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},n.prototype.toJSON=function(){return this.toString(16,2)},s&&(n.prototype.toBuffer=function(t,e){return this.toArrayLike(s,t,e)}),n.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},n.prototype.toArrayLike=function(t,e,i){this._strip();var n=this.byteLength(),s=i||Math.max(1,n);r(n<=s,"byte array longer than desired length"),r(s>0,"Requested array length <= 0");var o=function(t,e){return t.allocUnsafe?t.allocUnsafe(e):new t(e)}(t,s);return this["_toArrayLike"+("le"===e?"LE":"BE")](o,n),o},n.prototype._toArrayLikeLE=function(t,e){for(var r=0,i=0,n=0,s=0;n<this.length;n++){var o=this.words[n]<<s|i;t[r++]=255&o,r<t.length&&(t[r++]=o>>8&255),r<t.length&&(t[r++]=o>>16&255),6===s?(r<t.length&&(t[r++]=o>>24&255),i=0,s=0):(i=o>>>24,s+=2)}if(r<t.length)for(t[r++]=i;r<t.length;)t[r++]=0},n.prototype._toArrayLikeBE=function(t,e){for(var r=t.length-1,i=0,n=0,s=0;n<this.length;n++){var o=this.words[n]<<s|i;t[r--]=255&o,r>=0&&(t[r--]=o>>8&255),r>=0&&(t[r--]=o>>16&255),6===s?(r>=0&&(t[r--]=o>>24&255),i=0,s=0):(i=o>>>24,s+=2)}if(r>=0)for(t[r--]=i;r>=0;)t[r--]=0},Math.clz32?n.prototype._countBits=function(t){return 32-Math.clz32(t)}:n.prototype._countBits=function(t){var e=t,r=0;return e>=4096&&(r+=13,e>>>=13),e>=64&&(r+=7,e>>>=7),e>=8&&(r+=4,e>>>=4),e>=2&&(r+=2,e>>>=2),r+e},n.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,r=0;return 8191&e||(r+=13,e>>>=13),127&e||(r+=7,e>>>=7),15&e||(r+=4,e>>>=4),3&e||(r+=2,e>>>=2),1&e||r++,r},n.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return 26*(this.length-1)+e},n.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var r=this._zeroBits(this.words[e]);if(t+=r,26!==r)break}return t},n.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},n.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},n.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},n.prototype.isNeg=function(){return 0!==this.negative},n.prototype.neg=function(){return this.clone().ineg()},n.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},n.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this._strip()},n.prototype.ior=function(t){return r(!(this.negative|t.negative)),this.iuor(t)},n.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},n.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},n.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var r=0;r<e.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=e.length,this._strip()},n.prototype.iand=function(t){return r(!(this.negative|t.negative)),this.iuand(t)},n.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},n.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},n.prototype.iuxor=function(t){var e,r;this.length>t.length?(e=this,r=t):(e=t,r=this);for(var i=0;i<r.length;i++)this.words[i]=e.words[i]^r.words[i];if(this!==e)for(;i<e.length;i++)this.words[i]=e.words[i];return this.length=e.length,this._strip()},n.prototype.ixor=function(t){return r(!(this.negative|t.negative)),this.iuxor(t)},n.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},n.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},n.prototype.inotn=function(t){r("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),i=t%26;this._expand(e),i>0&&e--;for(var n=0;n<e;n++)this.words[n]=67108863&~this.words[n];return i>0&&(this.words[n]=~this.words[n]&67108863>>26-i),this._strip()},n.prototype.notn=function(t){return this.clone().inotn(t)},n.prototype.setn=function(t,e){r("number"==typeof t&&t>=0);var i=t/26|0,n=t%26;return this._expand(i+1),this.words[i]=e?this.words[i]|1<<n:this.words[i]&~(1<<n),this._strip()},n.prototype.iadd=function(t){var e,r,i;if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(r=this,i=t):(r=t,i=this);for(var n=0,s=0;s<i.length;s++)e=(0|r.words[s])+(0|i.words[s])+n,this.words[s]=67108863&e,n=e>>>26;for(;0!==n&&s<r.length;s++)e=(0|r.words[s])+n,this.words[s]=67108863&e,n=e>>>26;if(this.length=r.length,0!==n)this.words[this.length]=n,this.length++;else if(r!==this)for(;s<r.length;s++)this.words[s]=r.words[s];return this},n.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},n.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var r,i,n=this.cmp(t);if(0===n)return this.negative=0,this.length=1,this.words[0]=0,this;n>0?(r=this,i=t):(r=t,i=this);for(var s=0,o=0;o<i.length;o++)s=(e=(0|r.words[o])-(0|i.words[o])+s)>>26,this.words[o]=67108863&e;for(;0!==s&&o<r.length;o++)s=(e=(0|r.words[o])+s)>>26,this.words[o]=67108863&e;if(0===s&&o<r.length&&r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this.length=Math.max(this.length,o),r!==this&&(this.negative=1),this._strip()},n.prototype.sub=function(t){return this.clone().isub(t)};var g=function(t,e,r){var i,n,s,o=t.words,a=e.words,c=r.words,h=0,u=0|o[0],l=8191&u,f=u>>>13,d=0|o[1],p=8191&d,g=d>>>13,m=0|o[2],y=8191&m,v=m>>>13,w=0|o[3],b=8191&w,A=w>>>13,_=0|o[4],E=8191&_,I=_>>>13,S=0|o[5],M=8191&S,P=S>>>13,x=0|o[6],R=8191&x,N=x>>>13,C=0|o[7],O=8191&C,U=C>>>13,T=0|o[8],D=8191&T,B=T>>>13,k=0|o[9],q=8191&k,L=k>>>13,j=0|a[0],z=8191&j,F=j>>>13,K=0|a[1],H=8191&K,V=K>>>13,$=0|a[2],J=8191&$,G=$>>>13,W=0|a[3],Q=8191&W,Y=W>>>13,X=0|a[4],Z=8191&X,tt=X>>>13,et=0|a[5],rt=8191&et,it=et>>>13,nt=0|a[6],st=8191&nt,ot=nt>>>13,at=0|a[7],ct=8191&at,ht=at>>>13,ut=0|a[8],lt=8191&ut,ft=ut>>>13,dt=0|a[9],pt=8191&dt,gt=dt>>>13;r.negative=t.negative^e.negative,r.length=19;var mt=(h+(i=Math.imul(l,z))|0)+((8191&(n=(n=Math.imul(l,F))+Math.imul(f,z)|0))<<13)|0;h=((s=Math.imul(f,F))+(n>>>13)|0)+(mt>>>26)|0,mt&=67108863,i=Math.imul(p,z),n=(n=Math.imul(p,F))+Math.imul(g,z)|0,s=Math.imul(g,F);var yt=(h+(i=i+Math.imul(l,H)|0)|0)+((8191&(n=(n=n+Math.imul(l,V)|0)+Math.imul(f,H)|0))<<13)|0;h=((s=s+Math.imul(f,V)|0)+(n>>>13)|0)+(yt>>>26)|0,yt&=67108863,i=Math.imul(y,z),n=(n=Math.imul(y,F))+Math.imul(v,z)|0,s=Math.imul(v,F),i=i+Math.imul(p,H)|0,n=(n=n+Math.imul(p,V)|0)+Math.imul(g,H)|0,s=s+Math.imul(g,V)|0;var vt=(h+(i=i+Math.imul(l,J)|0)|0)+((8191&(n=(n=n+Math.imul(l,G)|0)+Math.imul(f,J)|0))<<13)|0;h=((s=s+Math.imul(f,G)|0)+(n>>>13)|0)+(vt>>>26)|0,vt&=67108863,i=Math.imul(b,z),n=(n=Math.imul(b,F))+Math.imul(A,z)|0,s=Math.imul(A,F),i=i+Math.imul(y,H)|0,n=(n=n+Math.imul(y,V)|0)+Math.imul(v,H)|0,s=s+Math.imul(v,V)|0,i=i+Math.imul(p,J)|0,n=(n=n+Math.imul(p,G)|0)+Math.imul(g,J)|0,s=s+Math.imul(g,G)|0;var wt=(h+(i=i+Math.imul(l,Q)|0)|0)+((8191&(n=(n=n+Math.imul(l,Y)|0)+Math.imul(f,Q)|0))<<13)|0;h=((s=s+Math.imul(f,Y)|0)+(n>>>13)|0)+(wt>>>26)|0,wt&=67108863,i=Math.imul(E,z),n=(n=Math.imul(E,F))+Math.imul(I,z)|0,s=Math.imul(I,F),i=i+Math.imul(b,H)|0,n=(n=n+Math.imul(b,V)|0)+Math.imul(A,H)|0,s=s+Math.imul(A,V)|0,i=i+Math.imul(y,J)|0,n=(n=n+Math.imul(y,G)|0)+Math.imul(v,J)|0,s=s+Math.imul(v,G)|0,i=i+Math.imul(p,Q)|0,n=(n=n+Math.imul(p,Y)|0)+Math.imul(g,Q)|0,s=s+Math.imul(g,Y)|0;var bt=(h+(i=i+Math.imul(l,Z)|0)|0)+((8191&(n=(n=n+Math.imul(l,tt)|0)+Math.imul(f,Z)|0))<<13)|0;h=((s=s+Math.imul(f,tt)|0)+(n>>>13)|0)+(bt>>>26)|0,bt&=67108863,i=Math.imul(M,z),n=(n=Math.imul(M,F))+Math.imul(P,z)|0,s=Math.imul(P,F),i=i+Math.imul(E,H)|0,n=(n=n+Math.imul(E,V)|0)+Math.imul(I,H)|0,s=s+Math.imul(I,V)|0,i=i+Math.imul(b,J)|0,n=(n=n+Math.imul(b,G)|0)+Math.imul(A,J)|0,s=s+Math.imul(A,G)|0,i=i+Math.imul(y,Q)|0,n=(n=n+Math.imul(y,Y)|0)+Math.imul(v,Q)|0,s=s+Math.imul(v,Y)|0,i=i+Math.imul(p,Z)|0,n=(n=n+Math.imul(p,tt)|0)+Math.imul(g,Z)|0,s=s+Math.imul(g,tt)|0;var At=(h+(i=i+Math.imul(l,rt)|0)|0)+((8191&(n=(n=n+Math.imul(l,it)|0)+Math.imul(f,rt)|0))<<13)|0;h=((s=s+Math.imul(f,it)|0)+(n>>>13)|0)+(At>>>26)|0,At&=67108863,i=Math.imul(R,z),n=(n=Math.imul(R,F))+Math.imul(N,z)|0,s=Math.imul(N,F),i=i+Math.imul(M,H)|0,n=(n=n+Math.imul(M,V)|0)+Math.imul(P,H)|0,s=s+Math.imul(P,V)|0,i=i+Math.imul(E,J)|0,n=(n=n+Math.imul(E,G)|0)+Math.imul(I,J)|0,s=s+Math.imul(I,G)|0,i=i+Math.imul(b,Q)|0,n=(n=n+Math.imul(b,Y)|0)+Math.imul(A,Q)|0,s=s+Math.imul(A,Y)|0,i=i+Math.imul(y,Z)|0,n=(n=n+Math.imul(y,tt)|0)+Math.imul(v,Z)|0,s=s+Math.imul(v,tt)|0,i=i+Math.imul(p,rt)|0,n=(n=n+Math.imul(p,it)|0)+Math.imul(g,rt)|0,s=s+Math.imul(g,it)|0;var _t=(h+(i=i+Math.imul(l,st)|0)|0)+((8191&(n=(n=n+Math.imul(l,ot)|0)+Math.imul(f,st)|0))<<13)|0;h=((s=s+Math.imul(f,ot)|0)+(n>>>13)|0)+(_t>>>26)|0,_t&=67108863,i=Math.imul(O,z),n=(n=Math.imul(O,F))+Math.imul(U,z)|0,s=Math.imul(U,F),i=i+Math.imul(R,H)|0,n=(n=n+Math.imul(R,V)|0)+Math.imul(N,H)|0,s=s+Math.imul(N,V)|0,i=i+Math.imul(M,J)|0,n=(n=n+Math.imul(M,G)|0)+Math.imul(P,J)|0,s=s+Math.imul(P,G)|0,i=i+Math.imul(E,Q)|0,n=(n=n+Math.imul(E,Y)|0)+Math.imul(I,Q)|0,s=s+Math.imul(I,Y)|0,i=i+Math.imul(b,Z)|0,n=(n=n+Math.imul(b,tt)|0)+Math.imul(A,Z)|0,s=s+Math.imul(A,tt)|0,i=i+Math.imul(y,rt)|0,n=(n=n+Math.imul(y,it)|0)+Math.imul(v,rt)|0,s=s+Math.imul(v,it)|0,i=i+Math.imul(p,st)|0,n=(n=n+Math.imul(p,ot)|0)+Math.imul(g,st)|0,s=s+Math.imul(g,ot)|0;var Et=(h+(i=i+Math.imul(l,ct)|0)|0)+((8191&(n=(n=n+Math.imul(l,ht)|0)+Math.imul(f,ct)|0))<<13)|0;h=((s=s+Math.imul(f,ht)|0)+(n>>>13)|0)+(Et>>>26)|0,Et&=67108863,i=Math.imul(D,z),n=(n=Math.imul(D,F))+Math.imul(B,z)|0,s=Math.imul(B,F),i=i+Math.imul(O,H)|0,n=(n=n+Math.imul(O,V)|0)+Math.imul(U,H)|0,s=s+Math.imul(U,V)|0,i=i+Math.imul(R,J)|0,n=(n=n+Math.imul(R,G)|0)+Math.imul(N,J)|0,s=s+Math.imul(N,G)|0,i=i+Math.imul(M,Q)|0,n=(n=n+Math.imul(M,Y)|0)+Math.imul(P,Q)|0,s=s+Math.imul(P,Y)|0,i=i+Math.imul(E,Z)|0,n=(n=n+Math.imul(E,tt)|0)+Math.imul(I,Z)|0,s=s+Math.imul(I,tt)|0,i=i+Math.imul(b,rt)|0,n=(n=n+Math.imul(b,it)|0)+Math.imul(A,rt)|0,s=s+Math.imul(A,it)|0,i=i+Math.imul(y,st)|0,n=(n=n+Math.imul(y,ot)|0)+Math.imul(v,st)|0,s=s+Math.imul(v,ot)|0,i=i+Math.imul(p,ct)|0,n=(n=n+Math.imul(p,ht)|0)+Math.imul(g,ct)|0,s=s+Math.imul(g,ht)|0;var It=(h+(i=i+Math.imul(l,lt)|0)|0)+((8191&(n=(n=n+Math.imul(l,ft)|0)+Math.imul(f,lt)|0))<<13)|0;h=((s=s+Math.imul(f,ft)|0)+(n>>>13)|0)+(It>>>26)|0,It&=67108863,i=Math.imul(q,z),n=(n=Math.imul(q,F))+Math.imul(L,z)|0,s=Math.imul(L,F),i=i+Math.imul(D,H)|0,n=(n=n+Math.imul(D,V)|0)+Math.imul(B,H)|0,s=s+Math.imul(B,V)|0,i=i+Math.imul(O,J)|0,n=(n=n+Math.imul(O,G)|0)+Math.imul(U,J)|0,s=s+Math.imul(U,G)|0,i=i+Math.imul(R,Q)|0,n=(n=n+Math.imul(R,Y)|0)+Math.imul(N,Q)|0,s=s+Math.imul(N,Y)|0,i=i+Math.imul(M,Z)|0,n=(n=n+Math.imul(M,tt)|0)+Math.imul(P,Z)|0,s=s+Math.imul(P,tt)|0,i=i+Math.imul(E,rt)|0,n=(n=n+Math.imul(E,it)|0)+Math.imul(I,rt)|0,s=s+Math.imul(I,it)|0,i=i+Math.imul(b,st)|0,n=(n=n+Math.imul(b,ot)|0)+Math.imul(A,st)|0,s=s+Math.imul(A,ot)|0,i=i+Math.imul(y,ct)|0,n=(n=n+Math.imul(y,ht)|0)+Math.imul(v,ct)|0,s=s+Math.imul(v,ht)|0,i=i+Math.imul(p,lt)|0,n=(n=n+Math.imul(p,ft)|0)+Math.imul(g,lt)|0,s=s+Math.imul(g,ft)|0;var St=(h+(i=i+Math.imul(l,pt)|0)|0)+((8191&(n=(n=n+Math.imul(l,gt)|0)+Math.imul(f,pt)|0))<<13)|0;h=((s=s+Math.imul(f,gt)|0)+(n>>>13)|0)+(St>>>26)|0,St&=67108863,i=Math.imul(q,H),n=(n=Math.imul(q,V))+Math.imul(L,H)|0,s=Math.imul(L,V),i=i+Math.imul(D,J)|0,n=(n=n+Math.imul(D,G)|0)+Math.imul(B,J)|0,s=s+Math.imul(B,G)|0,i=i+Math.imul(O,Q)|0,n=(n=n+Math.imul(O,Y)|0)+Math.imul(U,Q)|0,s=s+Math.imul(U,Y)|0,i=i+Math.imul(R,Z)|0,n=(n=n+Math.imul(R,tt)|0)+Math.imul(N,Z)|0,s=s+Math.imul(N,tt)|0,i=i+Math.imul(M,rt)|0,n=(n=n+Math.imul(M,it)|0)+Math.imul(P,rt)|0,s=s+Math.imul(P,it)|0,i=i+Math.imul(E,st)|0,n=(n=n+Math.imul(E,ot)|0)+Math.imul(I,st)|0,s=s+Math.imul(I,ot)|0,i=i+Math.imul(b,ct)|0,n=(n=n+Math.imul(b,ht)|0)+Math.imul(A,ct)|0,s=s+Math.imul(A,ht)|0,i=i+Math.imul(y,lt)|0,n=(n=n+Math.imul(y,ft)|0)+Math.imul(v,lt)|0,s=s+Math.imul(v,ft)|0;var Mt=(h+(i=i+Math.imul(p,pt)|0)|0)+((8191&(n=(n=n+Math.imul(p,gt)|0)+Math.imul(g,pt)|0))<<13)|0;h=((s=s+Math.imul(g,gt)|0)+(n>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,i=Math.imul(q,J),n=(n=Math.imul(q,G))+Math.imul(L,J)|0,s=Math.imul(L,G),i=i+Math.imul(D,Q)|0,n=(n=n+Math.imul(D,Y)|0)+Math.imul(B,Q)|0,s=s+Math.imul(B,Y)|0,i=i+Math.imul(O,Z)|0,n=(n=n+Math.imul(O,tt)|0)+Math.imul(U,Z)|0,s=s+Math.imul(U,tt)|0,i=i+Math.imul(R,rt)|0,n=(n=n+Math.imul(R,it)|0)+Math.imul(N,rt)|0,s=s+Math.imul(N,it)|0,i=i+Math.imul(M,st)|0,n=(n=n+Math.imul(M,ot)|0)+Math.imul(P,st)|0,s=s+Math.imul(P,ot)|0,i=i+Math.imul(E,ct)|0,n=(n=n+Math.imul(E,ht)|0)+Math.imul(I,ct)|0,s=s+Math.imul(I,ht)|0,i=i+Math.imul(b,lt)|0,n=(n=n+Math.imul(b,ft)|0)+Math.imul(A,lt)|0,s=s+Math.imul(A,ft)|0;var Pt=(h+(i=i+Math.imul(y,pt)|0)|0)+((8191&(n=(n=n+Math.imul(y,gt)|0)+Math.imul(v,pt)|0))<<13)|0;h=((s=s+Math.imul(v,gt)|0)+(n>>>13)|0)+(Pt>>>26)|0,Pt&=67108863,i=Math.imul(q,Q),n=(n=Math.imul(q,Y))+Math.imul(L,Q)|0,s=Math.imul(L,Y),i=i+Math.imul(D,Z)|0,n=(n=n+Math.imul(D,tt)|0)+Math.imul(B,Z)|0,s=s+Math.imul(B,tt)|0,i=i+Math.imul(O,rt)|0,n=(n=n+Math.imul(O,it)|0)+Math.imul(U,rt)|0,s=s+Math.imul(U,it)|0,i=i+Math.imul(R,st)|0,n=(n=n+Math.imul(R,ot)|0)+Math.imul(N,st)|0,s=s+Math.imul(N,ot)|0,i=i+Math.imul(M,ct)|0,n=(n=n+Math.imul(M,ht)|0)+Math.imul(P,ct)|0,s=s+Math.imul(P,ht)|0,i=i+Math.imul(E,lt)|0,n=(n=n+Math.imul(E,ft)|0)+Math.imul(I,lt)|0,s=s+Math.imul(I,ft)|0;var xt=(h+(i=i+Math.imul(b,pt)|0)|0)+((8191&(n=(n=n+Math.imul(b,gt)|0)+Math.imul(A,pt)|0))<<13)|0;h=((s=s+Math.imul(A,gt)|0)+(n>>>13)|0)+(xt>>>26)|0,xt&=67108863,i=Math.imul(q,Z),n=(n=Math.imul(q,tt))+Math.imul(L,Z)|0,s=Math.imul(L,tt),i=i+Math.imul(D,rt)|0,n=(n=n+Math.imul(D,it)|0)+Math.imul(B,rt)|0,s=s+Math.imul(B,it)|0,i=i+Math.imul(O,st)|0,n=(n=n+Math.imul(O,ot)|0)+Math.imul(U,st)|0,s=s+Math.imul(U,ot)|0,i=i+Math.imul(R,ct)|0,n=(n=n+Math.imul(R,ht)|0)+Math.imul(N,ct)|0,s=s+Math.imul(N,ht)|0,i=i+Math.imul(M,lt)|0,n=(n=n+Math.imul(M,ft)|0)+Math.imul(P,lt)|0,s=s+Math.imul(P,ft)|0;var Rt=(h+(i=i+Math.imul(E,pt)|0)|0)+((8191&(n=(n=n+Math.imul(E,gt)|0)+Math.imul(I,pt)|0))<<13)|0;h=((s=s+Math.imul(I,gt)|0)+(n>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,i=Math.imul(q,rt),n=(n=Math.imul(q,it))+Math.imul(L,rt)|0,s=Math.imul(L,it),i=i+Math.imul(D,st)|0,n=(n=n+Math.imul(D,ot)|0)+Math.imul(B,st)|0,s=s+Math.imul(B,ot)|0,i=i+Math.imul(O,ct)|0,n=(n=n+Math.imul(O,ht)|0)+Math.imul(U,ct)|0,s=s+Math.imul(U,ht)|0,i=i+Math.imul(R,lt)|0,n=(n=n+Math.imul(R,ft)|0)+Math.imul(N,lt)|0,s=s+Math.imul(N,ft)|0;var Nt=(h+(i=i+Math.imul(M,pt)|0)|0)+((8191&(n=(n=n+Math.imul(M,gt)|0)+Math.imul(P,pt)|0))<<13)|0;h=((s=s+Math.imul(P,gt)|0)+(n>>>13)|0)+(Nt>>>26)|0,Nt&=67108863,i=Math.imul(q,st),n=(n=Math.imul(q,ot))+Math.imul(L,st)|0,s=Math.imul(L,ot),i=i+Math.imul(D,ct)|0,n=(n=n+Math.imul(D,ht)|0)+Math.imul(B,ct)|0,s=s+Math.imul(B,ht)|0,i=i+Math.imul(O,lt)|0,n=(n=n+Math.imul(O,ft)|0)+Math.imul(U,lt)|0,s=s+Math.imul(U,ft)|0;var Ct=(h+(i=i+Math.imul(R,pt)|0)|0)+((8191&(n=(n=n+Math.imul(R,gt)|0)+Math.imul(N,pt)|0))<<13)|0;h=((s=s+Math.imul(N,gt)|0)+(n>>>13)|0)+(Ct>>>26)|0,Ct&=67108863,i=Math.imul(q,ct),n=(n=Math.imul(q,ht))+Math.imul(L,ct)|0,s=Math.imul(L,ht),i=i+Math.imul(D,lt)|0,n=(n=n+Math.imul(D,ft)|0)+Math.imul(B,lt)|0,s=s+Math.imul(B,ft)|0;var Ot=(h+(i=i+Math.imul(O,pt)|0)|0)+((8191&(n=(n=n+Math.imul(O,gt)|0)+Math.imul(U,pt)|0))<<13)|0;h=((s=s+Math.imul(U,gt)|0)+(n>>>13)|0)+(Ot>>>26)|0,Ot&=67108863,i=Math.imul(q,lt),n=(n=Math.imul(q,ft))+Math.imul(L,lt)|0,s=Math.imul(L,ft);var Ut=(h+(i=i+Math.imul(D,pt)|0)|0)+((8191&(n=(n=n+Math.imul(D,gt)|0)+Math.imul(B,pt)|0))<<13)|0;h=((s=s+Math.imul(B,gt)|0)+(n>>>13)|0)+(Ut>>>26)|0,Ut&=67108863;var Tt=(h+(i=Math.imul(q,pt))|0)+((8191&(n=(n=Math.imul(q,gt))+Math.imul(L,pt)|0))<<13)|0;return h=((s=Math.imul(L,gt))+(n>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,c[0]=mt,c[1]=yt,c[2]=vt,c[3]=wt,c[4]=bt,c[5]=At,c[6]=_t,c[7]=Et,c[8]=It,c[9]=St,c[10]=Mt,c[11]=Pt,c[12]=xt,c[13]=Rt,c[14]=Nt,c[15]=Ct,c[16]=Ot,c[17]=Ut,c[18]=Tt,0!==h&&(c[19]=h,r.length++),r};function m(t,e,r){r.negative=e.negative^t.negative,r.length=t.length+e.length;for(var i=0,n=0,s=0;s<r.length-1;s++){var o=n;n=0;for(var a=67108863&i,c=Math.min(s,e.length-1),h=Math.max(0,s-t.length+1);h<=c;h++){var u=s-h,l=(0|t.words[u])*(0|e.words[h]),f=67108863&l;a=67108863&(f=f+a|0),n+=(o=(o=o+(l/67108864|0)|0)+(f>>>26)|0)>>>26,o&=67108863}r.words[s]=a,i=o,o=n}return 0!==i?r.words[s]=i:r.length--,r._strip()}function y(t,e,r){return m(t,e,r)}Math.imul||(g=p),n.prototype.mulTo=function(t,e){var r=this.length+t.length;return 10===this.length&&10===t.length?g(this,t,e):r<63?p(this,t,e):r<1024?m(this,t,e):y(this,t,e)},n.prototype.mul=function(t){var e=new n(null);return e.words=new Array(this.length+t.length),this.mulTo(t,e)},n.prototype.mulf=function(t){var e=new n(null);return e.words=new Array(this.length+t.length),y(this,t,e)},n.prototype.imul=function(t){return this.clone().mulTo(t,this)},n.prototype.imuln=function(t){var e=t<0;e&&(t=-t),r("number"==typeof t),r(t<67108864);for(var i=0,n=0;n<this.length;n++){var s=(0|this.words[n])*t,o=(67108863&s)+(67108863&i);i>>=26,i+=s/67108864|0,i+=o>>>26,this.words[n]=67108863&o}return 0!==i&&(this.words[n]=i,this.length++),e?this.ineg():this},n.prototype.muln=function(t){return this.clone().imuln(t)},n.prototype.sqr=function(){return this.mul(this)},n.prototype.isqr=function(){return this.imul(this.clone())},n.prototype.pow=function(t){var e=function(t){for(var e=new Array(t.bitLength()),r=0;r<e.length;r++){var i=r/26|0,n=r%26;e[r]=t.words[i]>>>n&1}return e}(t);if(0===e.length)return new n(1);for(var r=this,i=0;i<e.length&&0===e[i];i++,r=r.sqr());if(++i<e.length)for(var s=r.sqr();i<e.length;i++,s=s.sqr())0!==e[i]&&(r=r.mul(s));return r},n.prototype.iushln=function(t){r("number"==typeof t&&t>=0);var e,i=t%26,n=(t-i)/26,s=67108863>>>26-i<<26-i;if(0!==i){var o=0;for(e=0;e<this.length;e++){var a=this.words[e]&s,c=(0|this.words[e])-a<<i;this.words[e]=c|o,o=a>>>26-i}o&&(this.words[e]=o,this.length++)}if(0!==n){for(e=this.length-1;e>=0;e--)this.words[e+n]=this.words[e];for(e=0;e<n;e++)this.words[e]=0;this.length+=n}return this._strip()},n.prototype.ishln=function(t){return r(0===this.negative),this.iushln(t)},n.prototype.iushrn=function(t,e,i){var n;r("number"==typeof t&&t>=0),n=e?(e-e%26)/26:0;var s=t%26,o=Math.min((t-s)/26,this.length),a=67108863^67108863>>>s<<s,c=i;if(n-=o,n=Math.max(0,n),c){for(var h=0;h<o;h++)c.words[h]=this.words[h];c.length=o}if(0!==o)if(this.length>o)for(this.length-=o,h=0;h<this.length;h++)this.words[h]=this.words[h+o];else this.words[0]=0,this.length=1;var u=0;for(h=this.length-1;h>=0&&(0!==u||h>=n);h--){var l=0|this.words[h];this.words[h]=u<<26-s|l>>>s,u=l&a}return c&&0!==u&&(c.words[c.length++]=u),0===this.length&&(this.words[0]=0,this.length=1),this._strip()},n.prototype.ishrn=function(t,e,i){return r(0===this.negative),this.iushrn(t,e,i)},n.prototype.shln=function(t){return this.clone().ishln(t)},n.prototype.ushln=function(t){return this.clone().iushln(t)},n.prototype.shrn=function(t){return this.clone().ishrn(t)},n.prototype.ushrn=function(t){return this.clone().iushrn(t)},n.prototype.testn=function(t){r("number"==typeof t&&t>=0);var e=t%26,i=(t-e)/26,n=1<<e;return!(this.length<=i||!(this.words[i]&n))},n.prototype.imaskn=function(t){r("number"==typeof t&&t>=0);var e=t%26,i=(t-e)/26;if(r(0===this.negative,"imaskn works only with positive numbers"),this.length<=i)return this;if(0!==e&&i++,this.length=Math.min(i,this.length),0!==e){var n=67108863^67108863>>>e<<e;this.words[this.length-1]&=n}return this._strip()},n.prototype.maskn=function(t){return this.clone().imaskn(t)},n.prototype.iaddn=function(t){return r("number"==typeof t),r(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<=t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},n.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=67108864;e++)this.words[e]-=67108864,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},n.prototype.isubn=function(t){if(r("number"==typeof t),r(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=67108864,this.words[e+1]-=1;return this._strip()},n.prototype.addn=function(t){return this.clone().iaddn(t)},n.prototype.subn=function(t){return this.clone().isubn(t)},n.prototype.iabs=function(){return this.negative=0,this},n.prototype.abs=function(){return this.clone().iabs()},n.prototype._ishlnsubmul=function(t,e,i){var n,s=t.length+i;this._expand(s);var o,a=0;for(n=0;n<t.length;n++){o=(0|this.words[n+i])+a;var c=(0|t.words[n])*e;a=((o-=67108863&c)>>26)-(c/67108864|0),this.words[n+i]=67108863&o}for(;n<this.length-i;n++)a=(o=(0|this.words[n+i])+a)>>26,this.words[n+i]=67108863&o;if(0===a)return this._strip();for(r(-1===a),a=0,n=0;n<this.length;n++)a=(o=-(0|this.words[n])+a)>>26,this.words[n]=67108863&o;return this.negative=1,this._strip()},n.prototype._wordDiv=function(t,e){var r=(this.length,t.length),i=this.clone(),s=t,o=0|s.words[s.length-1];0!=(r=26-this._countBits(o))&&(s=s.ushln(r),i.iushln(r),o=0|s.words[s.length-1]);var a,c=i.length-s.length;if("mod"!==e){(a=new n(null)).length=c+1,a.words=new Array(a.length);for(var h=0;h<a.length;h++)a.words[h]=0}var u=i.clone()._ishlnsubmul(s,1,c);0===u.negative&&(i=u,a&&(a.words[c]=1));for(var l=c-1;l>=0;l--){var f=67108864*(0|i.words[s.length+l])+(0|i.words[s.length+l-1]);for(f=Math.min(f/o|0,67108863),i._ishlnsubmul(s,f,l);0!==i.negative;)f--,i.negative=0,i._ishlnsubmul(s,1,l),i.isZero()||(i.negative^=1);a&&(a.words[l]=f)}return a&&a._strip(),i._strip(),"div"!==e&&0!==r&&i.iushrn(r),{div:a||null,mod:i}},n.prototype.divmod=function(t,e,i){return r(!t.isZero()),this.isZero()?{div:new n(0),mod:new n(0)}:0!==this.negative&&0===t.negative?(a=this.neg().divmod(t,e),"mod"!==e&&(s=a.div.neg()),"div"!==e&&(o=a.mod.neg(),i&&0!==o.negative&&o.iadd(t)),{div:s,mod:o}):0===this.negative&&0!==t.negative?(a=this.divmod(t.neg(),e),"mod"!==e&&(s=a.div.neg()),{div:s,mod:a.mod}):this.negative&t.negative?(a=this.neg().divmod(t.neg(),e),"div"!==e&&(o=a.mod.neg(),i&&0!==o.negative&&o.isub(t)),{div:a.div,mod:o}):t.length>this.length||this.cmp(t)<0?{div:new n(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new n(this.modrn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new n(this.modrn(t.words[0]))}:this._wordDiv(t,e);var s,o,a},n.prototype.div=function(t){return this.divmod(t,"div",!1).div},n.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},n.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},n.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var r=0!==e.div.negative?e.mod.isub(t):e.mod,i=t.ushrn(1),n=t.andln(1),s=r.cmp(i);return s<0||1===n&&0===s?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},n.prototype.modrn=function(t){var e=t<0;e&&(t=-t),r(t<=67108863);for(var i=(1<<26)%t,n=0,s=this.length-1;s>=0;s--)n=(i*n+(0|this.words[s]))%t;return e?-n:n},n.prototype.modn=function(t){return this.modrn(t)},n.prototype.idivn=function(t){var e=t<0;e&&(t=-t),r(t<=67108863);for(var i=0,n=this.length-1;n>=0;n--){var s=(0|this.words[n])+67108864*i;this.words[n]=s/t|0,i=s%t}return this._strip(),e?this.ineg():this},n.prototype.divn=function(t){return this.clone().idivn(t)},n.prototype.egcd=function(t){r(0===t.negative),r(!t.isZero());var e=this,i=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var s=new n(1),o=new n(0),a=new n(0),c=new n(1),h=0;e.isEven()&&i.isEven();)e.iushrn(1),i.iushrn(1),++h;for(var u=i.clone(),l=e.clone();!e.isZero();){for(var f=0,d=1;!(e.words[0]&d)&&f<26;++f,d<<=1);if(f>0)for(e.iushrn(f);f-- >0;)(s.isOdd()||o.isOdd())&&(s.iadd(u),o.isub(l)),s.iushrn(1),o.iushrn(1);for(var p=0,g=1;!(i.words[0]&g)&&p<26;++p,g<<=1);if(p>0)for(i.iushrn(p);p-- >0;)(a.isOdd()||c.isOdd())&&(a.iadd(u),c.isub(l)),a.iushrn(1),c.iushrn(1);e.cmp(i)>=0?(e.isub(i),s.isub(a),o.isub(c)):(i.isub(e),a.isub(s),c.isub(o))}return{a,b:c,gcd:i.iushln(h)}},n.prototype._invmp=function(t){r(0===t.negative),r(!t.isZero());var e,i=this,s=t.clone();i=0!==i.negative?i.umod(t):i.clone();for(var o=new n(1),a=new n(0),c=s.clone();i.cmpn(1)>0&&s.cmpn(1)>0;){for(var h=0,u=1;!(i.words[0]&u)&&h<26;++h,u<<=1);if(h>0)for(i.iushrn(h);h-- >0;)o.isOdd()&&o.iadd(c),o.iushrn(1);for(var l=0,f=1;!(s.words[0]&f)&&l<26;++l,f<<=1);if(l>0)for(s.iushrn(l);l-- >0;)a.isOdd()&&a.iadd(c),a.iushrn(1);i.cmp(s)>=0?(i.isub(s),o.isub(a)):(s.isub(i),a.isub(o))}return(e=0===i.cmpn(1)?o:a).cmpn(0)<0&&e.iadd(t),e},n.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),r=t.clone();e.negative=0,r.negative=0;for(var i=0;e.isEven()&&r.isEven();i++)e.iushrn(1),r.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;r.isEven();)r.iushrn(1);var n=e.cmp(r);if(n<0){var s=e;e=r,r=s}else if(0===n||0===r.cmpn(1))break;e.isub(r)}return r.iushln(i)},n.prototype.invm=function(t){return this.egcd(t).a.umod(t)},n.prototype.isEven=function(){return!(1&this.words[0])},n.prototype.isOdd=function(){return!(1&~this.words[0])},n.prototype.andln=function(t){return this.words[0]&t},n.prototype.bincn=function(t){r("number"==typeof t);var e=t%26,i=(t-e)/26,n=1<<e;if(this.length<=i)return this._expand(i+1),this.words[i]|=n,this;for(var s=n,o=i;0!==s&&o<this.length;o++){var a=0|this.words[o];s=(a+=s)>>>26,a&=67108863,this.words[o]=a}return 0!==s&&(this.words[o]=s,this.length++),this},n.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},n.prototype.cmpn=function(t){var e,i=t<0;if(0!==this.negative&&!i)return-1;if(0===this.negative&&i)return 1;if(this._strip(),this.length>1)e=1;else{i&&(t=-t),r(t<=67108863,"Number is too big");var n=0|this.words[0];e=n===t?0:n<t?-1:1}return 0!==this.negative?0|-e:e},n.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},n.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var e=0,r=this.length-1;r>=0;r--){var i=0|this.words[r],n=0|t.words[r];if(i!==n){i<n?e=-1:i>n&&(e=1);break}}return e},n.prototype.gtn=function(t){return 1===this.cmpn(t)},n.prototype.gt=function(t){return 1===this.cmp(t)},n.prototype.gten=function(t){return this.cmpn(t)>=0},n.prototype.gte=function(t){return this.cmp(t)>=0},n.prototype.ltn=function(t){return-1===this.cmpn(t)},n.prototype.lt=function(t){return-1===this.cmp(t)},n.prototype.lten=function(t){return this.cmpn(t)<=0},n.prototype.lte=function(t){return this.cmp(t)<=0},n.prototype.eqn=function(t){return 0===this.cmpn(t)},n.prototype.eq=function(t){return 0===this.cmp(t)},n.red=function(t){return new I(t)},n.prototype.toRed=function(t){return r(!this.red,"Already a number in reduction context"),r(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},n.prototype.fromRed=function(){return r(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},n.prototype._forceRed=function(t){return this.red=t,this},n.prototype.forceRed=function(t){return r(!this.red,"Already a number in reduction context"),this._forceRed(t)},n.prototype.redAdd=function(t){return r(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},n.prototype.redIAdd=function(t){return r(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},n.prototype.redSub=function(t){return r(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},n.prototype.redISub=function(t){return r(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},n.prototype.redShl=function(t){return r(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},n.prototype.redMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},n.prototype.redIMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},n.prototype.redSqr=function(){return r(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},n.prototype.redISqr=function(){return r(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},n.prototype.redSqrt=function(){return r(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},n.prototype.redInvm=function(){return r(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},n.prototype.redNeg=function(){return r(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},n.prototype.redPow=function(t){return r(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var v={k256:null,p224:null,p192:null,p25519:null};function w(t,e){this.name=t,this.p=new n(e,16),this.n=this.p.bitLength(),this.k=new n(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function b(){w.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function A(){w.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function _(){w.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function E(){w.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function I(t){if("string"==typeof t){var e=n._prime(t);this.m=e.p,this.prime=e}else r(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function S(t){I.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new n(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}w.prototype._tmp=function(){var t=new n(null);return t.words=new Array(Math.ceil(this.n/13)),t},w.prototype.ireduce=function(t){var e,r=t;do{this.split(r,this.tmp),e=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(e>this.n);var i=e<this.n?-1:r.ucmp(this.p);return 0===i?(r.words[0]=0,r.length=1):i>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},w.prototype.split=function(t,e){t.iushrn(this.n,0,e)},w.prototype.imulK=function(t){return t.imul(this.k)},i(b,w),b.prototype.split=function(t,e){for(var r=4194303,i=Math.min(t.length,9),n=0;n<i;n++)e.words[n]=t.words[n];if(e.length=i,t.length<=9)return t.words[0]=0,void(t.length=1);var s=t.words[9];for(e.words[e.length++]=s&r,n=10;n<t.length;n++){var o=0|t.words[n];t.words[n-10]=(o&r)<<4|s>>>22,s=o}s>>>=22,t.words[n-10]=s,0===s&&t.length>10?t.length-=10:t.length-=9},b.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,r=0;r<t.length;r++){var i=0|t.words[r];e+=977*i,t.words[r]=67108863&e,e=64*i+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(A,w),i(_,w),i(E,w),E.prototype.imulK=function(t){for(var e=0,r=0;r<t.length;r++){var i=19*(0|t.words[r])+e,n=67108863&i;i>>>=26,t.words[r]=n,e=i}return 0!==e&&(t.words[t.length++]=e),t},n._prime=function(t){if(v[t])return v[t];var e;if("k256"===t)e=new b;else if("p224"===t)e=new A;else if("p192"===t)e=new _;else{if("p25519"!==t)throw new Error("Unknown prime "+t);e=new E}return v[t]=e,e},I.prototype._verify1=function(t){r(0===t.negative,"red works only with positives"),r(t.red,"red works only with red numbers")},I.prototype._verify2=function(t,e){r(!(t.negative|e.negative),"red works only with positives"),r(t.red&&t.red===e.red,"red works only with red numbers")},I.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):(h(t,t.umod(this.m)._forceRed(this)),t)},I.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},I.prototype.add=function(t,e){this._verify2(t,e);var r=t.add(e);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},I.prototype.iadd=function(t,e){this._verify2(t,e);var r=t.iadd(e);return r.cmp(this.m)>=0&&r.isub(this.m),r},I.prototype.sub=function(t,e){this._verify2(t,e);var r=t.sub(e);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},I.prototype.isub=function(t,e){this._verify2(t,e);var r=t.isub(e);return r.cmpn(0)<0&&r.iadd(this.m),r},I.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},I.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},I.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},I.prototype.isqr=function(t){return this.imul(t,t.clone())},I.prototype.sqr=function(t){return this.mul(t,t)},I.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(r(e%2==1),3===e){var i=this.m.add(new n(1)).iushrn(2);return this.pow(t,i)}for(var s=this.m.subn(1),o=0;!s.isZero()&&0===s.andln(1);)o++,s.iushrn(1);r(!s.isZero());var a=new n(1).toRed(this),c=a.redNeg(),h=this.m.subn(1).iushrn(1),u=this.m.bitLength();for(u=new n(2*u*u).toRed(this);0!==this.pow(u,h).cmp(c);)u.redIAdd(c);for(var l=this.pow(u,s),f=this.pow(t,s.addn(1).iushrn(1)),d=this.pow(t,s),p=o;0!==d.cmp(a);){for(var g=d,m=0;0!==g.cmp(a);m++)g=g.redSqr();r(m<p);var y=this.pow(l,new n(1).iushln(p-m-1));f=f.redMul(y),l=y.redSqr(),d=d.redMul(l),p=m}return f},I.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},I.prototype.pow=function(t,e){if(e.isZero())return new n(1).toRed(this);if(0===e.cmpn(1))return t.clone();var r=new Array(16);r[0]=new n(1).toRed(this),r[1]=t;for(var i=2;i<r.length;i++)r[i]=this.mul(r[i-1],t);var s=r[0],o=0,a=0,c=e.bitLength()%26;for(0===c&&(c=26),i=e.length-1;i>=0;i--){for(var h=e.words[i],u=c-1;u>=0;u--){var l=h>>u&1;s!==r[0]&&(s=this.sqr(s)),0!==l||0!==o?(o<<=1,o|=l,(4==++a||0===i&&0===u)&&(s=this.mul(s,r[o]),a=0,o=0)):a=0}c=26}return s},I.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},I.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},n.mont=function(t){return new S(t)},i(S,I),S.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},S.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},S.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),s=n;return n.cmp(this.m)>=0?s=n.isub(this.m):n.cmpn(0)<0&&(s=n.iadd(this.m)),s._forceRed(this)},S.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new n(0)._forceRed(this);var r=t.mul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),s=r.isub(i).iushrn(this.shift),o=s;return s.cmp(this.m)>=0?o=s.isub(this.m):s.cmpn(0)<0&&(o=s.iadd(this.m)),o._forceRed(this)},S.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t,ir)}(Rr);var Cr=Rr.exports;const Or="bignumber/5.7.0";var Ur=Cr.BN;const Tr=new gr(Or),Dr={},Br=9007199254740991;let kr=!1;class qr{constructor(t,e){t!==Dr&&Tr.throwError("cannot call constructor directly; use BigNumber.from",gr.errors.UNSUPPORTED_OPERATION,{operation:"new (BigNumber)"}),this._hex=e,this._isBigNumber=!0,Object.freeze(this)}fromTwos(t){return jr(zr(this).fromTwos(t))}toTwos(t){return jr(zr(this).toTwos(t))}abs(){return"-"===this._hex[0]?qr.from(this._hex.substring(1)):this}add(t){return jr(zr(this).add(zr(t)))}sub(t){return jr(zr(this).sub(zr(t)))}div(t){return qr.from(t).isZero()&&Fr("division-by-zero","div"),jr(zr(this).div(zr(t)))}mul(t){return jr(zr(this).mul(zr(t)))}mod(t){const e=zr(t);return e.isNeg()&&Fr("division-by-zero","mod"),jr(zr(this).umod(e))}pow(t){const e=zr(t);return e.isNeg()&&Fr("negative-power","pow"),jr(zr(this).pow(e))}and(t){const e=zr(t);return(this.isNegative()||e.isNeg())&&Fr("unbound-bitwise-result","and"),jr(zr(this).and(e))}or(t){const e=zr(t);return(this.isNegative()||e.isNeg())&&Fr("unbound-bitwise-result","or"),jr(zr(this).or(e))}xor(t){const e=zr(t);return(this.isNegative()||e.isNeg())&&Fr("unbound-bitwise-result","xor"),jr(zr(this).xor(e))}mask(t){return(this.isNegative()||t<0)&&Fr("negative-width","mask"),jr(zr(this).maskn(t))}shl(t){return(this.isNegative()||t<0)&&Fr("negative-width","shl"),jr(zr(this).shln(t))}shr(t){return(this.isNegative()||t<0)&&Fr("negative-width","shr"),jr(zr(this).shrn(t))}eq(t){return zr(this).eq(zr(t))}lt(t){return zr(this).lt(zr(t))}lte(t){return zr(this).lte(zr(t))}gt(t){return zr(this).gt(zr(t))}gte(t){return zr(this).gte(zr(t))}isNegative(){return"-"===this._hex[0]}isZero(){return zr(this).isZero()}toNumber(){try{return zr(this).toNumber()}catch{Fr("overflow","toNumber",this.toString())}return null}toBigInt(){try{return BigInt(this.toString())}catch{}return Tr.throwError("this platform does not support BigInt",gr.errors.UNSUPPORTED_OPERATION,{value:this.toString()})}toString(){return arguments.length>0&&(10===arguments[0]?kr||(kr=!0,Tr.warn("BigNumber.toString does not accept any parameters; base-10 is assumed")):16===arguments[0]?Tr.throwError("BigNumber.toString does not accept any parameters; use bigNumber.toHexString()",gr.errors.UNEXPECTED_ARGUMENT,{}):Tr.throwError("BigNumber.toString does not accept parameters",gr.errors.UNEXPECTED_ARGUMENT,{})),zr(this).toString(10)}toHexString(){return this._hex}toJSON(t){return{type:"BigNumber",hex:this.toHexString()}}static from(t){if(t instanceof qr)return t;if("string"==typeof t)return t.match(/^-?0x[0-9a-f]+$/i)?new qr(Dr,Lr(t)):t.match(/^-?[0-9]+$/)?new qr(Dr,Lr(new Ur(t))):Tr.throwArgumentError("invalid BigNumber string","value",t);if("number"==typeof t)return t%1&&Fr("underflow","BigNumber.from",t),(t>=Br||t<=-Br)&&Fr("overflow","BigNumber.from",t),qr.from(String(t));const e=t;if("bigint"==typeof e)return qr.from(e.toString());if(br(e))return qr.from(Ir(e));if(e)if(e.toHexString){const t=e.toHexString();if("string"==typeof t)return qr.from(t)}else{let t=e._hex;if(null==t&&"BigNumber"===e.type&&(t=e.hex),"string"==typeof t&&(_r(t)||"-"===t[0]&&_r(t.substring(1))))return qr.from(t)}return Tr.throwArgumentError("invalid BigNumber value","value",t)}static isBigNumber(t){return!(!t||!t._isBigNumber)}}function Lr(t){if("string"!=typeof t)return Lr(t.toString(16));if("-"===t[0])return"-"===(t=t.substring(1))[0]&&Tr.throwArgumentError("invalid hex","value",t),"0x00"===(t=Lr(t))?t:"-"+t;if("0x"!==t.substring(0,2)&&(t="0x"+t),"0x"===t)return"0x00";for(t.length%2&&(t="0x0"+t.substring(2));t.length>4&&"0x00"===t.substring(0,4);)t="0x"+t.substring(4);return t}function jr(t){return qr.from(Lr(t))}function zr(t){const e=qr.from(t).toHexString();return"-"===e[0]?new Ur("-"+e.substring(3),16):new Ur(e.substring(2),16)}function Fr(t,e,r){const i={fault:t,operation:e};return null!=r&&(i.value=r),Tr.throwError(t,gr.errors.NUMERIC_FAULT,i)}const Kr=new gr(Or),Hr={},Vr=qr.from(0),$r=qr.from(-1);function Jr(t,e,r,i){const n={fault:e,operation:r};return void 0!==i&&(n.value=i),Kr.throwError(t,gr.errors.NUMERIC_FAULT,n)}let Gr="0";for(;Gr.length<256;)Gr+=Gr;function Wr(t){if("number"!=typeof t)try{t=qr.from(t).toNumber()}catch{}return"number"==typeof t&&t>=0&&t<=256&&!(t%1)?"1"+Gr.substring(0,t):Kr.throwArgumentError("invalid decimal size","decimals",t)}function Qr(t,e){null==e&&(e=0);const r=Wr(e),i=(t=qr.from(t)).lt(Vr);i&&(t=t.mul($r));let n=t.mod(r).toString();for(;n.length<r.length-1;)n="0"+n;n=n.match(/^([0-9]*[1-9]|0)(0*)/)[1];const s=t.div(r).toString();return t=1===r.length?s:s+"."+n,i&&(t="-"+t),t}function Yr(t,e){null==e&&(e=0);const r=Wr(e);("string"!=typeof t||!t.match(/^-?[0-9.]+$/))&&Kr.throwArgumentError("invalid decimal value","value",t);const i="-"===t.substring(0,1);i&&(t=t.substring(1)),"."===t&&Kr.throwArgumentError("missing value","value",t);const n=t.split(".");n.length>2&&Kr.throwArgumentError("too many decimal points","value",t);let s=n[0],o=n[1];for(s||(s="0"),o||(o="0");"0"===o[o.length-1];)o=o.substring(0,o.length-1);for(o.length>r.length-1&&Jr("fractional component exceeds decimals","underflow","parseFixed"),""===o&&(o="0");o.length<r.length-1;)o+="0";const a=qr.from(s),c=qr.from(o);let h=a.mul(r).add(c);return i&&(h=h.mul($r)),h}class Xr{constructor(t,e,r,i){t!==Hr&&Kr.throwError("cannot use FixedFormat constructor; use FixedFormat.from",gr.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.signed=e,this.width=r,this.decimals=i,this.name=(e?"":"u")+"fixed"+String(r)+"x"+String(i),this._multiplier=Wr(i),Object.freeze(this)}static from(t){if(t instanceof Xr)return t;"number"==typeof t&&(t=`fixed128x${t}`);let e=!0,r=128,i=18;if("string"==typeof t){if("fixed"!==t)if("ufixed"===t)e=!1;else{const n=t.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);n||Kr.throwArgumentError("invalid fixed format","format",t),e="u"!==n[1],r=parseInt(n[2]),i=parseInt(n[3])}}else if(t){const n=(e,r,i)=>null==t[e]?i:(typeof t[e]!==r&&Kr.throwArgumentError("invalid fixed format ("+e+" not "+r+")","format."+e,t[e]),t[e]);e=n("signed","boolean",e),r=n("width","number",r),i=n("decimals","number",i)}return r%8&&Kr.throwArgumentError("invalid fixed format width (not byte aligned)","format.width",r),i>80&&Kr.throwArgumentError("invalid fixed format (decimals too large)","format.decimals",i),new Xr(Hr,e,r,i)}}class Zr{constructor(t,e,r,i){t!==Hr&&Kr.throwError("cannot use FixedNumber constructor; use FixedNumber.from",gr.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.format=i,this._hex=e,this._value=r,this._isFixedNumber=!0,Object.freeze(this)}_checkFormat(t){this.format.name!==t.format.name&&Kr.throwArgumentError("incompatible format; use fixedNumber.toFormat","other",t)}addUnsafe(t){this._checkFormat(t);const e=Yr(this._value,this.format.decimals),r=Yr(t._value,t.format.decimals);return Zr.fromValue(e.add(r),this.format.decimals,this.format)}subUnsafe(t){this._checkFormat(t);const e=Yr(this._value,this.format.decimals),r=Yr(t._value,t.format.decimals);return Zr.fromValue(e.sub(r),this.format.decimals,this.format)}mulUnsafe(t){this._checkFormat(t);const e=Yr(this._value,this.format.decimals),r=Yr(t._value,t.format.decimals);return Zr.fromValue(e.mul(r).div(this.format._multiplier),this.format.decimals,this.format)}divUnsafe(t){this._checkFormat(t);const e=Yr(this._value,this.format.decimals),r=Yr(t._value,t.format.decimals);return Zr.fromValue(e.mul(this.format._multiplier).div(r),this.format.decimals,this.format)}floor(){const t=this.toString().split(".");1===t.length&&t.push("0");let e=Zr.from(t[0],this.format);const r=!t[1].match(/^(0*)$/);return this.isNegative()&&r&&(e=e.subUnsafe(ti.toFormat(e.format))),e}ceiling(){const t=this.toString().split(".");1===t.length&&t.push("0");let e=Zr.from(t[0],this.format);const r=!t[1].match(/^(0*)$/);return!this.isNegative()&&r&&(e=e.addUnsafe(ti.toFormat(e.format))),e}round(t){null==t&&(t=0);const e=this.toString().split(".");if(1===e.length&&e.push("0"),(t<0||t>80||t%1)&&Kr.throwArgumentError("invalid decimal count","decimals",t),e[1].length<=t)return this;const r=Zr.from("1"+Gr.substring(0,t),this.format),i=ei.toFormat(this.format);return this.mulUnsafe(r).addUnsafe(i).floor().divUnsafe(r)}isZero(){return"0.0"===this._value||"0"===this._value}isNegative(){return"-"===this._value[0]}toString(){return this._value}toHexString(t){return null==t?this._hex:(t%8&&Kr.throwArgumentError("invalid byte width","width",t),Mr(qr.from(this._hex).fromTwos(this.format.width).toTwos(t).toHexString(),t/8))}toUnsafeFloat(){return parseFloat(this.toString())}toFormat(t){return Zr.fromString(this._value,t)}static fromValue(t,e,r){return null==r&&null!=e&&!function(t){return null!=t&&(qr.isBigNumber(t)||"number"==typeof t&&t%1==0||"string"==typeof t&&!!t.match(/^-?[0-9]+$/)||_r(t)||"bigint"==typeof t||br(t))}(e)&&(r=e,e=null),null==e&&(e=0),null==r&&(r="fixed"),Zr.fromString(Qr(t,e),Xr.from(r))}static fromString(t,e){null==e&&(e="fixed");const r=Xr.from(e),i=Yr(t,r.decimals);!r.signed&&i.lt(Vr)&&Jr("unsigned value cannot be negative","overflow","value",t);let n=null;r.signed?n=i.toTwos(r.width).toHexString():(n=i.toHexString(),n=Mr(n,r.width/8));const s=Qr(i,r.decimals);return new Zr(Hr,n,s,r)}static fromBytes(t,e){null==e&&(e="fixed");const r=Xr.from(e);if(Ar(t).length>r.width/8)throw new Error("overflow");let i=qr.from(t);r.signed&&(i=i.fromTwos(r.width));const n=i.toTwos((r.signed?0:1)+r.width).toHexString(),s=Qr(i,r.decimals);return new Zr(Hr,n,s,r)}static from(t,e){if("string"==typeof t)return Zr.fromString(t,e);if(br(t))return Zr.fromBytes(t,e);try{return Zr.fromValue(t,0,e)}catch(t){if(t.code!==gr.errors.INVALID_ARGUMENT)throw t}return Kr.throwArgumentError("invalid FixedNumber value","value",t)}static isFixedNumber(t){return!(!t||!t._isFixedNumber)}}const ti=Zr.from(1),ei=Zr.from("0.5"),ri=new gr("strings/5.7.0");var ii,ni;function si(t,e,r,i,n){if(t===ni.BAD_PREFIX||t===ni.UNEXPECTED_CONTINUE){let t=0;for(let i=e+1;i<r.length&&r[i]>>6==2;i++)t++;return t}return t===ni.OVERRUN?r.length-e-1:0}function oi(t,e=ii.current){e!=ii.current&&(ri.checkNormalize(),t=t.normalize(e));let r=[];for(let e=0;e<t.length;e++){const i=t.charCodeAt(e);if(i<128)r.push(i);else if(i<2048)r.push(i>>6|192),r.push(63&i|128);else if(55296==(64512&i)){e++;const n=t.charCodeAt(e);if(e>=t.length||56320!=(64512&n))throw new Error("invalid utf-8 string");const s=65536+((1023&i)<<10)+(1023&n);r.push(s>>18|240),r.push(s>>12&63|128),r.push(s>>6&63|128),r.push(63&s|128)}else r.push(i>>12|224),r.push(i>>6&63|128),r.push(63&i|128)}return Ar(r)}function ai(t,e){e||(e=function(t){return[parseInt(t,16)]});let r=0,i={};return t.split(",").forEach((t=>{let n=t.split(":");r+=parseInt(n[0],16),i[r]=e(n[1])})),i}function ci(t){let e=0;return t.split(",").map((t=>{let r=t.split("-");1===r.length?r[1]="0":""===r[1]&&(r[1]="1");let i=e+parseInt(r[0],16);return e=parseInt(r[1],16),{l:i,h:e}}))}!function(t){t.current="",t.NFC="NFC",t.NFD="NFD",t.NFKC="NFKC",t.NFKD="NFKD"}(ii||(ii={})),function(t){t.UNEXPECTED_CONTINUE="unexpected continuation byte",t.BAD_PREFIX="bad codepoint prefix",t.OVERRUN="string overrun",t.MISSING_CONTINUE="missing continuation byte",t.OUT_OF_RANGE="out of UTF-8 range",t.UTF16_SURROGATE="UTF-16 surrogate",t.OVERLONG="overlong representation"}(ni||(ni={})),Object.freeze({error:function(t,e,r,i,n){return ri.throwArgumentError(`invalid codepoint at offset ${e}; ${t}`,"bytes",r)},ignore:si,replace:function(t,e,r,i,n){return t===ni.OVERLONG?(i.push(n),0):(i.push(65533),si(t,e,r))}}),ci("221,13-1b,5f-,40-10,51-f,11-3,3-3,2-2,2-4,8,2,15,2d,28-8,88,48,27-,3-5,11-20,27-,8,28,3-5,12,18,b-a,1c-4,6-16,2-d,2-2,2,1b-4,17-9,8f-,10,f,1f-2,1c-34,33-14e,4,36-,13-,6-2,1a-f,4,9-,3-,17,8,2-2,5-,2,8-,3-,4-8,2-3,3,6-,16-6,2-,7-3,3-,17,8,3,3,3-,2,6-3,3-,4-a,5,2-6,10-b,4,8,2,4,17,8,3,6-,b,4,4-,2-e,2-4,b-10,4,9-,3-,17,8,3-,5-,9-2,3-,4-7,3-3,3,4-3,c-10,3,7-2,4,5-2,3,2,3-2,3-2,4-2,9,4-3,6-2,4,5-8,2-e,d-d,4,9,4,18,b,6-3,8,4,5-6,3-8,3-3,b-11,3,9,4,18,b,6-3,8,4,5-6,3-6,2,3-3,b-11,3,9,4,18,11-3,7-,4,5-8,2-7,3-3,b-11,3,13-2,19,a,2-,8-2,2-3,7,2,9-11,4-b,3b-3,1e-24,3,2-,3,2-,2-5,5,8,4,2,2-,3,e,4-,6,2,7-,b-,3-21,49,23-5,1c-3,9,25,10-,2-2f,23,6,3,8-2,5-5,1b-45,27-9,2a-,2-3,5b-4,45-4,53-5,8,40,2,5-,8,2,5-,28,2,5-,20,2,5-,8,2,5-,8,8,18,20,2,5-,8,28,14-5,1d-22,56-b,277-8,1e-2,52-e,e,8-a,18-8,15-b,e,4,3-b,5e-2,b-15,10,b-5,59-7,2b-555,9d-3,5b-5,17-,7-,27-,7-,9,2,2,2,20-,36,10,f-,7,14-,4,a,54-3,2-6,6-5,9-,1c-10,13-1d,1c-14,3c-,10-6,32-b,240-30,28-18,c-14,a0,115-,3,66-,b-76,5,5-,1d,24,2,5-2,2,8-,35-2,19,f-10,1d-3,311-37f,1b,5a-b,d7-19,d-3,41,57-,68-4,29-3,5f,29-37,2e-2,25-c,2c-2,4e-3,30,78-3,64-,20,19b7-49,51a7-59,48e-2,38-738,2ba5-5b,222f-,3c-94,8-b,6-4,1b,6,2,3,3,6d-20,16e-f,41-,37-7,2e-2,11-f,5-b,18-,b,14,5-3,6,88-,2,bf-2,7-,7-,7-,4-2,8,8-9,8-2ff,20,5-b,1c-b4,27-,27-cbb1,f7-9,28-2,b5-221,56,48,3-,2-,3-,5,d,2,5,3,42,5-,9,8,1d,5,6,2-2,8,153-3,123-3,33-27fd,a6da-5128,21f-5df,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3,2-1d,61-ff7d"),"ad,34f,1806,180b,180c,180d,200b,200c,200d,2060,feff".split(",").map((t=>parseInt(t,16))),ai("b5:3bc,c3:ff,7:73,2:253,5:254,3:256,1:257,5:259,1:25b,3:260,1:263,2:269,1:268,5:26f,1:272,2:275,7:280,3:283,5:288,3:28a,1:28b,5:292,3f:195,1:1bf,29:19e,125:3b9,8b:3b2,1:3b8,1:3c5,3:3c6,1:3c0,1a:3ba,1:3c1,1:3c3,2:3b8,1:3b5,1bc9:3b9,1c:1f76,1:1f77,f:1f7a,1:1f7b,d:1f78,1:1f79,1:1f7c,1:1f7d,107:63,5:25b,4:68,1:68,1:68,3:69,1:69,1:6c,3:6e,4:70,1:71,1:72,1:72,1:72,7:7a,2:3c9,2:7a,2:6b,1:e5,1:62,1:63,3:65,1:66,2:6d,b:3b3,1:3c0,6:64,1b574:3b8,1a:3c3,20:3b8,1a:3c3,20:3b8,1a:3c3,20:3b8,1a:3c3,20:3b8,1a:3c3"),ai("179:1,2:1,2:1,5:1,2:1,a:4f,a:1,8:1,2:1,2:1,3:1,5:1,3:1,4:1,2:1,3:1,4:1,8:2,1:1,2:2,1:1,2:2,27:2,195:26,2:25,1:25,1:25,2:40,2:3f,1:3f,33:1,11:-6,1:-9,1ac7:-3a,6d:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,b:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,c:-8,2:-8,2:-8,2:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,49:-8,1:-8,1:-4a,1:-4a,d:-56,1:-56,1:-56,1:-56,d:-8,1:-8,f:-8,1:-8,3:-7"),ai("df:00730073,51:00690307,19:02BC006E,a7:006A030C,18a:002003B9,16:03B903080301,20:03C503080301,1d7:05650582,190f:00680331,1:00740308,1:0077030A,1:0079030A,1:006102BE,b6:03C50313,2:03C503130300,2:03C503130301,2:03C503130342,2a:1F0003B9,1:1F0103B9,1:1F0203B9,1:1F0303B9,1:1F0403B9,1:1F0503B9,1:1F0603B9,1:1F0703B9,1:1F0003B9,1:1F0103B9,1:1F0203B9,1:1F0303B9,1:1F0403B9,1:1F0503B9,1:1F0603B9,1:1F0703B9,1:1F2003B9,1:1F2103B9,1:1F2203B9,1:1F2303B9,1:1F2403B9,1:1F2503B9,1:1F2603B9,1:1F2703B9,1:1F2003B9,1:1F2103B9,1:1F2203B9,1:1F2303B9,1:1F2403B9,1:1F2503B9,1:1F2603B9,1:1F2703B9,1:1F6003B9,1:1F6103B9,1:1F6203B9,1:1F6303B9,1:1F6403B9,1:1F6503B9,1:1F6603B9,1:1F6703B9,1:1F6003B9,1:1F6103B9,1:1F6203B9,1:1F6303B9,1:1F6403B9,1:1F6503B9,1:1F6603B9,1:1F6703B9,3:1F7003B9,1:03B103B9,1:03AC03B9,2:03B10342,1:03B1034203B9,5:03B103B9,6:1F7403B9,1:03B703B9,1:03AE03B9,2:03B70342,1:03B7034203B9,5:03B703B9,6:03B903080300,1:03B903080301,3:03B90342,1:03B903080342,b:03C503080300,1:03C503080301,1:03C10313,2:03C50342,1:03C503080342,b:1F7C03B9,1:03C903B9,1:03CE03B9,2:03C90342,1:03C9034203B9,5:03C903B9,ac:00720073,5b:00B00063,6:00B00066,d:006E006F,a:0073006D,1:00740065006C,1:0074006D,124f:006800700061,2:00610075,2:006F0076,b:00700061,1:006E0061,1:03BC0061,1:006D0061,1:006B0061,1:006B0062,1:006D0062,1:00670062,3:00700066,1:006E0066,1:03BC0066,4:0068007A,1:006B0068007A,1:006D0068007A,1:00670068007A,1:00740068007A,15:00700061,1:006B00700061,1:006D00700061,1:006700700061,8:00700076,1:006E0076,1:03BC0076,1:006D0076,1:006B0076,1:006D0076,1:00700077,1:006E0077,1:03BC0077,1:006D0077,1:006B0077,1:006D0077,1:006B03C9,1:006D03C9,2:00620071,3:00632215006B0067,1:0063006F002E,1:00640062,1:00670079,2:00680070,2:006B006B,1:006B006D,9:00700068,2:00700070006D,1:00700072,2:00730076,1:00770062,c723:00660066,1:00660069,1:0066006C,1:006600660069,1:00660066006C,1:00730074,1:00730074,d:05740576,1:05740565,1:0574056B,1:057E0576,1:0574056D",(function(t){if(t.length%4!=0)throw new Error("bad data");let e=[];for(let r=0;r<t.length;r+=4)e.push(parseInt(t.substring(r,r+4),16));return e})),ci("80-20,2a0-,39c,32,f71,18e,7f2-f,19-7,30-4,7-5,f81-b,5,a800-20ff,4d1-1f,110,fa-6,d174-7,2e84-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,2,1f-5f,ff7f-20001");const hi="hash/5.7.0";function ui(t,e){null==e&&(e=1);const r=[],i=r.forEach,n=function(t,e){i.call(t,(function(t){e>0&&Array.isArray(t)?n(t,e-1):r.push(t)}))};return n(t,e),r}function li(t){return 1&t?~t>>1:t>>1}function fi(t,e){let r=Array(t);for(let i=0,n=-1;i<t;i++)r[i]=n+=1+e();return r}function di(t,e){let r=Array(t);for(let i=0,n=0;i<t;i++)r[i]=n+=li(e());return r}function pi(t,e){let r=fi(t(),t),i=t(),n=fi(i,t),s=function(t,e){let r=Array(t);for(let i=0;i<t;i++)r[i]=1+e();return r}(i,t);for(let t=0;t<i;t++)for(let e=0;e<s[t];e++)r.push(n[t]+e);return e?r.map((t=>e[t])):r}function gi(t,e,r){let i=Array(t).fill(void 0).map((()=>[]));for(let n=0;n<e;n++)di(t,r).forEach(((t,e)=>i[e].push(t)));return i}function mi(t,e){let r=1+e(),i=e(),n=function(t){let e=[];for(;;){let r=t();if(0==r)break;e.push(r)}return e}(e);return ui(gi(n.length,1+t,e).map(((t,e)=>{const s=t[0],o=t.slice(1);return Array(n[e]).fill(void 0).map(((t,e)=>{let n=e*i;return[s+e*r,o.map((t=>t+n))]}))})))}function yi(t,e){return gi(1+e(),1+t,e).map((t=>[t[0],t.slice(1)]))}const vi=function(t){return function(t){let e=0;return()=>t[e++]}(function(t){let e=0;function r(){return t[e++]<<8|t[e++]}let i=r(),n=1,s=[0,1];for(let t=1;t<i;t++)s.push(n+=r());let o=r(),a=e;e+=o;let c=0,h=0;function u(){return 0==c&&(h=h<<8|t[e++],c=8),h>>--c&1}const l=Math.pow(2,31),f=l>>>1,d=f>>1,p=l-1;let g=0;for(let t=0;t<31;t++)g=g<<1|u();let m=[],y=0,v=l;for(;;){let t=Math.floor(((g-y+1)*n-1)/v),e=0,r=i;for(;r-e>1;){let i=e+r>>>1;t<s[i]?r=i:e=i}if(0==e)break;m.push(e);let o=y+Math.floor(v*s[e]/n),a=y+Math.floor(v*s[e+1]/n)-1;for(;!((o^a)&f);)g=g<<1&p|u(),o=o<<1&p,a=a<<1&p|1;for(;o&~a&d;)g=g&f|g<<1&p>>>1|u(),o=o<<1^f,a=(a^f)<<1|f|1;y=o,v=1+a-o}let w=i-4;return m.map((e=>{switch(e-w){case 3:return w+65792+(t[a++]<<16|t[a++]<<8|t[a++]);case 2:return w+256+(t[a++]<<8|t[a++]);case 1:return w+t[a++];default:return e-1}}))}(t))}(function(t){t=atob(t);const e=[];for(let r=0;r<t.length;r++)e.push(t.charCodeAt(r));return Ar(e)}("AEQF2AO2DEsA2wIrAGsBRABxAN8AZwCcAEwAqgA0AGwAUgByADcATAAVAFYAIQAyACEAKAAYAFgAGwAjABQAMAAmADIAFAAfABQAKwATACoADgAbAA8AHQAYABoAGQAxADgALAAoADwAEwA9ABMAGgARAA4ADwAWABMAFgAIAA8AHgQXBYMA5BHJAS8JtAYoAe4AExozi0UAH21tAaMnBT8CrnIyhrMDhRgDygIBUAEHcoFHUPe8AXBjAewCjgDQR8IICIcEcQLwATXCDgzvHwBmBoHNAqsBdBcUAykgDhAMShskMgo8AY8jqAQfAUAfHw8BDw87MioGlCIPBwZCa4ELatMAAMspJVgsDl8AIhckSg8XAHdvTwBcIQEiDT4OPhUqbyECAEoAS34Aej8Ybx83JgT/Xw8gHxZ/7w8RICxPHA9vBw+Pfw8PHwAPFv+fAsAvCc8vEr8ivwD/EQ8Bol8OEBa/A78hrwAPCU8vESNvvwWfHwNfAVoDHr+ZAAED34YaAdJPAK7PLwSEgDLHAGo1Pz8Pvx9fUwMrpb8O/58VTzAPIBoXIyQJNF8hpwIVAT8YGAUADDNBaX3RAMomJCg9EhUeA29MABsZBTMNJipjOhc19gcIDR8bBwQHEggCWi6DIgLuAQYA+BAFCha3A5XiAEsqM7UFFgFLhAMjFTMYE1Klnw74nRVBG/ASCm0BYRN/BrsU3VoWy+S0vV8LQx+vN8gF2AC2AK5EAWwApgYDKmAAroQ0NDQ0AT+OCg7wAAIHRAbpNgVcBV0APTA5BfbPFgMLzcYL/QqqA82eBALKCjQCjqYCht0/k2+OAsXQAoP3ASTKDgDw6ACKAUYCMpIKJpRaAE4A5womABzZvs0REEKiACIQAd5QdAECAj4Ywg/wGqY2AVgAYADYvAoCGAEubA0gvAY2ALAAbpbvqpyEAGAEpgQAJgAG7gAgAEACmghUFwCqAMpAINQIwC4DthRAAPcycKgApoIdABwBfCisABoATwBqASIAvhnSBP8aH/ECeAKXAq40NjgDBTwFYQU6AXs3oABgAD4XNgmcCY1eCl5tIFZeUqGgyoNHABgAEQAaABNwWQAmABMATPMa3T34ADldyprmM1M2XociUQgLzvwAXT3xABgAEQAaABNwIGFAnADD8AAgAD4BBJWzaCcIAIEBFMAWwKoAAdq9BWAF5wLQpALEtQAKUSGkahR4GnJM+gsAwCgeFAiUAECQ0BQuL8AAIAAAADKeIheclvFqQAAETr4iAMxIARMgAMIoHhQIAn0E0pDQFC4HhznoAAAAIAI2C0/4lvFqQAAETgBJJwYCAy4ABgYAFAA8MBKYEH4eRhTkAjYeFcgACAYAeABsOqyQ5gRwDayqugEgaIIAtgoACgDmEABmBAWGme5OBJJA2m4cDeoAmITWAXwrMgOgAGwBCh6CBXYF1Tzg1wKAAFdiuABRAFwAXQBsAG8AdgBrAHYAbwCEAHEwfxQBVE5TEQADVFhTBwBDANILAqcCzgLTApQCrQL6vAAMAL8APLhNBKkE6glGKTAU4Dr4N2EYEwBCkABKk8rHAbYBmwIoAiU4Ajf/Aq4CowCAANIChzgaNBsCsTgeODcFXrgClQKdAqQBiQGYAqsCsjTsNHsfNPA0ixsAWTWiOAMFPDQSNCk2BDZHNow2TTZUNhk28Jk9VzI3QkEoAoICoQKwAqcAQAAxBV4FXbS9BW47YkIXP1ciUqs05DS/FwABUwJW11e6nHuYZmSh/RAYA8oMKvZ8KASoUAJYWAJ6ILAsAZSoqjpgA0ocBIhmDgDWAAawRDQoAAcuAj5iAHABZiR2AIgiHgCaAU68ACxuHAG0ygM8MiZIAlgBdF4GagJqAPZOHAMuBgoATkYAsABiAHgAMLoGDPj0HpKEBAAOJgAuALggTAHWAeAMEDbd20Uege0ADwAWADkAQgA9OHd+2MUQZBBhBgNNDkxxPxUQArEPqwvqERoM1irQ090ANK4H8ANYB/ADWANYB/AH8ANYB/ADWANYA1gDWBwP8B/YxRBkD00EcgWTBZAE2wiIJk4RhgctCNdUEnQjHEwDSgEBIypJITuYMxAlR0wRTQgIATZHbKx9PQNMMbBU+pCnA9AyVDlxBgMedhKlAC8PeCE1uk6DekxxpQpQT7NX9wBFBgASqwAS5gBJDSgAUCwGPQBI4zTYABNGAE2bAE3KAExdGABKaAbgAFBXAFCOAFBJABI2SWdObALDOq0//QomCZhvwHdTBkIQHCemEPgMNAG2ATwN7kvZBPIGPATKH34ZGg/OlZ0Ipi3eDO4m5C6igFsj9iqEBe5L9TzeC05RaQ9aC2YJ5DpkgU8DIgEOIowK3g06CG4Q9ArKbA3mEUYHOgPWSZsApgcCCxIdNhW2JhFirQsKOXgG/Br3C5AmsBMqev0F1BoiBk4BKhsAANAu6IWxWjJcHU9gBgQLJiPIFKlQIQ0mQLh4SRocBxYlqgKSQ3FKiFE3HpQh9zw+DWcuFFF9B/Y8BhlQC4I8n0asRQ8R0z6OPUkiSkwtBDaALDAnjAnQD4YMunxzAVoJIgmyDHITMhEYN8YIOgcaLpclJxYIIkaWYJsE+KAD9BPSAwwFQAlCBxQDthwuEy8VKgUOgSXYAvQ21i60ApBWgQEYBcwPJh/gEFFH4Q7qCJwCZgOEJewALhUiABginAhEZABgj9lTBi7MCMhqbSN1A2gU6GIRdAeSDlgHqBw0FcAc4nDJXgyGCSiksAlcAXYJmgFgBOQICjVcjKEgQmdUi1kYnCBiQUBd/QIyDGYVoES+h3kCjA9sEhwBNgF0BzoNAgJ4Ee4RbBCWCOyGBTW2M/k6JgRQIYQgEgooA1BszwsoJvoM+WoBpBJjAw00PnfvZ6xgtyUX/gcaMsZBYSHyC5NPzgydGsIYQ1QvGeUHwAP0GvQn60FYBgADpAQUOk4z7wS+C2oIjAlAAEoOpBgH2BhrCnKM0QEyjAG4mgNYkoQCcJAGOAcMAGgMiAV65gAeAqgIpAAGANADWAA6Aq4HngAaAIZCAT4DKDABIuYCkAOUCDLMAZYwAfQqBBzEDBYA+DhuSwLDsgKAa2ajBd5ZAo8CSjYBTiYEBk9IUgOwcuIA3ABMBhTgSAEWrEvMG+REAeBwLADIAPwABjYHBkIBzgH0bgC4AWALMgmjtLYBTuoqAIQAFmwB2AKKAN4ANgCA8gFUAE4FWvoF1AJQSgESMhksWGIBvAMgATQBDgB6BsyOpsoIIARuB9QCEBwV4gLvLwe2AgMi4BPOQsYCvd9WADIXUu5eZwqoCqdeaAC0YTQHMnM9UQAPH6k+yAdy/BZIiQImSwBQ5gBQQzSaNTFWSTYBpwGqKQK38AFtqwBI/wK37gK3rQK3sAK6280C0gK33AK3zxAAUEIAUD9SklKDArekArw5AEQAzAHCO147WTteO1k7XjtZO147WTteO1kDmChYI03AVU0oJqkKbV9GYewMpw3VRMk6ShPcYFJgMxPJLbgUwhXPJVcZPhq9JwYl5VUKDwUt1GYxCC00dhe9AEApaYNCY4ceMQpMHOhTklT5LRwAskujM7ANrRsWREEFSHXuYisWDwojAmSCAmJDXE6wXDchAqH4AmiZAmYKAp+FOBwMAmY8AmYnBG8EgAN/FAN+kzkHOXgYOYM6JCQCbB4CMjc4CwJtyAJtr/CLADRoRiwBaADfAOIASwYHmQyOAP8MwwAOtgJ3MAJ2o0ACeUxEAni7Hl3cRa9G9AJ8QAJ6yQJ9CgJ88UgBSH5kJQAsFklZSlwWGErNAtECAtDNSygDiFADh+dExpEzAvKiXQQDA69Lz0wuJgTQTU1NsAKLQAKK2cIcCB5EaAa4Ao44Ao5dQZiCAo7aAo5deVG1UzYLUtVUhgKT/AKTDQDqAB1VH1WwVdEHLBwplocy4nhnRTw6ApegAu+zWCKpAFomApaQApZ9nQCqWa1aCoJOADwClrYClk9cRVzSApnMApllXMtdCBoCnJw5wzqeApwXAp+cAp65iwAeEDIrEAKd8gKekwC2PmE1YfACntQCoG8BqgKeoCACnk+mY8lkKCYsAiewAiZ/AqD8AqBN2AKmMAKlzwKoAAB+AqfzaH1osgAESmodatICrOQCrK8CrWgCrQMCVx4CVd0CseLYAx9PbJgCsr4OArLpGGzhbWRtSWADJc4Ctl08QG6RAylGArhfArlIFgK5K3hwN3DiAr0aAy2zAzISAr6JcgMDM3ICvhtzI3NQAsPMAsMFc4N0TDZGdOEDPKgDPJsDPcACxX0CxkgCxhGKAshqUgLIRQLJUALJLwJkngLd03h6YniveSZL0QMYpGcDAmH1GfSVJXsMXpNevBICz2wCz20wTFTT9BSgAMeuAs90ASrrA04TfkwGAtwoAtuLAtJQA1JdA1NgAQIDVY2AikABzBfuYUZ2AILPg44C2sgC2d+EEYRKpz0DhqYAMANkD4ZyWvoAVgLfZgLeuXR4AuIw7RUB8zEoAfScAfLTiALr9ALpcXoAAur6AurlAPpIAboC7ooC652Wq5cEAu5AA4XhmHpw4XGiAvMEAGoDjheZlAL3FAORbwOSiAL3mQL52gL4Z5odmqy8OJsfA52EAv77ARwAOp8dn7QDBY4DpmsDptoA0sYDBmuhiaIGCgMMSgFgASACtgNGAJwEgLpoBgC8BGzAEowcggCEDC6kdjoAJAM0C5IKRoABZCgiAIzw3AYBLACkfng9ogigkgNmWAN6AEQCvrkEVqTGAwCsBRbAA+4iQkMCHR072jI2PTbUNsk2RjY5NvA23TZKNiU3EDcZN5I+RTxDRTBCJkK5VBYKFhZfwQCWygU3AJBRHpu+OytgNxa61A40GMsYjsn7BVwFXQVcBV0FaAVdBVwFXQVcBV0FXAVdBVwFXUsaCNyKAK4AAQUHBwKU7oICoW1e7jAEzgPxA+YDwgCkBFDAwADABKzAAOxFLhitA1UFTDeyPkM+bj51QkRCuwTQWWQ8X+0AWBYzsACNA8xwzAGm7EZ/QisoCTAbLDs6fnLfb8H2GccsbgFw13M1HAVkBW/Jxsm9CNRO8E8FDD0FBQw9FkcClOYCoMFegpDfADgcMiA2AJQACB8AsigKAIzIEAJKeBIApY5yPZQIAKQiHb4fvj5BKSRPQrZCOz0oXyxgOywfKAnGbgMClQaCAkILXgdeCD9IIGUgQj5fPoY+dT52Ao5CM0dAX9BTVG9SDzFwWTQAbxBzJF/lOEIQQglCCkKJIAls5AcClQICoKPMODEFxhi6KSAbiyfIRrMjtCgdWCAkPlFBIitCsEJRzAbMAV/OEyQzDg0OAQQEJ36i328/Mk9AybDJsQlq3tDRApUKAkFzXf1d/j9uALYP6hCoFgCTGD8kPsFKQiobrm0+zj0KSD8kPnVCRBwMDyJRTHFgMTJa5rwXQiQ2YfI/JD7BMEJEHGINTw4TOFlIRzwJO0icMQpyPyQ+wzJCRBv6DVgnKB01NgUKj2bwYzMqCoBkznBgEF+zYDIocwRIX+NgHj4HICNfh2C4CwdwFWpTG/lgUhYGAwRfv2Ts8mAaXzVgml/XYIJfuWC4HI1gUF9pYJZgMR6ilQHMAOwLAlDRefC0in4AXAEJA6PjCwc0IamOANMMCAECRQDFNRTZBgd+CwQlRA+r6+gLBDEFBnwUBXgKATIArwAGRAAHA3cDdAN2A3kDdwN9A3oDdQN7A30DfAN4A3oDfQAYEAAlAtYASwMAUAFsAHcKAHcAmgB3AHUAdQB2AHVu8UgAygDAAHcAdQB1AHYAdQALCgB3AAsAmgB3AAsCOwB3AAtu8UgAygDAAHgKAJoAdwB3AHUAdQB2AHUAeAB1AHUAdgB1bvFIAMoAwAALCgCaAHcACwB3AAsCOwB3AAtu8UgAygDAAH4ACwGgALcBpwC6AahdAu0COwLtbvFIAMoAwAALCgCaAu0ACwLtAAsCOwLtAAtu8UgAygDAA24ACwNvAAu0VsQAAzsAABCkjUIpAAsAUIusOggWcgMeBxVsGwL67U/2HlzmWOEeOgALASvuAAseAfpKUpnpGgYJDCIZM6YyARUE9ThqAD5iXQgnAJYJPnOzw0ZAEZxEKsIAkA4DhAHnTAIDxxUDK0lxCQlPYgIvIQVYJQBVqE1GakUAKGYiDToSBA1EtAYAXQJYAIF8GgMHRyAAIAjOe9YncekRAA0KACUrjwE7Ayc6AAYWAqaiKG4McEcqANoN3+Mg9TwCBhIkuCny+JwUQ29L008JluRxu3K+oAdqiHOqFH0AG5SUIfUJ5SxCGfxdipRzqTmT4V5Zb+r1Uo4Vm+NqSSEl2mNvR2JhIa8SpYO6ntdwFXHCWTCK8f2+Hxo7uiG3drDycAuKIMP5bhi06ACnqArH1rz4Rqg//lm6SgJGEVbF9xJHISaR6HxqxSnkw6shDnelHKNEfGUXSJRJ1GcsmtJw25xrZMDK9gXSm1/YMkdX4/6NKYOdtk/NQ3/NnDASjTc3fPjIjW/5sVfVObX2oTDWkr1dF9f3kxBsD3/3aQO8hPfRz+e0uEiJqt1161griu7gz8hDDwtpy+F+BWtefnKHZPAxcZoWbnznhJpy0e842j36bcNzGnIEusgGX0a8ZxsnjcSsPDZ09yZ36fCQbriHeQ72JRMILNl6ePPf2HWoVwgWAm1fb3V2sAY0+B6rAXqSwPBgseVmoqsBTSrm91+XasMYYySI8eeRxH3ZvHkMz3BQ5aJ3iUVbYPNM3/7emRtjlsMgv/9VyTsyt/mK+8fgWeT6SoFaclXqn42dAIsvAarF5vNNWHzKSkKQ/8Hfk5ZWK7r9yliOsooyBjRhfkHP4Q2DkWXQi6FG/9r/IwbmkV5T7JSopHKn1pJwm9tb5Ot0oyN1Z2mPpKXHTxx2nlK08fKk1hEYA8WgVVWL5lgx0iTv+KdojJeU23ZDjmiubXOxVXJKKi2Wjuh2HLZOFLiSC7Tls5SMh4f+Pj6xUSrNjFqLGehRNB8lC0QSLNmkJJx/wSG3MnjE9T1CkPwJI0wH2lfzwETIiVqUxg0dfu5q39Gt+hwdcxkhhNvQ4TyrBceof3Mhs/IxFci1HmHr4FMZgXEEczPiGCx0HRwzAqDq2j9AVm1kwN0mRVLWLylgtoPNapF5cY4Y1wJh/e0BBwZj44YgZrDNqvD/9Hv7GFYdUQeDJuQ3EWI4HaKqavU1XjC/n41kT4L79kqGq0kLhdTZvgP3TA3fS0ozVz+5piZsoOtIvBUFoMKbNcmBL6YxxaUAusHB38XrS8dQMnQwJfUUkpRoGr5AUeWicvBTzyK9g77+yCkf5PAysL7r/JjcZgrbvRpMW9iyaxZvKO6ceZN2EwIxKwVFPuvFuiEPGCoagbMo+SpydLrXqBzNCDGFCrO/rkcwa2xhokQZ5CdZ0AsU3JfSqJ6n5I14YA+P/uAgfhPU84Tlw7cEFfp7AEE8ey4sP12PTt4Cods1GRgDOB5xvyiR5m+Bx8O5nBCNctU8BevfV5A08x6RHd5jcwPTMDSZJOedIZ1cGQ704lxbAzqZOP05ZxaOghzSdvFBHYqomATARyAADK4elP8Ly3IrUZKfWh23Xy20uBUmLS4Pfagu9+oyVa2iPgqRP3F2CTUsvJ7+RYnN8fFZbU/HVvxvcFFDKkiTqV5UBZ3Gz54JAKByi9hkKMZJvuGgcSYXFmw08UyoQyVdfTD1/dMkCHXcTGAKeROgArsvmRrQTLUOXioOHGK2QkjHuoYFgXciZoTJd6Fs5q1QX1G+p/e26hYsEf7QZD1nnIyl/SFkNtYYmmBhpBrxl9WbY0YpHWRuw2Ll/tj9mD8P4snVzJl4F9J+1arVeTb9E5r2ILH04qStjxQNwn3m4YNqxmaNbLAqW2TN6LidwuJRqS+NXbtqxoeDXpxeGWmxzSkWxjkyCkX4NQRme6q5SAcC+M7+9ETfA/EwrzQajKakCwYyeunP6ZFlxU2oMEn1Pz31zeStW74G406ZJFCl1wAXIoUKkWotYEpOuXB1uVNxJ63dpJEqfxBeptwIHNrPz8BllZoIcBoXwgfJ+8VAUnVPvRvexnw0Ma/WiGYuJO5y8QTvEYBigFmhUxY5RqzE8OcywN/8m4UYrlaniJO75XQ6KSo9+tWHlu+hMi0UVdiKQp7NelnoZUzNaIyBPVeOwK6GNp+FfHuPOoyhaWuNvTYFkvxscMQWDh+zeFCFkgwbXftiV23ywJ4+uwRqmg9k3KzwIQpzppt8DBBOMbrqwQM5Gb05sEwdKzMiAqOloaA/lr0KA+1pr0/+HiWoiIjHA/wir2nIuS3PeU/ji3O6ZwoxcR1SZ9FhtLC5S0FIzFhbBWcGVP/KpxOPSiUoAdWUpqKH++6Scz507iCcxYI6rdMBICPJZea7OcmeFw5mObJSiqpjg2UoWNIs+cFhyDSt6geV5qgi3FunmwwDoGSMgerFOZGX1m0dMCYo5XOruxO063dwENK9DbnVM9wYFREzh4vyU1WYYJ/LRRp6oxgjqP/X5a8/4Af6p6NWkQferzBmXme0zY/4nwMJm/wd1tIqSwGz+E3xPEAOoZlJit3XddD7/BT1pllzOx+8bmQtANQ/S6fZexc6qi3W+Q2xcmXTUhuS5mpHQRvcxZUN0S5+PL9lXWUAaRZhEH8hTdAcuNMMCuVNKTEGtSUKNi3O6KhSaTzck8csZ2vWRZ+d7mW8c4IKwXIYd25S/zIftPkwPzufjEvOHWVD1m+FjpDVUTV0DGDuHj6QnaEwLu/dEgdLQOg9E1Sro9XHJ8ykLAwtPu+pxqKDuFexqON1sKQm7rwbE1E68UCfA/erovrTCG+DBSNg0l4goDQvZN6uNlbyLpcZAwj2UclycvLpIZMgv4yRlpb3YuMftozorbcGVHt/VeDV3+Fdf1TP0iuaCsPi2G4XeGhsyF1ubVDxkoJhmniQ0/jSg/eYML9KLfnCFgISWkp91eauR3IQvED0nAPXK+6hPCYs+n3+hCZbiskmVMG2da+0EsZPonUeIY8EbfusQXjsK/eFDaosbPjEfQS0RKG7yj5GG69M7MeO1HmiUYocgygJHL6M1qzUDDwUSmr99V7Sdr2F3JjQAJY+F0yH33Iv3+C9M38eML7gTgmNu/r2bUMiPvpYbZ6v1/IaESirBHNa7mPKn4dEmYg7v/+HQgPN1G79jBQ1+soydfDC2r+h2Bl/KIc5KjMK7OH6nb1jLsNf0EHVe2KBiE51ox636uyG6Lho0t3J34L5QY/ilE3mikaF4HKXG1mG1rCevT1Vv6GavltxoQe/bMrpZvRggnBxSEPEeEzkEdOxTnPXHVjUYdw8JYvjB/o7Eegc3Ma+NUxLLnsK0kJlinPmUHzHGtrk5+CAbVzFOBqpyy3QVUnzTDfC/0XD94/okH+OB+i7g9lolhWIjSnfIb+Eq43ZXOWmwvjyV/qqD+t0e+7mTEM74qP/Ozt8nmC7mRpyu63OB4KnUzFc074SqoyPUAgM+/TJGFo6T44EHnQU4X4z6qannVqgw/U7zCpwcmXV1AubIrvOmkKHazJAR55ePjp5tLBsN8vAqs3NAHdcEHOR2xQ0lsNAFzSUuxFQCFYvXLZJdOj9p4fNq6p0HBGUik2YzaI4xySy91KzhQ0+q1hjxvImRwPRf76tChlRkhRCi74NXZ9qUNeIwP+s5p+3m5nwPdNOHgSLD79n7O9m1n1uDHiMntq4nkYwV5OZ1ENbXxFd4PgrlvavZsyUO4MqYlqqn1O8W/I1dEZq5dXhrbETLaZIbC2Kj/Aa/QM+fqUOHdf0tXAQ1huZ3cmWECWSXy/43j35+Mvq9xws7JKseriZ1pEWKc8qlzNrGPUGcVgOa9cPJYIJsGnJTAUsEcDOEVULO5x0rXBijc1lgXEzQQKhROf8zIV82w8eswc78YX11KYLWQRcgHNJElBxfXr72lS2RBSl07qTKorO2uUDZr3sFhYsvnhLZn0A94KRzJ/7DEGIAhW5ZWFpL8gEwu1aLA9MuWZzNwl8Oze9Y+bX+v9gywRVnoB5I/8kXTXU3141yRLYrIOOz6SOnyHNy4SieqzkBXharjfjqq1q6tklaEbA8Qfm2DaIPs7OTq/nvJBjKfO2H9bH2cCMh1+5gspfycu8f/cuuRmtDjyqZ7uCIMyjdV3a+p3fqmXsRx4C8lujezIFHnQiVTXLXuI1XrwN3+siYYj2HHTvESUx8DlOTXpak9qFRK+L3mgJ1WsD7F4cu1aJoFoYQnu+wGDMOjJM3kiBQWHCcvhJ/HRdxodOQp45YZaOTA22Nb4XKCVxqkbwMYFhzYQYIAnCW8FW14uf98jhUG2zrKhQQ0q0CEq0t5nXyvUyvR8DvD69LU+g3i+HFWQMQ8PqZuHD+sNKAV0+M6EJC0szq7rEr7B5bQ8BcNHzvDMc9eqB5ZCQdTf80Obn4uzjwpYU7SISdtV0QGa9D3Wrh2BDQtpBKxaNFV+/Cy2P/Sv+8s7Ud0Fd74X4+o/TNztWgETUapy+majNQ68Lq3ee0ZO48VEbTZYiH1Co4OlfWef82RWeyUXo7woM03PyapGfikTnQinoNq5z5veLpeMV3HCAMTaZmA1oGLAn7XS3XYsz+XK7VMQsc4XKrmDXOLU/pSXVNUq8dIqTba///3x6LiLS6xs1xuCAYSfcQ3+rQgmu7uvf3THKt5Ooo97TqcbRqxx7EASizaQCBQllG/rYxVapMLgtLbZS64w1MDBMXX+PQpBKNwqUKOf2DDRDUXQf9EhOS0Qj4nTmlA8dzSLz/G1d+Ud8MTy/6ghhdiLpeerGY/UlDOfiuqFsMUU5/UYlP+BAmgRLuNpvrUaLlVkrqDievNVEAwF+4CoM1MZTmjxjJMsKJq+u8Zd7tNCUFy6LiyYXRJQ4VyvEQFFaCGKsxIwQkk7EzZ6LTJq2hUuPhvAW+gQnSG6J+MszC+7QCRHcnqDdyNRJ6T9xyS87A6MDutbzKGvGktpbXqtzWtXb9HsfK2cBMomjN9a4y+TaJLnXxAeX/HWzmf4cR4vALt/P4w4qgKY04ml4ZdLOinFYS6cup3G/1ie4+t1eOnpBNlqGqs75ilzkT4+DsZQxNvaSKJ//6zIbbk/M7LOhFmRc/1R+kBtz7JFGdZm/COotIdvQoXpTqP/1uqEUmCb/QWoGLMwO5ANcHzxdY48IGP5+J+zKOTBFZ4Pid+GTM+Wq12MV/H86xEJptBa6T+p3kgpwLedManBHC2GgNrFpoN2xnrMz9WFWX/8/ygSBkavq2Uv7FdCsLEYLu9LLIvAU0bNRDtzYl+/vXmjpIvuJFYjmI0im6QEYqnIeMsNjXG4vIutIGHijeAG/9EDBozKV5cldkHbLxHh25vT+ZEzbhXlqvpzKJwcEgfNwLAKFeo0/pvEE10XDB+EXRTXtSzJozQKFFAJhMxYkVaCW+E9AL7tMeU8acxidHqzb6lX4691UsDpy/LLRmT+epgW56+5Cw8tB4kMUv6s9lh3eRKbyGs+H/4mQMaYzPTf2OOdokEn+zzgvoD3FqNKk8QqGAXVsqcGdXrT62fSPkR2vROFi68A6se86UxRUk4cajfPyCC4G5wDhD+zNq4jodQ4u4n/m37Lr36n4LIAAsVr02dFi9AiwA81MYs2rm4eDlDNmdMRvEKRHfBwW5DdMNp0jPFZMeARqF/wL4XBfd+EMLBfMzpH5GH6NaW+1vrvMdg+VxDzatk3MXgO3ro3P/DpcC6+Mo4MySJhKJhSR01SGGGp5hPWmrrUgrv3lDnP+HhcI3nt3YqBoVAVTBAQT5iuhTg8nvPtd8ZeYj6w1x6RqGUBrSku7+N1+BaasZvjTk64RoIDlL8brpEcJx3OmY7jLoZsswdtmhfC/G21llXhITOwmvRDDeTTPbyASOa16cF5/A1fZAidJpqju3wYAy9avPR1ya6eNp9K8XYrrtuxlqi+bDKwlfrYdR0RRiKRVTLOH85+ZY7XSmzRpfZBJjaTa81VDcJHpZnZnSQLASGYW9l51ZV/h7eVzTi3Hv6hUsgc/51AqJRTkpbFVLXXszoBL8nBX0u/0jBLT8nH+fJePbrwURT58OY+UieRjd1vs04w0VG5VN2U6MoGZkQzKN/ptz0Q366dxoTGmj7i1NQGHi9GgnquXFYdrCfZBmeb7s0T6yrdlZH5cZuwHFyIJ/kAtGsTg0xH5taAAq44BAk1CPk9KVVbqQzrCUiFdF/6gtlPQ8bHHc1G1W92MXGZ5HEHftyLYs8mbD/9xYRUWkHmlM0zC2ilJlnNgV4bfALpQghxOUoZL7VTqtCHIaQSXm+YUMnpkXybnV+A6xlm2CVy8fn0Xlm2XRa0+zzOa21JWWmixfiPMSCZ7qA4rS93VN3pkpF1s5TonQjisHf7iU9ZGvUPOAKZcR1pbeVf/Ul7OhepGCaId9wOtqo7pJ7yLcBZ0pFkOF28y4zEI/kcUNmutBHaQpBdNM8vjCS6HZRokkeo88TBAjGyG7SR+6vUgTcyK9Imalj0kuxz0wmK+byQU11AiJFk/ya5dNduRClcnU64yGu/ieWSeOos1t3ep+RPIWQ2pyTYVbZltTbsb7NiwSi3AV+8KLWk7LxCnfZUetEM8ThnsSoGH38/nyAwFguJp8FjvlHtcWZuU4hPva0rHfr0UhOOJ/F6vS62FW7KzkmRll2HEc7oUq4fyi5T70Vl7YVIfsPHUCdHesf9Lk7WNVWO75JDkYbMI8TOW8JKVtLY9d6UJRITO8oKo0xS+o99Yy04iniGHAaGj88kEWgwv0OrHdY/nr76DOGNS59hXCGXzTKUvDl9iKpLSWYN1lxIeyywdNpTkhay74w2jFT6NS8qkjo5CxA1yfSYwp6AJIZNKIeEK5PJAW7ORgWgwp0VgzYpqovMrWxbu+DGZ6Lhie1RAqpzm8VUzKJOH3mCzWuTOLsN3VT/dv2eeYe9UjbR8YTBsLz7q60VN1sU51k+um1f8JxD5pPhbhSC8rRaB454tmh6YUWrJI3+GWY0qeWioj/tbkYITOkJaeuGt4JrJvHA+l0Gu7kY7XOaa05alMnRWVCXqFgLIwSY4uF59Ue5SU4QKuc/HamDxbr0x6csCetXGoP7Qn1Bk/J9DsynO/UD6iZ1Hyrz+jit0hDCwi/E9OjgKTbB3ZQKQ/0ZOvevfNHG0NK4Aj3Cp7NpRk07RT1i/S0EL93Ag8GRgKI9CfpajKyK6+Jj/PI1KO5/85VAwz2AwzP8FTBb075IxCXv6T9RVvWT2tUaqxDS92zrGUbWzUYk9mSs82pECH+fkqsDt93VW++4YsR/dHCYcQSYTO/KaBMDj9LSD/J/+z20Kq8XvZUAIHtm9hRPP3ItbuAu2Hm5lkPs92pd7kCxgRs0xOVBnZ13ccdA0aunrwv9SdqElJRC3g+oCu+nXyCgmXUs9yMjTMAIHfxZV+aPKcZeUBWt057Xo85Ks1Ir5gzEHCWqZEhrLZMuF11ziGtFQUds/EESajhagzcKsxamcSZxGth4UII+adPhQkUnx2WyN+4YWR+r3f8MnkyGFuR4zjzxJS8WsQYR5PTyRaD9ixa6Mh741nBHbzfjXHskGDq179xaRNrCIB1z1xRfWfjqw2pHc1zk9xlPpL8sQWAIuETZZhbnmL54rceXVNRvUiKrrqIkeogsl0XXb17ylNb0f4GA9Wd44vffEG8FSZGHEL2fbaTGRcSiCeA8PmA/f6Hz8HCS76fXUHwgwkzSwlI71ekZ7Fapmlk/KC+Hs8hUcw3N2LN5LhkVYyizYFl/uPeVP5lsoJHhhfWvvSWruCUW1ZcJOeuTbrDgywJ/qG07gZJplnTvLcYdNaH0KMYOYMGX+rB4NGPFmQsNaIwlWrfCezxre8zXBrsMT+edVLbLqN1BqB76JH4BvZTqUIMfGwPGEn+EnmTV86fPBaYbFL3DFEhjB45CewkXEAtJxk4/Ms2pPXnaRqdky0HOYdcUcE2zcXq4vaIvW2/v0nHFJH2XXe22ueDmq/18XGtELSq85j9X8q0tcNSSKJIX8FTuJF/Pf8j5PhqG2u+osvsLxYrvvfeVJL+4tkcXcr9JV7v0ERmj/X6fM3NC4j6dS1+9Umr2oPavqiAydTZPLMNRGY23LO9zAVDly7jD+70G5TPPLdhRIl4WxcYjLnM+SNcJ26FOrkrISUtPObIz5Zb3AG612krnpy15RMW+1cQjlnWFI6538qky9axd2oJmHIHP08KyP0ubGO+TQNOYuv2uh17yCIvR8VcStw7o1g0NM60sk+8Tq7YfIBJrtp53GkvzXH7OA0p8/n/u1satf/VJhtR1l8Wa6Gmaug7haSpaCaYQax6ta0mkutlb+eAOSG1aobM81D9A4iS1RRlzBBoVX6tU1S6WE2N9ORY6DfeLRC4l9Rvr5h95XDWB2mR1d4WFudpsgVYwiTwT31ljskD8ZyDOlm5DkGh9N/UB/0AI5Xvb8ZBmai2hQ4BWMqFwYnzxwB26YHSOv9WgY3JXnvoN+2R4rqGVh/LLDMtpFP+SpMGJNWvbIl5SOodbCczW2RKleksPoUeGEzrjtKHVdtZA+kfqO+rVx/iclCqwoopepvJpSTDjT+b9GWylGRF8EDbGlw6eUzmJM95Ovoz+kwLX3c2fTjFeYEsE7vUZm3mqdGJuKh2w9/QGSaqRHs99aScGOdDqkFcACoqdbBoQqqjamhH6Q9ng39JCg3lrGJwd50Qk9ovnqBTr8MME7Ps2wiVfygUmPoUBJJfJWX5Nda0nuncbFkA=="));new Set(pi(vi)),new Set(pi(vi)),function(t){let e=[];for(;;){let r=t();if(0==r)break;e.push(mi(r,t))}for(;;){let r=t()-1;if(r<0)break;e.push(yi(r,t))}!function(t){const e={};for(let r=0;r<t.length;r++){const i=t[r];e[i[0]]=i[1]}}(ui(e))}(vi),function(t){let e=pi(t).sort(((t,e)=>t-e));!function r(){let i=[];for(;;){let n=pi(t,e);if(0==n.length)break;i.push({set:new Set(n),node:r()})}i.sort(((t,e)=>e.set.size-t.set.size));let n=t(),s=n%3;n=n/3|0;let o=!!(1&n);return n>>=1,{branches:i,valid:s,fe0f:o,save:1==n,check:2==n}}()}(vi),new gr(hi),new Uint8Array(32).fill(0);const wi="Ethereum Signed Message:\n";function bi(t){return"string"==typeof t&&(t=oi(t)),xr(function(t){const e=t.map((t=>Ar(t))),r=e.reduce(((t,e)=>t+e.length),0),i=new Uint8Array(r);return e.reduce(((t,e)=>(i.set(e,t),t+e.length)),0),vr(i)}([oi(wi),oi(String(t.length)),t]))}new gr("rlp/5.7.0");const Ai=new gr("address/5.7.0");function _i(t){_r(t,20)||Ai.throwArgumentError("invalid address","address",t);const e=(t=t.toLowerCase()).substring(2).split(""),r=new Uint8Array(40);for(let t=0;t<40;t++)r[t]=e[t].charCodeAt(0);const i=Ar(xr(r));for(let t=0;t<40;t+=2)i[t>>1]>>4>=8&&(e[t]=e[t].toUpperCase()),(15&i[t>>1])>=8&&(e[t+1]=e[t+1].toUpperCase());return"0x"+e.join("")}const Ei={};for(let t=0;t<10;t++)Ei[String(t)]=String(t);for(let t=0;t<26;t++)Ei[String.fromCharCode(65+t)]=String(10+t);const Ii=Math.floor(function(t){return Math.log10?Math.log10(t):Math.log(t)/Math.LN10}(9007199254740991));function Si(t,e,r){Object.defineProperty(t,e,{enumerable:!0,value:r,writable:!1})}new gr("properties/5.7.0"),new gr(hi),new Uint8Array(32).fill(0),qr.from(-1);const Mi=qr.from(0),Pi=qr.from(1);qr.from("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),Mr(Pi.toHexString(),32),Mr(Mi.toHexString(),32);var xi={},Ri={},Ni=Ci;function Ci(t,e){if(!t)throw new Error(e||"Assertion failed")}Ci.equal=function(t,e,r){if(t!=e)throw new Error(r||"Assertion failed: "+t+" != "+e)};var Oi={exports:{}};"function"==typeof Object.create?Oi.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:Oi.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}};var Ui=Ni,Ti=Oi.exports;function Di(t,e){return!(55296!=(64512&t.charCodeAt(e))||e<0||e+1>=t.length)&&56320==(64512&t.charCodeAt(e+1))}function Bi(t){return(t>>>24|t>>>8&65280|t<<8&16711680|(255&t)<<24)>>>0}function ki(t){return 1===t.length?"0"+t:t}function qi(t){return 7===t.length?"0"+t:6===t.length?"00"+t:5===t.length?"000"+t:4===t.length?"0000"+t:3===t.length?"00000"+t:2===t.length?"000000"+t:1===t.length?"0000000"+t:t}Ri.inherits=Ti,Ri.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var r=[];if("string"==typeof t)if(e){if("hex"===e)for((t=t.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(t="0"+t),n=0;n<t.length;n+=2)r.push(parseInt(t[n]+t[n+1],16))}else for(var i=0,n=0;n<t.length;n++){var s=t.charCodeAt(n);s<128?r[i++]=s:s<2048?(r[i++]=s>>6|192,r[i++]=63&s|128):Di(t,n)?(s=65536+((1023&s)<<10)+(1023&t.charCodeAt(++n)),r[i++]=s>>18|240,r[i++]=s>>12&63|128,r[i++]=s>>6&63|128,r[i++]=63&s|128):(r[i++]=s>>12|224,r[i++]=s>>6&63|128,r[i++]=63&s|128)}else for(n=0;n<t.length;n++)r[n]=0|t[n];return r},Ri.toHex=function(t){for(var e="",r=0;r<t.length;r++)e+=ki(t[r].toString(16));return e},Ri.htonl=Bi,Ri.toHex32=function(t,e){for(var r="",i=0;i<t.length;i++){var n=t[i];"little"===e&&(n=Bi(n)),r+=qi(n.toString(16))}return r},Ri.zero2=ki,Ri.zero8=qi,Ri.join32=function(t,e,r,i){var n=r-e;Ui(n%4==0);for(var s=new Array(n/4),o=0,a=e;o<s.length;o++,a+=4){var c;c="big"===i?t[a]<<24|t[a+1]<<16|t[a+2]<<8|t[a+3]:t[a+3]<<24|t[a+2]<<16|t[a+1]<<8|t[a],s[o]=c>>>0}return s},Ri.split32=function(t,e){for(var r=new Array(4*t.length),i=0,n=0;i<t.length;i++,n+=4){var s=t[i];"big"===e?(r[n]=s>>>24,r[n+1]=s>>>16&255,r[n+2]=s>>>8&255,r[n+3]=255&s):(r[n+3]=s>>>24,r[n+2]=s>>>16&255,r[n+1]=s>>>8&255,r[n]=255&s)}return r},Ri.rotr32=function(t,e){return t>>>e|t<<32-e},Ri.rotl32=function(t,e){return t<<e|t>>>32-e},Ri.sum32=function(t,e){return t+e>>>0},Ri.sum32_3=function(t,e,r){return t+e+r>>>0},Ri.sum32_4=function(t,e,r,i){return t+e+r+i>>>0},Ri.sum32_5=function(t,e,r,i,n){return t+e+r+i+n>>>0},Ri.sum64=function(t,e,r,i){var n=t[e],s=i+t[e+1]>>>0,o=(s<i?1:0)+r+n;t[e]=o>>>0,t[e+1]=s},Ri.sum64_hi=function(t,e,r,i){return(e+i>>>0<e?1:0)+t+r>>>0},Ri.sum64_lo=function(t,e,r,i){return e+i>>>0},Ri.sum64_4_hi=function(t,e,r,i,n,s,o,a){var c=0,h=e;return c+=(h=h+i>>>0)<e?1:0,c+=(h=h+s>>>0)<s?1:0,t+r+n+o+(c+=(h=h+a>>>0)<a?1:0)>>>0},Ri.sum64_4_lo=function(t,e,r,i,n,s,o,a){return e+i+s+a>>>0},Ri.sum64_5_hi=function(t,e,r,i,n,s,o,a,c,h){var u=0,l=e;return u+=(l=l+i>>>0)<e?1:0,u+=(l=l+s>>>0)<s?1:0,u+=(l=l+a>>>0)<a?1:0,t+r+n+o+c+(u+=(l=l+h>>>0)<h?1:0)>>>0},Ri.sum64_5_lo=function(t,e,r,i,n,s,o,a,c,h){return e+i+s+a+h>>>0},Ri.rotr64_hi=function(t,e,r){return(e<<32-r|t>>>r)>>>0},Ri.rotr64_lo=function(t,e,r){return(t<<32-r|e>>>r)>>>0},Ri.shr64_hi=function(t,e,r){return t>>>r},Ri.shr64_lo=function(t,e,r){return(t<<32-r|e>>>r)>>>0};var Li={},ji=Ri,zi=Ni;function Fi(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}Li.BlockHash=Fi,Fi.prototype.update=function(t,e){if(t=ji.toArray(t,e),this.pending?this.pending=this.pending.concat(t):this.pending=t,this.pendingTotal+=t.length,this.pending.length>=this._delta8){var r=(t=this.pending).length%this._delta8;this.pending=t.slice(t.length-r,t.length),0===this.pending.length&&(this.pending=null),t=ji.join32(t,0,t.length-r,this.endian);for(var i=0;i<t.length;i+=this._delta32)this._update(t,i,i+this._delta32)}return this},Fi.prototype.digest=function(t){return this.update(this._pad()),zi(null===this.pending),this._digest(t)},Fi.prototype._pad=function(){var t=this.pendingTotal,e=this._delta8,r=e-(t+this.padLength)%e,i=new Array(r+this.padLength);i[0]=128;for(var n=1;n<r;n++)i[n]=0;if(t<<=3,"big"===this.endian){for(var s=8;s<this.padLength;s++)i[n++]=0;i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=t>>>24&255,i[n++]=t>>>16&255,i[n++]=t>>>8&255,i[n++]=255&t}else for(i[n++]=255&t,i[n++]=t>>>8&255,i[n++]=t>>>16&255,i[n++]=t>>>24&255,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,s=8;s<this.padLength;s++)i[n++]=0;return i};var Ki={},Hi={},Vi=Ri.rotr32;function $i(t,e,r){return t&e^~t&r}function Ji(t,e,r){return t&e^t&r^e&r}function Gi(t,e,r){return t^e^r}Hi.ft_1=function(t,e,r,i){return 0===t?$i(e,r,i):1===t||3===t?Gi(e,r,i):2===t?Ji(e,r,i):void 0},Hi.ch32=$i,Hi.maj32=Ji,Hi.p32=Gi,Hi.s0_256=function(t){return Vi(t,2)^Vi(t,13)^Vi(t,22)},Hi.s1_256=function(t){return Vi(t,6)^Vi(t,11)^Vi(t,25)},Hi.g0_256=function(t){return Vi(t,7)^Vi(t,18)^t>>>3},Hi.g1_256=function(t){return Vi(t,17)^Vi(t,19)^t>>>10};var Wi=Ri,Qi=Li,Yi=Hi,Xi=Wi.rotl32,Zi=Wi.sum32,tn=Wi.sum32_5,en=Yi.ft_1,rn=Qi.BlockHash,nn=[1518500249,1859775393,2400959708,3395469782];function sn(){if(!(this instanceof sn))return new sn;rn.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.W=new Array(80)}Wi.inherits(sn,rn);var on=sn;sn.blockSize=512,sn.outSize=160,sn.hmacStrength=80,sn.padLength=64,sn.prototype._update=function(t,e){for(var r=this.W,i=0;i<16;i++)r[i]=t[e+i];for(;i<r.length;i++)r[i]=Xi(r[i-3]^r[i-8]^r[i-14]^r[i-16],1);var n=this.h[0],s=this.h[1],o=this.h[2],a=this.h[3],c=this.h[4];for(i=0;i<r.length;i++){var h=~~(i/20),u=tn(Xi(n,5),en(h,s,o,a),c,r[i],nn[h]);c=a,a=o,o=Xi(s,30),s=n,n=u}this.h[0]=Zi(this.h[0],n),this.h[1]=Zi(this.h[1],s),this.h[2]=Zi(this.h[2],o),this.h[3]=Zi(this.h[3],a),this.h[4]=Zi(this.h[4],c)},sn.prototype._digest=function(t){return"hex"===t?Wi.toHex32(this.h,"big"):Wi.split32(this.h,"big")};var an=Ri,cn=Li,hn=Hi,un=Ni,ln=an.sum32,fn=an.sum32_4,dn=an.sum32_5,pn=hn.ch32,gn=hn.maj32,mn=hn.s0_256,yn=hn.s1_256,vn=hn.g0_256,wn=hn.g1_256,bn=cn.BlockHash,An=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function _n(){if(!(this instanceof _n))return new _n;bn.call(this),this.h=[**********,**********,**********,**********,**********,**********,528734635,**********],this.k=An,this.W=new Array(64)}an.inherits(_n,bn);var En=_n;_n.blockSize=512,_n.outSize=256,_n.hmacStrength=192,_n.padLength=64,_n.prototype._update=function(t,e){for(var r=this.W,i=0;i<16;i++)r[i]=t[e+i];for(;i<r.length;i++)r[i]=fn(wn(r[i-2]),r[i-7],vn(r[i-15]),r[i-16]);var n=this.h[0],s=this.h[1],o=this.h[2],a=this.h[3],c=this.h[4],h=this.h[5],u=this.h[6],l=this.h[7];for(un(this.k.length===r.length),i=0;i<r.length;i++){var f=dn(l,yn(c),pn(c,h,u),this.k[i],r[i]),d=ln(mn(n),gn(n,s,o));l=u,u=h,h=c,c=ln(a,f),a=o,o=s,s=n,n=ln(f,d)}this.h[0]=ln(this.h[0],n),this.h[1]=ln(this.h[1],s),this.h[2]=ln(this.h[2],o),this.h[3]=ln(this.h[3],a),this.h[4]=ln(this.h[4],c),this.h[5]=ln(this.h[5],h),this.h[6]=ln(this.h[6],u),this.h[7]=ln(this.h[7],l)},_n.prototype._digest=function(t){return"hex"===t?an.toHex32(this.h,"big"):an.split32(this.h,"big")};var In=Ri,Sn=En;function Mn(){if(!(this instanceof Mn))return new Mn;Sn.call(this),this.h=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]}In.inherits(Mn,Sn);var Pn=Mn;Mn.blockSize=512,Mn.outSize=224,Mn.hmacStrength=192,Mn.padLength=64,Mn.prototype._digest=function(t){return"hex"===t?In.toHex32(this.h.slice(0,7),"big"):In.split32(this.h.slice(0,7),"big")};var xn=Ri,Rn=Li,Nn=Ni,Cn=xn.rotr64_hi,On=xn.rotr64_lo,Un=xn.shr64_hi,Tn=xn.shr64_lo,Dn=xn.sum64,Bn=xn.sum64_hi,kn=xn.sum64_lo,qn=xn.sum64_4_hi,Ln=xn.sum64_4_lo,jn=xn.sum64_5_hi,zn=xn.sum64_5_lo,Fn=Rn.BlockHash,Kn=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function Hn(){if(!(this instanceof Hn))return new Hn;Fn.call(this),this.h=[**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,725511199,528734635,4215389547,**********,327033209],this.k=Kn,this.W=new Array(160)}xn.inherits(Hn,Fn);var Vn=Hn;function $n(t,e,r,i,n){var s=t&r^~t&n;return s<0&&(s+=4294967296),s}function Jn(t,e,r,i,n,s){var o=e&i^~e&s;return o<0&&(o+=4294967296),o}function Gn(t,e,r,i,n){var s=t&r^t&n^r&n;return s<0&&(s+=4294967296),s}function Wn(t,e,r,i,n,s){var o=e&i^e&s^i&s;return o<0&&(o+=4294967296),o}function Qn(t,e){var r=Cn(t,e,28)^Cn(e,t,2)^Cn(e,t,7);return r<0&&(r+=4294967296),r}function Yn(t,e){var r=On(t,e,28)^On(e,t,2)^On(e,t,7);return r<0&&(r+=4294967296),r}function Xn(t,e){var r=Cn(t,e,14)^Cn(t,e,18)^Cn(e,t,9);return r<0&&(r+=4294967296),r}function Zn(t,e){var r=On(t,e,14)^On(t,e,18)^On(e,t,9);return r<0&&(r+=4294967296),r}function ts(t,e){var r=Cn(t,e,1)^Cn(t,e,8)^Un(t,e,7);return r<0&&(r+=4294967296),r}function es(t,e){var r=On(t,e,1)^On(t,e,8)^Tn(t,e,7);return r<0&&(r+=4294967296),r}function rs(t,e){var r=Cn(t,e,19)^Cn(e,t,29)^Un(t,e,6);return r<0&&(r+=4294967296),r}function is(t,e){var r=On(t,e,19)^On(e,t,29)^Tn(t,e,6);return r<0&&(r+=4294967296),r}Hn.blockSize=1024,Hn.outSize=512,Hn.hmacStrength=192,Hn.padLength=128,Hn.prototype._prepareBlock=function(t,e){for(var r=this.W,i=0;i<32;i++)r[i]=t[e+i];for(;i<r.length;i+=2){var n=rs(r[i-4],r[i-3]),s=is(r[i-4],r[i-3]),o=r[i-14],a=r[i-13],c=ts(r[i-30],r[i-29]),h=es(r[i-30],r[i-29]),u=r[i-32],l=r[i-31];r[i]=qn(n,s,o,a,c,h,u,l),r[i+1]=Ln(n,s,o,a,c,h,u,l)}},Hn.prototype._update=function(t,e){this._prepareBlock(t,e);var r=this.W,i=this.h[0],n=this.h[1],s=this.h[2],o=this.h[3],a=this.h[4],c=this.h[5],h=this.h[6],u=this.h[7],l=this.h[8],f=this.h[9],d=this.h[10],p=this.h[11],g=this.h[12],m=this.h[13],y=this.h[14],v=this.h[15];Nn(this.k.length===r.length);for(var w=0;w<r.length;w+=2){var b=y,A=v,_=Xn(l,f),E=Zn(l,f),I=$n(l,0,d,0,g),S=Jn(0,f,0,p,0,m),M=this.k[w],P=this.k[w+1],x=r[w],R=r[w+1],N=jn(b,A,_,E,I,S,M,P,x,R),C=zn(b,A,_,E,I,S,M,P,x,R);b=Qn(i,n),A=Yn(i,n),_=Gn(i,0,s,0,a),E=Wn(0,n,0,o,0,c);var O=Bn(b,A,_,E),U=kn(b,A,_,E);y=g,v=m,g=d,m=p,d=l,p=f,l=Bn(h,u,N,C),f=kn(u,u,N,C),h=a,u=c,a=s,c=o,s=i,o=n,i=Bn(N,C,O,U),n=kn(N,C,O,U)}Dn(this.h,0,i,n),Dn(this.h,2,s,o),Dn(this.h,4,a,c),Dn(this.h,6,h,u),Dn(this.h,8,l,f),Dn(this.h,10,d,p),Dn(this.h,12,g,m),Dn(this.h,14,y,v)},Hn.prototype._digest=function(t){return"hex"===t?xn.toHex32(this.h,"big"):xn.split32(this.h,"big")};var ns=Ri,ss=Vn;function os(){if(!(this instanceof os))return new os;ss.call(this),this.h=[3418070365,3238371032,1654270250,914150663,2438529370,812702999,355462360,4144912697,1731405415,4290775857,2394180231,1750603025,3675008525,1694076839,1203062813,3204075428]}ns.inherits(os,ss);var as=os;os.blockSize=1024,os.outSize=384,os.hmacStrength=192,os.padLength=128,os.prototype._digest=function(t){return"hex"===t?ns.toHex32(this.h.slice(0,12),"big"):ns.split32(this.h.slice(0,12),"big")},Ki.sha1=on,Ki.sha224=Pn,Ki.sha256=En,Ki.sha384=as,Ki.sha512=Vn;var cs={},hs=Ri,us=Li,ls=hs.rotl32,fs=hs.sum32,ds=hs.sum32_3,ps=hs.sum32_4,gs=us.BlockHash;function ms(){if(!(this instanceof ms))return new ms;gs.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.endian="little"}function ys(t,e,r,i){return t<=15?e^r^i:t<=31?e&r|~e&i:t<=47?(e|~r)^i:t<=63?e&i|r&~i:e^(r|~i)}function vs(t){return t<=15?0:t<=31?1518500249:t<=47?1859775393:t<=63?2400959708:2840853838}function ws(t){return t<=15?1352829926:t<=31?1548603684:t<=47?1836072691:t<=63?2053994217:0}hs.inherits(ms,gs),cs.ripemd160=ms,ms.blockSize=512,ms.outSize=160,ms.hmacStrength=192,ms.padLength=64,ms.prototype._update=function(t,e){for(var r=this.h[0],i=this.h[1],n=this.h[2],s=this.h[3],o=this.h[4],a=r,c=i,h=n,u=s,l=o,f=0;f<80;f++){var d=fs(ls(ps(r,ys(f,i,n,s),t[bs[f]+e],vs(f)),_s[f]),o);r=o,o=s,s=ls(n,10),n=i,i=d,d=fs(ls(ps(a,ys(79-f,c,h,u),t[As[f]+e],ws(f)),Es[f]),l),a=l,l=u,u=ls(h,10),h=c,c=d}d=ds(this.h[1],n,u),this.h[1]=ds(this.h[2],s,l),this.h[2]=ds(this.h[3],o,a),this.h[3]=ds(this.h[4],r,c),this.h[4]=ds(this.h[0],i,h),this.h[0]=d},ms.prototype._digest=function(t){return"hex"===t?hs.toHex32(this.h,"little"):hs.split32(this.h,"little")};var bs=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],As=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],_s=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],Es=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11],Is=Ri,Ss=Ni;function Ms(t,e,r){if(!(this instanceof Ms))return new Ms(t,e,r);this.Hash=t,this.blockSize=t.blockSize/8,this.outSize=t.outSize/8,this.inner=null,this.outer=null,this._init(Is.toArray(e,r))}var Ps=Ms;function xs(t,e,r){return r={path:e,exports:{},require:function(t,e){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(e??r.path)}},t(r,r.exports),r.exports}Ms.prototype._init=function(t){t.length>this.blockSize&&(t=(new this.Hash).update(t).digest()),Ss(t.length<=this.blockSize);for(var e=t.length;e<this.blockSize;e++)t.push(0);for(e=0;e<t.length;e++)t[e]^=54;for(this.inner=(new this.Hash).update(t),e=0;e<t.length;e++)t[e]^=106;this.outer=(new this.Hash).update(t)},Ms.prototype.update=function(t,e){return this.inner.update(t,e),this},Ms.prototype.digest=function(t){return this.outer.update(this.inner.digest()),this.outer.digest(t)},function(t){var e=t;e.utils=Ri,e.common=Li,e.sha=Ki,e.ripemd=cs,e.hmac=Ps,e.sha1=e.sha.sha1,e.sha256=e.sha.sha256,e.sha224=e.sha.sha224,e.sha384=e.sha.sha384,e.sha512=e.sha.sha512,e.ripemd160=e.ripemd.ripemd160}(xi);var Rs=Ns;function Ns(t,e){if(!t)throw new Error(e||"Assertion failed")}Ns.equal=function(t,e,r){if(t!=e)throw new Error(r||"Assertion failed: "+t+" != "+e)};var Cs=xs((function(t,e){var r=e;function i(t){return 1===t.length?"0"+t:t}function n(t){for(var e="",r=0;r<t.length;r++)e+=i(t[r].toString(16));return e}r.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var r=[];if("string"!=typeof t){for(var i=0;i<t.length;i++)r[i]=0|t[i];return r}if("hex"===e)for((t=t.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(t="0"+t),i=0;i<t.length;i+=2)r.push(parseInt(t[i]+t[i+1],16));else for(i=0;i<t.length;i++){var n=t.charCodeAt(i),s=n>>8,o=255&n;s?r.push(s,o):r.push(o)}return r},r.zero2=i,r.toHex=n,r.encode=function(t,e){return"hex"===e?n(t):t}})),Os=xs((function(t,e){var r=e;r.assert=Rs,r.toArray=Cs.toArray,r.zero2=Cs.zero2,r.toHex=Cs.toHex,r.encode=Cs.encode,r.getNAF=function(t,e,r){var i=new Array(Math.max(t.bitLength(),r)+1);i.fill(0);for(var n=1<<e+1,s=t.clone(),o=0;o<i.length;o++){var a,c=s.andln(n-1);s.isOdd()?(a=c>(n>>1)-1?(n>>1)-c:c,s.isubn(a)):a=0,i[o]=a,s.iushrn(1)}return i},r.getJSF=function(t,e){var r=[[],[]];t=t.clone(),e=e.clone();for(var i,n=0,s=0;t.cmpn(-n)>0||e.cmpn(-s)>0;){var o,a,c=t.andln(3)+n&3,h=e.andln(3)+s&3;3===c&&(c=-1),3===h&&(h=-1),o=1&c?3!=(i=t.andln(7)+n&7)&&5!==i||2!==h?c:-c:0,r[0].push(o),a=1&h?3!=(i=e.andln(7)+s&7)&&5!==i||2!==c?h:-h:0,r[1].push(a),2*n===o+1&&(n=1-n),2*s===a+1&&(s=1-s),t.iushrn(1),e.iushrn(1)}return r},r.cachedProperty=function(t,e,r){var i="_"+e;t.prototype[e]=function(){return void 0!==this[i]?this[i]:this[i]=r.call(this)}},r.parseBytes=function(t){return"string"==typeof t?r.toArray(t,"hex"):t},r.intFromLE=function(t){return new Cr(t,"hex","le")}})),Us=Os.getNAF,Ts=Os.getJSF,Ds=Os.assert;function Bs(t,e){this.type=t,this.p=new Cr(e.p,16),this.red=e.prime?Cr.red(e.prime):Cr.mont(this.p),this.zero=new Cr(0).toRed(this.red),this.one=new Cr(1).toRed(this.red),this.two=new Cr(2).toRed(this.red),this.n=e.n&&new Cr(e.n,16),this.g=e.g&&this.pointFromJSON(e.g,e.gRed),this._wnafT1=new Array(4),this._wnafT2=new Array(4),this._wnafT3=new Array(4),this._wnafT4=new Array(4),this._bitLength=this.n?this.n.bitLength():0;var r=this.n&&this.p.div(this.n);!r||r.cmpn(100)>0?this.redN=null:(this._maxwellTrick=!0,this.redN=this.n.toRed(this.red))}var ks=Bs;function qs(t,e){this.curve=t,this.type=e,this.precomputed=null}Bs.prototype.point=function(){throw new Error("Not implemented")},Bs.prototype.validate=function(){throw new Error("Not implemented")},Bs.prototype._fixedNafMul=function(t,e){Ds(t.precomputed);var r=t._getDoubles(),i=Us(e,1,this._bitLength),n=(1<<r.step+1)-(r.step%2==0?2:1);n/=3;var s,o,a=[];for(s=0;s<i.length;s+=r.step){o=0;for(var c=s+r.step-1;c>=s;c--)o=(o<<1)+i[c];a.push(o)}for(var h=this.jpoint(null,null,null),u=this.jpoint(null,null,null),l=n;l>0;l--){for(s=0;s<a.length;s++)(o=a[s])===l?u=u.mixedAdd(r.points[s]):o===-l&&(u=u.mixedAdd(r.points[s].neg()));h=h.add(u)}return h.toP()},Bs.prototype._wnafMul=function(t,e){var r=4,i=t._getNAFPoints(r);r=i.wnd;for(var n=i.points,s=Us(e,r,this._bitLength),o=this.jpoint(null,null,null),a=s.length-1;a>=0;a--){for(var c=0;a>=0&&0===s[a];a--)c++;if(a>=0&&c++,o=o.dblp(c),a<0)break;var h=s[a];Ds(0!==h),o="affine"===t.type?h>0?o.mixedAdd(n[h-1>>1]):o.mixedAdd(n[-h-1>>1].neg()):h>0?o.add(n[h-1>>1]):o.add(n[-h-1>>1].neg())}return"affine"===t.type?o.toP():o},Bs.prototype._wnafMulAdd=function(t,e,r,i,n){var s,o,a,c=this._wnafT1,h=this._wnafT2,u=this._wnafT3,l=0;for(s=0;s<i;s++){var f=(a=e[s])._getNAFPoints(t);c[s]=f.wnd,h[s]=f.points}for(s=i-1;s>=1;s-=2){var d=s-1,p=s;if(1===c[d]&&1===c[p]){var g=[e[d],null,null,e[p]];0===e[d].y.cmp(e[p].y)?(g[1]=e[d].add(e[p]),g[2]=e[d].toJ().mixedAdd(e[p].neg())):0===e[d].y.cmp(e[p].y.redNeg())?(g[1]=e[d].toJ().mixedAdd(e[p]),g[2]=e[d].add(e[p].neg())):(g[1]=e[d].toJ().mixedAdd(e[p]),g[2]=e[d].toJ().mixedAdd(e[p].neg()));var m=[-3,-1,-5,-7,0,7,5,1,3],y=Ts(r[d],r[p]);for(l=Math.max(y[0].length,l),u[d]=new Array(l),u[p]=new Array(l),o=0;o<l;o++){var v=0|y[0][o],w=0|y[1][o];u[d][o]=m[3*(v+1)+(w+1)],u[p][o]=0,h[d]=g}}else u[d]=Us(r[d],c[d],this._bitLength),u[p]=Us(r[p],c[p],this._bitLength),l=Math.max(u[d].length,l),l=Math.max(u[p].length,l)}var b=this.jpoint(null,null,null),A=this._wnafT4;for(s=l;s>=0;s--){for(var _=0;s>=0;){var E=!0;for(o=0;o<i;o++)A[o]=0|u[o][s],0!==A[o]&&(E=!1);if(!E)break;_++,s--}if(s>=0&&_++,b=b.dblp(_),s<0)break;for(o=0;o<i;o++){var I=A[o];0!==I&&(I>0?a=h[o][I-1>>1]:I<0&&(a=h[o][-I-1>>1].neg()),b="affine"===a.type?b.mixedAdd(a):b.add(a))}}for(s=0;s<i;s++)h[s]=null;return n?b:b.toP()},Bs.BasePoint=qs,qs.prototype.eq=function(){throw new Error("Not implemented")},qs.prototype.validate=function(){return this.curve.validate(this)},Bs.prototype.decodePoint=function(t,e){t=Os.toArray(t,e);var r=this.p.byteLength();if((4===t[0]||6===t[0]||7===t[0])&&t.length-1==2*r)return 6===t[0]?Ds(t[t.length-1]%2==0):7===t[0]&&Ds(t[t.length-1]%2==1),this.point(t.slice(1,1+r),t.slice(1+r,1+2*r));if((2===t[0]||3===t[0])&&t.length-1===r)return this.pointFromX(t.slice(1,1+r),3===t[0]);throw new Error("Unknown point format")},qs.prototype.encodeCompressed=function(t){return this.encode(t,!0)},qs.prototype._encode=function(t){var e=this.curve.p.byteLength(),r=this.getX().toArray("be",e);return t?[this.getY().isEven()?2:3].concat(r):[4].concat(r,this.getY().toArray("be",e))},qs.prototype.encode=function(t,e){return Os.encode(this._encode(e),t)},qs.prototype.precompute=function(t){if(this.precomputed)return this;var e={doubles:null,naf:null,beta:null};return e.naf=this._getNAFPoints(8),e.doubles=this._getDoubles(4,t),e.beta=this._getBeta(),this.precomputed=e,this},qs.prototype._hasDoubles=function(t){if(!this.precomputed)return!1;var e=this.precomputed.doubles;return!!e&&e.points.length>=Math.ceil((t.bitLength()+1)/e.step)},qs.prototype._getDoubles=function(t,e){if(this.precomputed&&this.precomputed.doubles)return this.precomputed.doubles;for(var r=[this],i=this,n=0;n<e;n+=t){for(var s=0;s<t;s++)i=i.dbl();r.push(i)}return{step:t,points:r}},qs.prototype._getNAFPoints=function(t){if(this.precomputed&&this.precomputed.naf)return this.precomputed.naf;for(var e=[this],r=(1<<t)-1,i=1===r?null:this.dbl(),n=1;n<r;n++)e[n]=e[n-1].add(i);return{wnd:t,points:e}},qs.prototype._getBeta=function(){return null},qs.prototype.dblp=function(t){for(var e=this,r=0;r<t;r++)e=e.dbl();return e};var Ls=xs((function(t){"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}})),js=Os.assert;function zs(t){ks.call(this,"short",t),this.a=new Cr(t.a,16).toRed(this.red),this.b=new Cr(t.b,16).toRed(this.red),this.tinv=this.two.redInvm(),this.zeroA=0===this.a.fromRed().cmpn(0),this.threeA=0===this.a.fromRed().sub(this.p).cmpn(-3),this.endo=this._getEndomorphism(t),this._endoWnafT1=new Array(4),this._endoWnafT2=new Array(4)}Ls(zs,ks);var Fs=zs;function Ks(t,e,r,i){ks.BasePoint.call(this,t,"affine"),null===e&&null===r?(this.x=null,this.y=null,this.inf=!0):(this.x=new Cr(e,16),this.y=new Cr(r,16),i&&(this.x.forceRed(this.curve.red),this.y.forceRed(this.curve.red)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.inf=!1)}function Hs(t,e,r,i){ks.BasePoint.call(this,t,"jacobian"),null===e&&null===r&&null===i?(this.x=this.curve.one,this.y=this.curve.one,this.z=new Cr(0)):(this.x=new Cr(e,16),this.y=new Cr(r,16),this.z=new Cr(i,16)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.zOne=this.z===this.curve.one}zs.prototype._getEndomorphism=function(t){if(this.zeroA&&this.g&&this.n&&1===this.p.modn(3)){var e,r,i;if(t.beta)e=new Cr(t.beta,16).toRed(this.red);else{var n=this._getEndoRoots(this.p);e=(e=n[0].cmp(n[1])<0?n[0]:n[1]).toRed(this.red)}if(t.lambda)r=new Cr(t.lambda,16);else{var s=this._getEndoRoots(this.n);0===this.g.mul(s[0]).x.cmp(this.g.x.redMul(e))?r=s[0]:(r=s[1],js(0===this.g.mul(r).x.cmp(this.g.x.redMul(e))))}return i=t.basis?t.basis.map((function(t){return{a:new Cr(t.a,16),b:new Cr(t.b,16)}})):this._getEndoBasis(r),{beta:e,lambda:r,basis:i}}},zs.prototype._getEndoRoots=function(t){var e=t===this.p?this.red:Cr.mont(t),r=new Cr(2).toRed(e).redInvm(),i=r.redNeg(),n=new Cr(3).toRed(e).redNeg().redSqrt().redMul(r);return[i.redAdd(n).fromRed(),i.redSub(n).fromRed()]},zs.prototype._getEndoBasis=function(t){for(var e,r,i,n,s,o,a,c,h,u=this.n.ushrn(Math.floor(this.n.bitLength()/2)),l=t,f=this.n.clone(),d=new Cr(1),p=new Cr(0),g=new Cr(0),m=new Cr(1),y=0;0!==l.cmpn(0);){var v=f.div(l);c=f.sub(v.mul(l)),h=g.sub(v.mul(d));var w=m.sub(v.mul(p));if(!i&&c.cmp(u)<0)e=a.neg(),r=d,i=c.neg(),n=h;else if(i&&2==++y)break;a=c,f=l,l=c,g=d,d=h,m=p,p=w}s=c.neg(),o=h;var b=i.sqr().add(n.sqr());return s.sqr().add(o.sqr()).cmp(b)>=0&&(s=e,o=r),i.negative&&(i=i.neg(),n=n.neg()),s.negative&&(s=s.neg(),o=o.neg()),[{a:i,b:n},{a:s,b:o}]},zs.prototype._endoSplit=function(t){var e=this.endo.basis,r=e[0],i=e[1],n=i.b.mul(t).divRound(this.n),s=r.b.neg().mul(t).divRound(this.n),o=n.mul(r.a),a=s.mul(i.a),c=n.mul(r.b),h=s.mul(i.b);return{k1:t.sub(o).sub(a),k2:c.add(h).neg()}},zs.prototype.pointFromX=function(t,e){(t=new Cr(t,16)).red||(t=t.toRed(this.red));var r=t.redSqr().redMul(t).redIAdd(t.redMul(this.a)).redIAdd(this.b),i=r.redSqrt();if(0!==i.redSqr().redSub(r).cmp(this.zero))throw new Error("invalid point");var n=i.fromRed().isOdd();return(e&&!n||!e&&n)&&(i=i.redNeg()),this.point(t,i)},zs.prototype.validate=function(t){if(t.inf)return!0;var e=t.x,r=t.y,i=this.a.redMul(e),n=e.redSqr().redMul(e).redIAdd(i).redIAdd(this.b);return 0===r.redSqr().redISub(n).cmpn(0)},zs.prototype._endoWnafMulAdd=function(t,e,r){for(var i=this._endoWnafT1,n=this._endoWnafT2,s=0;s<t.length;s++){var o=this._endoSplit(e[s]),a=t[s],c=a._getBeta();o.k1.negative&&(o.k1.ineg(),a=a.neg(!0)),o.k2.negative&&(o.k2.ineg(),c=c.neg(!0)),i[2*s]=a,i[2*s+1]=c,n[2*s]=o.k1,n[2*s+1]=o.k2}for(var h=this._wnafMulAdd(1,i,n,2*s,r),u=0;u<2*s;u++)i[u]=null,n[u]=null;return h},Ls(Ks,ks.BasePoint),zs.prototype.point=function(t,e,r){return new Ks(this,t,e,r)},zs.prototype.pointFromJSON=function(t,e){return Ks.fromJSON(this,t,e)},Ks.prototype._getBeta=function(){if(this.curve.endo){var t=this.precomputed;if(t&&t.beta)return t.beta;var e=this.curve.point(this.x.redMul(this.curve.endo.beta),this.y);if(t){var r=this.curve,i=function(t){return r.point(t.x.redMul(r.endo.beta),t.y)};t.beta=e,e.precomputed={beta:null,naf:t.naf&&{wnd:t.naf.wnd,points:t.naf.points.map(i)},doubles:t.doubles&&{step:t.doubles.step,points:t.doubles.points.map(i)}}}return e}},Ks.prototype.toJSON=function(){return this.precomputed?[this.x,this.y,this.precomputed&&{doubles:this.precomputed.doubles&&{step:this.precomputed.doubles.step,points:this.precomputed.doubles.points.slice(1)},naf:this.precomputed.naf&&{wnd:this.precomputed.naf.wnd,points:this.precomputed.naf.points.slice(1)}}]:[this.x,this.y]},Ks.fromJSON=function(t,e,r){"string"==typeof e&&(e=JSON.parse(e));var i=t.point(e[0],e[1],r);if(!e[2])return i;function n(e){return t.point(e[0],e[1],r)}var s=e[2];return i.precomputed={beta:null,doubles:s.doubles&&{step:s.doubles.step,points:[i].concat(s.doubles.points.map(n))},naf:s.naf&&{wnd:s.naf.wnd,points:[i].concat(s.naf.points.map(n))}},i},Ks.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+">"},Ks.prototype.isInfinity=function(){return this.inf},Ks.prototype.add=function(t){if(this.inf)return t;if(t.inf)return this;if(this.eq(t))return this.dbl();if(this.neg().eq(t))return this.curve.point(null,null);if(0===this.x.cmp(t.x))return this.curve.point(null,null);var e=this.y.redSub(t.y);0!==e.cmpn(0)&&(e=e.redMul(this.x.redSub(t.x).redInvm()));var r=e.redSqr().redISub(this.x).redISub(t.x),i=e.redMul(this.x.redSub(r)).redISub(this.y);return this.curve.point(r,i)},Ks.prototype.dbl=function(){if(this.inf)return this;var t=this.y.redAdd(this.y);if(0===t.cmpn(0))return this.curve.point(null,null);var e=this.curve.a,r=this.x.redSqr(),i=t.redInvm(),n=r.redAdd(r).redIAdd(r).redIAdd(e).redMul(i),s=n.redSqr().redISub(this.x.redAdd(this.x)),o=n.redMul(this.x.redSub(s)).redISub(this.y);return this.curve.point(s,o)},Ks.prototype.getX=function(){return this.x.fromRed()},Ks.prototype.getY=function(){return this.y.fromRed()},Ks.prototype.mul=function(t){return t=new Cr(t,16),this.isInfinity()?this:this._hasDoubles(t)?this.curve._fixedNafMul(this,t):this.curve.endo?this.curve._endoWnafMulAdd([this],[t]):this.curve._wnafMul(this,t)},Ks.prototype.mulAdd=function(t,e,r){var i=[this,e],n=[t,r];return this.curve.endo?this.curve._endoWnafMulAdd(i,n):this.curve._wnafMulAdd(1,i,n,2)},Ks.prototype.jmulAdd=function(t,e,r){var i=[this,e],n=[t,r];return this.curve.endo?this.curve._endoWnafMulAdd(i,n,!0):this.curve._wnafMulAdd(1,i,n,2,!0)},Ks.prototype.eq=function(t){return this===t||this.inf===t.inf&&(this.inf||0===this.x.cmp(t.x)&&0===this.y.cmp(t.y))},Ks.prototype.neg=function(t){if(this.inf)return this;var e=this.curve.point(this.x,this.y.redNeg());if(t&&this.precomputed){var r=this.precomputed,i=function(t){return t.neg()};e.precomputed={naf:r.naf&&{wnd:r.naf.wnd,points:r.naf.points.map(i)},doubles:r.doubles&&{step:r.doubles.step,points:r.doubles.points.map(i)}}}return e},Ks.prototype.toJ=function(){return this.inf?this.curve.jpoint(null,null,null):this.curve.jpoint(this.x,this.y,this.curve.one)},Ls(Hs,ks.BasePoint),zs.prototype.jpoint=function(t,e,r){return new Hs(this,t,e,r)},Hs.prototype.toP=function(){if(this.isInfinity())return this.curve.point(null,null);var t=this.z.redInvm(),e=t.redSqr(),r=this.x.redMul(e),i=this.y.redMul(e).redMul(t);return this.curve.point(r,i)},Hs.prototype.neg=function(){return this.curve.jpoint(this.x,this.y.redNeg(),this.z)},Hs.prototype.add=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;var e=t.z.redSqr(),r=this.z.redSqr(),i=this.x.redMul(e),n=t.x.redMul(r),s=this.y.redMul(e.redMul(t.z)),o=t.y.redMul(r.redMul(this.z)),a=i.redSub(n),c=s.redSub(o);if(0===a.cmpn(0))return 0!==c.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var h=a.redSqr(),u=h.redMul(a),l=i.redMul(h),f=c.redSqr().redIAdd(u).redISub(l).redISub(l),d=c.redMul(l.redISub(f)).redISub(s.redMul(u)),p=this.z.redMul(t.z).redMul(a);return this.curve.jpoint(f,d,p)},Hs.prototype.mixedAdd=function(t){if(this.isInfinity())return t.toJ();if(t.isInfinity())return this;var e=this.z.redSqr(),r=this.x,i=t.x.redMul(e),n=this.y,s=t.y.redMul(e).redMul(this.z),o=r.redSub(i),a=n.redSub(s);if(0===o.cmpn(0))return 0!==a.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var c=o.redSqr(),h=c.redMul(o),u=r.redMul(c),l=a.redSqr().redIAdd(h).redISub(u).redISub(u),f=a.redMul(u.redISub(l)).redISub(n.redMul(h)),d=this.z.redMul(o);return this.curve.jpoint(l,f,d)},Hs.prototype.dblp=function(t){if(0===t)return this;if(this.isInfinity())return this;if(!t)return this.dbl();var e;if(this.curve.zeroA||this.curve.threeA){var r=this;for(e=0;e<t;e++)r=r.dbl();return r}var i=this.curve.a,n=this.curve.tinv,s=this.x,o=this.y,a=this.z,c=a.redSqr().redSqr(),h=o.redAdd(o);for(e=0;e<t;e++){var u=s.redSqr(),l=h.redSqr(),f=l.redSqr(),d=u.redAdd(u).redIAdd(u).redIAdd(i.redMul(c)),p=s.redMul(l),g=d.redSqr().redISub(p.redAdd(p)),m=p.redISub(g),y=d.redMul(m);y=y.redIAdd(y).redISub(f);var v=h.redMul(a);e+1<t&&(c=c.redMul(f)),s=g,a=v,h=y}return this.curve.jpoint(s,h.redMul(n),a)},Hs.prototype.dbl=function(){return this.isInfinity()?this:this.curve.zeroA?this._zeroDbl():this.curve.threeA?this._threeDbl():this._dbl()},Hs.prototype._zeroDbl=function(){var t,e,r;if(this.zOne){var i=this.x.redSqr(),n=this.y.redSqr(),s=n.redSqr(),o=this.x.redAdd(n).redSqr().redISub(i).redISub(s);o=o.redIAdd(o);var a=i.redAdd(i).redIAdd(i),c=a.redSqr().redISub(o).redISub(o),h=s.redIAdd(s);h=(h=h.redIAdd(h)).redIAdd(h),t=c,e=a.redMul(o.redISub(c)).redISub(h),r=this.y.redAdd(this.y)}else{var u=this.x.redSqr(),l=this.y.redSqr(),f=l.redSqr(),d=this.x.redAdd(l).redSqr().redISub(u).redISub(f);d=d.redIAdd(d);var p=u.redAdd(u).redIAdd(u),g=p.redSqr(),m=f.redIAdd(f);m=(m=m.redIAdd(m)).redIAdd(m),t=g.redISub(d).redISub(d),e=p.redMul(d.redISub(t)).redISub(m),r=(r=this.y.redMul(this.z)).redIAdd(r)}return this.curve.jpoint(t,e,r)},Hs.prototype._threeDbl=function(){var t,e,r;if(this.zOne){var i=this.x.redSqr(),n=this.y.redSqr(),s=n.redSqr(),o=this.x.redAdd(n).redSqr().redISub(i).redISub(s);o=o.redIAdd(o);var a=i.redAdd(i).redIAdd(i).redIAdd(this.curve.a),c=a.redSqr().redISub(o).redISub(o);t=c;var h=s.redIAdd(s);h=(h=h.redIAdd(h)).redIAdd(h),e=a.redMul(o.redISub(c)).redISub(h),r=this.y.redAdd(this.y)}else{var u=this.z.redSqr(),l=this.y.redSqr(),f=this.x.redMul(l),d=this.x.redSub(u).redMul(this.x.redAdd(u));d=d.redAdd(d).redIAdd(d);var p=f.redIAdd(f),g=(p=p.redIAdd(p)).redAdd(p);t=d.redSqr().redISub(g),r=this.y.redAdd(this.z).redSqr().redISub(l).redISub(u);var m=l.redSqr();m=(m=(m=m.redIAdd(m)).redIAdd(m)).redIAdd(m),e=d.redMul(p.redISub(t)).redISub(m)}return this.curve.jpoint(t,e,r)},Hs.prototype._dbl=function(){var t=this.curve.a,e=this.x,r=this.y,i=this.z,n=i.redSqr().redSqr(),s=e.redSqr(),o=r.redSqr(),a=s.redAdd(s).redIAdd(s).redIAdd(t.redMul(n)),c=e.redAdd(e),h=(c=c.redIAdd(c)).redMul(o),u=a.redSqr().redISub(h.redAdd(h)),l=h.redISub(u),f=o.redSqr();f=(f=(f=f.redIAdd(f)).redIAdd(f)).redIAdd(f);var d=a.redMul(l).redISub(f),p=r.redAdd(r).redMul(i);return this.curve.jpoint(u,d,p)},Hs.prototype.trpl=function(){if(!this.curve.zeroA)return this.dbl().add(this);var t=this.x.redSqr(),e=this.y.redSqr(),r=this.z.redSqr(),i=e.redSqr(),n=t.redAdd(t).redIAdd(t),s=n.redSqr(),o=this.x.redAdd(e).redSqr().redISub(t).redISub(i),a=(o=(o=(o=o.redIAdd(o)).redAdd(o).redIAdd(o)).redISub(s)).redSqr(),c=i.redIAdd(i);c=(c=(c=c.redIAdd(c)).redIAdd(c)).redIAdd(c);var h=n.redIAdd(o).redSqr().redISub(s).redISub(a).redISub(c),u=e.redMul(h);u=(u=u.redIAdd(u)).redIAdd(u);var l=this.x.redMul(a).redISub(u);l=(l=l.redIAdd(l)).redIAdd(l);var f=this.y.redMul(h.redMul(c.redISub(h)).redISub(o.redMul(a)));f=(f=(f=f.redIAdd(f)).redIAdd(f)).redIAdd(f);var d=this.z.redAdd(o).redSqr().redISub(r).redISub(a);return this.curve.jpoint(l,f,d)},Hs.prototype.mul=function(t,e){return t=new Cr(t,e),this.curve._wnafMul(this,t)},Hs.prototype.eq=function(t){if("affine"===t.type)return this.eq(t.toJ());if(this===t)return!0;var e=this.z.redSqr(),r=t.z.redSqr();if(0!==this.x.redMul(r).redISub(t.x.redMul(e)).cmpn(0))return!1;var i=e.redMul(this.z),n=r.redMul(t.z);return 0===this.y.redMul(n).redISub(t.y.redMul(i)).cmpn(0)},Hs.prototype.eqXToP=function(t){var e=this.z.redSqr(),r=t.toRed(this.curve.red).redMul(e);if(0===this.x.cmp(r))return!0;for(var i=t.clone(),n=this.curve.redN.redMul(e);;){if(i.iadd(this.curve.n),i.cmp(this.curve.p)>=0)return!1;if(r.redIAdd(n),0===this.x.cmp(r))return!0}},Hs.prototype.inspect=function(){return this.isInfinity()?"<EC JPoint Infinity>":"<EC JPoint x: "+this.x.toString(16,2)+" y: "+this.y.toString(16,2)+" z: "+this.z.toString(16,2)+">"},Hs.prototype.isInfinity=function(){return 0===this.z.cmpn(0)};var Vs=xs((function(t,e){var r=e;r.base=ks,r.short=Fs,r.mont=null,r.edwards=null})),$s=xs((function(t,e){var r,i=e,n=Os.assert;function s(t){"short"===t.type?this.curve=new Vs.short(t):"edwards"===t.type?this.curve=new Vs.edwards(t):this.curve=new Vs.mont(t),this.g=this.curve.g,this.n=this.curve.n,this.hash=t.hash,n(this.g.validate(),"Invalid curve"),n(this.g.mul(this.n).isInfinity(),"Invalid curve, G*N != O")}function o(t,e){Object.defineProperty(i,t,{configurable:!0,enumerable:!0,get:function(){var r=new s(e);return Object.defineProperty(i,t,{configurable:!0,enumerable:!0,value:r}),r}})}i.PresetCurve=s,o("p192",{type:"short",prime:"p192",p:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff",a:"ffffffff ffffffff ffffffff fffffffe ffffffff fffffffc",b:"64210519 e59c80e7 0fa7e9ab 72243049 feb8deec c146b9b1",n:"ffffffff ffffffff ffffffff 99def836 146bc9b1 b4d22831",hash:xi.sha256,gRed:!1,g:["188da80e b03090f6 7cbf20eb 43a18800 f4ff0afd 82ff1012","07192b95 ffc8da78 631011ed 6b24cdd5 73f977a1 1e794811"]}),o("p224",{type:"short",prime:"p224",p:"ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001",a:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff fffffffe",b:"b4050a85 0c04b3ab f5413256 5044b0b7 d7bfd8ba 270b3943 2355ffb4",n:"ffffffff ffffffff ffffffff ffff16a2 e0b8f03e 13dd2945 5c5c2a3d",hash:xi.sha256,gRed:!1,g:["b70e0cbd 6bb4bf7f 321390b9 4a03c1d3 56c21122 343280d6 115c1d21","bd376388 b5f723fb 4c22dfe6 cd4375a0 5a074764 44d58199 85007e34"]}),o("p256",{type:"short",prime:null,p:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff ffffffff",a:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff fffffffc",b:"5ac635d8 aa3a93e7 b3ebbd55 769886bc 651d06b0 cc53b0f6 3bce3c3e 27d2604b",n:"ffffffff 00000000 ffffffff ffffffff bce6faad a7179e84 f3b9cac2 fc632551",hash:xi.sha256,gRed:!1,g:["6b17d1f2 e12c4247 f8bce6e5 63a440f2 77037d81 2deb33a0 f4a13945 d898c296","4fe342e2 fe1a7f9b 8ee7eb4a 7c0f9e16 2bce3357 6b315ece cbb64068 37bf51f5"]}),o("p384",{type:"short",prime:null,p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 ffffffff",a:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 fffffffc",b:"b3312fa7 e23ee7e4 988e056b e3f82d19 181d9c6e fe814112 0314088f 5013875a c656398d 8a2ed19d 2a85c8ed d3ec2aef",n:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff c7634d81 f4372ddf 581a0db2 48b0a77a ecec196a ccc52973",hash:xi.sha384,gRed:!1,g:["aa87ca22 be8b0537 8eb1c71e f320ad74 6e1d3b62 8ba79b98 59f741e0 82542a38 5502f25d bf55296c 3a545e38 72760ab7","3617de4a 96262c6f 5d9e98bf 9292dc29 f8f41dbd 289a147c e9da3113 b5f0b8c0 0a60b1ce 1d7e819d 7a431d7c 90ea0e5f"]}),o("p521",{type:"short",prime:null,p:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff",a:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffc",b:"00000051 953eb961 8e1c9a1f 929a21a0 b68540ee a2da725b 99b315f3 b8b48991 8ef109e1 56193951 ec7e937b 1652c0bd 3bb1bf07 3573df88 3d2c34f1 ef451fd4 6b503f00",n:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffa 51868783 bf2f966b 7fcc0148 f709a5d0 3bb5c9b8 899c47ae bb6fb71e 91386409",hash:xi.sha512,gRed:!1,g:["000000c6 858e06b7 0404e9cd 9e3ecb66 2395b442 9c648139 053fb521 f828af60 6b4d3dba a14b5e77 efe75928 fe1dc127 a2ffa8de 3348b3c1 856a429b f97e7e31 c2e5bd66","00000118 39296a78 9a3bc004 5c8a5fb4 2c7d1bd9 98f54449 579b4468 17afbd17 273e662c 97ee7299 5ef42640 c550b901 3fad0761 353c7086 a272c240 88be9476 9fd16650"]}),o("curve25519",{type:"mont",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"76d06",b:"1",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:xi.sha256,gRed:!1,g:["9"]}),o("ed25519",{type:"edwards",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"-1",c:"1",d:"52036cee2b6ffe73 8cc740797779e898 00700a4d4141d8ab 75eb4dca135978a3",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:xi.sha256,gRed:!1,g:["216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a","6666666666666666666666666666666666666666666666666666666666666658"]});try{r=null.crash()}catch{r=void 0}o("secp256k1",{type:"short",prime:"k256",p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f",a:"0",b:"7",n:"ffffffff ffffffff ffffffff fffffffe baaedce6 af48a03b bfd25e8c d0364141",h:"1",hash:xi.sha256,beta:"7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee",lambda:"5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72",basis:[{a:"3086d221a7d46bcde86c90e49284eb15",b:"-e4437ed6010e88286f547fa90abfe4c3"},{a:"114ca50f7a8e2f3f657c1108d9d44cfd8",b:"3086d221a7d46bcde86c90e49284eb15"}],gRed:!1,g:["79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798","483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8",r]})}));function Js(t){if(!(this instanceof Js))return new Js(t);this.hash=t.hash,this.predResist=!!t.predResist,this.outLen=this.hash.outSize,this.minEntropy=t.minEntropy||this.hash.hmacStrength,this._reseed=null,this.reseedInterval=null,this.K=null,this.V=null;var e=Cs.toArray(t.entropy,t.entropyEnc||"hex"),r=Cs.toArray(t.nonce,t.nonceEnc||"hex"),i=Cs.toArray(t.pers,t.persEnc||"hex");Rs(e.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._init(e,r,i)}var Gs=Js;Js.prototype._init=function(t,e,r){var i=t.concat(e).concat(r);this.K=new Array(this.outLen/8),this.V=new Array(this.outLen/8);for(var n=0;n<this.V.length;n++)this.K[n]=0,this.V[n]=1;this._update(i),this._reseed=1,this.reseedInterval=281474976710656},Js.prototype._hmac=function(){return new xi.hmac(this.hash,this.K)},Js.prototype._update=function(t){var e=this._hmac().update(this.V).update([0]);t&&(e=e.update(t)),this.K=e.digest(),this.V=this._hmac().update(this.V).digest(),t&&(this.K=this._hmac().update(this.V).update([1]).update(t).digest(),this.V=this._hmac().update(this.V).digest())},Js.prototype.reseed=function(t,e,r,i){"string"!=typeof e&&(i=r,r=e,e=null),t=Cs.toArray(t,e),r=Cs.toArray(r,i),Rs(t.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._update(t.concat(r||[])),this._reseed=1},Js.prototype.generate=function(t,e,r,i){if(this._reseed>this.reseedInterval)throw new Error("Reseed is required");"string"!=typeof e&&(i=r,r=e,e=null),r&&(r=Cs.toArray(r,i||"hex"),this._update(r));for(var n=[];n.length<t;)this.V=this._hmac().update(this.V).digest(),n=n.concat(this.V);var s=n.slice(0,t);return this._update(r),this._reseed++,Cs.encode(s,e)};var Ws=Os.assert;function Qs(t,e){this.ec=t,this.priv=null,this.pub=null,e.priv&&this._importPrivate(e.priv,e.privEnc),e.pub&&this._importPublic(e.pub,e.pubEnc)}var Ys=Qs;Qs.fromPublic=function(t,e,r){return e instanceof Qs?e:new Qs(t,{pub:e,pubEnc:r})},Qs.fromPrivate=function(t,e,r){return e instanceof Qs?e:new Qs(t,{priv:e,privEnc:r})},Qs.prototype.validate=function(){var t=this.getPublic();return t.isInfinity()?{result:!1,reason:"Invalid public key"}:t.validate()?t.mul(this.ec.curve.n).isInfinity()?{result:!0,reason:null}:{result:!1,reason:"Public key * N != O"}:{result:!1,reason:"Public key is not a point"}},Qs.prototype.getPublic=function(t,e){return"string"==typeof t&&(e=t,t=null),this.pub||(this.pub=this.ec.g.mul(this.priv)),e?this.pub.encode(e,t):this.pub},Qs.prototype.getPrivate=function(t){return"hex"===t?this.priv.toString(16,2):this.priv},Qs.prototype._importPrivate=function(t,e){this.priv=new Cr(t,e||16),this.priv=this.priv.umod(this.ec.curve.n)},Qs.prototype._importPublic=function(t,e){if(t.x||t.y)return"mont"===this.ec.curve.type?Ws(t.x,"Need x coordinate"):("short"===this.ec.curve.type||"edwards"===this.ec.curve.type)&&Ws(t.x&&t.y,"Need both x and y coordinate"),void(this.pub=this.ec.curve.point(t.x,t.y));this.pub=this.ec.curve.decodePoint(t,e)},Qs.prototype.derive=function(t){return t.validate()||Ws(t.validate(),"public point not validated"),t.mul(this.priv).getX()},Qs.prototype.sign=function(t,e,r){return this.ec.sign(t,this,e,r)},Qs.prototype.verify=function(t,e){return this.ec.verify(t,e,this)},Qs.prototype.inspect=function(){return"<Key priv: "+(this.priv&&this.priv.toString(16,2))+" pub: "+(this.pub&&this.pub.inspect())+" >"};var Xs=Os.assert;function Zs(t,e){if(t instanceof Zs)return t;this._importDER(t,e)||(Xs(t.r&&t.s,"Signature without r or s"),this.r=new Cr(t.r,16),this.s=new Cr(t.s,16),void 0===t.recoveryParam?this.recoveryParam=null:this.recoveryParam=t.recoveryParam)}var to=Zs;function eo(){this.place=0}function ro(t,e){var r=t[e.place++];if(!(128&r))return r;var i=15&r;if(0===i||i>4)return!1;for(var n=0,s=0,o=e.place;s<i;s++,o++)n<<=8,n|=t[o],n>>>=0;return!(n<=127)&&(e.place=o,n)}function io(t){for(var e=0,r=t.length-1;!t[e]&&!(128&t[e+1])&&e<r;)e++;return 0===e?t:t.slice(e)}function no(t,e){if(e<128)t.push(e);else{var r=1+(Math.log(e)/Math.LN2>>>3);for(t.push(128|r);--r;)t.push(e>>>(r<<3)&255);t.push(e)}}Zs.prototype._importDER=function(t,e){t=Os.toArray(t,e);var r=new eo;if(48!==t[r.place++])return!1;var i=ro(t,r);if(!1===i||i+r.place!==t.length||2!==t[r.place++])return!1;var n=ro(t,r);if(!1===n)return!1;var s=t.slice(r.place,n+r.place);if(r.place+=n,2!==t[r.place++])return!1;var o=ro(t,r);if(!1===o||t.length!==o+r.place)return!1;var a=t.slice(r.place,o+r.place);if(0===s[0]){if(!(128&s[1]))return!1;s=s.slice(1)}if(0===a[0]){if(!(128&a[1]))return!1;a=a.slice(1)}return this.r=new Cr(s),this.s=new Cr(a),this.recoveryParam=null,!0},Zs.prototype.toDER=function(t){var e=this.r.toArray(),r=this.s.toArray();for(128&e[0]&&(e=[0].concat(e)),128&r[0]&&(r=[0].concat(r)),e=io(e),r=io(r);!(r[0]||128&r[1]);)r=r.slice(1);var i=[2];no(i,e.length),(i=i.concat(e)).push(2),no(i,r.length);var n=i.concat(r),s=[48];return no(s,n.length),s=s.concat(n),Os.encode(s,t)};var so=function(){throw new Error("unsupported")},oo=Os.assert;function ao(t){if(!(this instanceof ao))return new ao(t);"string"==typeof t&&(oo(Object.prototype.hasOwnProperty.call($s,t),"Unknown curve "+t),t=$s[t]),t instanceof $s.PresetCurve&&(t={curve:t}),this.curve=t.curve.curve,this.n=this.curve.n,this.nh=this.n.ushrn(1),this.g=this.curve.g,this.g=t.curve.g,this.g.precompute(t.curve.n.bitLength()+1),this.hash=t.hash||t.curve.hash}var co=ao;ao.prototype.keyPair=function(t){return new Ys(this,t)},ao.prototype.keyFromPrivate=function(t,e){return Ys.fromPrivate(this,t,e)},ao.prototype.keyFromPublic=function(t,e){return Ys.fromPublic(this,t,e)},ao.prototype.genKeyPair=function(t){t||(t={});for(var e=new Gs({hash:this.hash,pers:t.pers,persEnc:t.persEnc||"utf8",entropy:t.entropy||so(this.hash.hmacStrength),entropyEnc:t.entropy&&t.entropyEnc||"utf8",nonce:this.n.toArray()}),r=this.n.byteLength(),i=this.n.sub(new Cr(2));;){var n=new Cr(e.generate(r));if(!(n.cmp(i)>0))return n.iaddn(1),this.keyFromPrivate(n)}},ao.prototype._truncateToN=function(t,e){var r=8*t.byteLength()-this.n.bitLength();return r>0&&(t=t.ushrn(r)),!e&&t.cmp(this.n)>=0?t.sub(this.n):t},ao.prototype.sign=function(t,e,r,i){"object"==typeof r&&(i=r,r=null),i||(i={}),e=this.keyFromPrivate(e,r),t=this._truncateToN(new Cr(t,16));for(var n=this.n.byteLength(),s=e.getPrivate().toArray("be",n),o=t.toArray("be",n),a=new Gs({hash:this.hash,entropy:s,nonce:o,pers:i.pers,persEnc:i.persEnc||"utf8"}),c=this.n.sub(new Cr(1)),h=0;;h++){var u=i.k?i.k(h):new Cr(a.generate(this.n.byteLength()));if(!((u=this._truncateToN(u,!0)).cmpn(1)<=0||u.cmp(c)>=0)){var l=this.g.mul(u);if(!l.isInfinity()){var f=l.getX(),d=f.umod(this.n);if(0!==d.cmpn(0)){var p=u.invm(this.n).mul(d.mul(e.getPrivate()).iadd(t));if(0!==(p=p.umod(this.n)).cmpn(0)){var g=(l.getY().isOdd()?1:0)|(0!==f.cmp(d)?2:0);return i.canonical&&p.cmp(this.nh)>0&&(p=this.n.sub(p),g^=1),new to({r:d,s:p,recoveryParam:g})}}}}}},ao.prototype.verify=function(t,e,r,i){t=this._truncateToN(new Cr(t,16)),r=this.keyFromPublic(r,i);var n=(e=new to(e,"hex")).r,s=e.s;if(n.cmpn(1)<0||n.cmp(this.n)>=0||s.cmpn(1)<0||s.cmp(this.n)>=0)return!1;var o,a=s.invm(this.n),c=a.mul(t).umod(this.n),h=a.mul(n).umod(this.n);return this.curve._maxwellTrick?!(o=this.g.jmulAdd(c,r.getPublic(),h)).isInfinity()&&o.eqXToP(n):!(o=this.g.mulAdd(c,r.getPublic(),h)).isInfinity()&&0===o.getX().umod(this.n).cmp(n)},ao.prototype.recoverPubKey=function(t,e,r,i){oo((3&r)===r,"The recovery param is more than two bits"),e=new to(e,i);var n=this.n,s=new Cr(t),o=e.r,a=e.s,c=1&r,h=r>>1;if(o.cmp(this.curve.p.umod(this.curve.n))>=0&&h)throw new Error("Unable to find sencond key candinate");o=h?this.curve.pointFromX(o.add(this.curve.n),c):this.curve.pointFromX(o,c);var u=e.r.invm(n),l=n.sub(s).mul(u).umod(n),f=a.mul(u).umod(n);return this.g.mulAdd(l,o,f)},ao.prototype.getKeyRecoveryParam=function(t,e,r,i){if(null!==(e=new to(e,i)).recoveryParam)return e.recoveryParam;for(var n=0;n<4;n++){var s;try{s=this.recoverPubKey(t,e,n)}catch{continue}if(s.eq(r))return n}throw new Error("Unable to find valid recovery factor")};var ho=xs((function(t,e){var r=e;r.version="6.5.4",r.utils=Os,r.rand=function(){throw new Error("unsupported")},r.curve=Vs,r.curves=$s,r.ec=co,r.eddsa=null})).ec;const uo=new gr("signing-key/5.7.0");let lo=null;function fo(){return lo||(lo=new ho("secp256k1")),lo}class po{constructor(t){Si(this,"curve","secp256k1"),Si(this,"privateKey",Ir(t)),32!==function(t){if("string"!=typeof t)t=Ir(t);else if(!_r(t)||t.length%2)return null;return(t.length-2)/2}(this.privateKey)&&uo.throwArgumentError("invalid private key","privateKey","[[ REDACTED ]]");const e=fo().keyFromPrivate(Ar(this.privateKey));Si(this,"publicKey","0x"+e.getPublic(!1,"hex")),Si(this,"compressedPublicKey","0x"+e.getPublic(!0,"hex")),Si(this,"_isSigningKey",!0)}_addPoint(t){const e=fo().keyFromPublic(Ar(this.publicKey)),r=fo().keyFromPublic(Ar(t));return"0x"+e.pub.add(r.pub).encodeCompressed("hex")}signDigest(t){const e=fo().keyFromPrivate(Ar(this.privateKey)),r=Ar(t);32!==r.length&&uo.throwArgumentError("bad digest length","digest",t);const i=e.sign(r,{canonical:!0});return Pr({recoveryParam:i.recoveryParam,r:Mr("0x"+i.r.toString(16),32),s:Mr("0x"+i.s.toString(16),32)})}computeSharedSecret(t){const e=fo().keyFromPrivate(Ar(this.privateKey)),r=fo().keyFromPublic(Ar(go(t)));return Mr("0x"+e.derive(r.getPublic()).toString(16),32)}static isSigningKey(t){return!(!t||!t._isSigningKey)}}function go(t,e){const r=Ar(t);if(32===r.length){const t=new po(r);return e?"0x"+fo().keyFromPrivate(r).getPublic(!0,"hex"):t.publicKey}return 33===r.length?e?Ir(r):"0x"+fo().keyFromPublic(r).getPublic(!1,"hex"):65===r.length?e?"0x"+fo().keyFromPublic(r).getPublic(!0,"hex"):Ir(r):uo.throwArgumentError("invalid public or private key","key","[REDACTED]")}var mo;function yo(t,e){return function(t){return function(t){let e=null;if("string"!=typeof t&&Ai.throwArgumentError("invalid address","address",t),t.match(/^(0x)?[0-9a-fA-F]{40}$/))"0x"!==t.substring(0,2)&&(t="0x"+t),e=_i(t),t.match(/([A-F].*[a-f])|([a-f].*[A-F])/)&&e!==t&&Ai.throwArgumentError("bad address checksum","address",t);else if(t.match(/^XE[0-9]{2}[0-9A-Za-z]{30,31}$/)){for(t.substring(2,4)!==function(t){let e=(t=(t=t.toUpperCase()).substring(4)+t.substring(0,2)+"00").split("").map((t=>Ei[t])).join("");for(;e.length>=Ii;){let t=e.substring(0,Ii);e=parseInt(t,10)%97+e.substring(t.length)}let r=String(98-parseInt(e,10)%97);for(;r.length<2;)r="0"+r;return r}(t)&&Ai.throwArgumentError("bad icap checksum","address",t),e=function(t){return new Ur(t,36).toString(16)}(t.substring(4));e.length<40;)e="0"+e;e=_i("0x"+e)}else Ai.throwArgumentError("invalid address","address",t);return e}(Sr(xr(Sr(go(t),1)),12))}(function(t,e){const r=Pr(e),i={r:Ar(r.r),s:Ar(r.s)};return"0x"+fo().recoverPubKey(Ar(t),i,r.recoveryParam).encode("hex",!1)}(Ar(t),e))}new gr("transactions/5.7.0"),function(t){t[t.legacy=0]="legacy",t[t.eip2930=1]="eip2930",t[t.eip1559=2]="eip1559"}(mo||(mo={}));const vo="https://rpc.walletconnect.com/v1";var wo=Object.defineProperty,bo=Object.defineProperties,Ao=Object.getOwnPropertyDescriptors,_o=Object.getOwnPropertySymbols,Eo=Object.prototype.hasOwnProperty,Io=Object.prototype.propertyIsEnumerable,So=(t,e,r)=>e in t?wo(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;const Mo=t=>t?.split(":"),Po=t=>{const e=t&&Mo(t);if(e)return t.includes("did:pkh:")?e[3]:e[1]},xo=t=>{const e=t&&Mo(t);if(e)return e[2]+":"+e[3]},Ro=t=>{const e=t&&Mo(t);if(e)return e.pop()};async function No(t){const{cacao:e,projectId:r}=t,{s:i,p:n}=e,s=Co(n,n.iss),o=Ro(n.iss);return await async function(t,e,r,i,n,s){switch(r.t){case"eip191":return function(t,e,r){return yo(bi(e),r).toLowerCase()===t.toLowerCase()}(t,e,r.s);case"eip1271":return await async function(t,e,r,i,n,s){try{const o="0x1626ba7e",a="0000000000000000000000000000000000000000000000000000000000000040",c="0000000000000000000000000000000000000000000000000000000000000041",h=r.substring(2),u=o+bi(e).substring(2)+a+c+h,l=await fetch(`${s||vo}/?chainId=${i}&projectId=${n}`,{method:"POST",body:JSON.stringify({id:Date.now()+Math.floor(1e3*Math.random()),jsonrpc:"2.0",method:"eth_call",params:[{to:t,data:u},"latest"]})}),{result:f}=await l.json();return!!f&&f.slice(0,o.length).toLowerCase()===o.toLowerCase()}catch(t){return console.error("isValidEip1271Signature: ",t),!1}}(t,e,r.s,i,n,s);default:throw new Error(`verifySignature failed: Attempted to verify CacaoSignature with unknown type: ${r.t}`)}}(o,s,i,Po(n.iss),r)}const Co=(t,e)=>{const r=`${t.domain} wants you to sign in with your Ethereum account:`,i=Ro(e);if(!t.aud&&!t.uri)throw new Error("Either `aud` or `uri` is required to construct the message");let n=t.statement||void 0;const s=`URI: ${t.aud||t.uri}`,o=`Version: ${t.version}`,a=`Chain ID: ${Po(e)}`,c=`Nonce: ${t.nonce}`,h=`Issued At: ${t.iat}`,u=t.resources?`Resources:${t.resources.map((t=>`\n- ${t}`)).join("")}`:void 0,l=Lo(t.resources);return l&&(n=function(t="",e){Oo(e);const r="I further authorize the stated URI to perform the following actions on my behalf: ";if(t.includes(r))return t;const i=[];let n=0;return Object.keys(e.att).forEach((t=>{const r=Object.keys(e.att[t]).map((t=>({ability:t.split("/")[0],action:t.split("/")[1]})));r.sort(((t,e)=>t.action.localeCompare(e.action)));const s={};r.forEach((t=>{s[t.ability]||(s[t.ability]=[]),s[t.ability].push(t.action)}));const o=Object.keys(s).map((e=>(n++,`(${n}) '${e}': '${s[e].join("', '")}' for '${t}'.`)));i.push(o.join(", ").replace(".,","."))})),`${t?t+" ":""}${r}${i.join(" ")}`}(n,Do(l))),[r,i,"",n,"",s,o,a,c,h,u].filter((t=>null!=t)).join("\n")};function Oo(t){if(!t)throw new Error("No recap provided, value is undefined");if(!t.att)throw new Error("No `att` property found");const e=Object.keys(t.att);if(null==e||!e.length)throw new Error("No resources found in `att` property");e.forEach((e=>{const r=t.att[e];if(Array.isArray(r))throw new Error(`Resource must be an object: ${e}`);if("object"!=typeof r)throw new Error(`Resource must be an object: ${e}`);if(!Object.keys(r).length)throw new Error(`Resource object is empty: ${e}`);Object.keys(r).forEach((t=>{const e=r[t];if(!Array.isArray(e))throw new Error(`Ability limits ${t} must be an array of objects, found: ${e}`);if(!e.length)throw new Error(`Value of ${t} is empty array, must be an array with objects`);e.forEach((e=>{if("object"!=typeof e)throw new Error(`Ability limits (${t}) must be an array of objects, found: ${e}`)}))}))}))}function Uo(t,e,r={}){e=e?.sort(((t,e)=>t.localeCompare(e)));const i=e.map((e=>({[`${t}/${e}`]:[r]})));return Object.assign({},...i)}function To(t){return Oo(t),`urn:recap:${function(t){return Buffer.from(JSON.stringify(t)).toString("base64")}(t).replace(/=/g,"")}`}function Do(t){const e=function(t){return JSON.parse(Buffer.from(t,"base64").toString("utf-8"))}(t.replace("urn:recap:",""));return Oo(e),e}function Bo(t,e){const r=function(t,e){Oo(t),Oo(e);const r=Object.keys(t.att).concat(Object.keys(e.att)).sort(((t,e)=>t.localeCompare(e))),i={att:{}};return r.forEach((r=>{var n,s;Object.keys((null==(n=t.att)?void 0:n[r])||{}).concat(Object.keys((null==(s=e.att)?void 0:s[r])||{})).sort(((t,e)=>t.localeCompare(e))).forEach((n=>{var s,o;i.att[r]=((t,e)=>bo(t,Ao(e)))(((t,e)=>{for(var r in e||(e={}))Eo.call(e,r)&&So(t,r,e[r]);if(_o)for(var r of _o(e))Io.call(e,r)&&So(t,r,e[r]);return t})({},i.att[r]),{[n]:(null==(s=t.att[r])?void 0:s[n])||(null==(o=e.att[r])?void 0:o[n])})}))})),i}(Do(t),Do(e));return To(r)}function ko(t){var e;const r=Do(t);Oo(r);const i=null==(e=r.att)?void 0:e.eip155;return i?Object.keys(i).map((t=>t.split("/")[1])):[]}function qo(t){const e=Do(t);Oo(e);const r=[];return Object.values(e.att).forEach((t=>{Object.values(t).forEach((t=>{var e;null!=(e=t?.[0])&&e.chains&&r.push(t[0].chains)}))})),[...new Set(r.flat())]}function Lo(t){if(!t)return;const e=t?.[t.length-1];return function(t){return t&&t.includes("urn:recap:")}(e)?e:void 0}const jo="base10",zo="base16",Fo="base64pad",Ko="utf8";function Ho(){return Se((0,Q.po)(32),zo)}function Vo(t){return Se((0,Y.tW)(Ie(t,zo)),zo)}function $o(t){return Se((0,Y.tW)(Ie(t,Ko)),zo)}function Jo(t){return Number(Se(t,jo))}function Go(t){const e=Ie(t,Fo),r=e.slice(0,1);if(1===Jo(r)){const t=33,i=t+12,n=e.slice(1,t),s=e.slice(t,i);return{type:r,sealed:e.slice(i),iv:s,senderPublicKey:n}}const i=e.slice(1,13);return{type:r,sealed:e.slice(13),iv:i}}function Wo(t){const e=t?.type||0;if(1===e){if(typeof t?.senderPublicKey>"u")throw new Error("missing sender public key");if(typeof t?.receiverPublicKey>"u")throw new Error("missing receiver public key")}return{type:e,senderPublicKey:t?.senderPublicKey,receiverPublicKey:t?.receiverPublicKey}}function Qo(t){return 1===t.type&&"string"==typeof t.senderPublicKey&&"string"==typeof t.receiverPublicKey}function Yo(t){return t?.relay||{protocol:"irn"}}function Xo(t){const e=Me.CG[t];if(typeof e>"u")throw new Error(`Relay Protocol not supported: ${t}`);return e}var Zo=Object.defineProperty,ta=Object.defineProperties,ea=Object.getOwnPropertyDescriptors,ra=Object.getOwnPropertySymbols,ia=Object.prototype.hasOwnProperty,na=Object.prototype.propertyIsEnumerable,sa=(t,e,r)=>e in t?Zo(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,oa=(t,e)=>{for(var r in e||(e={}))ia.call(e,r)&&sa(t,r,e[r]);if(ra)for(var r of ra(e))na.call(e,r)&&sa(t,r,e[r]);return t};function aa(t,e="-"){const r={},i="relay"+e;return Object.keys(t).forEach((e=>{if(e.startsWith(i)){const n=e.replace(i,""),s=t[e];r[n]=s}})),r}function ca(t){const e=(t=(t=t.includes("wc://")?t.replace("wc://",""):t).includes("wc:")?t.replace("wc:",""):t).indexOf(":"),r=-1!==t.indexOf("?")?t.indexOf("?"):void 0,i=t.substring(0,e),n=t.substring(e+1,r).split("@"),s=typeof r<"u"?t.substring(r):"",o=J.parse(s),a="string"==typeof o.methods?o.methods.split(","):void 0;return{protocol:i,topic:ha(n[0]),version:parseInt(n[1],10),symKey:o.symKey,relay:aa(o),methods:a,expiryTimestamp:o.expiryTimestamp?parseInt(o.expiryTimestamp,10):void 0}}function ha(t){return t.startsWith("//")?t.substring(2):t}function ua(t){const e=[];return t.forEach((t=>{const[r,i]=t.split(":");e.push(`${r}:${i}`)})),e}function la(t){return t.includes(":")}function fa(t){return la(t)?t.split(":")[0]:t}function da(t,e){const r=function(t){const e={};return t?.forEach((t=>{const[r,i]=t.split(":");e[r]||(e[r]={accounts:[],chains:[],events:[]}),e[r].accounts.push(t),e[r].chains.push(`${r}:${i}`)})),e}(e=e.map((t=>t.replace("did:pkh:",""))));for(const[e,i]of Object.entries(r))i.methods?i.methods=er(i.methods,t):i.methods=t,i.events=["chainChanged","accountsChanged"];return r}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;const pa={INVALID_METHOD:{message:"Invalid method.",code:1001},INVALID_EVENT:{message:"Invalid event.",code:1002},INVALID_UPDATE_REQUEST:{message:"Invalid update request.",code:1003},INVALID_EXTEND_REQUEST:{message:"Invalid extend request.",code:1004},INVALID_SESSION_SETTLE_REQUEST:{message:"Invalid session settle request.",code:1005},UNAUTHORIZED_METHOD:{message:"Unauthorized method.",code:3001},UNAUTHORIZED_EVENT:{message:"Unauthorized event.",code:3002},UNAUTHORIZED_UPDATE_REQUEST:{message:"Unauthorized update request.",code:3003},UNAUTHORIZED_EXTEND_REQUEST:{message:"Unauthorized extend request.",code:3004},USER_REJECTED:{message:"User rejected.",code:5e3},USER_REJECTED_CHAINS:{message:"User rejected chains.",code:5001},USER_REJECTED_METHODS:{message:"User rejected methods.",code:5002},USER_REJECTED_EVENTS:{message:"User rejected events.",code:5003},UNSUPPORTED_CHAINS:{message:"Unsupported chains.",code:5100},UNSUPPORTED_METHODS:{message:"Unsupported methods.",code:5101},UNSUPPORTED_EVENTS:{message:"Unsupported events.",code:5102},UNSUPPORTED_ACCOUNTS:{message:"Unsupported accounts.",code:5103},UNSUPPORTED_NAMESPACE_KEY:{message:"Unsupported namespace key.",code:5104},USER_DISCONNECTED:{message:"User disconnected.",code:6e3},SESSION_SETTLEMENT_FAILED:{message:"Session settlement failed.",code:7e3},WC_METHOD_UNSUPPORTED:{message:"Unsupported wc_ method.",code:10001}},ga={NOT_INITIALIZED:{message:"Not initialized.",code:1},NO_MATCHING_KEY:{message:"No matching key.",code:2},RESTORE_WILL_OVERRIDE:{message:"Restore will override.",code:3},RESUBSCRIBED:{message:"Resubscribed.",code:4},MISSING_OR_INVALID:{message:"Missing or invalid.",code:5},EXPIRED:{message:"Expired.",code:6},UNKNOWN_TYPE:{message:"Unknown type.",code:7},MISMATCHED_TOPIC:{message:"Mismatched topic.",code:8},NON_CONFORMING_NAMESPACES:{message:"Non conforming namespaces.",code:9}};function ma(t,e){const{message:r,code:i}=ga[t];return{message:e?`${r} ${e}`:r,code:i}}function ya(t,e){const{message:r,code:i}=pa[t];return{message:e?`${r} ${e}`:r,code:i}}function va(t,e){return!!Array.isArray(t)&&(!(typeof e<"u"&&t.length)||t.every(e))}function wa(t){return Object.getPrototypeOf(t)===Object.prototype&&Object.keys(t).length}function ba(t){return typeof t>"u"}function Aa(t,e){return!(!e||!ba(t))||"string"==typeof t&&!!t.trim().length}function _a(t,e){return!(!e||!ba(t))||"number"==typeof t&&!isNaN(t)}function Ea(t){return!(!Aa(t,!1)||!t.includes(":"))&&2===t.split(":").length}function Ia(t){let e=!0;return va(t)?t.length&&(e=t.every((t=>Aa(t,!1)))):e=!1,e}function Sa(t,e){let r=null;return Object.values(t).forEach((t=>{if(r)return;const i=function(t,e){let r=null;return Ia(t?.methods)?Ia(t?.events)||(r=ya("UNSUPPORTED_EVENTS",`${e}, events should be an array of strings or empty array for no events`)):r=ya("UNSUPPORTED_METHODS",`${e}, methods should be an array of strings or empty array for no methods`),r}(t,`${e}, namespace`);i&&(r=i)})),r}function Ma(t,e){let r=null;if(t&&wa(t)){const i=Sa(t,e);i&&(r=i);const n=function(t,e){let r=null;return Object.values(t).forEach((t=>{if(r)return;const i=function(t,e){let r=null;return va(t)?t.forEach((t=>{r||function(t){if(Aa(t,!1)&&t.includes(":")){const e=t.split(":");if(3===e.length){const t=e[0]+":"+e[1];return!!e[2]&&Ea(t)}}return!1}(t)||(r=ya("UNSUPPORTED_ACCOUNTS",`${e}, account ${t} should be a string and conform to "namespace:chainId:address" format`))})):r=ya("UNSUPPORTED_ACCOUNTS",`${e}, accounts should be an array of strings conforming to "namespace:chainId:address" format`),r}(t?.accounts,`${e} namespace`);i&&(r=i)})),r}(t,e);n&&(r=n)}else r=ma("MISSING_OR_INVALID",`${e}, namespaces should be an object with data`);return r}function Pa(t){return Aa(t.protocol,!0)}function xa(t){return typeof t<"u"&&null!==typeof t}function Ra(t,e){return!(!Ea(e)||!function(t){const e=[];return Object.values(t).forEach((t=>{e.push(...ua(t.accounts))})),e}(t).includes(e))}function Na(t,e,r){let i=null;const n=function(t){const e={};return Object.keys(t).forEach((r=>{var i;r.includes(":")?e[r]=t[r]:null==(i=t[r].chains)||i.forEach((i=>{e[i]={methods:t[r].methods,events:t[r].events}}))})),e}(t),s=function(t){const e={};return Object.keys(t).forEach((r=>{if(r.includes(":"))e[r]=t[r];else{const i=ua(t[r].accounts);i?.forEach((i=>{e[i]={accounts:t[r].accounts.filter((t=>t.includes(`${i}:`))),methods:t[r].methods,events:t[r].events}}))}})),e}(e),o=Object.keys(n),a=Object.keys(s),c=Ca(Object.keys(t)),h=Ca(Object.keys(e)),u=c.filter((t=>!h.includes(t)));return u.length&&(i=ma("NON_CONFORMING_NAMESPACES",`${r} namespaces keys don't satisfy requiredNamespaces.\n      Required: ${u.toString()}\n      Received: ${Object.keys(e).toString()}`)),Ve(o,a)||(i=ma("NON_CONFORMING_NAMESPACES",`${r} namespaces chains don't satisfy required namespaces.\n      Required: ${o.toString()}\n      Approved: ${a.toString()}`)),Object.keys(e).forEach((t=>{if(!t.includes(":")||i)return;const n=ua(e[t].accounts);n.includes(t)||(i=ma("NON_CONFORMING_NAMESPACES",`${r} namespaces accounts don't satisfy namespace accounts for ${t}\n        Required: ${t}\n        Approved: ${n.toString()}`))})),o.forEach((t=>{i||(Ve(n[t].methods,s[t].methods)?Ve(n[t].events,s[t].events)||(i=ma("NON_CONFORMING_NAMESPACES",`${r} namespaces events don't satisfy namespace events for ${t}`)):i=ma("NON_CONFORMING_NAMESPACES",`${r} namespaces methods don't satisfy namespace methods for ${t}`))})),i}function Ca(t){return[...new Set(t.map((t=>t.includes(":")?t.split(":")[0]:t)))]}function Oa(){const t=Ke();return new Promise((e=>{switch(t){case qe.browser:e(Fe()&&navigator?.onLine);break;case qe.reactNative:e(async function(){if(ze()&&typeof r.g<"u"&&null!=r.g&&r.g.NetInfo){const t=await(null==r.g?void 0:r.g.NetInfo.fetch());return t?.isConnected}return!0}());break;case qe.node:default:e(!0)}}))}const Ua={};class Ta{static get(t){return Ua[t]}static set(t,e){Ua[t]=e}static delete(t){delete Ua[t]}}const Da=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,Ba=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,ka=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function qa(t,e){if(!("__proto__"===t||"constructor"===t&&e&&"object"==typeof e&&"prototype"in e))return e;!function(t){console.warn(`[destr] Dropping "${t}" key to prevent prototype pollution.`)}(t)}function La(t,e={}){if("string"!=typeof t)return t;const r=t.trim();if('"'===t[0]&&t.endsWith('"')&&!t.includes("\\"))return r.slice(1,-1);if(r.length<=9){const t=r.toLowerCase();if("true"===t)return!0;if("false"===t)return!1;if("undefined"===t)return;if("null"===t)return null;if("nan"===t)return Number.NaN;if("infinity"===t)return Number.POSITIVE_INFINITY;if("-infinity"===t)return Number.NEGATIVE_INFINITY}if(!ka.test(t)){if(e.strict)throw new SyntaxError("[destr] Invalid JSON");return t}try{if(Da.test(t)||Ba.test(t)){if(e.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(t,qa)}return JSON.parse(t)}catch(r){if(e.strict)throw r;return t}}function ja(t,...e){try{return(r=t(...e))&&"function"==typeof r.then?r:Promise.resolve(r)}catch(t){return Promise.reject(t)}var r}function za(t){if(function(t){const e=typeof t;return null===t||"object"!==e&&"function"!==e}(t))return String(t);if(function(t){const e=Object.getPrototypeOf(t);return!e||e.isPrototypeOf(Object)}(t)||Array.isArray(t))return JSON.stringify(t);if("function"==typeof t.toJSON)return za(t.toJSON());throw new Error("[unstorage] Cannot stringify value!")}function Fa(){if(void 0===typeof Buffer)throw new TypeError("[unstorage] Buffer is not supported!")}const Ka="base64:";function Ha(t){return t?t.split("?")[0].replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,""):""}function Va(...t){return Ha(t.join(":"))}function $a(t){return(t=Ha(t))?t+":":""}const Ja=()=>{const t=new Map;return{name:"memory",options:{},hasItem:e=>t.has(e),getItem:e=>t.get(e)??null,getItemRaw:e=>t.get(e)??null,setItem(e,r){t.set(e,r)},setItemRaw(e,r){t.set(e,r)},removeItem(e){t.delete(e)},getKeys:()=>Array.from(t.keys()),clear(){t.clear()},dispose(){t.clear()}}};function Ga(t,e,r){return t.watch?t.watch(((t,i)=>e(t,r+i))):()=>{}}async function Wa(t){"function"==typeof t.dispose&&await ja(t.dispose)}function Qa(t){return new Promise(((e,r)=>{t.oncomplete=t.onsuccess=()=>e(t.result),t.onabort=t.onerror=()=>r(t.error)}))}function Ya(t,e){const r=indexedDB.open(t);r.onupgradeneeded=()=>r.result.createObjectStore(e);const i=Qa(r);return(t,r)=>i.then((i=>r(i.transaction(e,t).objectStore(e))))}let Xa;function Za(){return Xa||(Xa=Ya("keyval-store","keyval")),Xa}function tc(t,e=Za()){return e("readonly",(e=>Qa(e.get(t))))}var ec=r(13554),rc=(t={})=>{const e=t.base&&t.base.length>0?`${t.base}:`:"",r=t=>e+t;let i;return t.dbName&&t.storeName&&(i=Ya(t.dbName,t.storeName)),{name:"idb-keyval",options:t,hasItem:async t=>!(typeof await tc(r(t),i)>"u"),getItem:async t=>await tc(r(t),i)??null,setItem:(t,e)=>function(t,e,r=Za()){return r("readwrite",(r=>(r.put(e,t),Qa(r.transaction))))}(r(t),e,i),removeItem:t=>function(t,e=Za()){return e("readwrite",(e=>(e.delete(t),Qa(e.transaction))))}(r(t),i),getKeys:()=>function(t=Za()){return t("readonly",(t=>{if(t.getAllKeys)return Qa(t.getAllKeys());const e=[];return function(t,e){return t.openCursor().onsuccess=function(){this.result&&(e(this.result),this.result.continue())},Qa(t.transaction)}(t,(t=>e.push(t.key))).then((()=>e))}))}(i),clear:()=>function(t=Za()){return t("readwrite",(t=>(t.clear(),Qa(t.transaction))))}(i)}};class ic{constructor(){this.indexedDb=function(t={}){const e={mounts:{"":t.driver||Ja()},mountpoints:[""],watching:!1,watchListeners:[],unwatch:{}},r=t=>{for(const r of e.mountpoints)if(t.startsWith(r))return{base:r,relativeKey:t.slice(r.length),driver:e.mounts[r]};return{base:"",relativeKey:t,driver:e.mounts[""]}},i=(t,r)=>e.mountpoints.filter((e=>e.startsWith(t)||r&&t.startsWith(e))).map((r=>({relativeBase:t.length>r.length?t.slice(r.length):void 0,mountpoint:r,driver:e.mounts[r]}))),n=(t,r)=>{if(e.watching){r=Ha(r);for(const i of e.watchListeners)i(t,r)}},s=async()=>{if(e.watching){for(const t in e.unwatch)await e.unwatch[t]();e.unwatch={},e.watching=!1}},o=(t,e,i)=>{const n=new Map,s=t=>{let e=n.get(t.base);return e||(e={driver:t.driver,base:t.base,items:[]},n.set(t.base,e)),e};for(const i of t){const t="string"==typeof i,n=Ha(t?i:i.key),o=t?void 0:i.value,a=t||!i.options?e:{...e,...i.options},c=r(n);s(c).items.push({key:n,value:o,relativeKey:c.relativeKey,options:a})}return Promise.all([...n.values()].map((t=>i(t)))).then((t=>t.flat()))},a={hasItem(t,e={}){t=Ha(t);const{relativeKey:i,driver:n}=r(t);return ja(n.hasItem,i,e)},getItem(t,e={}){t=Ha(t);const{relativeKey:i,driver:n}=r(t);return ja(n.getItem,i,e).then((t=>La(t)))},getItems:(t,e)=>o(t,e,(t=>t.driver.getItems?ja(t.driver.getItems,t.items.map((t=>({key:t.relativeKey,options:t.options}))),e).then((e=>e.map((e=>({key:Va(t.base,e.key),value:La(e.value)}))))):Promise.all(t.items.map((e=>ja(t.driver.getItem,e.relativeKey,e.options).then((t=>({key:e.key,value:La(t)})))))))),getItemRaw(t,e={}){t=Ha(t);const{relativeKey:i,driver:n}=r(t);return n.getItemRaw?ja(n.getItemRaw,i,e):ja(n.getItem,i,e).then((t=>function(t){return"string"!=typeof t?t:t.startsWith(Ka)?(Fa(),Buffer.from(t.slice(7),"base64")):t}(t)))},async setItem(t,e,i={}){if(void 0===e)return a.removeItem(t);t=Ha(t);const{relativeKey:s,driver:o}=r(t);o.setItem&&(await ja(o.setItem,s,za(e),i),o.watch||n("update",t))},async setItems(t,e){await o(t,e,(async t=>{if(t.driver.setItems)return ja(t.driver.setItems,t.items.map((t=>({key:t.relativeKey,value:za(t.value),options:t.options}))),e);t.driver.setItem&&await Promise.all(t.items.map((e=>ja(t.driver.setItem,e.relativeKey,za(e.value),e.options))))}))},async setItemRaw(t,e,i={}){if(void 0===e)return a.removeItem(t,i);t=Ha(t);const{relativeKey:s,driver:o}=r(t);if(o.setItemRaw)await ja(o.setItemRaw,s,e,i);else{if(!o.setItem)return;await ja(o.setItem,s,function(t){if("string"==typeof t)return t;Fa();const e=Buffer.from(t).toString("base64");return Ka+e}(e),i)}o.watch||n("update",t)},async removeItem(t,e={}){"boolean"==typeof e&&(e={removeMeta:e}),t=Ha(t);const{relativeKey:i,driver:s}=r(t);s.removeItem&&(await ja(s.removeItem,i,e),(e.removeMeta||e.removeMata)&&await ja(s.removeItem,i+"$",e),s.watch||n("remove",t))},async getMeta(t,e={}){"boolean"==typeof e&&(e={nativeOnly:e}),t=Ha(t);const{relativeKey:i,driver:n}=r(t),s=Object.create(null);if(n.getMeta&&Object.assign(s,await ja(n.getMeta,i,e)),!e.nativeOnly){const t=await ja(n.getItem,i+"$",e).then((t=>La(t)));t&&"object"==typeof t&&("string"==typeof t.atime&&(t.atime=new Date(t.atime)),"string"==typeof t.mtime&&(t.mtime=new Date(t.mtime)),Object.assign(s,t))}return s},setMeta(t,e,r={}){return this.setItem(t+"$",e,r)},removeMeta(t,e={}){return this.removeItem(t+"$",e)},async getKeys(t,e={}){t=$a(t);const r=i(t,!0);let n=[];const s=[];for(const t of r){const r=(await ja(t.driver.getKeys,t.relativeBase,e)).map((e=>t.mountpoint+Ha(e))).filter((t=>!n.some((e=>t.startsWith(e)))));s.push(...r),n=[t.mountpoint,...n.filter((e=>!e.startsWith(t.mountpoint)))]}return t?s.filter((e=>e.startsWith(t)&&!e.endsWith("$"))):s.filter((t=>!t.endsWith("$")))},async clear(t,e={}){t=$a(t),await Promise.all(i(t,!1).map((async t=>{if(t.driver.clear)return ja(t.driver.clear,t.relativeBase,e);if(t.driver.removeItem){const r=await t.driver.getKeys(t.relativeBase||"",e);return Promise.all(r.map((r=>t.driver.removeItem(r,e))))}})))},async dispose(){await Promise.all(Object.values(e.mounts).map((t=>Wa(t))))},watch:async t=>(await(async()=>{if(!e.watching){e.watching=!0;for(const t in e.mounts)e.unwatch[t]=await Ga(e.mounts[t],n,t)}})(),e.watchListeners.push(t),async()=>{e.watchListeners=e.watchListeners.filter((e=>e!==t)),0===e.watchListeners.length&&await s()}),async unwatch(){e.watchListeners=[],await s()},mount(t,r){if((t=$a(t))&&e.mounts[t])throw new Error(`already mounted at ${t}`);return t&&(e.mountpoints.push(t),e.mountpoints.sort(((t,e)=>e.length-t.length))),e.mounts[t]=r,e.watching&&Promise.resolve(Ga(r,n,t)).then((r=>{e.unwatch[t]=r})).catch(console.error),a},async unmount(t,r=!0){(t=$a(t))&&e.mounts[t]&&(e.watching&&t in e.unwatch&&(e.unwatch[t](),delete e.unwatch[t]),r&&await Wa(e.mounts[t]),e.mountpoints=e.mountpoints.filter((e=>e!==t)),delete e.mounts[t])},getMount(t=""){t=Ha(t)+":";const e=r(t);return{driver:e.driver,base:e.base}},getMounts:(t="",e={})=>(t=Ha(t),i(t,e.parents).map((t=>({driver:t.driver,base:t.mountpoint}))))};return a}({driver:rc({dbName:"WALLET_CONNECT_V2_INDEXED_DB",storeName:"keyvaluestorage"})})}async getKeys(){return this.indexedDb.getKeys()}async getEntries(){return(await this.indexedDb.getItems(await this.indexedDb.getKeys())).map((t=>[t.key,t.value]))}async getItem(t){const e=await this.indexedDb.getItem(t);if(null!==e)return e}async setItem(t,e){await this.indexedDb.setItem(t,(0,ec.h)(e))}async removeItem(t){await this.indexedDb.removeItem(t)}}var nc=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof r.g<"u"?r.g:typeof self<"u"?self:{},sc={exports:{}};function oc(t){var e;return[t[0],(0,ec.j)(null!=(e=t[1])?e:"")]}!function(){let t;function e(){}t=e,t.prototype.getItem=function(t){return this.hasOwnProperty(t)?String(this[t]):null},t.prototype.setItem=function(t,e){this[t]=String(e)},t.prototype.removeItem=function(t){delete this[t]},t.prototype.clear=function(){const t=this;Object.keys(t).forEach((function(e){t[e]=void 0,delete t[e]}))},t.prototype.key=function(t){return t=t||0,Object.keys(this)[t]},t.prototype.__defineGetter__("length",(function(){return Object.keys(this).length})),typeof nc<"u"&&nc.localStorage?sc.exports=nc.localStorage:typeof window<"u"&&window.localStorage?sc.exports=window.localStorage:sc.exports=new e}();class ac{constructor(){this.localStorage=sc.exports}async getKeys(){return Object.keys(this.localStorage)}async getEntries(){return Object.entries(this.localStorage).map(oc)}async getItem(t){const e=this.localStorage.getItem(t);if(null!==e)return(0,ec.j)(e)}async setItem(t,e){this.localStorage.setItem(t,(0,ec.h)(e))}async removeItem(t){this.localStorage.removeItem(t)}}class cc{constructor(){this.initialized=!1,this.setInitialized=t=>{this.storage=t,this.initialized=!0};const t=new ac;this.storage=t;try{(async(t,e,r)=>{const i="wc_storage_version",n=await e.getItem(i);if(n&&n>=1)return void r(e);const s=await t.getKeys();if(!s.length)return void r(e);const o=[];for(;s.length;){const r=s.shift();if(!r)continue;const i=r.toLowerCase();if(i.includes("wc@")||i.includes("walletconnect")||i.includes("wc_")||i.includes("wallet_connect")){const i=await t.getItem(r);await e.setItem(r,i),o.push(r)}}await e.setItem(i,1),r(e),(async(t,e)=>{e.length&&e.forEach((async e=>{await t.removeItem(e)}))})(t,o)})(t,new ic,this.setInitialized)}catch{this.initialized=!0}}async getKeys(){return await this.initialize(),this.storage.getKeys()}async getEntries(){return await this.initialize(),this.storage.getEntries()}async getItem(t){return await this.initialize(),this.storage.getItem(t)}async setItem(t,e){return await this.initialize(),this.storage.setItem(t,e)}async removeItem(t){return await this.initialize(),this.storage.removeItem(t)}async initialize(){this.initialized||await new Promise((t=>{const e=setInterval((()=>{this.initialized&&(clearInterval(e),t())}),20)}))}}class hc{}class uc extends hc{constructor(t){super()}}const lc=H.FIVE_SECONDS,fc="heartbeat_pulse";class dc extends uc{constructor(t){super(t),this.events=new C.EventEmitter,this.interval=lc,this.interval=t?.interval||lc}static async init(t){const e=new dc(t);return await e.init(),e}async init(){await this.initialize()}stop(){clearInterval(this.intervalRef)}on(t,e){this.events.on(t,e)}once(t,e){this.events.once(t,e)}off(t,e){this.events.off(t,e)}removeListener(t,e){this.events.removeListener(t,e)}async initialize(){this.intervalRef=setInterval((()=>this.pulse()),(0,H.toMiliseconds)(this.interval))}pulse(){this.events.emit(fc)}}var pc=r(29838);class gc extends hc{constructor(t){super(),this.opts=t,this.protocol="wc",this.version=2}}class mc extends hc{constructor(t,e){super(),this.core=t,this.logger=e,this.records=new Map}}class yc{constructor(t,e){this.logger=t,this.core=e}}class vc extends hc{constructor(t,e){super(),this.relayer=t,this.logger=e}}class wc extends hc{constructor(t){super()}}class bc{constructor(t,e,r,i){this.core=t,this.logger=e,this.name=r}}class Ac extends hc{constructor(t,e){super(),this.relayer=t,this.logger=e}}class _c extends hc{constructor(t,e){super(),this.core=t,this.logger=e}}class Ec{constructor(t,e){this.projectId=t,this.logger=e}}class Ic{constructor(t,e){this.projectId=t,this.logger=e}}O();class Sc{constructor(t){this.opts=t,this.protocol="wc",this.version=2}}C.EventEmitter;class Mc{constructor(t){this.client=t}}var Pc=r(37070),xc=r(74450);const Rc="base64url",Nc="utf8",Cc=":",Oc="did",Uc="key",Tc="base58btc",Dc="z",Bc="K36";var kc=r(45238),qc=r(75930),Lc=r(27302),jc=r(44117);function zc(t){return(0,Lc.toString)((0,jc.fromString)((0,ec.h)(t),Nc),Rc)}function Fc(t){const e=(0,jc.fromString)(Bc,Tc),r=Dc+(0,Lc.toString)(function(t,e){e||(e=t.reduce(((t,e)=>t+e.length),0));const r=(0,kc.K)(e);let i=0;for(const e of t)r.set(e,i),i+=e.length;return(0,qc.o)(r)}([e,t]),Tc);return[Oc,Uc,r].join(Cc)}function Kc(t=(0,xc.randomBytes)(32)){return Pc.K(t)}r(55665);const Hc=function(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),i=0;i<r.length;i++)r[i]=255;for(var n=0;n<t.length;n++){var s=t.charAt(n),o=s.charCodeAt(0);if(255!==r[o])throw new TypeError(s+" is ambiguous");r[o]=n}var a=t.length,c=t.charAt(0),h=Math.log(a)/Math.log(256),u=Math.log(256)/Math.log(a);function l(t){if("string"!=typeof t)throw new TypeError("Expected String");if(0===t.length)return new Uint8Array;var e=0;if(" "!==t[e]){for(var i=0,n=0;t[e]===c;)i++,e++;for(var s=(t.length-e)*h+1>>>0,o=new Uint8Array(s);t[e];){var u=r[t.charCodeAt(e)];if(255===u)return;for(var l=0,f=s-1;(0!==u||l<n)&&-1!==f;f--,l++)u+=a*o[f]>>>0,o[f]=u%256>>>0,u=u/256>>>0;if(0!==u)throw new Error("Non-zero carry");n=l,e++}if(" "!==t[e]){for(var d=s-n;d!==s&&0===o[d];)d++;for(var p=new Uint8Array(i+(s-d)),g=i;d!==s;)p[g++]=o[d++];return p}}}return{encode:function(e){if(e instanceof Uint8Array||(ArrayBuffer.isView(e)?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):Array.isArray(e)&&(e=Uint8Array.from(e))),!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===e.length)return"";for(var r=0,i=0,n=0,s=e.length;n!==s&&0===e[n];)n++,r++;for(var o=(s-n)*u+1>>>0,h=new Uint8Array(o);n!==s;){for(var l=e[n],f=0,d=o-1;(0!==l||f<i)&&-1!==d;d--,f++)l+=256*h[d]>>>0,h[d]=l%a>>>0,l=l/a>>>0;if(0!==l)throw new Error("Non-zero carry");i=f,n++}for(var p=o-i;p!==o&&0===h[p];)p++;for(var g=c.repeat(r);p<o;++p)g+=t.charAt(h[p]);return g},decodeUnsafe:l,decode:function(t){var r=l(t);if(r)return r;throw new Error(`Non-${e} character`)}}},Vc=(new Uint8Array(0),t=>{if(t instanceof Uint8Array&&"Uint8Array"===t.constructor.name)return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")});class $c{constructor(t,e,r){this.name=t,this.prefix=e,this.baseEncode=r}encode(t){if(t instanceof Uint8Array)return`${this.prefix}${this.baseEncode(t)}`;throw Error("Unknown type, must be binary type")}}class Jc{constructor(t,e,r){if(this.name=t,this.prefix=e,void 0===e.codePointAt(0))throw new Error("Invalid prefix character");this.prefixCodePoint=e.codePointAt(0),this.baseDecode=r}decode(t){if("string"==typeof t){if(t.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(t)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(t.slice(this.prefix.length))}throw Error("Can only multibase decode strings")}or(t){return Wc(this,t)}}class Gc{constructor(t){this.decoders=t}or(t){return Wc(this,t)}decode(t){const e=t[0],r=this.decoders[e];if(r)return r.decode(t);throw RangeError(`Unable to decode multibase string ${JSON.stringify(t)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const Wc=(t,e)=>new Gc({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class Qc{constructor(t,e,r,i){this.name=t,this.prefix=e,this.baseEncode=r,this.baseDecode=i,this.encoder=new $c(t,e,r),this.decoder=new Jc(t,e,i)}encode(t){return this.encoder.encode(t)}decode(t){return this.decoder.decode(t)}}const Yc=({name:t,prefix:e,encode:r,decode:i})=>new Qc(t,e,r,i),Xc=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:n}=Hc(r,e);return Yc({prefix:t,name:e,encode:i,decode:t=>Vc(n(t))})},Zc=({name:t,prefix:e,bitsPerChar:r,alphabet:i})=>Yc({prefix:e,name:t,encode:t=>((t,e,r)=>{const i="="===e[e.length-1],n=(1<<r)-1;let s="",o=0,a=0;for(let i=0;i<t.length;++i)for(a=a<<8|t[i],o+=8;o>r;)o-=r,s+=e[n&a>>o];if(o&&(s+=e[n&a<<r-o]),i)for(;s.length*r&7;)s+="=";return s})(t,i,r),decode:e=>((t,e,r,i)=>{const n={};for(let t=0;t<e.length;++t)n[e[t]]=t;let s=t.length;for(;"="===t[s-1];)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,h=0;for(let e=0;e<s;++e){const s=n[t[e]];if(void 0===s)throw new SyntaxError(`Non-${i} character`);c=c<<r|s,a+=r,a>=8&&(a-=8,o[h++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o})(e,i,r,t)}),th=Yc({prefix:"\0",name:"identity",encode:t=>(t=>(new TextDecoder).decode(t))(t),decode:t=>(t=>(new TextEncoder).encode(t))(t)}),eh=Zc({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1}),rh=Zc({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3}),ih=Xc({prefix:"9",name:"base10",alphabet:"0123456789"}),nh=Zc({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),sh=Zc({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4}),oh=Zc({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),ah=Zc({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),ch=Zc({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),hh=Zc({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),uh=Zc({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),lh=Zc({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),fh=Zc({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),dh=Zc({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),ph=Zc({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5}),gh=Xc({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),mh=Xc({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"}),yh=Xc({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),vh=Xc({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"}),wh=Zc({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),bh=Zc({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),Ah=Zc({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),_h=Zc({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6}),Eh=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),Ih=Eh.reduce(((t,e,r)=>(t[r]=e,t)),[]),Sh=Eh.reduce(((t,e,r)=>(t[e.codePointAt(0)]=r,t)),[]),Mh=Yc({prefix:"🚀",name:"base256emoji",encode:function(t){return t.reduce(((t,e)=>t+Ih[e]),"")},decode:function(t){const e=[];for(const r of t){const t=Sh[r.codePointAt(0)];if(void 0===t)throw new Error(`Non-base256emoji character: ${r}`);e.push(t)}return new Uint8Array(e)}});var Ph=128,xh=-128,Rh=Math.pow(2,31),Nh=Math.pow(2,7),Ch=Math.pow(2,14),Oh=Math.pow(2,21),Uh=Math.pow(2,28),Th=Math.pow(2,35),Dh=Math.pow(2,42),Bh=Math.pow(2,49),kh=Math.pow(2,56),qh=Math.pow(2,63);const Lh=function t(e,r,i){r=r||[];for(var n=i=i||0;e>=Rh;)r[i++]=255&e|Ph,e/=128;for(;e&xh;)r[i++]=255&e|Ph,e>>>=7;return r[i]=0|e,t.bytes=i-n+1,r},jh=function(t){return t<Nh?1:t<Ch?2:t<Oh?3:t<Uh?4:t<Th?5:t<Dh?6:t<Bh?7:t<kh?8:t<qh?9:10},zh=(t,e,r=0)=>(Lh(t,e,r),e),Fh=t=>jh(t),Kh=(t,e)=>{const r=e.byteLength,i=Fh(t),n=i+Fh(r),s=new Uint8Array(n+r);return zh(t,s,0),zh(r,s,i),s.set(e,n),new Hh(t,r,e,s)};class Hh{constructor(t,e,r,i){this.code=t,this.size=e,this.digest=r,this.bytes=i}}const Vh=({name:t,code:e,encode:r})=>new $h(t,e,r);class $h{constructor(t,e,r){this.name=t,this.code=e,this.encode=r}digest(t){if(t instanceof Uint8Array){const e=this.encode(t);return e instanceof Uint8Array?Kh(this.code,e):e.then((t=>Kh(this.code,t)))}throw Error("Unknown type, must be binary type")}}const Jh=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),Gh=Vh({name:"sha2-256",code:18,encode:Jh("SHA-256")}),Wh=Vh({name:"sha2-512",code:19,encode:Jh("SHA-512")}),Qh=Vc,Yh={code:0,name:"identity",encode:Qh,digest:t=>Kh(0,Qh(t))},Xh="raw",Zh=85,tu=t=>Vc(t),eu=t=>Vc(t),ru=new TextEncoder,iu=new TextDecoder,nu="json",su=512,ou=t=>ru.encode(JSON.stringify(t)),au=t=>JSON.parse(iu.decode(t));Symbol.toStringTag,Symbol.for("nodejs.util.inspect.custom"),Symbol.for("@ipld/js-cid/CID");const cu={...y,...v,...w,...b,...A,..._,...E,...I,...S,...M};function hu(t,e,r,i){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:i}}}const uu=hu("utf8","u",(t=>"u"+new TextDecoder("utf8").decode(t)),(t=>(new TextEncoder).encode(t.substring(1)))),lu=hu("ascii","a",(t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}),(t=>{const e=function(t=0){return null!=globalThis.Buffer&&null!=globalThis.Buffer.allocUnsafe?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}((t=t.substring(1)).length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e})),fu={utf8:uu,"utf-8":uu,hex:cu.base16,latin1:lu,ascii:lu,binary:lu,...cu};var du=r(27907),pu=r(39031);const gu=t=>t.split("?")[0],mu=typeof WebSocket<"u"?WebSocket:typeof r.g<"u"&&typeof r.g.WebSocket<"u"?r.g.WebSocket:typeof window<"u"&&typeof window.WebSocket<"u"?window.WebSocket:typeof self<"u"&&typeof self.WebSocket<"u"?self.WebSocket:r(90796);class yu{constructor(t){if(this.url=t,this.events=new C.EventEmitter,this.registering=!1,!(0,pu.isWsUrl)(t))throw new Error(`Provided URL is not compatible with WebSocket connection: ${t}`);this.url=t}get connected(){return typeof this.socket<"u"}get connecting(){return this.registering}on(t,e){this.events.on(t,e)}once(t,e){this.events.once(t,e)}off(t,e){this.events.off(t,e)}removeListener(t,e){this.events.removeListener(t,e)}async open(t=this.url){await this.register(t)}async close(){return new Promise(((t,e)=>{typeof this.socket>"u"?e(new Error("Connection already closed")):(this.socket.onclose=e=>{this.onClose(e),t()},this.socket.close())}))}async send(t){typeof this.socket>"u"&&(this.socket=await this.register());try{this.socket.send((0,ec.h)(t))}catch(e){this.onError(t.id,e)}}register(t=this.url){if(!(0,pu.isWsUrl)(t))throw new Error(`Provided URL is not compatible with WebSocket connection: ${t}`);if(this.registering){const t=this.events.getMaxListeners();return(this.events.listenerCount("register_error")>=t||this.events.listenerCount("open")>=t)&&this.events.setMaxListeners(t+1),new Promise(((t,e)=>{this.events.once("register_error",(t=>{this.resetMaxListeners(),e(t)})),this.events.once("open",(()=>{if(this.resetMaxListeners(),typeof this.socket>"u")return e(new Error("WebSocket connection is missing or invalid"));t(this.socket)}))}))}return this.url=t,this.registering=!0,new Promise(((e,i)=>{const n=new URLSearchParams(t).get("origin"),s=(0,pu.isReactNative)()?{headers:{origin:n}}:{rejectUnauthorized:!(0,pu.isLocalhostUrl)(t)},o=new mu(t,[],s);typeof WebSocket<"u"||typeof r.g<"u"&&typeof r.g.WebSocket<"u"||typeof window<"u"&&typeof window.WebSocket<"u"||typeof self<"u"&&typeof self.WebSocket<"u"?o.onerror=t=>{const e=t;i(this.emitError(e.error))}:o.on("error",(t=>{i(this.emitError(t))})),o.onopen=()=>{this.onOpen(o),e(o)}}))}onOpen(t){t.onmessage=t=>this.onPayload(t),t.onclose=t=>this.onClose(t),this.socket=t,this.registering=!1,this.events.emit("open")}onClose(t){this.socket=void 0,this.registering=!1,this.events.emit("close",t)}onPayload(t){if(typeof t.data>"u")return;const e="string"==typeof t.data?(0,ec.j)(t.data):t.data;this.events.emit("payload",e)}onError(t,e){const r=this.parseError(e),i=r.message||r.toString(),n=(0,pu.formatJsonRpcError)(t,i);this.events.emit("payload",n)}parseError(t,e=this.url){return(0,pu.parseConnectionError)(t,gu(e),"WS")}resetMaxListeners(){this.events.getMaxListeners()>10&&this.events.setMaxListeners(10)}emitError(t){const e=this.parseError(new Error(t?.message||`WebSocket connection failed for host: ${gu(this.url)}`));return this.events.emit("register_error",e),e}}var vu=r(8142),wu=r.n(vu),bu=r(50916),Au=r.n(bu),_u=function(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),i=0;i<r.length;i++)r[i]=255;for(var n=0;n<t.length;n++){var s=t.charAt(n),o=s.charCodeAt(0);if(255!==r[o])throw new TypeError(s+" is ambiguous");r[o]=n}var a=t.length,c=t.charAt(0),h=Math.log(a)/Math.log(256),u=Math.log(256)/Math.log(a);function l(t){if("string"!=typeof t)throw new TypeError("Expected String");if(0===t.length)return new Uint8Array;var e=0;if(" "!==t[e]){for(var i=0,n=0;t[e]===c;)i++,e++;for(var s=(t.length-e)*h+1>>>0,o=new Uint8Array(s);t[e];){var u=r[t.charCodeAt(e)];if(255===u)return;for(var l=0,f=s-1;(0!==u||l<n)&&-1!==f;f--,l++)u+=a*o[f]>>>0,o[f]=u%256>>>0,u=u/256>>>0;if(0!==u)throw new Error("Non-zero carry");n=l,e++}if(" "!==t[e]){for(var d=s-n;d!==s&&0===o[d];)d++;for(var p=new Uint8Array(i+(s-d)),g=i;d!==s;)p[g++]=o[d++];return p}}}return{encode:function(e){if(e instanceof Uint8Array||(ArrayBuffer.isView(e)?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):Array.isArray(e)&&(e=Uint8Array.from(e))),!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===e.length)return"";for(var r=0,i=0,n=0,s=e.length;n!==s&&0===e[n];)n++,r++;for(var o=(s-n)*u+1>>>0,h=new Uint8Array(o);n!==s;){for(var l=e[n],f=0,d=o-1;(0!==l||f<i)&&-1!==d;d--,f++)l+=256*h[d]>>>0,h[d]=l%a>>>0,l=l/a>>>0;if(0!==l)throw new Error("Non-zero carry");i=f,n++}for(var p=o-i;p!==o&&0===h[p];)p++;for(var g=c.repeat(r);p<o;++p)g+=t.charAt(h[p]);return g},decodeUnsafe:l,decode:function(t){var r=l(t);if(r)return r;throw new Error(`Non-${e} character`)}}};const Eu=t=>{if(t instanceof Uint8Array&&"Uint8Array"===t.constructor.name)return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")};class Iu{constructor(t,e,r){this.name=t,this.prefix=e,this.baseEncode=r}encode(t){if(t instanceof Uint8Array)return`${this.prefix}${this.baseEncode(t)}`;throw Error("Unknown type, must be binary type")}}class Su{constructor(t,e,r){if(this.name=t,this.prefix=e,void 0===e.codePointAt(0))throw new Error("Invalid prefix character");this.prefixCodePoint=e.codePointAt(0),this.baseDecode=r}decode(t){if("string"==typeof t){if(t.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(t)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(t.slice(this.prefix.length))}throw Error("Can only multibase decode strings")}or(t){return Pu(this,t)}}class Mu{constructor(t){this.decoders=t}or(t){return Pu(this,t)}decode(t){const e=t[0],r=this.decoders[e];if(r)return r.decode(t);throw RangeError(`Unable to decode multibase string ${JSON.stringify(t)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const Pu=(t,e)=>new Mu({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class xu{constructor(t,e,r,i){this.name=t,this.prefix=e,this.baseEncode=r,this.baseDecode=i,this.encoder=new Iu(t,e,r),this.decoder=new Su(t,e,i)}encode(t){return this.encoder.encode(t)}decode(t){return this.decoder.decode(t)}}const Ru=({name:t,prefix:e,encode:r,decode:i})=>new xu(t,e,r,i),Nu=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:n}=_u(r,e);return Ru({prefix:t,name:e,encode:i,decode:t=>Eu(n(t))})},Cu=({name:t,prefix:e,bitsPerChar:r,alphabet:i})=>Ru({prefix:e,name:t,encode:t=>((t,e,r)=>{const i="="===e[e.length-1],n=(1<<r)-1;let s="",o=0,a=0;for(let i=0;i<t.length;++i)for(a=a<<8|t[i],o+=8;o>r;)o-=r,s+=e[n&a>>o];if(o&&(s+=e[n&a<<r-o]),i)for(;s.length*r&7;)s+="=";return s})(t,i,r),decode:e=>((t,e,r,i)=>{const n={};for(let t=0;t<e.length;++t)n[e[t]]=t;let s=t.length;for(;"="===t[s-1];)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,h=0;for(let e=0;e<s;++e){const s=n[t[e]];if(void 0===s)throw new SyntaxError(`Non-${i} character`);c=c<<r|s,a+=r,a>=8&&(a-=8,o[h++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o})(e,i,r,t)}),Ou=Ru({prefix:"\0",name:"identity",encode:t=>(t=>(new TextDecoder).decode(t))(t),decode:t=>(t=>(new TextEncoder).encode(t))(t)});var Uu=Object.freeze({__proto__:null,identity:Ou});const Tu=Cu({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var Du=Object.freeze({__proto__:null,base2:Tu});const Bu=Cu({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var ku=Object.freeze({__proto__:null,base8:Bu});const qu=Nu({prefix:"9",name:"base10",alphabet:"0123456789"});var Lu=Object.freeze({__proto__:null,base10:qu});const ju=Cu({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),zu=Cu({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var Fu=Object.freeze({__proto__:null,base16:ju,base16upper:zu});const Ku=Cu({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),Hu=Cu({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Vu=Cu({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),$u=Cu({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),Ju=Cu({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Gu=Cu({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),Wu=Cu({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),Qu=Cu({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),Yu=Cu({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var Xu=Object.freeze({__proto__:null,base32:Ku,base32upper:Hu,base32pad:Vu,base32padupper:$u,base32hex:Ju,base32hexupper:Gu,base32hexpad:Wu,base32hexpadupper:Qu,base32z:Yu});const Zu=Nu({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),tl=Nu({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var el=Object.freeze({__proto__:null,base36:Zu,base36upper:tl});const rl=Nu({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),il=Nu({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var nl=Object.freeze({__proto__:null,base58btc:rl,base58flickr:il});const sl=Cu({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),ol=Cu({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),al=Cu({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),cl=Cu({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var hl=Object.freeze({__proto__:null,base64:sl,base64pad:ol,base64url:al,base64urlpad:cl});const ul=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),ll=ul.reduce(((t,e,r)=>(t[r]=e,t)),[]),fl=ul.reduce(((t,e,r)=>(t[e.codePointAt(0)]=r,t)),[]),dl=Ru({prefix:"🚀",name:"base256emoji",encode:function(t){return t.reduce(((t,e)=>t+ll[e]),"")},decode:function(t){const e=[];for(const r of t){const t=fl[r.codePointAt(0)];if(void 0===t)throw new Error(`Non-base256emoji character: ${r}`);e.push(t)}return new Uint8Array(e)}});var pl=Object.freeze({__proto__:null,base256emoji:dl}),gl=128,ml=-128,yl=Math.pow(2,31),vl=Math.pow(2,7),wl=Math.pow(2,14),bl=Math.pow(2,21),Al=Math.pow(2,28),_l=Math.pow(2,35),El=Math.pow(2,42),Il=Math.pow(2,49),Sl=Math.pow(2,56),Ml=Math.pow(2,63),Pl=function t(e,r,i){r=r||[];for(var n=i=i||0;e>=yl;)r[i++]=255&e|gl,e/=128;for(;e&ml;)r[i++]=255&e|gl,e>>>=7;return r[i]=0|e,t.bytes=i-n+1,r},xl=function(t){return t<vl?1:t<wl?2:t<bl?3:t<Al?4:t<_l?5:t<El?6:t<Il?7:t<Sl?8:t<Ml?9:10};const Rl=(t,e,r=0)=>(Pl(t,e,r),e),Nl=t=>xl(t),Cl=(t,e)=>{const r=e.byteLength,i=Nl(t),n=i+Nl(r),s=new Uint8Array(n+r);return Rl(t,s,0),Rl(r,s,i),s.set(e,n),new Ol(t,r,e,s)};class Ol{constructor(t,e,r,i){this.code=t,this.size=e,this.digest=r,this.bytes=i}}const Ul=({name:t,code:e,encode:r})=>new Tl(t,e,r);class Tl{constructor(t,e,r){this.name=t,this.code=e,this.encode=r}digest(t){if(t instanceof Uint8Array){const e=this.encode(t);return e instanceof Uint8Array?Cl(this.code,e):e.then((t=>Cl(this.code,t)))}throw Error("Unknown type, must be binary type")}}const Dl=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),Bl=Ul({name:"sha2-256",code:18,encode:Dl("SHA-256")}),kl=Ul({name:"sha2-512",code:19,encode:Dl("SHA-512")});Object.freeze({__proto__:null,sha256:Bl,sha512:kl});const ql=Eu,Ll={code:0,name:"identity",encode:ql,digest:t=>Cl(0,ql(t))};Object.freeze({__proto__:null,identity:Ll}),new TextEncoder,new TextDecoder;const jl={...Uu,...Du,...ku,...Lu,...Fu,...Xu,...el,...nl,...hl,...pl};function zl(t,e,r,i){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:i}}}const Fl=zl("utf8","u",(t=>"u"+new TextDecoder("utf8").decode(t)),(t=>(new TextEncoder).encode(t.substring(1)))),Kl=zl("ascii","a",(t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}),(t=>{const e=function(t=0){return null!=globalThis.Buffer&&null!=globalThis.Buffer.allocUnsafe?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}((t=t.substring(1)).length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e})),Hl={utf8:Fl,"utf-8":Fl,hex:jl.base16,latin1:Kl,ascii:Kl,binary:Kl,...jl},Vl="core",$l=`wc@2:${Vl}:`,Jl={database:":memory:"},Gl="client_ed25519_seed",Wl=H.ONE_DAY,Ql=H.SIX_HOURS,Yl="wss://relay.walletconnect.com",Xl="wss://relay.walletconnect.org",Zl="relayer_message",tf="relayer_message_ack",ef="relayer_connection_stalled",rf="relayer_publish",nf="payload",sf="connect",of="disconnect",af="error",cf=H.ONE_SECOND,hf="subscription_created",uf="subscription_deleted",lf=1e3*H.FIVE_SECONDS,ff={wc_pairingDelete:{req:{ttl:H.ONE_DAY,prompt:!1,tag:1e3},res:{ttl:H.ONE_DAY,prompt:!1,tag:1001}},wc_pairingPing:{req:{ttl:H.THIRTY_SECONDS,prompt:!1,tag:1002},res:{ttl:H.THIRTY_SECONDS,prompt:!1,tag:1003}},unregistered_method:{req:{ttl:H.ONE_DAY,prompt:!1,tag:0},res:{ttl:H.ONE_DAY,prompt:!1,tag:0}}},df="pairing_create",pf="pairing_delete",gf="history_created",mf="history_updated",yf="history_deleted",vf="expirer_created",wf="expirer_deleted",bf="expirer_expired",Af="verify-api",_f="https://verify.walletconnect.com",Ef="https://verify.walletconnect.org",If=[_f,Ef];class Sf{constructor(t,e){this.core=t,this.logger=e,this.keychain=new Map,this.name="keychain",this.version="0.3",this.initialized=!1,this.storagePrefix=$l,this.init=async()=>{if(!this.initialized){const t=await this.getKeyChain();typeof t<"u"&&(this.keychain=t),this.initialized=!0}},this.has=t=>(this.isInitialized(),this.keychain.has(t)),this.set=async(t,e)=>{this.isInitialized(),this.keychain.set(t,e),await this.persist()},this.get=t=>{this.isInitialized();const e=this.keychain.get(t);if(typeof e>"u"){const{message:e}=ma("NO_MATCHING_KEY",`${this.name}: ${t}`);throw new Error(e)}return e},this.del=async t=>{this.isInitialized(),this.keychain.delete(t),await this.persist()},this.core=t,this.logger=(0,pc.U5)(e,this.name)}get context(){return(0,pc.oI)(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setKeyChain(t){await this.core.storage.setItem(this.storageKey,$e(t))}async getKeyChain(){const t=await this.core.storage.getItem(this.storageKey);return typeof t<"u"?Je(t):void 0}async persist(){await this.setKeyChain(this.keychain)}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}}class Mf{constructor(t,e,r){this.core=t,this.logger=e,this.name="crypto",this.initialized=!1,this.init=async()=>{this.initialized||(await this.keychain.init(),this.initialized=!0)},this.hasKeys=t=>(this.isInitialized(),this.keychain.has(t)),this.getClientId=async()=>(this.isInitialized(),Fc(Kc(await this.getClientSeed()).publicKey)),this.generateKeyPair=()=>{this.isInitialized();const t=function(){const t=X.TZ();return{privateKey:Se(t.secretKey,zo),publicKey:Se(t.publicKey,zo)}}();return this.setPrivateKey(t.publicKey,t.privateKey)},this.signJWT=async t=>{this.isInitialized();const e=Kc(await this.getClientSeed()),r=Ho(),i=Wl;return await async function(t,e,r,i,n=(0,H.fromMiliseconds)(Date.now())){const s={alg:"EdDSA",typ:"JWT"},o={iss:Fc(i.publicKey),sub:t,aud:e,iat:n,exp:n+r},a=(c={header:s,payload:o},(0,jc.fromString)([zc(c.header),zc(c.payload)].join("."),"utf8"));var c;return function(t){return[zc(t.header),zc(t.payload),(e=t.signature,(0,Lc.toString)(e,Rc))].join(".");var e}({header:s,payload:o,signature:Pc._S(i.secretKey,a)})}(r,t,i,e)},this.generateSharedKey=(t,e,r)=>{this.isInitialized();const i=function(t,e){const r=X.Tc(Ie(t,zo),Ie(e,zo),!0);return Se(new W.i(Y.aD,r).expand(32),zo)}(this.getPrivateKey(t),e);return this.setSymKey(i,r)},this.setSymKey=async(t,e)=>{this.isInitialized();const r=e||Vo(t);return await this.keychain.set(r,t),r},this.deleteKeyPair=async t=>{this.isInitialized(),await this.keychain.del(t)},this.deleteSymKey=async t=>{this.isInitialized(),await this.keychain.del(t)},this.encode=async(t,e,r)=>{this.isInitialized();const i=Wo(r),n=(0,ec.h)(e);if(Qo(i)){const e=i.senderPublicKey,r=i.receiverPublicKey;t=await this.generateSharedKey(e,r)}const s=this.getSymKey(t),{type:o,senderPublicKey:a}=i;return function(t){const e=function(t){return Ie(`${t}`,jo)}(typeof t.type<"u"?t.type:0);if(1===Jo(e)&&typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");const r=typeof t.senderPublicKey<"u"?Ie(t.senderPublicKey,zo):void 0,i=typeof t.iv<"u"?Ie(t.iv,zo):(0,Q.po)(12);return function(t){if(1===Jo(t.type)){if(typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");return Se(tt([t.type,t.senderPublicKey,t.iv,t.sealed]),Fo)}return Se(tt([t.type,t.iv,t.sealed]),Fo)}({type:e,sealed:new G.g6(Ie(t.symKey,zo)).seal(i,Ie(t.message,Ko)),iv:i,senderPublicKey:r})}({type:o,symKey:s,message:n,senderPublicKey:a})},this.decode=async(t,e,r)=>{this.isInitialized();const i=function(t,e){const r=Go(t);return Wo({type:Jo(r.type),senderPublicKey:typeof r.senderPublicKey<"u"?Se(r.senderPublicKey,zo):void 0,receiverPublicKey:e?.receiverPublicKey})}(e,r);if(Qo(i)){const e=i.receiverPublicKey,r=i.senderPublicKey;t=await this.generateSharedKey(e,r)}try{const r=function(t){const e=new G.g6(Ie(t.symKey,zo)),{sealed:r,iv:i}=Go(t.encoded),n=e.open(i,r);if(null===n)throw new Error("Failed to decrypt");return Se(n,Ko)}({symKey:this.getSymKey(t),encoded:e});return(0,ec.j)(r)}catch(e){this.logger.error(`Failed to decode message from topic: '${t}', clientId: '${await this.getClientId()}'`),this.logger.error(e)}},this.getPayloadType=t=>Jo(Go(t).type),this.getPayloadSenderPublicKey=t=>{const e=Go(t);return e.senderPublicKey?function(t,e="utf8"){const r=fu[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return"utf8"!==e&&"utf-8"!==e||null==globalThis.Buffer||null==globalThis.Buffer.from?r.encoder.encode(t).substring(1):globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8")}(e.senderPublicKey,zo):void 0},this.core=t,this.logger=(0,pc.U5)(e,this.name),this.keychain=r||new Sf(this.core,this.logger)}get context(){return(0,pc.oI)(this.logger)}async setPrivateKey(t,e){return await this.keychain.set(t,e),t}getPrivateKey(t){return this.keychain.get(t)}async getClientSeed(){let t="";try{t=this.keychain.get(Gl)}catch{t=Ho(),await this.keychain.set(Gl,t)}return function(t,e="utf8"){const r=Hl[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return"utf8"!==e&&"utf-8"!==e||null==globalThis.Buffer||null==globalThis.Buffer.from?r.decoder.decode(`${r.prefix}${t}`):globalThis.Buffer.from(t,"utf8")}(t,"base16")}getSymKey(t){return this.keychain.get(t)}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}}class Pf extends yc{constructor(t,e){super(t,e),this.logger=t,this.core=e,this.messages=new Map,this.name="messages",this.version="0.3",this.initialized=!1,this.storagePrefix=$l,this.init=async()=>{if(!this.initialized){this.logger.trace("Initialized");try{const t=await this.getRelayerMessages();typeof t<"u"&&(this.messages=t),this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",size:this.messages.size})}catch(t){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(t)}finally{this.initialized=!0}}},this.set=async(t,e)=>{this.isInitialized();const r=$o(e);let i=this.messages.get(t);return typeof i>"u"&&(i={}),typeof i[r]<"u"||(i[r]=e,this.messages.set(t,i),await this.persist()),r},this.get=t=>{this.isInitialized();let e=this.messages.get(t);return typeof e>"u"&&(e={}),e},this.has=(t,e)=>(this.isInitialized(),typeof this.get(t)[$o(e)]<"u"),this.del=async t=>{this.isInitialized(),this.messages.delete(t),await this.persist()},this.logger=(0,pc.U5)(t,this.name),this.core=e}get context(){return(0,pc.oI)(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setRelayerMessages(t){await this.core.storage.setItem(this.storageKey,$e(t))}async getRelayerMessages(){const t=await this.core.storage.getItem(this.storageKey);return typeof t<"u"?Je(t):void 0}async persist(){await this.setRelayerMessages(this.messages)}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}}class xf extends vc{constructor(t,e){super(t,e),this.relayer=t,this.logger=e,this.events=new C.EventEmitter,this.name="publisher",this.queue=new Map,this.publishTimeout=(0,H.toMiliseconds)(H.ONE_MINUTE),this.failedPublishTimeout=(0,H.toMiliseconds)(H.ONE_SECOND),this.needsTransportRestart=!1,this.publish=async(t,e,r)=>{var i;this.logger.debug("Publishing Payload"),this.logger.trace({type:"method",method:"publish",params:{topic:t,message:e,opts:r}});const n=r?.ttl||Ql,s=Yo(r),o=r?.prompt||!1,a=r?.tag||0,c=r?.id||(0,pu.getBigIntRpcId)().toString(),h={topic:t,message:e,opts:{ttl:n,relay:s,prompt:o,tag:a,id:c}},u=`Failed to publish payload, please try again. id:${c} tag:${a}`,l=Date.now();let f,d=1;try{for(;void 0===f;){if(Date.now()-l>this.publishTimeout)throw new Error(u);this.logger.trace({id:c,attempts:d},`publisher.publish - attempt ${d}`),f=await await We(this.rpcPublish(t,e,n,s,o,a,c).catch((t=>this.logger.warn(t))),this.publishTimeout,u),d++,f||await new Promise((t=>setTimeout(t,this.failedPublishTimeout)))}this.relayer.events.emit(rf,h),this.logger.debug("Successfully Published Payload"),this.logger.trace({type:"method",method:"publish",params:{id:c,topic:t,message:e,opts:r}})}catch(t){if(this.logger.debug("Failed to Publish Payload"),this.logger.error(t),null!=(i=r?.internal)&&i.throwOnFailedPublish)throw t;this.queue.set(c,h)}},this.on=(t,e)=>{this.events.on(t,e)},this.once=(t,e)=>{this.events.once(t,e)},this.off=(t,e)=>{this.events.off(t,e)},this.removeListener=(t,e)=>{this.events.removeListener(t,e)},this.relayer=t,this.logger=(0,pc.U5)(e,this.name),this.registerEventListeners()}get context(){return(0,pc.oI)(this.logger)}rpcPublish(t,e,r,i,n,s,o){var a,c,h,u;const l={method:Xo(i.protocol).publish,params:{topic:t,message:e,ttl:r,prompt:n,tag:s},id:o};return ba(null==(a=l.params)?void 0:a.prompt)&&(null==(c=l.params)||delete c.prompt),ba(null==(h=l.params)?void 0:h.tag)&&(null==(u=l.params)||delete u.tag),this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"message",direction:"outgoing",request:l}),this.relayer.request(l)}removeRequestFromQueue(t){this.queue.delete(t)}checkQueue(){this.queue.forEach((async t=>{const{topic:e,message:r,opts:i}=t;await this.publish(e,r,i)}))}registerEventListeners(){this.relayer.core.heartbeat.on(fc,(()=>{if(this.needsTransportRestart)return this.needsTransportRestart=!1,void this.relayer.events.emit(ef);this.checkQueue()})),this.relayer.on(tf,(t=>{this.removeRequestFromQueue(t.id.toString())}))}}class Rf{constructor(){this.map=new Map,this.set=(t,e)=>{const r=this.get(t);this.exists(t,e)||this.map.set(t,[...r,e])},this.get=t=>this.map.get(t)||[],this.exists=(t,e)=>this.get(t).includes(e),this.delete=(t,e)=>{if(typeof e>"u")return void this.map.delete(t);if(!this.map.has(t))return;const r=this.get(t);if(!this.exists(t,e))return;const i=r.filter((t=>t!==e));i.length?this.map.set(t,i):this.map.delete(t)},this.clear=()=>{this.map.clear()}}get topics(){return Array.from(this.map.keys())}}var Nf=Object.defineProperty,Cf=Object.defineProperties,Of=Object.getOwnPropertyDescriptors,Uf=Object.getOwnPropertySymbols,Tf=Object.prototype.hasOwnProperty,Df=Object.prototype.propertyIsEnumerable,Bf=(t,e,r)=>e in t?Nf(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,kf=(t,e)=>{for(var r in e||(e={}))Tf.call(e,r)&&Bf(t,r,e[r]);if(Uf)for(var r of Uf(e))Df.call(e,r)&&Bf(t,r,e[r]);return t},qf=(t,e)=>Cf(t,Of(e));class Lf extends Ac{constructor(t,e){super(t,e),this.relayer=t,this.logger=e,this.subscriptions=new Map,this.topicMap=new Rf,this.events=new C.EventEmitter,this.name="subscription",this.version="0.3",this.pending=new Map,this.cached=[],this.initialized=!1,this.pendingSubscriptionWatchLabel="pending_sub_watch_label",this.pollingInterval=20,this.storagePrefix=$l,this.subscribeTimeout=(0,H.toMiliseconds)(H.ONE_MINUTE),this.restartInProgress=!1,this.batchSubscribeTopicsLimit=500,this.pendingBatchMessages=[],this.init=async()=>{this.initialized||(this.logger.trace("Initialized"),this.registerEventListeners(),this.clientId=await this.relayer.core.crypto.getClientId())},this.subscribe=async(t,e)=>{await this.restartToComplete(),this.isInitialized(),this.logger.debug("Subscribing Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:t,opts:e}});try{const r=Yo(e),i={topic:t,relay:r};this.pending.set(t,i);const n=await this.rpcSubscribe(t,r);return"string"==typeof n&&(this.onSubscribe(n,i),this.logger.debug("Successfully Subscribed Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:t,opts:e}})),n}catch(t){throw this.logger.debug("Failed to Subscribe Topic"),this.logger.error(t),t}},this.unsubscribe=async(t,e)=>{await this.restartToComplete(),this.isInitialized(),typeof e?.id<"u"?await this.unsubscribeById(t,e.id,e):await this.unsubscribeByTopic(t,e)},this.isSubscribed=async t=>{if(this.topics.includes(t))return!0;const e=`${this.pendingSubscriptionWatchLabel}_${t}`;return await new Promise(((r,i)=>{const n=new H.Watch;n.start(e);const s=setInterval((()=>{!this.pending.has(t)&&this.topics.includes(t)&&(clearInterval(s),n.stop(e),r(!0)),n.elapsed(e)>=lf&&(clearInterval(s),n.stop(e),i(new Error("Subscription resolution timeout")))}),this.pollingInterval)})).catch((()=>!1))},this.on=(t,e)=>{this.events.on(t,e)},this.once=(t,e)=>{this.events.once(t,e)},this.off=(t,e)=>{this.events.off(t,e)},this.removeListener=(t,e)=>{this.events.removeListener(t,e)},this.start=async()=>{await this.onConnect()},this.stop=async()=>{await this.onDisconnect()},this.restart=async()=>{this.restartInProgress=!0,await this.restore(),await this.reset(),this.restartInProgress=!1},this.relayer=t,this.logger=(0,pc.U5)(e,this.name),this.clientId=""}get context(){return(0,pc.oI)(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.relayer.core.customStoragePrefix+"//"+this.name}get length(){return this.subscriptions.size}get ids(){return Array.from(this.subscriptions.keys())}get values(){return Array.from(this.subscriptions.values())}get topics(){return this.topicMap.topics}hasSubscription(t,e){let r=!1;try{r=this.getSubscription(t).topic===e}catch{}return r}onEnable(){this.cached=[],this.initialized=!0}onDisable(){this.cached=this.values,this.subscriptions.clear(),this.topicMap.clear()}async unsubscribeByTopic(t,e){const r=this.topicMap.get(t);await Promise.all(r.map((async r=>await this.unsubscribeById(t,r,e))))}async unsubscribeById(t,e,r){this.logger.debug("Unsubscribing Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:t,id:e,opts:r}});try{const i=Yo(r);await this.rpcUnsubscribe(t,e,i);const n=ya("USER_DISCONNECTED",`${this.name}, ${t}`);await this.onUnsubscribe(t,e,n),this.logger.debug("Successfully Unsubscribed Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:t,id:e,opts:r}})}catch(t){throw this.logger.debug("Failed to Unsubscribe Topic"),this.logger.error(t),t}}async rpcSubscribe(t,e){const r={method:Xo(e.protocol).subscribe,params:{topic:t}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:r});try{return await await We(this.relayer.request(r).catch((t=>this.logger.warn(t))),this.subscribeTimeout)?$o(t+this.clientId):null}catch{this.logger.debug("Outgoing Relay Subscribe Payload stalled"),this.relayer.events.emit(ef)}return null}async rpcBatchSubscribe(t){if(!t.length)return;const e={method:Xo(t[0].relay.protocol).batchSubscribe,params:{topics:t.map((t=>t.topic))}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:e});try{return await await We(this.relayer.request(e).catch((t=>this.logger.warn(t))),this.subscribeTimeout)}catch{this.relayer.events.emit(ef)}}async rpcBatchFetchMessages(t){if(!t.length)return;const e={method:Xo(t[0].relay.protocol).batchFetchMessages,params:{topics:t.map((t=>t.topic))}};let r;this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:e});try{r=await await We(this.relayer.request(e).catch((t=>this.logger.warn(t))),this.subscribeTimeout)}catch{this.relayer.events.emit(ef)}return r}rpcUnsubscribe(t,e,r){const i={method:Xo(r.protocol).unsubscribe,params:{topic:t,id:e}};return this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i}),this.relayer.request(i)}onSubscribe(t,e){this.setSubscription(t,qf(kf({},e),{id:t})),this.pending.delete(e.topic)}onBatchSubscribe(t){t.length&&t.forEach((t=>{this.setSubscription(t.id,kf({},t)),this.pending.delete(t.topic)}))}async onUnsubscribe(t,e,r){this.events.removeAllListeners(e),this.hasSubscription(e,t)&&this.deleteSubscription(e,r),await this.relayer.messages.del(t)}async setRelayerSubscriptions(t){await this.relayer.core.storage.setItem(this.storageKey,t)}async getRelayerSubscriptions(){return await this.relayer.core.storage.getItem(this.storageKey)}setSubscription(t,e){this.logger.debug("Setting subscription"),this.logger.trace({type:"method",method:"setSubscription",id:t,subscription:e}),this.addSubscription(t,e)}addSubscription(t,e){this.subscriptions.set(t,kf({},e)),this.topicMap.set(e.topic,t),this.events.emit(hf,e)}getSubscription(t){this.logger.debug("Getting subscription"),this.logger.trace({type:"method",method:"getSubscription",id:t});const e=this.subscriptions.get(t);if(!e){const{message:e}=ma("NO_MATCHING_KEY",`${this.name}: ${t}`);throw new Error(e)}return e}deleteSubscription(t,e){this.logger.debug("Deleting subscription"),this.logger.trace({type:"method",method:"deleteSubscription",id:t,reason:e});const r=this.getSubscription(t);this.subscriptions.delete(t),this.topicMap.delete(r.topic,t),this.events.emit(uf,qf(kf({},r),{reason:e}))}async persist(){await this.setRelayerSubscriptions(this.values),this.events.emit("subscription_sync")}async reset(){if(this.cached.length){const t=Math.ceil(this.cached.length/this.batchSubscribeTopicsLimit);for(let e=0;e<t;e++){const t=this.cached.splice(0,this.batchSubscribeTopicsLimit);await this.batchFetchMessages(t),await this.batchSubscribe(t)}}this.events.emit("subscription_resubscribed")}async restore(){try{const t=await this.getRelayerSubscriptions();if(typeof t>"u"||!t.length)return;if(this.subscriptions.size){const{message:t}=ma("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`),new Error(t)}this.cached=t,this.logger.debug(`Successfully Restored subscriptions for ${this.name}`),this.logger.trace({type:"method",method:"restore",subscriptions:this.values})}catch(t){this.logger.debug(`Failed to Restore subscriptions for ${this.name}`),this.logger.error(t)}}async batchSubscribe(t){if(!t.length)return;const e=await this.rpcBatchSubscribe(t);va(e)&&this.onBatchSubscribe(e.map(((e,r)=>qf(kf({},t[r]),{id:e}))))}async batchFetchMessages(t){if(!t.length)return;this.logger.trace(`Fetching batch messages for ${t.length} subscriptions`);const e=await this.rpcBatchFetchMessages(t);e&&e.messages&&(this.pendingBatchMessages=this.pendingBatchMessages.concat(e.messages))}async onConnect(){await this.restart(),this.onEnable()}onDisconnect(){this.onDisable()}async checkPending(){if(!this.initialized||!this.relayer.connected)return;const t=[];this.pending.forEach((e=>{t.push(e)})),await this.batchSubscribe(t),this.pendingBatchMessages.length&&(await this.relayer.handleBatchMessageEvents(this.pendingBatchMessages),this.pendingBatchMessages=[])}registerEventListeners(){this.relayer.core.heartbeat.on(fc,(async()=>{await this.checkPending()})),this.events.on(hf,(async t=>{const e=hf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,data:t}),await this.persist()})),this.events.on(uf,(async t=>{const e=uf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,data:t}),await this.persist()}))}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}async restartToComplete(){this.restartInProgress&&await new Promise((t=>{const e=setInterval((()=>{this.restartInProgress||(clearInterval(e),t())}),this.pollingInterval)}))}}var jf=Object.defineProperty,zf=Object.getOwnPropertySymbols,Ff=Object.prototype.hasOwnProperty,Kf=Object.prototype.propertyIsEnumerable,Hf=(t,e,r)=>e in t?jf(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;class Vf extends wc{constructor(t){super(t),this.protocol="wc",this.version=2,this.events=new C.EventEmitter,this.name="relayer",this.transportExplicitlyClosed=!1,this.initialized=!1,this.connectionAttemptInProgress=!1,this.connectionStatusPollingInterval=20,this.staleConnectionErrors=["socket hang up","stalled","interrupted"],this.hasExperiencedNetworkDisruption=!1,this.requestsInFlight=new Map,this.heartBeatTimeout=(0,H.toMiliseconds)(H.THIRTY_SECONDS+H.ONE_SECOND),this.request=async t=>{var e,r;this.logger.debug("Publishing Request Payload");const i=t.id||(0,pu.getBigIntRpcId)().toString();await this.toEstablishConnection();try{const n=this.provider.request(t);this.requestsInFlight.set(i,{promise:n,request:t}),this.logger.trace({id:i,method:t.method,topic:null==(e=t.params)?void 0:e.topic},"relayer.request - attempt to publish...");const s=await new Promise((async(t,e)=>{const r=()=>{e(new Error(`relayer.request - publish interrupted, id: ${i}`))};this.provider.on(of,r);const s=await n;this.provider.off(of,r),t(s)}));return this.logger.trace({id:i,method:t.method,topic:null==(r=t.params)?void 0:r.topic},"relayer.request - published"),s}catch(t){throw this.logger.debug(`Failed to Publish Request: ${i}`),t}finally{this.requestsInFlight.delete(i)}},this.resetPingTimeout=()=>{if(je())try{clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout((()=>{var t,e,r;null==(r=null==(e=null==(t=this.provider)?void 0:t.connection)?void 0:e.socket)||r.terminate()}),this.heartBeatTimeout)}catch(t){this.logger.warn(t)}},this.onPayloadHandler=t=>{this.onProviderPayload(t),this.resetPingTimeout()},this.onConnectHandler=()=>{this.startPingTimeout(),this.events.emit("relayer_connect")},this.onDisconnectHandler=()=>{this.onProviderDisconnect()},this.onProviderErrorHandler=t=>{this.logger.error(t),this.events.emit("relayer_error",t),this.logger.info("Fatal socket error received, closing transport"),this.transportClose()},this.registerProviderListeners=()=>{this.provider.on(nf,this.onPayloadHandler),this.provider.on(sf,this.onConnectHandler),this.provider.on(of,this.onDisconnectHandler),this.provider.on(af,this.onProviderErrorHandler)},this.core=t.core,this.logger=typeof t.logger<"u"&&"string"!=typeof t.logger?(0,pc.U5)(t.logger,this.name):(0,pc.h6)((0,pc.iP)({level:t.logger||"error"})),this.messages=new Pf(this.logger,t.core),this.subscriber=new Lf(this,this.logger),this.publisher=new xf(this,this.logger),this.relayUrl=t?.relayUrl||Yl,this.projectId=t.projectId,this.bundleId=function(){var t;try{return ze()&&typeof r.g<"u"&&typeof(null==r.g?void 0:r.g.Application)<"u"?null==(t=r.g.Application)?void 0:t.applicationId:void 0}catch{return}}(),this.provider={}}async init(){this.logger.trace("Initialized"),this.registerEventListeners(),await Promise.all([this.messages.init(),this.subscriber.init()]);try{await this.transportOpen()}catch{this.logger.warn(`Connection via ${this.relayUrl} failed, attempting to connect via failover domain ${Xl}...`),await this.restartTransport(Xl)}this.initialized=!0,setTimeout((async()=>{0===this.subscriber.topics.length&&0===this.subscriber.pending.size&&(this.logger.info("No topics subscribed to after init, closing transport"),await this.transportClose(),this.transportExplicitlyClosed=!1)}),1e4)}get context(){return(0,pc.oI)(this.logger)}get connected(){var t,e,r;return 1===(null==(r=null==(e=null==(t=this.provider)?void 0:t.connection)?void 0:e.socket)?void 0:r.readyState)}get connecting(){var t,e,r;return 0===(null==(r=null==(e=null==(t=this.provider)?void 0:t.connection)?void 0:e.socket)?void 0:r.readyState)}async publish(t,e,r){this.isInitialized(),await this.publisher.publish(t,e,r),await this.recordMessageEvent({topic:t,message:e,publishedAt:Date.now()})}async subscribe(t,e){var r;this.isInitialized();let i,n=(null==(r=this.subscriber.topicMap.get(t))?void 0:r[0])||"";const s=e=>{e.topic===t&&(this.subscriber.off(hf,s),i())};return await Promise.all([new Promise((t=>{i=t,this.subscriber.on(hf,s)})),new Promise((async r=>{n=await this.subscriber.subscribe(t,e)||n,r()}))]),n}async unsubscribe(t,e){this.isInitialized(),await this.subscriber.unsubscribe(t,e)}on(t,e){this.events.on(t,e)}once(t,e){this.events.once(t,e)}off(t,e){this.events.off(t,e)}removeListener(t,e){this.events.removeListener(t,e)}async transportDisconnect(){if(!this.hasExperiencedNetworkDisruption&&this.connected&&this.requestsInFlight.size>0)try{await Promise.all(Array.from(this.requestsInFlight.values()).map((t=>t.promise)))}catch(t){this.logger.warn(t)}this.hasExperiencedNetworkDisruption||this.connected?await We(this.provider.disconnect(),2e3,"provider.disconnect()").catch((()=>this.onProviderDisconnect())):this.onProviderDisconnect()}async transportClose(){this.transportExplicitlyClosed=!0,await this.transportDisconnect()}async transportOpen(t){await this.confirmOnlineStateOrThrow(),t&&t!==this.relayUrl&&(this.relayUrl=t,await this.transportDisconnect()),await this.createProvider(),this.connectionAttemptInProgress=!0,this.transportExplicitlyClosed=!1;try{await new Promise((async(t,e)=>{const r=()=>{this.provider.off(of,r),e(new Error("Connection interrupted while trying to subscribe"))};this.provider.on(of,r),await We(this.provider.connect(),(0,H.toMiliseconds)(H.ONE_MINUTE),`Socket stalled when trying to connect to ${this.relayUrl}`).catch((t=>{e(t)})),await this.subscriber.start(),this.hasExperiencedNetworkDisruption=!1,t()}))}catch(t){this.logger.error(t);const e=t;if(this.hasExperiencedNetworkDisruption=!0,!this.isConnectionStalled(e.message))throw t}finally{this.connectionAttemptInProgress=!1}}async restartTransport(t){this.connectionAttemptInProgress||(this.relayUrl=t||this.relayUrl,await this.confirmOnlineStateOrThrow(),await this.transportClose(),await this.transportOpen())}async confirmOnlineStateOrThrow(){if(!await Oa())throw new Error("No internet connection detected. Please restart your network and try again.")}async handleBatchMessageEvents(t){if(0===t?.length)return void this.logger.trace("Batch message events is empty. Ignoring...");const e=t.sort(((t,e)=>t.publishedAt-e.publishedAt));this.logger.trace(`Batch of ${e.length} message events sorted`);for(const t of e)try{await this.onMessageEvent(t)}catch(t){this.logger.warn(t)}this.logger.trace(`Batch of ${e.length} message events processed`)}startPingTimeout(){var t,e,r,i,n;if(je())try{null!=(e=null==(t=this.provider)?void 0:t.connection)&&e.socket&&(null==(n=null==(i=null==(r=this.provider)?void 0:r.connection)?void 0:i.socket)||n.once("ping",(()=>{this.resetPingTimeout()}))),this.resetPingTimeout()}catch(t){this.logger.warn(t)}}isConnectionStalled(t){return this.staleConnectionErrors.some((e=>t.includes(e)))}async createProvider(){this.provider.connection&&this.unregisterProviderListeners();const t=await this.core.crypto.signJWT(this.relayUrl);this.provider=new du.F(new yu(function({protocol:t,version:e,relayUrl:r,sdkVersion:i,auth:n,projectId:s,useOnCloseEvent:o,bundleId:a}){const c=r.split("?"),h={auth:n,ua:He(t,e,i),projectId:s,useOnCloseEvent:o||void 0,origin:a||void 0},u=function(t,e){let r=J.parse(t);return r=Be(Be({},r),e),J.stringify(r)}(c[1]||"",h);return c[0]+"?"+u}({sdkVersion:"2.13.0",protocol:this.protocol,version:this.version,relayUrl:this.relayUrl,projectId:this.projectId,auth:t,useOnCloseEvent:!0,bundleId:this.bundleId}))),this.registerProviderListeners()}async recordMessageEvent(t){const{topic:e,message:r}=t;await this.messages.set(e,r)}async shouldIgnoreMessageEvent(t){const{topic:e,message:r}=t;if(!r||0===r.length)return this.logger.debug(`Ignoring invalid/empty message: ${r}`),!0;if(!await this.subscriber.isSubscribed(e))return this.logger.debug(`Ignoring message for non-subscribed topic ${e}`),!0;const i=this.messages.has(e,r);return i&&this.logger.debug(`Ignoring duplicate message: ${r}`),i}async onProviderPayload(t){if(this.logger.debug("Incoming Relay Payload"),this.logger.trace({type:"payload",direction:"incoming",payload:t}),(0,pu.isJsonRpcRequest)(t)){if(!t.method.endsWith("_subscription"))return;const e=t.params,{topic:r,message:i,publishedAt:n}=e.data,s={topic:r,message:i,publishedAt:n};this.logger.debug("Emitting Relayer Payload"),this.logger.trace(((t,e)=>{for(var r in e||(e={}))Ff.call(e,r)&&Hf(t,r,e[r]);if(zf)for(var r of zf(e))Kf.call(e,r)&&Hf(t,r,e[r]);return t})({type:"event",event:e.id},s)),this.events.emit(e.id,s),await this.acknowledgePayload(t),await this.onMessageEvent(s)}else(0,pu.isJsonRpcResponse)(t)&&this.events.emit(tf,t)}async onMessageEvent(t){await this.shouldIgnoreMessageEvent(t)||(this.events.emit(Zl,t),await this.recordMessageEvent(t))}async acknowledgePayload(t){const e=(0,pu.formatJsonRpcResult)(t.id,!0);await this.provider.connection.send(e)}unregisterProviderListeners(){this.provider.off(nf,this.onPayloadHandler),this.provider.off(sf,this.onConnectHandler),this.provider.off(of,this.onDisconnectHandler),this.provider.off(af,this.onProviderErrorHandler),clearTimeout(this.pingTimeout)}async registerEventListeners(){let t=await Oa();!function(t){switch(Ke()){case qe.browser:!function(t){!ze()&&Fe()&&(window.addEventListener("online",(()=>t(!0))),window.addEventListener("offline",(()=>t(!1))))}(t);break;case qe.reactNative:!function(t){ze()&&typeof r.g<"u"&&null!=r.g&&r.g.NetInfo&&r.g?.NetInfo.addEventListener((e=>t(e?.isConnected)))}(t);case qe.node:}}((async e=>{t!==e&&(t=e,e?await this.restartTransport().catch((t=>this.logger.error(t))):(this.hasExperiencedNetworkDisruption=!0,await this.transportDisconnect(),this.transportExplicitlyClosed=!1))}))}async onProviderDisconnect(){await this.subscriber.stop(),this.requestsInFlight.clear(),clearTimeout(this.pingTimeout),this.events.emit("relayer_disconnect"),this.connectionAttemptInProgress=!1,!this.transportExplicitlyClosed&&setTimeout((async()=>{await this.transportOpen().catch((t=>this.logger.error(t)))}),(0,H.toMiliseconds)(cf))}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}async toEstablishConnection(){await this.confirmOnlineStateOrThrow(),!this.connected&&(this.connectionAttemptInProgress&&await new Promise((t=>{const e=setInterval((()=>{this.connected&&(clearInterval(e),t())}),this.connectionStatusPollingInterval)})),await this.transportOpen())}}var $f=Object.defineProperty,Jf=Object.getOwnPropertySymbols,Gf=Object.prototype.hasOwnProperty,Wf=Object.prototype.propertyIsEnumerable,Qf=(t,e,r)=>e in t?$f(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Yf=(t,e)=>{for(var r in e||(e={}))Gf.call(e,r)&&Qf(t,r,e[r]);if(Jf)for(var r of Jf(e))Wf.call(e,r)&&Qf(t,r,e[r]);return t};class Xf extends bc{constructor(t,e,r,i=$l,n=void 0){super(t,e,r,i),this.core=t,this.logger=e,this.name=r,this.map=new Map,this.version="0.3",this.cached=[],this.initialized=!1,this.storagePrefix=$l,this.recentlyDeleted=[],this.recentlyDeletedLimit=200,this.init=async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach((t=>{this.getKey&&null!==t&&!ba(t)?this.map.set(this.getKey(t),t):function(t){var e;return null==(e=t?.proposer)?void 0:e.publicKey}(t)?this.map.set(t.id,t):function(t){return t?.topic}(t)&&this.map.set(t.topic,t)})),this.cached=[],this.initialized=!0)},this.set=async(t,e)=>{this.isInitialized(),this.map.has(t)?await this.update(t,e):(this.logger.debug("Setting value"),this.logger.trace({type:"method",method:"set",key:t,value:e}),this.map.set(t,e),await this.persist())},this.get=t=>(this.isInitialized(),this.logger.debug("Getting value"),this.logger.trace({type:"method",method:"get",key:t}),this.getData(t)),this.getAll=t=>(this.isInitialized(),t?this.values.filter((e=>Object.keys(t).every((r=>wu()(e[r],t[r]))))):this.values),this.update=async(t,e)=>{this.isInitialized(),this.logger.debug("Updating value"),this.logger.trace({type:"method",method:"update",key:t,update:e});const r=Yf(Yf({},this.getData(t)),e);this.map.set(t,r),await this.persist()},this.delete=async(t,e)=>{this.isInitialized(),this.map.has(t)&&(this.logger.debug("Deleting value"),this.logger.trace({type:"method",method:"delete",key:t,reason:e}),this.map.delete(t),this.addToRecentlyDeleted(t),await this.persist())},this.logger=(0,pc.U5)(e,this.name),this.storagePrefix=i,this.getKey=n}get context(){return(0,pc.oI)(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.map.size}get keys(){return Array.from(this.map.keys())}get values(){return Array.from(this.map.values())}addToRecentlyDeleted(t){this.recentlyDeleted.push(t),this.recentlyDeleted.length>=this.recentlyDeletedLimit&&this.recentlyDeleted.splice(0,this.recentlyDeletedLimit/2)}async setDataStore(t){await this.core.storage.setItem(this.storageKey,t)}async getDataStore(){return await this.core.storage.getItem(this.storageKey)}getData(t){const e=this.map.get(t);if(!e){if(this.recentlyDeleted.includes(t)){const{message:e}=ma("MISSING_OR_INVALID",`Record was recently deleted - ${this.name}: ${t}`);throw this.logger.error(e),new Error(e)}const{message:e}=ma("NO_MATCHING_KEY",`${this.name}: ${t}`);throw this.logger.error(e),new Error(e)}return e}async persist(){await this.setDataStore(this.values)}async restore(){try{const t=await this.getDataStore();if(typeof t>"u"||!t.length)return;if(this.map.size){const{message:t}=ma("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=t,this.logger.debug(`Successfully Restored value for ${this.name}`),this.logger.trace({type:"method",method:"restore",value:this.values})}catch(t){this.logger.debug(`Failed to Restore value for ${this.name}`),this.logger.error(t)}}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}}class Zf{constructor(t,e){this.core=t,this.logger=e,this.name="pairing",this.version="0.3",this.events=new(O()),this.initialized=!1,this.storagePrefix=$l,this.ignoredPayloadTypes=[1],this.registeredMethods=[],this.init=async()=>{this.initialized||(await this.pairings.init(),await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.initialized=!0,this.logger.trace("Initialized"))},this.register=({methods:t})=>{this.isInitialized(),this.registeredMethods=[...new Set([...this.registeredMethods,...t])]},this.create=async t=>{this.isInitialized();const e=Ho(),r=await this.core.crypto.setSymKey(e),i=Xe(H.FIVE_MINUTES),n={protocol:"irn"},s={topic:r,expiry:i,relay:n,active:!1},o=function(t){return`${t.protocol}:${t.topic}@${t.version}?`+J.stringify(oa(((t,e)=>ta(t,ea(e)))(oa({symKey:t.symKey},function(t,e="-"){const r={};return Object.keys(t).forEach((i=>{const n="relay"+e+i;t[i]&&(r[n]=t[i])})),r}(t.relay)),{expiryTimestamp:t.expiryTimestamp}),t.methods?{methods:t.methods.join(",")}:{}))}({protocol:this.core.protocol,version:this.core.version,topic:r,symKey:e,relay:n,expiryTimestamp:i,methods:t?.methods});return this.core.expirer.set(r,i),await this.pairings.set(r,s),await this.core.relayer.subscribe(r),{topic:r,uri:o}},this.pair=async t=>{this.isInitialized(),this.isValidPair(t);const{topic:e,symKey:r,relay:i,expiryTimestamp:n,methods:s}=ca(t.uri);let o;if(this.pairings.keys.includes(e)&&(o=this.pairings.get(e),o.active))throw new Error(`Pairing already exists: ${e}. Please try again with a new connection URI.`);const a=n||Xe(H.FIVE_MINUTES),c={topic:e,relay:i,expiry:a,active:!1,methods:s};return this.core.expirer.set(e,a),await this.pairings.set(e,c),t.activatePairing&&await this.activate({topic:e}),this.events.emit(df,c),this.core.crypto.keychain.has(e)||await this.core.crypto.setSymKey(r,e),await this.core.relayer.subscribe(e,{relay:i}),c},this.activate=async({topic:t})=>{this.isInitialized();const e=Xe(H.THIRTY_DAYS);this.core.expirer.set(t,e),await this.pairings.update(t,{active:!0,expiry:e})},this.ping=async t=>{this.isInitialized(),await this.isValidPing(t);const{topic:e}=t;if(this.pairings.keys.includes(e)){const t=await this.sendRequest(e,"wc_pairingPing",{}),{done:r,resolve:i,reject:n}=Ge();this.events.once(tr("pairing_ping",t),(({error:t})=>{t?n(t):i()})),await r()}},this.updateExpiry=async({topic:t,expiry:e})=>{this.isInitialized(),await this.pairings.update(t,{expiry:e})},this.updateMetadata=async({topic:t,metadata:e})=>{this.isInitialized(),await this.pairings.update(t,{peerMetadata:e})},this.getPairings=()=>(this.isInitialized(),this.pairings.values),this.disconnect=async t=>{this.isInitialized(),await this.isValidDisconnect(t);const{topic:e}=t;this.pairings.keys.includes(e)&&(await this.sendRequest(e,"wc_pairingDelete",ya("USER_DISCONNECTED")),await this.deletePairing(e))},this.sendRequest=async(t,e,r)=>{const i=(0,pu.formatJsonRpcRequest)(e,r),n=await this.core.crypto.encode(t,i),s=ff[e].req;return this.core.history.set(t,i),this.core.relayer.publish(t,n,s),i.id},this.sendResult=async(t,e,r)=>{const i=(0,pu.formatJsonRpcResult)(t,r),n=await this.core.crypto.encode(e,i),s=await this.core.history.get(e,t),o=ff[s.request.method].res;await this.core.relayer.publish(e,n,o),await this.core.history.resolve(i)},this.sendError=async(t,e,r)=>{const i=(0,pu.formatJsonRpcError)(t,r),n=await this.core.crypto.encode(e,i),s=await this.core.history.get(e,t),o=ff[s.request.method]?ff[s.request.method].res:ff.unregistered_method.res;await this.core.relayer.publish(e,n,o),await this.core.history.resolve(i)},this.deletePairing=async(t,e)=>{await this.core.relayer.unsubscribe(t),await Promise.all([this.pairings.delete(t,ya("USER_DISCONNECTED")),this.core.crypto.deleteSymKey(t),e?Promise.resolve():this.core.expirer.del(t)])},this.cleanup=async()=>{const t=this.pairings.getAll().filter((t=>Ze(t.expiry)));await Promise.all(t.map((t=>this.deletePairing(t.topic))))},this.onRelayEventRequest=t=>{const{topic:e,payload:r}=t;switch(r.method){case"wc_pairingPing":return this.onPairingPingRequest(e,r);case"wc_pairingDelete":return this.onPairingDeleteRequest(e,r);default:return this.onUnknownRpcMethodRequest(e,r)}},this.onRelayEventResponse=async t=>{const{topic:e,payload:r}=t,i=(await this.core.history.get(e,r.id)).request.method;return"wc_pairingPing"===i?this.onPairingPingResponse(e,r):this.onUnknownRpcMethodResponse(i)},this.onPairingPingRequest=async(t,e)=>{const{id:r}=e;try{this.isValidPing({topic:t}),await this.sendResult(r,t,!0),this.events.emit("pairing_ping",{id:r,topic:t})}catch(e){await this.sendError(r,t,e),this.logger.error(e)}},this.onPairingPingResponse=(t,e)=>{const{id:r}=e;setTimeout((()=>{(0,pu.isJsonRpcResult)(e)?this.events.emit(tr("pairing_ping",r),{}):(0,pu.isJsonRpcError)(e)&&this.events.emit(tr("pairing_ping",r),{error:e.error})}),500)},this.onPairingDeleteRequest=async(t,e)=>{const{id:r}=e;try{this.isValidDisconnect({topic:t}),await this.deletePairing(t),this.events.emit(pf,{id:r,topic:t})}catch(e){await this.sendError(r,t,e),this.logger.error(e)}},this.onUnknownRpcMethodRequest=async(t,e)=>{const{id:r,method:i}=e;try{if(this.registeredMethods.includes(i))return;const e=ya("WC_METHOD_UNSUPPORTED",i);await this.sendError(r,t,e),this.logger.error(e)}catch(e){await this.sendError(r,t,e),this.logger.error(e)}},this.onUnknownRpcMethodResponse=t=>{this.registeredMethods.includes(t)||this.logger.error(ya("WC_METHOD_UNSUPPORTED",t))},this.isValidPair=t=>{var e;if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`pair() params: ${t}`);throw new Error(e)}if(!function(t){if(Aa(t,!1))try{return typeof new URL(t)<"u"}catch{return!1}return!1}(t.uri)){const{message:e}=ma("MISSING_OR_INVALID",`pair() uri: ${t.uri}`);throw new Error(e)}const r=ca(t.uri);if(null==(e=r?.relay)||!e.protocol){const{message:t}=ma("MISSING_OR_INVALID","pair() uri#relay-protocol");throw new Error(t)}if(null==r||!r.symKey){const{message:t}=ma("MISSING_OR_INVALID","pair() uri#symKey");throw new Error(t)}if(null!=r&&r.expiryTimestamp&&(0,H.toMiliseconds)(r?.expiryTimestamp)<Date.now()){const{message:t}=ma("EXPIRED","pair() URI has expired. Please try again with a new connection URI.");throw new Error(t)}},this.isValidPing=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`ping() params: ${t}`);throw new Error(e)}const{topic:e}=t;await this.isValidPairingTopic(e)},this.isValidDisconnect=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`disconnect() params: ${t}`);throw new Error(e)}const{topic:e}=t;await this.isValidPairingTopic(e)},this.isValidPairingTopic=async t=>{if(!Aa(t,!1)){const{message:e}=ma("MISSING_OR_INVALID",`pairing topic should be a string: ${t}`);throw new Error(e)}if(!this.pairings.keys.includes(t)){const{message:e}=ma("NO_MATCHING_KEY",`pairing topic doesn't exist: ${t}`);throw new Error(e)}if(Ze(this.pairings.get(t).expiry)){await this.deletePairing(t);const{message:e}=ma("EXPIRED",`pairing topic: ${t}`);throw new Error(e)}},this.core=t,this.logger=(0,pc.U5)(e,this.name),this.pairings=new Xf(this.core,this.logger,this.name,this.storagePrefix)}get context(){return(0,pc.oI)(this.logger)}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}registerRelayerEvents(){this.core.relayer.on(Zl,(async t=>{const{topic:e,message:r}=t;if(!this.pairings.keys.includes(e)||this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(r)))return;const i=await this.core.crypto.decode(e,r);try{(0,pu.isJsonRpcRequest)(i)?(this.core.history.set(e,i),this.onRelayEventRequest({topic:e,payload:i})):(0,pu.isJsonRpcResponse)(i)&&(await this.core.history.resolve(i),await this.onRelayEventResponse({topic:e,payload:i}),this.core.history.delete(e,i.id))}catch(t){this.logger.error(t)}}))}registerExpirerEvents(){this.core.expirer.on(bf,(async t=>{const{topic:e}=Ye(t.target);e&&this.pairings.keys.includes(e)&&(await this.deletePairing(e,!0),this.events.emit("pairing_expire",{topic:e}))}))}}class td extends mc{constructor(t,e){super(t,e),this.core=t,this.logger=e,this.records=new Map,this.events=new C.EventEmitter,this.name="history",this.version="0.3",this.cached=[],this.initialized=!1,this.storagePrefix=$l,this.init=async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach((t=>this.records.set(t.id,t))),this.cached=[],this.registerEventListeners(),this.initialized=!0)},this.set=(t,e,r)=>{if(this.isInitialized(),this.logger.debug("Setting JSON-RPC request history record"),this.logger.trace({type:"method",method:"set",topic:t,request:e,chainId:r}),this.records.has(e.id))return;const i={id:e.id,topic:t,request:{method:e.method,params:e.params||null},chainId:r,expiry:Xe(H.THIRTY_DAYS)};this.records.set(i.id,i),this.persist(),this.events.emit(gf,i)},this.resolve=async t=>{if(this.isInitialized(),this.logger.debug("Updating JSON-RPC response history record"),this.logger.trace({type:"method",method:"update",response:t}),!this.records.has(t.id))return;const e=await this.getRecord(t.id);typeof e.response>"u"&&(e.response=(0,pu.isJsonRpcError)(t)?{error:t.error}:{result:t.result},this.records.set(e.id,e),this.persist(),this.events.emit(mf,e))},this.get=async(t,e)=>(this.isInitialized(),this.logger.debug("Getting record"),this.logger.trace({type:"method",method:"get",topic:t,id:e}),await this.getRecord(e)),this.delete=(t,e)=>{this.isInitialized(),this.logger.debug("Deleting record"),this.logger.trace({type:"method",method:"delete",id:e}),this.values.forEach((r=>{if(r.topic===t){if(typeof e<"u"&&r.id!==e)return;this.records.delete(r.id),this.events.emit(yf,r)}})),this.persist()},this.exists=async(t,e)=>(this.isInitialized(),!!this.records.has(e)&&(await this.getRecord(e)).topic===t),this.on=(t,e)=>{this.events.on(t,e)},this.once=(t,e)=>{this.events.once(t,e)},this.off=(t,e)=>{this.events.off(t,e)},this.removeListener=(t,e)=>{this.events.removeListener(t,e)},this.logger=(0,pc.U5)(e,this.name)}get context(){return(0,pc.oI)(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get size(){return this.records.size}get keys(){return Array.from(this.records.keys())}get values(){return Array.from(this.records.values())}get pending(){const t=[];return this.values.forEach((e=>{if(typeof e.response<"u")return;const r={topic:e.topic,request:(0,pu.formatJsonRpcRequest)(e.request.method,e.request.params,e.id),chainId:e.chainId};return t.push(r)})),t}async setJsonRpcRecords(t){await this.core.storage.setItem(this.storageKey,t)}async getJsonRpcRecords(){return await this.core.storage.getItem(this.storageKey)}getRecord(t){this.isInitialized();const e=this.records.get(t);if(!e){const{message:e}=ma("NO_MATCHING_KEY",`${this.name}: ${t}`);throw new Error(e)}return e}async persist(){await this.setJsonRpcRecords(this.values),this.events.emit("history_sync")}async restore(){try{const t=await this.getJsonRpcRecords();if(typeof t>"u"||!t.length)return;if(this.records.size){const{message:t}=ma("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=t,this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",records:this.values})}catch(t){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(t)}}registerEventListeners(){this.events.on(gf,(t=>{const e=gf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,record:t})})),this.events.on(mf,(t=>{const e=mf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,record:t})})),this.events.on(yf,(t=>{const e=yf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,record:t})})),this.core.heartbeat.on(fc,(()=>{this.cleanup()}))}cleanup(){try{this.isInitialized();let t=!1;this.records.forEach((e=>{(0,H.toMiliseconds)(e.expiry||0)-Date.now()<=0&&(this.logger.info(`Deleting expired history log: ${e.id}`),this.records.delete(e.id),this.events.emit(yf,e,!1),t=!0)})),t&&this.persist()}catch(t){this.logger.warn(t)}}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}}class ed extends _c{constructor(t,e){super(t,e),this.core=t,this.logger=e,this.expirations=new Map,this.events=new C.EventEmitter,this.name="expirer",this.version="0.3",this.cached=[],this.initialized=!1,this.storagePrefix=$l,this.init=async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach((t=>this.expirations.set(t.target,t))),this.cached=[],this.registerEventListeners(),this.initialized=!0)},this.has=t=>{try{const e=this.formatTarget(t);return typeof this.getExpiration(e)<"u"}catch{return!1}},this.set=(t,e)=>{this.isInitialized();const r=this.formatTarget(t),i={target:r,expiry:e};this.expirations.set(r,i),this.checkExpiry(r,i),this.events.emit(vf,{target:r,expiration:i})},this.get=t=>{this.isInitialized();const e=this.formatTarget(t);return this.getExpiration(e)},this.del=t=>{if(this.isInitialized(),this.has(t)){const e=this.formatTarget(t),r=this.getExpiration(e);this.expirations.delete(e),this.events.emit(wf,{target:e,expiration:r})}},this.on=(t,e)=>{this.events.on(t,e)},this.once=(t,e)=>{this.events.once(t,e)},this.off=(t,e)=>{this.events.off(t,e)},this.removeListener=(t,e)=>{this.events.removeListener(t,e)},this.logger=(0,pc.U5)(e,this.name)}get context(){return(0,pc.oI)(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.expirations.size}get keys(){return Array.from(this.expirations.keys())}get values(){return Array.from(this.expirations.values())}formatTarget(t){if("string"==typeof t)return function(t){return Qe("topic",t)}(t);if("number"==typeof t)return function(t){return Qe("id",t)}(t);const{message:e}=ma("UNKNOWN_TYPE","Target type: "+typeof t);throw new Error(e)}async setExpirations(t){await this.core.storage.setItem(this.storageKey,t)}async getExpirations(){return await this.core.storage.getItem(this.storageKey)}async persist(){await this.setExpirations(this.values),this.events.emit("expirer_sync")}async restore(){try{const t=await this.getExpirations();if(typeof t>"u"||!t.length)return;if(this.expirations.size){const{message:t}=ma("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=t,this.logger.debug(`Successfully Restored expirations for ${this.name}`),this.logger.trace({type:"method",method:"restore",expirations:this.values})}catch(t){this.logger.debug(`Failed to Restore expirations for ${this.name}`),this.logger.error(t)}}getExpiration(t){const e=this.expirations.get(t);if(!e){const{message:e}=ma("NO_MATCHING_KEY",`${this.name}: ${t}`);throw this.logger.warn(e),new Error(e)}return e}checkExpiry(t,e){const{expiry:r}=e;(0,H.toMiliseconds)(r)-Date.now()<=0&&this.expire(t,e)}expire(t,e){this.expirations.delete(t),this.events.emit(bf,{target:t,expiration:e})}checkExpirations(){this.core.relayer.connected&&this.expirations.forEach(((t,e)=>this.checkExpiry(e,t)))}registerEventListeners(){this.core.heartbeat.on(fc,(()=>this.checkExpirations())),this.events.on(vf,(t=>{const e=vf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,data:t}),this.persist()})),this.events.on(bf,(t=>{const e=bf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,data:t}),this.persist()})),this.events.on(wf,(t=>{const e=wf;this.logger.info(`Emitting ${e}`),this.logger.debug({type:"event",event:e,data:t}),this.persist()}))}isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}}}class rd extends Ec{constructor(t,e){super(t,e),this.projectId=t,this.logger=e,this.name=Af,this.initialized=!1,this.queue=[],this.verifyDisabled=!1,this.init=async t=>{if(this.verifyDisabled||ze()||!Fe())return;const e=this.getVerifyUrl(t?.verifyUrl);this.verifyUrl!==e&&this.removeIframe(),this.verifyUrl=e;try{await this.createIframe()}catch(t){this.logger.info(`Verify iframe failed to load: ${this.verifyUrl}`),this.logger.info(t)}if(!this.initialized){this.removeIframe(),this.verifyUrl=Ef;try{await this.createIframe()}catch(t){this.logger.info(`Verify iframe failed to load: ${this.verifyUrl}`),this.logger.info(t),this.verifyDisabled=!0}}},this.register=async t=>{this.initialized?this.sendPost(t.attestationId):(this.addToQueue(t.attestationId),await this.init())},this.resolve=async t=>{if(this.isDevEnv)return"";const e=this.getVerifyUrl(t?.verifyUrl);let r;try{r=await this.fetchAttestation(t.attestationId,e)}catch(i){this.logger.info(`failed to resolve attestation: ${t.attestationId} from url: ${e}`),this.logger.info(i),r=await this.fetchAttestation(t.attestationId,Ef)}return r},this.fetchAttestation=async(t,e)=>{this.logger.info(`resolving attestation: ${t} from url: ${e}`);const r=this.startAbortTimer(2*H.ONE_SECOND),i=await fetch(`${e}/attestation/${t}`,{signal:this.abortController.signal});return clearTimeout(r),200===i.status?await i.json():void 0},this.addToQueue=t=>{this.queue.push(t)},this.processQueue=()=>{0!==this.queue.length&&(this.queue.forEach((t=>this.sendPost(t))),this.queue=[])},this.sendPost=t=>{var e;try{if(!this.iframe)return;null==(e=this.iframe.contentWindow)||e.postMessage(t,"*"),this.logger.info(`postMessage sent: ${t} ${this.verifyUrl}`)}catch{}},this.createIframe=async()=>{let t;const e=r=>{"verify_ready"===r.data&&(this.onInit(),window.removeEventListener("message",e),t())};await Promise.race([new Promise((r=>{const i=document.getElementById(Af);if(i)return this.iframe=i,this.onInit(),r();window.addEventListener("message",e);const n=document.createElement("iframe");n.id=Af,n.src=`${this.verifyUrl}/${this.projectId}`,n.style.display="none",document.body.append(n),this.iframe=n,t=r})),new Promise(((t,r)=>setTimeout((()=>{window.removeEventListener("message",e),r("verify iframe load timeout")}),(0,H.toMiliseconds)(H.FIVE_SECONDS))))])},this.onInit=()=>{this.initialized=!0,this.processQueue()},this.removeIframe=()=>{this.iframe&&(this.iframe.remove(),this.iframe=void 0,this.initialized=!1)},this.getVerifyUrl=t=>{let e=t||_f;return If.includes(e)||(this.logger.info(`verify url: ${e}, not included in trusted list, assigning default: ${_f}`),e=_f),e},this.logger=(0,pc.U5)(e,this.name),this.verifyUrl=_f,this.abortController=new AbortController,this.isDevEnv=je()&&process.env.IS_VITEST}get context(){return(0,pc.oI)(this.logger)}startAbortTimer(t){return this.abortController=new AbortController,setTimeout((()=>this.abortController.abort()),(0,H.toMiliseconds)(t))}}class id extends Ic{constructor(t,e){super(t,e),this.projectId=t,this.logger=e,this.context="echo",this.registerDeviceToken=async t=>{const{clientId:e,token:r,notificationType:i,enableEncrypted:n=!1}=t,s=`https://echo.walletconnect.com/${this.projectId}/clients`;await Au()(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:e,type:i,token:r,always_raw:n})})},this.logger=(0,pc.U5)(e,this.context)}}var nd=Object.defineProperty,sd=Object.getOwnPropertySymbols,od=Object.prototype.hasOwnProperty,ad=Object.prototype.propertyIsEnumerable,cd=(t,e,r)=>e in t?nd(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,hd=(t,e)=>{for(var r in e||(e={}))od.call(e,r)&&cd(t,r,e[r]);if(sd)for(var r of sd(e))ad.call(e,r)&&cd(t,r,e[r]);return t};class ud extends gc{constructor(t){var e;super(t),this.protocol="wc",this.version=2,this.name=Vl,this.events=new C.EventEmitter,this.initialized=!1,this.on=(t,e)=>this.events.on(t,e),this.once=(t,e)=>this.events.once(t,e),this.off=(t,e)=>this.events.off(t,e),this.removeListener=(t,e)=>this.events.removeListener(t,e),this.projectId=t?.projectId,this.relayUrl=t?.relayUrl||Yl,this.customStoragePrefix=null!=t&&t.customStoragePrefix?`:${t.customStoragePrefix}`:"";const r=(0,pc.iP)({level:"string"==typeof t?.logger&&t.logger?t.logger:"error"}),{logger:i,chunkLoggerController:n}=(0,pc.D5)({opts:r,maxSizeInBytes:t?.maxLogBlobSizeInBytes,loggerOverride:t?.logger});this.logChunkController=n,null!=(e=this.logChunkController)&&e.downloadLogsBlobInBrowser&&(window.downloadLogsBlobInBrowser=async()=>{var t,e;null!=(t=this.logChunkController)&&t.downloadLogsBlobInBrowser&&(null==(e=this.logChunkController)||e.downloadLogsBlobInBrowser({clientId:await this.crypto.getClientId()}))}),this.logger=(0,pc.U5)(i,this.name),this.heartbeat=new dc,this.crypto=new Mf(this,this.logger,t?.keychain),this.history=new td(this,this.logger),this.expirer=new ed(this,this.logger),this.storage=null!=t&&t.storage?t.storage:new cc(hd(hd({},Jl),t?.storageOptions)),this.relayer=new Vf({core:this,logger:this.logger,relayUrl:this.relayUrl,projectId:this.projectId}),this.pairing=new Zf(this,this.logger),this.verify=new rd(this.projectId||"",this.logger),this.echoClient=new id(this.projectId||"",this.logger)}static async init(t){const e=new ud(t);await e.initialize();const r=await e.crypto.getClientId();return await e.storage.setItem("WALLETCONNECT_CLIENT_ID",r),e}get context(){return(0,pc.oI)(this.logger)}async start(){this.initialized||await this.initialize()}async getLogsBlob(){var t;return null==(t=this.logChunkController)?void 0:t.logsToBlob({clientId:await this.crypto.getClientId()})}async initialize(){this.logger.trace("Initialized");try{await this.crypto.init(),await this.history.init(),await this.expirer.init(),await this.relayer.init(),await this.heartbeat.init(),await this.pairing.init(),this.initialized=!0,this.logger.info("Core Initialization Success")}catch(t){throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`,t),this.logger.error(t.message),t}}}const ld=ud,fd="client",dd=`wc@2:${fd}:`,pd=fd,gd="WALLETCONNECT_DEEPLINK_CHOICE",md="Proposal expired",yd=H.SEVEN_DAYS,vd={wc_sessionPropose:{req:{ttl:H.FIVE_MINUTES,prompt:!0,tag:1100},res:{ttl:H.FIVE_MINUTES,prompt:!1,tag:1101}},wc_sessionSettle:{req:{ttl:H.FIVE_MINUTES,prompt:!1,tag:1102},res:{ttl:H.FIVE_MINUTES,prompt:!1,tag:1103}},wc_sessionUpdate:{req:{ttl:H.ONE_DAY,prompt:!1,tag:1104},res:{ttl:H.ONE_DAY,prompt:!1,tag:1105}},wc_sessionExtend:{req:{ttl:H.ONE_DAY,prompt:!1,tag:1106},res:{ttl:H.ONE_DAY,prompt:!1,tag:1107}},wc_sessionRequest:{req:{ttl:H.FIVE_MINUTES,prompt:!0,tag:1108},res:{ttl:H.FIVE_MINUTES,prompt:!1,tag:1109}},wc_sessionEvent:{req:{ttl:H.FIVE_MINUTES,prompt:!0,tag:1110},res:{ttl:H.FIVE_MINUTES,prompt:!1,tag:1111}},wc_sessionDelete:{req:{ttl:H.ONE_DAY,prompt:!1,tag:1112},res:{ttl:H.ONE_DAY,prompt:!1,tag:1113}},wc_sessionPing:{req:{ttl:H.ONE_DAY,prompt:!1,tag:1114},res:{ttl:H.ONE_DAY,prompt:!1,tag:1115}},wc_sessionAuthenticate:{req:{ttl:H.ONE_HOUR,prompt:!0,tag:1116},res:{ttl:H.ONE_HOUR,prompt:!1,tag:1117}}},wd={min:H.FIVE_MINUTES,max:H.SEVEN_DAYS},bd="IDLE",Ad="ACTIVE",_d=["wc_sessionPropose","wc_sessionRequest","wc_authRequest"],Ed="wc@1.5:auth:",Id=`${Ed}:PUB_KEY`;var Sd=Object.defineProperty,Md=Object.defineProperties,Pd=Object.getOwnPropertyDescriptors,xd=Object.getOwnPropertySymbols,Rd=Object.prototype.hasOwnProperty,Nd=Object.prototype.propertyIsEnumerable,Cd=(t,e,r)=>e in t?Sd(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Od=(t,e)=>{for(var r in e||(e={}))Rd.call(e,r)&&Cd(t,r,e[r]);if(xd)for(var r of xd(e))Nd.call(e,r)&&Cd(t,r,e[r]);return t},Ud=(t,e)=>Md(t,Pd(e));class Td extends Mc{constructor(t){super(t),this.name="engine",this.events=new(O()),this.initialized=!1,this.requestQueue={state:bd,queue:[]},this.sessionRequestQueue={state:bd,queue:[]},this.requestQueueDelay=H.ONE_SECOND,this.expectedPairingMethodMap=new Map,this.recentlyDeletedMap=new Map,this.recentlyDeletedLimit=200,this.init=async()=>{this.initialized||(await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.registerPairingEvents(),this.client.core.pairing.register({methods:Object.keys(vd)}),this.initialized=!0,setTimeout((()=>{this.sessionRequestQueue.queue=this.getPendingSessionRequests(),this.processSessionRequestQueue()}),(0,H.toMiliseconds)(this.requestQueueDelay)))},this.connect=async t=>{await this.isInitialized();const e=Ud(Od({},t),{requiredNamespaces:t.requiredNamespaces||{},optionalNamespaces:t.optionalNamespaces||{}});await this.isValidConnect(e);const{pairingTopic:r,requiredNamespaces:i,optionalNamespaces:n,sessionProperties:s,relays:o}=e;let a,c=r,h=!1;try{c&&(h=this.client.core.pairing.pairings.get(c).active)}catch(t){throw this.client.logger.error(`connect() -> pairing.get(${c}) failed`),t}if(!c||!h){const{topic:t,uri:e}=await this.client.core.pairing.create();c=t,a=e}if(!c){const{message:t}=ma("NO_MATCHING_KEY",`connect() pairing topic: ${c}`);throw new Error(t)}const u=await this.client.core.crypto.generateKeyPair(),l=vd.wc_sessionPropose.req.ttl||H.FIVE_MINUTES,f=Xe(l),d=Od({requiredNamespaces:i,optionalNamespaces:n,relays:o??[{protocol:"irn"}],proposer:{publicKey:u,metadata:this.client.metadata},expiryTimestamp:f},s&&{sessionProperties:s}),{reject:p,resolve:g,done:m}=Ge(l,md);this.events.once(tr("session_connect"),(async({error:t,session:e})=>{if(t)p(t);else if(e){e.self.publicKey=u;const t=Ud(Od({},e),{requiredNamespaces:d.requiredNamespaces,optionalNamespaces:d.optionalNamespaces});await this.client.session.set(e.topic,t),await this.setExpiry(e.topic,e.expiry),c&&await this.client.core.pairing.updateMetadata({topic:c,metadata:e.peer.metadata}),g(t)}}));const y=await this.sendRequest({topic:c,method:"wc_sessionPropose",params:d,throwOnFailedPublish:!0});return await this.setProposal(y,Od({id:y},d)),{uri:a,approval:m}},this.pair=async t=>{await this.isInitialized();try{return await this.client.core.pairing.pair(t)}catch(t){throw this.client.logger.error("pair() failed"),t}},this.approve=async t=>{await this.isInitialized();try{await this.isValidApprove(t)}catch(t){throw this.client.logger.error("approve() -> isValidApprove() failed"),t}const{id:e,relayProtocol:r,namespaces:i,sessionProperties:n,sessionConfig:s}=t;let o;try{o=this.client.proposal.get(e)}catch(t){throw this.client.logger.error(`approve() -> proposal.get(${e}) failed`),t}let{pairingTopic:a,proposer:c,requiredNamespaces:h,optionalNamespaces:u}=o;a=a||"";const l=await this.client.core.crypto.generateKeyPair(),f=c.publicKey,d=await this.client.core.crypto.generateSharedKey(l,f),p=Od(Od({relay:{protocol:r??"irn"},namespaces:i,pairingTopic:a,controller:{publicKey:l,metadata:this.client.metadata},expiry:Xe(yd)},n&&{sessionProperties:n}),s&&{sessionConfig:s});await this.client.core.relayer.subscribe(d);const g=Ud(Od({},p),{topic:d,requiredNamespaces:h,optionalNamespaces:u,pairingTopic:a,acknowledged:!1,self:p.controller,peer:{publicKey:c.publicKey,metadata:c.metadata},controller:l});await this.client.session.set(d,g);try{await this.sendResult({id:e,topic:a,result:{relay:{protocol:r??"irn"},responderPublicKey:l},throwOnFailedPublish:!0}),await this.sendRequest({topic:d,method:"wc_sessionSettle",params:p,throwOnFailedPublish:!0})}catch(t){throw this.client.logger.error(t),this.client.session.delete(d,ya("USER_DISCONNECTED")),await this.client.core.relayer.unsubscribe(d),t}return await this.client.core.pairing.updateMetadata({topic:a,metadata:c.metadata}),await this.client.proposal.delete(e,ya("USER_DISCONNECTED")),await this.client.core.pairing.activate({topic:a}),await this.setExpiry(d,Xe(yd)),{topic:d,acknowledged:()=>new Promise((t=>setTimeout((()=>t(this.client.session.get(d))),500)))}},this.reject=async t=>{await this.isInitialized();try{await this.isValidReject(t)}catch(t){throw this.client.logger.error("reject() -> isValidReject() failed"),t}const{id:e,reason:r}=t;let i;try{i=this.client.proposal.get(e).pairingTopic}catch(t){throw this.client.logger.error(`reject() -> proposal.get(${e}) failed`),t}i&&(await this.sendError({id:e,topic:i,error:r}),await this.client.proposal.delete(e,ya("USER_DISCONNECTED")))},this.update=async t=>{await this.isInitialized();try{await this.isValidUpdate(t)}catch(t){throw this.client.logger.error("update() -> isValidUpdate() failed"),t}const{topic:e,namespaces:r}=t,{done:i,resolve:n,reject:s}=Ge(),o=(0,pu.payloadId)(),a=(0,pu.getBigIntRpcId)().toString(),c=this.client.session.get(e).namespaces;return this.events.once(tr("session_update",o),(({error:t})=>{t?s(t):n()})),await this.client.session.update(e,{namespaces:r}),await this.sendRequest({topic:e,method:"wc_sessionUpdate",params:{namespaces:r},throwOnFailedPublish:!0,clientRpcId:o,relayRpcId:a}).catch((t=>{this.client.logger.error(t),this.client.session.update(e,{namespaces:c}),s(t)})),{acknowledged:i}},this.extend=async t=>{await this.isInitialized();try{await this.isValidExtend(t)}catch(t){throw this.client.logger.error("extend() -> isValidExtend() failed"),t}const{topic:e}=t,r=(0,pu.payloadId)(),{done:i,resolve:n,reject:s}=Ge();return this.events.once(tr("session_extend",r),(({error:t})=>{t?s(t):n()})),await this.setExpiry(e,Xe(yd)),this.sendRequest({topic:e,method:"wc_sessionExtend",params:{},clientRpcId:r,throwOnFailedPublish:!0}).catch((t=>{s(t)})),{acknowledged:i}},this.request=async t=>{await this.isInitialized();try{await this.isValidRequest(t)}catch(t){throw this.client.logger.error("request() -> isValidRequest() failed"),t}const{chainId:e,request:i,topic:n,expiry:s=vd.wc_sessionRequest.req.ttl}=t,o=this.client.session.get(n),a=(0,pu.payloadId)(),c=(0,pu.getBigIntRpcId)().toString(),{done:h,resolve:u,reject:l}=Ge(s,"Request expired. Please try again.");return this.events.once(tr("session_request",a),(({error:t,result:e})=>{t?l(t):u(e)})),await Promise.all([new Promise((async t=>{await this.sendRequest({clientRpcId:a,relayRpcId:c,topic:n,method:"wc_sessionRequest",params:{request:Ud(Od({},i),{expiryTimestamp:Xe(s)}),chainId:e},expiry:s,throwOnFailedPublish:!0}).catch((t=>l(t))),this.client.events.emit("session_request_sent",{topic:n,request:i,chainId:e,id:a}),t()})),new Promise((async t=>{var e;if(null==(e=o.sessionConfig)||!e.disableDeepLink){const t=await async function(t,e){try{return await t.getItem(e)||(Fe()?localStorage.getItem(e):void 0)}catch(t){console.error(t)}}(this.client.core.storage,gd);!async function({id:t,topic:e,wcDeepLink:i}){try{if(!i)return;const n="string"==typeof i?JSON.parse(i):i;let s=n?.href;if("string"!=typeof s)return;s.endsWith("/")&&(s=s.slice(0,-1));const o=`${s}/wc?requestId=${t}&sessionTopic=${e}`,a=Ke();a===qe.browser?o.startsWith("https://")||o.startsWith("http://")?window.open(o,"_blank","noreferrer noopener"):window.open(o,"_self","noreferrer noopener"):a===qe.reactNative&&typeof(null==r.g?void 0:r.g.Linking)<"u"&&await r.g.Linking.openURL(o)}catch(t){console.error(t)}}({id:a,topic:n,wcDeepLink:t})}t()})),h()]).then((t=>t[2]))},this.respond=async t=>{await this.isInitialized(),await this.isValidRespond(t);const{topic:e,response:r}=t,{id:i}=r;(0,pu.isJsonRpcResult)(r)?await this.sendResult({id:i,topic:e,result:r.result,throwOnFailedPublish:!0}):(0,pu.isJsonRpcError)(r)&&await this.sendError({id:i,topic:e,error:r.error}),this.cleanupAfterResponse(t)},this.ping=async t=>{await this.isInitialized();try{await this.isValidPing(t)}catch(t){throw this.client.logger.error("ping() -> isValidPing() failed"),t}const{topic:e}=t;if(this.client.session.keys.includes(e)){const t=(0,pu.payloadId)(),r=(0,pu.getBigIntRpcId)().toString(),{done:i,resolve:n,reject:s}=Ge();this.events.once(tr("session_ping",t),(({error:t})=>{t?s(t):n()})),await Promise.all([this.sendRequest({topic:e,method:"wc_sessionPing",params:{},throwOnFailedPublish:!0,clientRpcId:t,relayRpcId:r}),i()])}else this.client.core.pairing.pairings.keys.includes(e)&&await this.client.core.pairing.ping({topic:e})},this.emit=async t=>{await this.isInitialized(),await this.isValidEmit(t);const{topic:e,event:r,chainId:i}=t,n=(0,pu.getBigIntRpcId)().toString();await this.sendRequest({topic:e,method:"wc_sessionEvent",params:{event:r,chainId:i},throwOnFailedPublish:!0,relayRpcId:n})},this.disconnect=async t=>{await this.isInitialized(),await this.isValidDisconnect(t);const{topic:e}=t;if(this.client.session.keys.includes(e))await this.sendRequest({topic:e,method:"wc_sessionDelete",params:ya("USER_DISCONNECTED"),throwOnFailedPublish:!0}),await this.deleteSession({topic:e,emitEvent:!1});else{if(!this.client.core.pairing.pairings.keys.includes(e)){const{message:t}=ma("MISMATCHED_TOPIC",`Session or pairing topic not found: ${e}`);throw new Error(t)}await this.client.core.pairing.disconnect({topic:e})}},this.find=t=>(this.isInitialized(),this.client.session.getAll().filter((e=>function(t,e){const{requiredNamespaces:r}=e,i=Object.keys(t.namespaces),n=Object.keys(r);let s=!0;return!!Ve(n,i)&&(i.forEach((e=>{const{accounts:i,methods:n,events:o}=t.namespaces[e],a=ua(i),c=r[e];Ve(Ne(e,c),a)&&Ve(c.methods,n)&&Ve(c.events,o)||(s=!1)})),s)}(e,t)))),this.getPendingSessionRequests=()=>this.client.pendingRequest.getAll(),this.authenticate=async t=>{this.isInitialized(),this.isValidAuthenticate(t);const{chains:e,statement:r="",uri:i,domain:n,nonce:s,type:o,exp:a,nbf:c,methods:h=[],expiry:u}=t,l=[...t.resources||[]],{topic:f,uri:d}=await this.client.core.pairing.create({methods:["wc_sessionAuthenticate"]});this.client.logger.info({message:"Generated new pairing",pairing:{topic:f,uri:d}});const p=await this.client.core.crypto.generateKeyPair(),g=Vo(p);if(await Promise.all([this.client.auth.authKeys.set(Id,{responseTopic:g,publicKey:p}),this.client.auth.pairingTopics.set(g,{topic:g,pairingTopic:f})]),await this.client.core.relayer.subscribe(g),this.client.logger.info(`sending request to new pairing topic: ${f}`),h.length>0){const{namespace:t}=xe(e[0]);let r=function(t,e,r){const i=function(t,e,r,i={}){return r?.sort(((t,e)=>t.localeCompare(e))),{att:{[t]:Uo(e,r,i)}}}(t,e,r);return To(i)}(t,"request",h);Lo(l)&&(r=Bo(r,l.pop())),l.push(r)}const m=u&&u>vd.wc_sessionAuthenticate.req.ttl?u:vd.wc_sessionAuthenticate.req.ttl,y={authPayload:{type:o??"caip122",chains:e,statement:r,aud:i,domain:n,version:"1",nonce:s,iat:(new Date).toISOString(),exp:a,nbf:c,resources:l},requester:{publicKey:p,metadata:this.client.metadata},expiryTimestamp:Xe(m)},v={requiredNamespaces:{},optionalNamespaces:{eip155:{chains:e,methods:[...new Set(["personal_sign",...h])],events:["chainChanged","accountsChanged"]}},relays:[{protocol:"irn"}],proposer:{publicKey:p,metadata:this.client.metadata},expiryTimestamp:Xe(vd.wc_sessionPropose.req.ttl)},{done:w,resolve:b,reject:A}=Ge(m,"Request expired"),_=async({error:t,session:e})=>{if(this.events.off(tr("session_request",I),E),t)A(t);else if(e){e.self.publicKey=p,await this.client.session.set(e.topic,e),await this.setExpiry(e.topic,e.expiry),f&&await this.client.core.pairing.updateMetadata({topic:f,metadata:e.peer.metadata});const t=this.client.session.get(e.topic);await this.deleteProposal(S),b({session:t})}},E=async t=>{if(await this.deletePendingAuthRequest(I,{message:"fulfilled",code:0}),t.error){const e=ya("WC_METHOD_UNSUPPORTED","wc_sessionAuthenticate");return t.error.code===e.code?void 0:(this.events.off(tr("session_connect"),_),A(t.error.message))}await this.deleteProposal(S),this.events.off(tr("session_connect"),_);const{cacaos:e,responder:r}=t.result,i=[],n=[];for(const t of e){await No({cacao:t,projectId:this.client.core.projectId})||(this.client.logger.error(t,"Signature verification failed"),A(ya("SESSION_SETTLEMENT_FAILED","Signature verification failed")));const{p:e}=t,r=Lo(e.resources),s=[xo(e.iss)],o=Ro(e.iss);if(r){const t=ko(r),e=qo(r);i.push(...t),s.push(...e)}for(const t of s)n.push(`${t}:${o}`)}const s=await this.client.core.crypto.generateSharedKey(p,r.publicKey);let o;i.length>0&&(o={topic:s,acknowledged:!0,self:{publicKey:p,metadata:this.client.metadata},peer:r,controller:r.publicKey,expiry:Xe(yd),requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:f,namespaces:da([...new Set(i)],[...new Set(n)])},await this.client.core.relayer.subscribe(s),await this.client.session.set(s,o),o=this.client.session.get(s)),b({auths:e,session:o})},I=(0,pu.payloadId)(),S=(0,pu.payloadId)();this.events.once(tr("session_connect"),_),this.events.once(tr("session_request",I),E);try{await Promise.all([this.sendRequest({topic:f,method:"wc_sessionAuthenticate",params:y,expiry:t.expiry,throwOnFailedPublish:!0,clientRpcId:I}),this.sendRequest({topic:f,method:"wc_sessionPropose",params:v,expiry:vd.wc_sessionPropose.req.ttl,throwOnFailedPublish:!0,clientRpcId:S})])}catch(t){throw this.events.off(tr("session_connect"),_),this.events.off(tr("session_request",I),E),t}return await this.setProposal(S,Od({id:S},v)),await this.setAuthRequest(I,{request:Ud(Od({},y),{verifyContext:{}}),pairingTopic:f}),{uri:d,response:w}},this.approveSessionAuthenticate=async t=>{this.isInitialized();const{id:e,auths:r}=t,i=this.getPendingAuthRequest(e);if(!i)throw new Error(`Could not find pending auth request with id ${e}`);const n=i.requester.publicKey,s=await this.client.core.crypto.generateKeyPair(),o=Vo(n),a={type:1,receiverPublicKey:n,senderPublicKey:s},c=[],h=[];for(const t of r){if(!await No({cacao:t,projectId:this.client.core.projectId})){const t=ya("SESSION_SETTLEMENT_FAILED","Signature verification failed");throw await this.sendError({id:e,topic:o,error:t,encodeOpts:a}),new Error(t.message)}const{p:r}=t,i=Lo(r.resources),n=[xo(r.iss)],s=Ro(r.iss);if(i){const t=ko(i),e=qo(i);c.push(...t),n.push(...e)}for(const t of n)h.push(`${t}:${s}`)}const u=await this.client.core.crypto.generateSharedKey(s,n);let l;return c?.length>0&&(l={topic:u,acknowledged:!0,self:{publicKey:s,metadata:this.client.metadata},peer:{publicKey:n,metadata:i.requester.metadata},controller:n,expiry:Xe(yd),authentication:r,requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:"",namespaces:da([...new Set(c)],[...new Set(h)])},await this.client.core.relayer.subscribe(u),await this.client.session.set(u,l)),await this.sendResult({topic:o,id:e,result:{cacaos:r,responder:{publicKey:s,metadata:this.client.metadata}},encodeOpts:a,throwOnFailedPublish:!0}),await this.client.auth.requests.delete(e,{message:"fullfilled",code:0}),await this.client.core.pairing.activate({topic:i.pairingTopic}),{session:l}},this.rejectSessionAuthenticate=async t=>{await this.isInitialized();const{id:e,reason:r}=t,i=this.getPendingAuthRequest(e);if(!i)throw new Error(`Could not find pending auth request with id ${e}`);const n=i.requester.publicKey,s=await this.client.core.crypto.generateKeyPair(),o=Vo(n),a={type:1,receiverPublicKey:n,senderPublicKey:s};await this.sendError({id:e,topic:o,error:r,encodeOpts:a}),await this.client.auth.requests.delete(e,{message:"rejected",code:0}),await this.client.proposal.delete(e,ya("USER_DISCONNECTED"))},this.formatAuthMessage=t=>{this.isInitialized();const{request:e,iss:r}=t;return Co(e,r)},this.cleanupDuplicatePairings=async t=>{if(t.pairingTopic)try{const e=this.client.core.pairing.pairings.get(t.pairingTopic),r=this.client.core.pairing.pairings.getAll().filter((r=>{var i,n;return(null==(i=r.peerMetadata)?void 0:i.url)&&(null==(n=r.peerMetadata)?void 0:n.url)===t.peer.metadata.url&&r.topic&&r.topic!==e.topic}));if(0===r.length)return;this.client.logger.info(`Cleaning up ${r.length} duplicate pairing(s)`),await Promise.all(r.map((t=>this.client.core.pairing.disconnect({topic:t.topic})))),this.client.logger.info("Duplicate pairings clean up finished")}catch(t){this.client.logger.error(t)}},this.deleteSession=async t=>{const{topic:e,expirerHasDeleted:r=!1,emitEvent:i=!0,id:n=0}=t,{self:s}=this.client.session.get(e);await this.client.core.relayer.unsubscribe(e),await this.client.session.delete(e,ya("USER_DISCONNECTED")),this.addToRecentlyDeleted(e,"session"),this.client.core.crypto.keychain.has(s.publicKey)&&await this.client.core.crypto.deleteKeyPair(s.publicKey),this.client.core.crypto.keychain.has(e)&&await this.client.core.crypto.deleteSymKey(e),r||this.client.core.expirer.del(e),this.client.core.storage.removeItem(gd).catch((t=>this.client.logger.warn(t))),this.getPendingSessionRequests().forEach((t=>{t.topic===e&&this.deletePendingSessionRequest(t.id,ya("USER_DISCONNECTED"))})),i&&this.client.events.emit("session_delete",{id:n,topic:e})},this.deleteProposal=async(t,e)=>{await Promise.all([this.client.proposal.delete(t,ya("USER_DISCONNECTED")),e?Promise.resolve():this.client.core.expirer.del(t)]),this.addToRecentlyDeleted(t,"proposal")},this.deletePendingSessionRequest=async(t,e,r=!1)=>{await Promise.all([this.client.pendingRequest.delete(t,e),r?Promise.resolve():this.client.core.expirer.del(t)]),this.addToRecentlyDeleted(t,"request"),this.sessionRequestQueue.queue=this.sessionRequestQueue.queue.filter((e=>e.id!==t)),r&&(this.sessionRequestQueue.state=bd,this.client.events.emit("session_request_expire",{id:t}))},this.deletePendingAuthRequest=async(t,e,r=!1)=>{await Promise.all([this.client.auth.requests.delete(t,e),r?Promise.resolve():this.client.core.expirer.del(t)])},this.setExpiry=async(t,e)=>{this.client.session.keys.includes(t)&&(this.client.core.expirer.set(t,e),await this.client.session.update(t,{expiry:e}))},this.setProposal=async(t,e)=>{this.client.core.expirer.set(t,Xe(vd.wc_sessionPropose.req.ttl)),await this.client.proposal.set(t,e)},this.setAuthRequest=async(t,e)=>{const{request:r,pairingTopic:i}=e;this.client.core.expirer.set(t,r.expiryTimestamp),await this.client.auth.requests.set(t,{authPayload:r.authPayload,requester:r.requester,expiryTimestamp:r.expiryTimestamp,id:t,pairingTopic:i,verifyContext:r.verifyContext})},this.setPendingSessionRequest=async t=>{const{id:e,topic:r,params:i,verifyContext:n}=t,s=i.request.expiryTimestamp||Xe(vd.wc_sessionRequest.req.ttl);this.client.core.expirer.set(e,s),await this.client.pendingRequest.set(e,{id:e,topic:r,params:i,verifyContext:n})},this.sendRequest=async t=>{const{topic:e,method:r,params:i,expiry:n,relayRpcId:s,clientRpcId:o,throwOnFailedPublish:a}=t,c=(0,pu.formatJsonRpcRequest)(r,i,o);if(Fe()&&_d.includes(r)){const t=$o(JSON.stringify(c));this.client.core.verify.register({attestationId:t})}let h;try{h=await this.client.core.crypto.encode(e,c)}catch(t){throw await this.cleanup(),this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${e} failed`),t}const u=vd[r].req;return n&&(u.ttl=n),s&&(u.id=s),this.client.core.history.set(e,c),a?(u.internal=Ud(Od({},u.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(e,h,u)):this.client.core.relayer.publish(e,h,u).catch((t=>this.client.logger.error(t))),c.id},this.sendResult=async t=>{const{id:e,topic:r,result:i,throwOnFailedPublish:n,encodeOpts:s}=t,o=(0,pu.formatJsonRpcResult)(e,i);let a,c;try{a=await this.client.core.crypto.encode(r,o,s)}catch(t){throw await this.cleanup(),this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${r} failed`),t}try{c=await this.client.core.history.get(r,e)}catch(t){throw this.client.logger.error(`sendResult() -> history.get(${r}, ${e}) failed`),t}const h=vd[c.request.method].res;n?(h.internal=Ud(Od({},h.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(r,a,h)):this.client.core.relayer.publish(r,a,h).catch((t=>this.client.logger.error(t))),await this.client.core.history.resolve(o)},this.sendError=async t=>{const{id:e,topic:r,error:i,encodeOpts:n}=t,s=(0,pu.formatJsonRpcError)(e,i);let o,a;try{o=await this.client.core.crypto.encode(r,s,n)}catch(t){throw await this.cleanup(),this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${r} failed`),t}try{a=await this.client.core.history.get(r,e)}catch(t){throw this.client.logger.error(`sendError() -> history.get(${r}, ${e}) failed`),t}const c=vd[a.request.method].res;this.client.core.relayer.publish(r,o,c),await this.client.core.history.resolve(s)},this.cleanup=async()=>{const t=[],e=[];this.client.session.getAll().forEach((e=>{let r=!1;Ze(e.expiry)&&(r=!0),this.client.core.crypto.keychain.has(e.topic)||(r=!0),r&&t.push(e.topic)})),this.client.proposal.getAll().forEach((t=>{Ze(t.expiryTimestamp)&&e.push(t.id)})),await Promise.all([...t.map((t=>this.deleteSession({topic:t}))),...e.map((t=>this.deleteProposal(t)))])},this.onRelayEventRequest=async t=>{this.requestQueue.queue.push(t),await this.processRequestsQueue()},this.processRequestsQueue=async()=>{if(this.requestQueue.state!==Ad){for(this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`);this.requestQueue.queue.length>0;){this.requestQueue.state=Ad;const t=this.requestQueue.queue.shift();if(t)try{this.processRequest(t),await new Promise((t=>setTimeout(t,300)))}catch(t){this.client.logger.warn(t)}}this.requestQueue.state=bd}else this.client.logger.info("Request queue already active, skipping...")},this.processRequest=t=>{const{topic:e,payload:r}=t,i=r.method;if(!this.shouldIgnorePairingRequest({topic:e,requestMethod:i}))switch(i){case"wc_sessionPropose":return this.onSessionProposeRequest(e,r);case"wc_sessionSettle":return this.onSessionSettleRequest(e,r);case"wc_sessionUpdate":return this.onSessionUpdateRequest(e,r);case"wc_sessionExtend":return this.onSessionExtendRequest(e,r);case"wc_sessionPing":return this.onSessionPingRequest(e,r);case"wc_sessionDelete":return this.onSessionDeleteRequest(e,r);case"wc_sessionRequest":return this.onSessionRequest(e,r);case"wc_sessionEvent":return this.onSessionEventRequest(e,r);case"wc_sessionAuthenticate":return this.onSessionAuthenticateRequest(e,r);default:return this.client.logger.info(`Unsupported request method ${i}`)}},this.onRelayEventResponse=async t=>{const{topic:e,payload:r}=t,i=(await this.client.core.history.get(e,r.id)).request.method;switch(i){case"wc_sessionPropose":return this.onSessionProposeResponse(e,r);case"wc_sessionSettle":return this.onSessionSettleResponse(e,r);case"wc_sessionUpdate":return this.onSessionUpdateResponse(e,r);case"wc_sessionExtend":return this.onSessionExtendResponse(e,r);case"wc_sessionPing":return this.onSessionPingResponse(e,r);case"wc_sessionRequest":return this.onSessionRequestResponse(e,r);case"wc_sessionAuthenticate":return this.onSessionAuthenticateResponse(e,r);default:return this.client.logger.info(`Unsupported response method ${i}`)}},this.onRelayEventUnknownPayload=t=>{const{topic:e}=t,{message:r}=ma("MISSING_OR_INVALID",`Decoded payload on topic ${e} is not identifiable as a JSON-RPC request or a response.`);throw new Error(r)},this.shouldIgnorePairingRequest=t=>{const{topic:e,requestMethod:r}=t,i=this.expectedPairingMethodMap.get(e);return!(!i||i.includes(r)||!(i.includes("wc_sessionAuthenticate")&&this.client.events.listenerCount("session_authenticate")>0))},this.onSessionProposeRequest=async(t,e)=>{const{params:r,id:i}=e;try{this.isValidConnect(Od({},e.params));const n=r.expiryTimestamp||Xe(vd.wc_sessionPropose.req.ttl),s=Od({id:i,pairingTopic:t,expiryTimestamp:n},r);await this.setProposal(i,s);const o=$o(JSON.stringify(e)),a=await this.getVerifyContext(o,s.proposer.metadata);this.client.events.emit("session_proposal",{id:i,params:s,verifyContext:a})}catch(e){await this.sendError({id:i,topic:t,error:e}),this.client.logger.error(e)}},this.onSessionProposeResponse=async(t,e)=>{const{id:r}=e;if((0,pu.isJsonRpcResult)(e)){const{result:i}=e;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",result:i});const n=this.client.proposal.get(r);this.client.logger.trace({type:"method",method:"onSessionProposeResponse",proposal:n});const s=n.proposer.publicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",selfPublicKey:s});const o=i.responderPublicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",peerPublicKey:o});const a=await this.client.core.crypto.generateSharedKey(s,o);this.client.logger.trace({type:"method",method:"onSessionProposeResponse",sessionTopic:a});const c=await this.client.core.relayer.subscribe(a);this.client.logger.trace({type:"method",method:"onSessionProposeResponse",subscriptionId:c}),await this.client.core.pairing.activate({topic:t})}else if((0,pu.isJsonRpcError)(e)){await this.client.proposal.delete(r,ya("USER_DISCONNECTED"));const t=tr("session_connect");if(0===this.events.listenerCount(t))throw new Error(`emitting ${t} without any listeners, 954`);this.events.emit(tr("session_connect"),{error:e.error})}},this.onSessionSettleRequest=async(t,e)=>{const{id:r,params:i}=e;try{this.isValidSessionSettleRequest(i);const{relay:r,controller:n,expiry:s,namespaces:o,sessionProperties:a,pairingTopic:c,sessionConfig:h}=e.params,u=Od(Od({topic:t,relay:r,expiry:s,namespaces:o,acknowledged:!0,pairingTopic:c,requiredNamespaces:{},optionalNamespaces:{},controller:n.publicKey,self:{publicKey:"",metadata:this.client.metadata},peer:{publicKey:n.publicKey,metadata:n.metadata}},a&&{sessionProperties:a}),h&&{sessionConfig:h});await this.sendResult({id:e.id,topic:t,result:!0,throwOnFailedPublish:!0});const l=tr("session_connect");if(0===this.events.listenerCount(l))throw new Error(`emitting ${l} without any listeners 997`);this.events.emit(tr("session_connect"),{session:u}),this.cleanupDuplicatePairings(u)}catch(e){await this.sendError({id:r,topic:t,error:e}),this.client.logger.error(e)}},this.onSessionSettleResponse=async(t,e)=>{const{id:r}=e;(0,pu.isJsonRpcResult)(e)?(await this.client.session.update(t,{acknowledged:!0}),this.events.emit(tr("session_approve",r),{})):(0,pu.isJsonRpcError)(e)&&(await this.client.session.delete(t,ya("USER_DISCONNECTED")),this.events.emit(tr("session_approve",r),{error:e.error}))},this.onSessionUpdateRequest=async(t,e)=>{const{params:r,id:i}=e;try{const e=`${t}_session_update`,n=Ta.get(e);if(n&&this.isRequestOutOfSync(n,i))return this.client.logger.info(`Discarding out of sync request - ${i}`),void this.sendError({id:i,topic:t,error:ya("INVALID_UPDATE_REQUEST")});this.isValidUpdate(Od({topic:t},r));try{Ta.set(e,i),await this.client.session.update(t,{namespaces:r.namespaces}),await this.sendResult({id:i,topic:t,result:!0,throwOnFailedPublish:!0})}catch(t){throw Ta.delete(e),t}this.client.events.emit("session_update",{id:i,topic:t,params:r})}catch(e){await this.sendError({id:i,topic:t,error:e}),this.client.logger.error(e)}},this.isRequestOutOfSync=(t,e)=>parseInt(e.toString().slice(0,-3))<=parseInt(t.toString().slice(0,-3)),this.onSessionUpdateResponse=(t,e)=>{const{id:r}=e,i=tr("session_update",r);if(0===this.events.listenerCount(i))throw new Error(`emitting ${i} without any listeners`);(0,pu.isJsonRpcResult)(e)?this.events.emit(tr("session_update",r),{}):(0,pu.isJsonRpcError)(e)&&this.events.emit(tr("session_update",r),{error:e.error})},this.onSessionExtendRequest=async(t,e)=>{const{id:r}=e;try{this.isValidExtend({topic:t}),await this.setExpiry(t,Xe(yd)),await this.sendResult({id:r,topic:t,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_extend",{id:r,topic:t})}catch(e){await this.sendError({id:r,topic:t,error:e}),this.client.logger.error(e)}},this.onSessionExtendResponse=(t,e)=>{const{id:r}=e,i=tr("session_extend",r);if(0===this.events.listenerCount(i))throw new Error(`emitting ${i} without any listeners`);(0,pu.isJsonRpcResult)(e)?this.events.emit(tr("session_extend",r),{}):(0,pu.isJsonRpcError)(e)&&this.events.emit(tr("session_extend",r),{error:e.error})},this.onSessionPingRequest=async(t,e)=>{const{id:r}=e;try{this.isValidPing({topic:t}),await this.sendResult({id:r,topic:t,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_ping",{id:r,topic:t})}catch(e){await this.sendError({id:r,topic:t,error:e}),this.client.logger.error(e)}},this.onSessionPingResponse=(t,e)=>{const{id:r}=e,i=tr("session_ping",r);if(0===this.events.listenerCount(i))throw new Error(`emitting ${i} without any listeners`);setTimeout((()=>{(0,pu.isJsonRpcResult)(e)?this.events.emit(tr("session_ping",r),{}):(0,pu.isJsonRpcError)(e)&&this.events.emit(tr("session_ping",r),{error:e.error})}),500)},this.onSessionDeleteRequest=async(t,e)=>{const{id:r}=e;try{this.isValidDisconnect({topic:t,reason:e.params}),await Promise.all([new Promise((e=>{this.client.core.relayer.once(rf,(async()=>{e(await this.deleteSession({topic:t,id:r}))}))})),this.sendResult({id:r,topic:t,result:!0,throwOnFailedPublish:!0}),this.cleanupPendingSentRequestsForTopic({topic:t,error:ya("USER_DISCONNECTED")})])}catch(t){this.client.logger.error(t)}},this.onSessionRequest=async(t,e)=>{var r;const{id:i,params:n}=e;try{await this.isValidRequest(Od({topic:t},n));const e=$o(JSON.stringify((0,pu.formatJsonRpcRequest)("wc_sessionRequest",n,i))),s=this.client.session.get(t),o={id:i,topic:t,params:n,verifyContext:await this.getVerifyContext(e,s.peer.metadata)};await this.setPendingSessionRequest(o),null!=(r=this.client.signConfig)&&r.disableRequestQueue?this.emitSessionRequest(o):(this.addSessionRequestToSessionRequestQueue(o),this.processSessionRequestQueue())}catch(e){await this.sendError({id:i,topic:t,error:e}),this.client.logger.error(e)}},this.onSessionRequestResponse=(t,e)=>{const{id:r}=e,i=tr("session_request",r);if(0===this.events.listenerCount(i))throw new Error(`emitting ${i} without any listeners`);(0,pu.isJsonRpcResult)(e)?this.events.emit(tr("session_request",r),{result:e.result}):(0,pu.isJsonRpcError)(e)&&this.events.emit(tr("session_request",r),{error:e.error})},this.onSessionEventRequest=async(t,e)=>{const{id:r,params:i}=e;try{const e=`${t}_session_event_${i.event.name}`,n=Ta.get(e);if(n&&this.isRequestOutOfSync(n,r))return void this.client.logger.info(`Discarding out of sync request - ${r}`);this.isValidEmit(Od({topic:t},i)),this.client.events.emit("session_event",{id:r,topic:t,params:i}),Ta.set(e,r)}catch(e){await this.sendError({id:r,topic:t,error:e}),this.client.logger.error(e)}},this.onSessionAuthenticateResponse=(t,e)=>{const{id:r}=e;this.client.logger.trace({type:"method",method:"onSessionAuthenticateResponse",topic:t,payload:e}),(0,pu.isJsonRpcResult)(e)?this.events.emit(tr("session_request",r),{result:e.result}):(0,pu.isJsonRpcError)(e)&&this.events.emit(tr("session_request",r),{error:e.error})},this.onSessionAuthenticateRequest=async(t,e)=>{const{requester:r,authPayload:i,expiryTimestamp:n}=e.params,s=$o(JSON.stringify(e)),o=await this.getVerifyContext(s,this.client.metadata),a={requester:r,pairingTopic:t,id:e.id,authPayload:i,verifyContext:o,expiryTimestamp:n};await this.setAuthRequest(e.id,{request:a,pairingTopic:t}),this.client.events.emit("session_authenticate",{topic:t,params:e.params,id:e.id})},this.addSessionRequestToSessionRequestQueue=t=>{this.sessionRequestQueue.queue.push(t)},this.cleanupAfterResponse=t=>{this.deletePendingSessionRequest(t.response.id,{message:"fulfilled",code:0}),setTimeout((()=>{this.sessionRequestQueue.state=bd,this.processSessionRequestQueue()}),(0,H.toMiliseconds)(this.requestQueueDelay))},this.cleanupPendingSentRequestsForTopic=({topic:t,error:e})=>{const r=this.client.core.history.pending;r.length>0&&r.filter((e=>e.topic===t&&"wc_sessionRequest"===e.request.method)).forEach((t=>{const r=tr("session_request",t.request.id);if(0===this.events.listenerCount(r))throw new Error(`emitting ${r} without any listeners`);this.events.emit(tr("session_request",t.request.id),{error:e})}))},this.processSessionRequestQueue=()=>{if(this.sessionRequestQueue.state===Ad)return void this.client.logger.info("session request queue is already active.");const t=this.sessionRequestQueue.queue[0];if(t)try{this.sessionRequestQueue.state=Ad,this.emitSessionRequest(t)}catch(t){this.client.logger.error(t)}else this.client.logger.info("session request queue is empty.")},this.emitSessionRequest=t=>{this.client.events.emit("session_request",t)},this.onPairingCreated=t=>{if(t.methods&&this.expectedPairingMethodMap.set(t.topic,t.methods),t.active)return;const e=this.client.proposal.getAll().find((e=>e.pairingTopic===t.topic));e&&this.onSessionProposeRequest(t.topic,(0,pu.formatJsonRpcRequest)("wc_sessionPropose",{requiredNamespaces:e.requiredNamespaces,optionalNamespaces:e.optionalNamespaces,relays:e.relays,proposer:e.proposer,sessionProperties:e.sessionProperties},e.id))},this.isValidConnect=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`connect() params: ${JSON.stringify(t)}`);throw new Error(e)}const{pairingTopic:e,requiredNamespaces:r,optionalNamespaces:i,sessionProperties:n,relays:s}=t;if(ba(e)||await this.isValidPairingTopic(e),!function(t){let e=!1;return t?t&&va(t)&&t.length&&t.forEach((t=>{e=Pa(t)})):e=!0,e}(s)){const{message:t}=ma("MISSING_OR_INVALID",`connect() relays: ${s}`);throw new Error(t)}!ba(r)&&0!==wa(r)&&this.validateNamespaces(r,"requiredNamespaces"),!ba(i)&&0!==wa(i)&&this.validateNamespaces(i,"optionalNamespaces"),ba(n)||this.validateSessionProps(n,"sessionProperties")},this.validateNamespaces=(t,e)=>{const r=function(t,e,r){let i=null;if(t&&wa(t)){const n=Sa(t,e);n&&(i=n);const s=function(t,e,r){let i=null;return Object.entries(t).forEach((([t,n])=>{if(i)return;const s=function(t,e,r){let i=null;return va(e)&&e.length?e.forEach((t=>{i||Ea(t)||(i=ya("UNSUPPORTED_CHAINS",`${r}, chain ${t} should be a string and conform to "namespace:chainId" format`))})):Ea(t)||(i=ya("UNSUPPORTED_CHAINS",`${r}, chains must be defined as "namespace:chainId" e.g. "eip155:1": {...} in the namespace key OR as an array of CAIP-2 chainIds e.g. eip155: { chains: ["eip155:1", "eip155:5"] }`)),i}(t,Ne(t,n),`${e} ${r}`);s&&(i=s)})),i}(t,e,r);s&&(i=s)}else i=ma("MISSING_OR_INVALID",`${e}, ${r} should be an object with data`);return i}(t,"connect()",e);if(r)throw new Error(r.message)},this.isValidApprove=async t=>{if(!xa(t))throw new Error(ma("MISSING_OR_INVALID",`approve() params: ${t}`).message);const{id:e,namespaces:r,relayProtocol:i,sessionProperties:n}=t;this.checkRecentlyDeleted(e),await this.isValidProposalId(e);const s=this.client.proposal.get(e),o=Ma(r,"approve()");if(o)throw new Error(o.message);const a=Na(s.requiredNamespaces,r,"approve()");if(a)throw new Error(a.message);if(!Aa(i,!0)){const{message:t}=ma("MISSING_OR_INVALID",`approve() relayProtocol: ${i}`);throw new Error(t)}ba(n)||this.validateSessionProps(n,"sessionProperties")},this.isValidReject=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`reject() params: ${t}`);throw new Error(e)}const{id:e,reason:r}=t;if(this.checkRecentlyDeleted(e),await this.isValidProposalId(e),!function(t){return!!(t&&"object"==typeof t&&t.code&&_a(t.code,!1)&&t.message&&Aa(t.message,!1))}(r)){const{message:t}=ma("MISSING_OR_INVALID",`reject() reason: ${JSON.stringify(r)}`);throw new Error(t)}},this.isValidSessionSettleRequest=t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`onSessionSettleRequest() params: ${t}`);throw new Error(e)}const{relay:e,controller:r,namespaces:i,expiry:n}=t;if(!Pa(e)){const{message:t}=ma("MISSING_OR_INVALID","onSessionSettleRequest() relay protocol should be a string");throw new Error(t)}const s=function(t){let e=null;return Aa(t?.publicKey,!1)||(e=ma("MISSING_OR_INVALID","onSessionSettleRequest() controller public key should be a string")),e}(r);if(s)throw new Error(s.message);const o=Ma(i,"onSessionSettleRequest()");if(o)throw new Error(o.message);if(Ze(n)){const{message:t}=ma("EXPIRED","onSessionSettleRequest()");throw new Error(t)}},this.isValidUpdate=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`update() params: ${t}`);throw new Error(e)}const{topic:e,namespaces:r}=t;this.checkRecentlyDeleted(e),await this.isValidSessionTopic(e);const i=this.client.session.get(e),n=Ma(r,"update()");if(n)throw new Error(n.message);const s=Na(i.requiredNamespaces,r,"update()");if(s)throw new Error(s.message)},this.isValidExtend=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`extend() params: ${t}`);throw new Error(e)}const{topic:e}=t;this.checkRecentlyDeleted(e),await this.isValidSessionTopic(e)},this.isValidRequest=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`request() params: ${t}`);throw new Error(e)}const{topic:e,request:r,chainId:i,expiry:n}=t;this.checkRecentlyDeleted(e),await this.isValidSessionTopic(e);const{namespaces:s}=this.client.session.get(e);if(!Ra(s,i)){const{message:t}=ma("MISSING_OR_INVALID",`request() chainId: ${i}`);throw new Error(t)}if(!function(t){return!(ba(t)||!Aa(t.method,!1))}(r)){const{message:t}=ma("MISSING_OR_INVALID",`request() ${JSON.stringify(r)}`);throw new Error(t)}if(!function(t,e,r){return!!Aa(r,!1)&&function(t,e){const r=[];return Object.values(t).forEach((t=>{ua(t.accounts).includes(e)&&r.push(...t.methods)})),r}(t,e).includes(r)}(s,i,r.method)){const{message:t}=ma("MISSING_OR_INVALID",`request() method: ${r.method}`);throw new Error(t)}if(n&&!function(t,e){return _a(t,!1)&&t<=e.max&&t>=e.min}(n,wd)){const{message:t}=ma("MISSING_OR_INVALID",`request() expiry: ${n}. Expiry must be a number (in seconds) between ${wd.min} and ${wd.max}`);throw new Error(t)}},this.isValidRespond=async t=>{var e;if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`respond() params: ${t}`);throw new Error(e)}const{topic:r,response:i}=t;try{await this.isValidSessionTopic(r)}catch(r){throw null!=(e=t?.response)&&e.id&&this.cleanupAfterResponse(t),r}if(!function(t){return!(ba(t)||ba(t.result)&&ba(t.error)||!_a(t.id,!1)||!Aa(t.jsonrpc,!1))}(i)){const{message:t}=ma("MISSING_OR_INVALID",`respond() response: ${JSON.stringify(i)}`);throw new Error(t)}},this.isValidPing=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`ping() params: ${t}`);throw new Error(e)}const{topic:e}=t;await this.isValidSessionOrPairingTopic(e)},this.isValidEmit=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`emit() params: ${t}`);throw new Error(e)}const{topic:e,event:r,chainId:i}=t;await this.isValidSessionTopic(e);const{namespaces:n}=this.client.session.get(e);if(!Ra(n,i)){const{message:t}=ma("MISSING_OR_INVALID",`emit() chainId: ${i}`);throw new Error(t)}if(!function(t){return!(ba(t)||!Aa(t.name,!1))}(r)){const{message:t}=ma("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(r)}`);throw new Error(t)}if(!function(t,e,r){return!!Aa(r,!1)&&function(t,e){const r=[];return Object.values(t).forEach((t=>{ua(t.accounts).includes(e)&&r.push(...t.events)})),r}(t,e).includes(r)}(n,i,r.name)){const{message:t}=ma("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(r)}`);throw new Error(t)}},this.isValidDisconnect=async t=>{if(!xa(t)){const{message:e}=ma("MISSING_OR_INVALID",`disconnect() params: ${t}`);throw new Error(e)}const{topic:e}=t;await this.isValidSessionOrPairingTopic(e)},this.isValidAuthenticate=t=>{const{chains:e,uri:r,domain:i,nonce:n}=t;if(!Array.isArray(e)||0===e.length)throw new Error("chains is required and must be a non-empty array");if(!Aa(r,!1))throw new Error("uri is required parameter");if(!Aa(i,!1))throw new Error("domain is required parameter");if(!Aa(n,!1))throw new Error("nonce is required parameter");if([...new Set(e.map((t=>xe(t).namespace)))].length>1)throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");const{namespace:s}=xe(e[0]);if("eip155"!==s)throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.")},this.getVerifyContext=async(t,e)=>{const r={verified:{verifyUrl:e.verifyUrl||_f,validation:"UNKNOWN",origin:e.url||""}};try{const i=await this.client.core.verify.resolve({attestationId:t,verifyUrl:e.verifyUrl});i&&(r.verified.origin=i.origin,r.verified.isScam=i.isScam,r.verified.validation=i.origin===new URL(e.url).origin?"VALID":"INVALID")}catch(t){this.client.logger.info(t)}return this.client.logger.info(`Verify context: ${JSON.stringify(r)}`),r},this.validateSessionProps=(t,e)=>{Object.values(t).forEach((t=>{if(!Aa(t,!1)){const{message:r}=ma("MISSING_OR_INVALID",`${e} must be in Record<string, string> format. Received: ${JSON.stringify(t)}`);throw new Error(r)}}))},this.getPendingAuthRequest=t=>{const e=this.client.auth.requests.get(t);return"object"==typeof e?e:void 0},this.addToRecentlyDeleted=(t,e)=>{if(this.recentlyDeletedMap.set(t,e),this.recentlyDeletedMap.size>=this.recentlyDeletedLimit){let t=0;const e=this.recentlyDeletedLimit/2;for(const r of this.recentlyDeletedMap.keys()){if(t++>=e)break;this.recentlyDeletedMap.delete(r)}}},this.checkRecentlyDeleted=t=>{const e=this.recentlyDeletedMap.get(t);if(e){const{message:r}=ma("MISSING_OR_INVALID",`Record was recently deleted - ${e}: ${t}`);throw new Error(r)}}}async isInitialized(){if(!this.initialized){const{message:t}=ma("NOT_INITIALIZED",this.name);throw new Error(t)}await this.client.core.relayer.confirmOnlineStateOrThrow()}registerRelayerEvents(){this.client.core.relayer.on(Zl,(async t=>{const{topic:e,message:r}=t,{publicKey:i}=this.client.auth.authKeys.keys.includes(Id)?this.client.auth.authKeys.get(Id):{responseTopic:void 0,publicKey:void 0},n=await this.client.core.crypto.decode(e,r,{receiverPublicKey:i});try{(0,pu.isJsonRpcRequest)(n)?(this.client.core.history.set(e,n),this.onRelayEventRequest({topic:e,payload:n})):(0,pu.isJsonRpcResponse)(n)?(await this.client.core.history.resolve(n),await this.onRelayEventResponse({topic:e,payload:n}),this.client.core.history.delete(e,n.id)):this.onRelayEventUnknownPayload({topic:e,payload:n})}catch(t){this.client.logger.error(t)}}))}registerExpirerEvents(){this.client.core.expirer.on(bf,(async t=>{const{topic:e,id:r}=Ye(t.target);return r&&this.client.pendingRequest.keys.includes(r)?await this.deletePendingSessionRequest(r,ma("EXPIRED"),!0):r&&this.client.auth.requests.keys.includes(r)?await this.deletePendingAuthRequest(r,ma("EXPIRED"),!0):void(e?this.client.session.keys.includes(e)&&(await this.deleteSession({topic:e,expirerHasDeleted:!0}),this.client.events.emit("session_expire",{topic:e})):r&&(await this.deleteProposal(r,!0),this.client.events.emit("proposal_expire",{id:r})))}))}registerPairingEvents(){this.client.core.pairing.events.on(df,(t=>this.onPairingCreated(t))),this.client.core.pairing.events.on(pf,(t=>{this.addToRecentlyDeleted(t.topic,"pairing")}))}isValidPairingTopic(t){if(!Aa(t,!1)){const{message:e}=ma("MISSING_OR_INVALID",`pairing topic should be a string: ${t}`);throw new Error(e)}if(!this.client.core.pairing.pairings.keys.includes(t)){const{message:e}=ma("NO_MATCHING_KEY",`pairing topic doesn't exist: ${t}`);throw new Error(e)}if(Ze(this.client.core.pairing.pairings.get(t).expiry)){const{message:e}=ma("EXPIRED",`pairing topic: ${t}`);throw new Error(e)}}async isValidSessionTopic(t){if(!Aa(t,!1)){const{message:e}=ma("MISSING_OR_INVALID",`session topic should be a string: ${t}`);throw new Error(e)}if(this.checkRecentlyDeleted(t),!this.client.session.keys.includes(t)){const{message:e}=ma("NO_MATCHING_KEY",`session topic doesn't exist: ${t}`);throw new Error(e)}if(Ze(this.client.session.get(t).expiry)){await this.deleteSession({topic:t});const{message:e}=ma("EXPIRED",`session topic: ${t}`);throw new Error(e)}if(!this.client.core.crypto.keychain.has(t)){const{message:e}=ma("MISSING_OR_INVALID",`session topic does not exist in keychain: ${t}`);throw await this.deleteSession({topic:t}),new Error(e)}}async isValidSessionOrPairingTopic(t){if(this.checkRecentlyDeleted(t),this.client.session.keys.includes(t))await this.isValidSessionTopic(t);else{if(!this.client.core.pairing.pairings.keys.includes(t)){if(Aa(t,!1)){const{message:e}=ma("NO_MATCHING_KEY",`session or pairing topic doesn't exist: ${t}`);throw new Error(e)}{const{message:e}=ma("MISSING_OR_INVALID",`session or pairing topic should be a string: ${t}`);throw new Error(e)}}this.isValidPairingTopic(t)}}async isValidProposalId(t){if(!function(t){return"number"==typeof t}(t)){const{message:e}=ma("MISSING_OR_INVALID",`proposal id should be a number: ${t}`);throw new Error(e)}if(!this.client.proposal.keys.includes(t)){const{message:e}=ma("NO_MATCHING_KEY",`proposal id doesn't exist: ${t}`);throw new Error(e)}if(Ze(this.client.proposal.get(t).expiryTimestamp)){await this.deleteProposal(t);const{message:e}=ma("EXPIRED",`proposal id: ${t}`);throw new Error(e)}}}class Dd extends Xf{constructor(t,e){super(t,e,"proposal",dd),this.core=t,this.logger=e}}class Bd extends Xf{constructor(t,e){super(t,e,"session",dd),this.core=t,this.logger=e}}class kd extends Xf{constructor(t,e){super(t,e,"request",dd,(t=>t.id)),this.core=t,this.logger=e}}class qd extends Xf{constructor(t,e){super(t,e,"authKeys",Ed,(()=>Id)),this.core=t,this.logger=e}}class Ld extends Xf{constructor(t,e){super(t,e,"pairingTopics",Ed),this.core=t,this.logger=e}}class jd extends Xf{constructor(t,e){super(t,e,"requests",Ed,(t=>t.id)),this.core=t,this.logger=e}}class zd{constructor(t,e){this.core=t,this.logger=e,this.authKeys=new qd(this.core,this.logger),this.pairingTopics=new Ld(this.core,this.logger),this.requests=new jd(this.core,this.logger)}async init(){await this.authKeys.init(),await this.pairingTopics.init(),await this.requests.init()}}class Fd extends Sc{constructor(t){super(t),this.protocol="wc",this.version=2,this.name=pd,this.events=new C.EventEmitter,this.on=(t,e)=>this.events.on(t,e),this.once=(t,e)=>this.events.once(t,e),this.off=(t,e)=>this.events.off(t,e),this.removeListener=(t,e)=>this.events.removeListener(t,e),this.removeAllListeners=t=>this.events.removeAllListeners(t),this.connect=async t=>{try{return await this.engine.connect(t)}catch(t){throw this.logger.error(t.message),t}},this.pair=async t=>{try{return await this.engine.pair(t)}catch(t){throw this.logger.error(t.message),t}},this.approve=async t=>{try{return await this.engine.approve(t)}catch(t){throw this.logger.error(t.message),t}},this.reject=async t=>{try{return await this.engine.reject(t)}catch(t){throw this.logger.error(t.message),t}},this.update=async t=>{try{return await this.engine.update(t)}catch(t){throw this.logger.error(t.message),t}},this.extend=async t=>{try{return await this.engine.extend(t)}catch(t){throw this.logger.error(t.message),t}},this.request=async t=>{try{return await this.engine.request(t)}catch(t){throw this.logger.error(t.message),t}},this.respond=async t=>{try{return await this.engine.respond(t)}catch(t){throw this.logger.error(t.message),t}},this.ping=async t=>{try{return await this.engine.ping(t)}catch(t){throw this.logger.error(t.message),t}},this.emit=async t=>{try{return await this.engine.emit(t)}catch(t){throw this.logger.error(t.message),t}},this.disconnect=async t=>{try{return await this.engine.disconnect(t)}catch(t){throw this.logger.error(t.message),t}},this.find=t=>{try{return this.engine.find(t)}catch(t){throw this.logger.error(t.message),t}},this.getPendingSessionRequests=()=>{try{return this.engine.getPendingSessionRequests()}catch(t){throw this.logger.error(t.message),t}},this.authenticate=async t=>{try{return await this.engine.authenticate(t)}catch(t){throw this.logger.error(t.message),t}},this.formatAuthMessage=t=>{try{return this.engine.formatAuthMessage(t)}catch(t){throw this.logger.error(t.message),t}},this.approveSessionAuthenticate=async t=>{try{return await this.engine.approveSessionAuthenticate(t)}catch(t){throw this.logger.error(t.message),t}},this.rejectSessionAuthenticate=async t=>{try{return await this.engine.rejectSessionAuthenticate(t)}catch(t){throw this.logger.error(t.message),t}},this.name=t?.name||pd,this.metadata=t?.metadata||(0,$.g)()||{name:"",description:"",url:"",icons:[""]},this.signConfig=t?.signConfig;const e=typeof t?.logger<"u"&&"string"!=typeof t?.logger?t.logger:(0,pc.h6)((0,pc.iP)({level:t?.logger||"error"}));this.core=t?.core||new ld(t),this.logger=(0,pc.U5)(e,this.name),this.session=new Bd(this.core,this.logger),this.proposal=new Dd(this.core,this.logger),this.pendingRequest=new kd(this.core,this.logger),this.engine=new Td(this),this.auth=new zd(this.core,this.logger)}static async init(t){const e=new Fd(t);return await e.initialize(),e}get context(){return(0,pc.oI)(this.logger)}get pairing(){return this.core.pairing.pairings}async initialize(){this.logger.trace("Initialized");try{await this.core.start(),await this.session.init(),await this.proposal.init(),await this.pendingRequest.init(),await this.engine.init(),await this.auth.init(),this.core.verify.init({verifyUrl:this.metadata.verifyUrl}),this.logger.info("SignClient Initialization Success")}catch(t){throw this.logger.info("SignClient Initialization Failure"),this.logger.error(t.message),t}}}var Kd=r(36647);const Hd="error",Vd="wc@2:universal_provider:",$d="default_chain_changed";var Jd=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof r.g<"u"?r.g:typeof self<"u"?self:{},Gd={exports:{}};!function(t,e){(function(){var r,i="Expected a function",n="__lodash_hash_undefined__",s="__lodash_placeholder__",o=32,a=128,c=1/0,h=9007199254740991,u=NaN,l=**********,f=l-1,d=l>>>1,p=[["ary",a],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",o],["partialRight",64],["rearg",256]],g="[object Arguments]",m="[object Array]",y="[object Boolean]",v="[object Date]",w="[object Error]",b="[object Function]",A="[object GeneratorFunction]",_="[object Map]",E="[object Number]",I="[object Object]",S="[object Promise]",M="[object RegExp]",P="[object Set]",x="[object String]",R="[object Symbol]",N="[object WeakMap]",C="[object ArrayBuffer]",O="[object DataView]",U="[object Float32Array]",T="[object Float64Array]",D="[object Int8Array]",B="[object Int16Array]",k="[object Int32Array]",q="[object Uint8Array]",L="[object Uint8ClampedArray]",j="[object Uint16Array]",z="[object Uint32Array]",F=/\b__p \+= '';/g,K=/\b(__p \+=) '' \+/g,H=/(__e\(.*?\)|\b__t\)) \+\n'';/g,V=/&(?:amp|lt|gt|quot|#39);/g,$=/[&<>"']/g,J=RegExp(V.source),G=RegExp($.source),W=/<%-([\s\S]+?)%>/g,Q=/<%([\s\S]+?)%>/g,Y=/<%=([\s\S]+?)%>/g,X=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Z=/^\w*$/,tt=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,et=/[\\^$.*+?()[\]{}|]/g,rt=RegExp(et.source),it=/^\s+/,nt=/\s/,st=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,ot=/\{\n\/\* \[wrapped with (.+)\] \*/,at=/,? & /,ct=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,ht=/[()=,{}\[\]\/\s]/,ut=/\\(\\)?/g,lt=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,ft=/\w*$/,dt=/^[-+]0x[0-9a-f]+$/i,pt=/^0b[01]+$/i,gt=/^\[object .+?Constructor\]$/,mt=/^0o[0-7]+$/i,yt=/^(?:0|[1-9]\d*)$/,vt=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,wt=/($^)/,bt=/['\n\r\u2028\u2029\\]/g,At="\\ud800-\\udfff",_t="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Et="\\u2700-\\u27bf",It="a-z\\xdf-\\xf6\\xf8-\\xff",St="A-Z\\xc0-\\xd6\\xd8-\\xde",Mt="\\ufe0e\\ufe0f",Pt="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",xt="["+At+"]",Rt="["+Pt+"]",Nt="["+_t+"]",Ct="\\d+",Ot="["+Et+"]",Ut="["+It+"]",Tt="[^"+At+Pt+Ct+Et+It+St+"]",Dt="\\ud83c[\\udffb-\\udfff]",Bt="[^"+At+"]",kt="(?:\\ud83c[\\udde6-\\uddff]){2}",qt="[\\ud800-\\udbff][\\udc00-\\udfff]",Lt="["+St+"]",jt="\\u200d",zt="(?:"+Ut+"|"+Tt+")",Ft="(?:"+Lt+"|"+Tt+")",Kt="(?:['’](?:d|ll|m|re|s|t|ve))?",Ht="(?:['’](?:D|LL|M|RE|S|T|VE))?",Vt="(?:"+Nt+"|"+Dt+")?",$t="["+Mt+"]?",Jt=$t+Vt+"(?:"+jt+"(?:"+[Bt,kt,qt].join("|")+")"+$t+Vt+")*",Gt="(?:"+[Ot,kt,qt].join("|")+")"+Jt,Wt="(?:"+[Bt+Nt+"?",Nt,kt,qt,xt].join("|")+")",Qt=RegExp("['’]","g"),Yt=RegExp(Nt,"g"),Xt=RegExp(Dt+"(?="+Dt+")|"+Wt+Jt,"g"),Zt=RegExp([Lt+"?"+Ut+"+"+Kt+"(?="+[Rt,Lt,"$"].join("|")+")",Ft+"+"+Ht+"(?="+[Rt,Lt+zt,"$"].join("|")+")",Lt+"?"+zt+"+"+Kt,Lt+"+"+Ht,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Ct,Gt].join("|"),"g"),te=RegExp("["+jt+At+_t+Mt+"]"),ee=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,re=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],ie=-1,ne={};ne[U]=ne[T]=ne[D]=ne[B]=ne[k]=ne[q]=ne[L]=ne[j]=ne[z]=!0,ne[g]=ne[m]=ne[C]=ne[y]=ne[O]=ne[v]=ne[w]=ne[b]=ne[_]=ne[E]=ne[I]=ne[M]=ne[P]=ne[x]=ne[N]=!1;var se={};se[g]=se[m]=se[C]=se[O]=se[y]=se[v]=se[U]=se[T]=se[D]=se[B]=se[k]=se[_]=se[E]=se[I]=se[M]=se[P]=se[x]=se[R]=se[q]=se[L]=se[j]=se[z]=!0,se[w]=se[b]=se[N]=!1;var oe={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ae=parseFloat,ce=parseInt,he="object"==typeof Jd&&Jd&&Jd.Object===Object&&Jd,ue="object"==typeof self&&self&&self.Object===Object&&self,le=he||ue||Function("return this")(),fe=e&&!e.nodeType&&e,de=fe&&t&&!t.nodeType&&t,pe=de&&de.exports===fe,ge=pe&&he.process,me=function(){try{return de&&de.require&&de.require("util").types||ge&&ge.binding&&ge.binding("util")}catch{}}(),ye=me&&me.isArrayBuffer,ve=me&&me.isDate,we=me&&me.isMap,be=me&&me.isRegExp,Ae=me&&me.isSet,_e=me&&me.isTypedArray;function Ee(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}function Ie(t,e,r,i){for(var n=-1,s=null==t?0:t.length;++n<s;){var o=t[n];e(i,o,r(o),t)}return i}function Se(t,e){for(var r=-1,i=null==t?0:t.length;++r<i&&!1!==e(t[r],r,t););return t}function Me(t,e){for(var r=null==t?0:t.length;r--&&!1!==e(t[r],r,t););return t}function Pe(t,e){for(var r=-1,i=null==t?0:t.length;++r<i;)if(!e(t[r],r,t))return!1;return!0}function xe(t,e){for(var r=-1,i=null==t?0:t.length,n=0,s=[];++r<i;){var o=t[r];e(o,r,t)&&(s[n++]=o)}return s}function Re(t,e){return!(null==t||!t.length)&&Le(t,e,0)>-1}function Ne(t,e,r){for(var i=-1,n=null==t?0:t.length;++i<n;)if(r(e,t[i]))return!0;return!1}function Ce(t,e){for(var r=-1,i=null==t?0:t.length,n=Array(i);++r<i;)n[r]=e(t[r],r,t);return n}function Oe(t,e){for(var r=-1,i=e.length,n=t.length;++r<i;)t[n+r]=e[r];return t}function Ue(t,e,r,i){var n=-1,s=null==t?0:t.length;for(i&&s&&(r=t[++n]);++n<s;)r=e(r,t[n],n,t);return r}function Te(t,e,r,i){var n=null==t?0:t.length;for(i&&n&&(r=t[--n]);n--;)r=e(r,t[n],n,t);return r}function De(t,e){for(var r=-1,i=null==t?0:t.length;++r<i;)if(e(t[r],r,t))return!0;return!1}var Be=Ke("length");function ke(t,e,r){var i;return r(t,(function(t,r,n){if(e(t,r,n))return i=r,!1})),i}function qe(t,e,r,i){for(var n=t.length,s=r+(i?1:-1);i?s--:++s<n;)if(e(t[s],s,t))return s;return-1}function Le(t,e,r){return e==e?function(t,e,r){for(var i=r-1,n=t.length;++i<n;)if(t[i]===e)return i;return-1}(t,e,r):qe(t,ze,r)}function je(t,e,r,i){for(var n=r-1,s=t.length;++n<s;)if(i(t[n],e))return n;return-1}function ze(t){return t!=t}function Fe(t,e){var r=null==t?0:t.length;return r?$e(t,e)/r:u}function Ke(t){return function(e){return null==e?r:e[t]}}function He(t){return function(e){return null==t?r:t[e]}}function Ve(t,e,r,i,n){return n(t,(function(t,n,s){r=i?(i=!1,t):e(r,t,n,s)})),r}function $e(t,e){for(var i,n=-1,s=t.length;++n<s;){var o=e(t[n]);o!==r&&(i=i===r?o:i+o)}return i}function Je(t,e){for(var r=-1,i=Array(t);++r<t;)i[r]=e(r);return i}function Ge(t){return t&&t.slice(0,ur(t)+1).replace(it,"")}function We(t){return function(e){return t(e)}}function Qe(t,e){return Ce(e,(function(e){return t[e]}))}function Ye(t,e){return t.has(e)}function Xe(t,e){for(var r=-1,i=t.length;++r<i&&Le(e,t[r],0)>-1;);return r}function Ze(t,e){for(var r=t.length;r--&&Le(e,t[r],0)>-1;);return r}var tr=He({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),er=He({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function rr(t){return"\\"+oe[t]}function ir(t){return te.test(t)}function nr(t){var e=-1,r=Array(t.size);return t.forEach((function(t,i){r[++e]=[i,t]})),r}function sr(t,e){return function(r){return t(e(r))}}function or(t,e){for(var r=-1,i=t.length,n=0,o=[];++r<i;){var a=t[r];(a===e||a===s)&&(t[r]=s,o[n++]=r)}return o}function ar(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}function cr(t){return ir(t)?function(t){for(var e=Xt.lastIndex=0;Xt.test(t);)++e;return e}(t):Be(t)}function hr(t){return ir(t)?function(t){return t.match(Xt)||[]}(t):function(t){return t.split("")}(t)}function ur(t){for(var e=t.length;e--&&nt.test(t.charAt(e)););return e}var lr=He({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),fr=function t(e){var nt=(e=null==e?le:fr.defaults(le.Object(),e,fr.pick(le,re))).Array,At=e.Date,_t=e.Error,Et=e.Function,It=e.Math,St=e.Object,Mt=e.RegExp,Pt=e.String,xt=e.TypeError,Rt=nt.prototype,Nt=Et.prototype,Ct=St.prototype,Ot=e["__core-js_shared__"],Ut=Nt.toString,Tt=Ct.hasOwnProperty,Dt=0,Bt=function(){var t=/[^.]+$/.exec(Ot&&Ot.keys&&Ot.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),kt=Ct.toString,qt=Ut.call(St),Lt=le._,jt=Mt("^"+Ut.call(Tt).replace(et,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),zt=pe?e.Buffer:r,Ft=e.Symbol,Kt=e.Uint8Array,Ht=zt?zt.allocUnsafe:r,Vt=sr(St.getPrototypeOf,St),$t=St.create,Jt=Ct.propertyIsEnumerable,Gt=Rt.splice,Wt=Ft?Ft.isConcatSpreadable:r,Xt=Ft?Ft.iterator:r,te=Ft?Ft.toStringTag:r,oe=function(){try{var t=cs(St,"defineProperty");return t({},"",{}),t}catch{}}(),he=e.clearTimeout!==le.clearTimeout&&e.clearTimeout,ue=At&&At.now!==le.Date.now&&At.now,fe=e.setTimeout!==le.setTimeout&&e.setTimeout,de=It.ceil,ge=It.floor,me=St.getOwnPropertySymbols,Be=zt?zt.isBuffer:r,He=e.isFinite,dr=Rt.join,pr=sr(St.keys,St),gr=It.max,mr=It.min,yr=At.now,vr=e.parseInt,wr=It.random,br=Rt.reverse,Ar=cs(e,"DataView"),_r=cs(e,"Map"),Er=cs(e,"Promise"),Ir=cs(e,"Set"),Sr=cs(e,"WeakMap"),Mr=cs(St,"create"),Pr=Sr&&new Sr,xr={},Rr=Ts(Ar),Nr=Ts(_r),Cr=Ts(Er),Or=Ts(Ir),Ur=Ts(Sr),Tr=Ft?Ft.prototype:r,Dr=Tr?Tr.valueOf:r,Br=Tr?Tr.toString:r;function kr(t){if(Xo(t)&&!zo(t)&&!(t instanceof zr)){if(t instanceof jr)return t;if(Tt.call(t,"__wrapped__"))return Ds(t)}return new jr(t)}var qr=function(){function t(){}return function(e){if(!Yo(e))return{};if($t)return $t(e);t.prototype=e;var i=new t;return t.prototype=r,i}}();function Lr(){}function jr(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=r}function zr(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=l,this.__views__=[]}function Fr(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function Kr(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function Hr(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function Vr(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new Hr;++e<r;)this.add(t[e])}function $r(t){var e=this.__data__=new Kr(t);this.size=e.size}function Jr(t,e){var r=zo(t),i=!r&&jo(t),n=!r&&!i&&Vo(t),s=!r&&!i&&!n&&oa(t),o=r||i||n||s,a=o?Je(t.length,Pt):[],c=a.length;for(var h in t)(e||Tt.call(t,h))&&(!o||!("length"==h||n&&("offset"==h||"parent"==h)||s&&("buffer"==h||"byteLength"==h||"byteOffset"==h)||gs(h,c)))&&a.push(h);return a}function Gr(t){var e=t.length;return e?t[Hi(0,e-1)]:r}function Wr(t,e){return Cs(Mn(t),ni(e,0,t.length))}function Qr(t){return Cs(Mn(t))}function Yr(t,e,i){(i!==r&&!ko(t[e],i)||i===r&&!(e in t))&&ri(t,e,i)}function Xr(t,e,i){var n=t[e];(!Tt.call(t,e)||!ko(n,i)||i===r&&!(e in t))&&ri(t,e,i)}function Zr(t,e){for(var r=t.length;r--;)if(ko(t[r][0],e))return r;return-1}function ti(t,e,r,i){return hi(t,(function(t,n,s){e(i,t,r(t),s)})),i}function ei(t,e){return t&&Pn(e,xa(e),t)}function ri(t,e,r){"__proto__"==e&&oe?oe(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}function ii(t,e){for(var i=-1,n=e.length,s=nt(n),o=null==t;++i<n;)s[i]=o?r:Ea(t,e[i]);return s}function ni(t,e,i){return t==t&&(i!==r&&(t=t<=i?t:i),e!==r&&(t=t>=e?t:e)),t}function si(t,e,i,n,s,o){var a,c=1&e,h=2&e,u=4&e;if(i&&(a=s?i(t,n,s,o):i(t)),a!==r)return a;if(!Yo(t))return t;var l=zo(t);if(l){if(a=function(t){var e=t.length,r=new t.constructor(e);return e&&"string"==typeof t[0]&&Tt.call(t,"index")&&(r.index=t.index,r.input=t.input),r}(t),!c)return Mn(t,a)}else{var f=ls(t),d=f==b||f==A;if(Vo(t))return bn(t,c);if(f==I||f==g||d&&!s){if(a=h||d?{}:ds(t),!c)return h?function(t,e){return Pn(t,us(t),e)}(t,function(t,e){return t&&Pn(e,Ra(e),t)}(a,t)):function(t,e){return Pn(t,hs(t),e)}(t,ei(a,t))}else{if(!se[f])return s?t:{};a=function(t,e,r){var i=t.constructor;switch(e){case C:return An(t);case y:case v:return new i(+t);case O:return function(t,e){var r=e?An(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}(t,r);case U:case T:case D:case B:case k:case q:case L:case j:case z:return _n(t,r);case _:return new i;case E:case x:return new i(t);case M:return function(t){var e=new t.constructor(t.source,ft.exec(t));return e.lastIndex=t.lastIndex,e}(t);case P:return new i;case R:return function(t){return Dr?St(Dr.call(t)):{}}(t)}}(t,f,c)}}o||(o=new $r);var p=o.get(t);if(p)return p;o.set(t,a),ia(t)?t.forEach((function(r){a.add(si(r,e,i,r,t,o))})):Zo(t)&&t.forEach((function(r,n){a.set(n,si(r,e,i,n,t,o))}));var m=l?r:(u?h?es:ts:h?Ra:xa)(t);return Se(m||t,(function(r,n){m&&(r=t[n=r]),Xr(a,n,si(r,e,i,n,t,o))})),a}function oi(t,e,i){var n=i.length;if(null==t)return!n;for(t=St(t);n--;){var s=i[n],o=e[s],a=t[s];if(a===r&&!(s in t)||!o(a))return!1}return!0}function ai(t,e,n){if("function"!=typeof t)throw new xt(i);return Ps((function(){t.apply(r,n)}),e)}function ci(t,e,r,i){var n=-1,s=Re,o=!0,a=t.length,c=[],h=e.length;if(!a)return c;r&&(e=Ce(e,We(r))),i?(s=Ne,o=!1):e.length>=200&&(s=Ye,o=!1,e=new Vr(e));t:for(;++n<a;){var u=t[n],l=null==r?u:r(u);if(u=i||0!==u?u:0,o&&l==l){for(var f=h;f--;)if(e[f]===l)continue t;c.push(u)}else s(e,l,i)||c.push(u)}return c}kr.templateSettings={escape:W,evaluate:Q,interpolate:Y,variable:"",imports:{_:kr}},kr.prototype=Lr.prototype,kr.prototype.constructor=kr,jr.prototype=qr(Lr.prototype),jr.prototype.constructor=jr,zr.prototype=qr(Lr.prototype),zr.prototype.constructor=zr,Fr.prototype.clear=function(){this.__data__=Mr?Mr(null):{},this.size=0},Fr.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Fr.prototype.get=function(t){var e=this.__data__;if(Mr){var i=e[t];return i===n?r:i}return Tt.call(e,t)?e[t]:r},Fr.prototype.has=function(t){var e=this.__data__;return Mr?e[t]!==r:Tt.call(e,t)},Fr.prototype.set=function(t,e){var i=this.__data__;return this.size+=this.has(t)?0:1,i[t]=Mr&&e===r?n:e,this},Kr.prototype.clear=function(){this.__data__=[],this.size=0},Kr.prototype.delete=function(t){var e=this.__data__,r=Zr(e,t);return!(r<0||(r==e.length-1?e.pop():Gt.call(e,r,1),--this.size,0))},Kr.prototype.get=function(t){var e=this.__data__,i=Zr(e,t);return i<0?r:e[i][1]},Kr.prototype.has=function(t){return Zr(this.__data__,t)>-1},Kr.prototype.set=function(t,e){var r=this.__data__,i=Zr(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this},Hr.prototype.clear=function(){this.size=0,this.__data__={hash:new Fr,map:new(_r||Kr),string:new Fr}},Hr.prototype.delete=function(t){var e=os(this,t).delete(t);return this.size-=e?1:0,e},Hr.prototype.get=function(t){return os(this,t).get(t)},Hr.prototype.has=function(t){return os(this,t).has(t)},Hr.prototype.set=function(t,e){var r=os(this,t),i=r.size;return r.set(t,e),this.size+=r.size==i?0:1,this},Vr.prototype.add=Vr.prototype.push=function(t){return this.__data__.set(t,n),this},Vr.prototype.has=function(t){return this.__data__.has(t)},$r.prototype.clear=function(){this.__data__=new Kr,this.size=0},$r.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},$r.prototype.get=function(t){return this.__data__.get(t)},$r.prototype.has=function(t){return this.__data__.has(t)},$r.prototype.set=function(t,e){var r=this.__data__;if(r instanceof Kr){var i=r.__data__;if(!_r||i.length<199)return i.push([t,e]),this.size=++r.size,this;r=this.__data__=new Hr(i)}return r.set(t,e),this.size=r.size,this};var hi=Nn(yi),ui=Nn(vi,!0);function li(t,e){var r=!0;return hi(t,(function(t,i,n){return r=!!e(t,i,n)})),r}function fi(t,e,i){for(var n=-1,s=t.length;++n<s;){var o=t[n],a=e(o);if(null!=a&&(c===r?a==a&&!sa(a):i(a,c)))var c=a,h=o}return h}function di(t,e){var r=[];return hi(t,(function(t,i,n){e(t,i,n)&&r.push(t)})),r}function pi(t,e,r,i,n){var s=-1,o=t.length;for(r||(r=ps),n||(n=[]);++s<o;){var a=t[s];e>0&&r(a)?e>1?pi(a,e-1,r,i,n):Oe(n,a):i||(n[n.length]=a)}return n}var gi=Cn(),mi=Cn(!0);function yi(t,e){return t&&gi(t,e,xa)}function vi(t,e){return t&&mi(t,e,xa)}function wi(t,e){return xe(e,(function(e){return Go(t[e])}))}function bi(t,e){for(var i=0,n=(e=mn(e,t)).length;null!=t&&i<n;)t=t[Us(e[i++])];return i&&i==n?t:r}function Ai(t,e,r){var i=e(t);return zo(t)?i:Oe(i,r(t))}function _i(t){return null==t?t===r?"[object Undefined]":"[object Null]":te&&te in St(t)?function(t){var e=Tt.call(t,te),i=t[te];try{t[te]=r;var n=!0}catch{}var s=kt.call(t);return n&&(e?t[te]=i:delete t[te]),s}(t):function(t){return kt.call(t)}(t)}function Ei(t,e){return t>e}function Ii(t,e){return null!=t&&Tt.call(t,e)}function Si(t,e){return null!=t&&e in St(t)}function Mi(t,e,i){for(var n=i?Ne:Re,s=t[0].length,o=t.length,a=o,c=nt(o),h=1/0,u=[];a--;){var l=t[a];a&&e&&(l=Ce(l,We(e))),h=mr(l.length,h),c[a]=!i&&(e||s>=120&&l.length>=120)?new Vr(a&&l):r}l=t[0];var f=-1,d=c[0];t:for(;++f<s&&u.length<h;){var p=l[f],g=e?e(p):p;if(p=i||0!==p?p:0,!(d?Ye(d,g):n(u,g,i))){for(a=o;--a;){var m=c[a];if(!(m?Ye(m,g):n(t[a],g,i)))continue t}d&&d.push(g),u.push(p)}}return u}function Pi(t,e,i){var n=null==(t=Is(t,e=mn(e,t)))?t:t[Us($s(e))];return null==n?r:Ee(n,t,i)}function xi(t){return Xo(t)&&_i(t)==g}function Ri(t,e,i,n,s){return t===e||(null==t||null==e||!Xo(t)&&!Xo(e)?t!=t&&e!=e:function(t,e,i,n,s,o){var a=zo(t),c=zo(e),h=a?m:ls(t),u=c?m:ls(e),l=(h=h==g?I:h)==I,f=(u=u==g?I:u)==I,d=h==u;if(d&&Vo(t)){if(!Vo(e))return!1;a=!0,l=!1}if(d&&!l)return o||(o=new $r),a||oa(t)?Xn(t,e,i,n,s,o):function(t,e,r,i,n,s,o){switch(r){case O:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case C:return!(t.byteLength!=e.byteLength||!s(new Kt(t),new Kt(e)));case y:case v:case E:return ko(+t,+e);case w:return t.name==e.name&&t.message==e.message;case M:case x:return t==e+"";case _:var a=nr;case P:var c=1&i;if(a||(a=ar),t.size!=e.size&&!c)return!1;var h=o.get(t);if(h)return h==e;i|=2,o.set(t,e);var u=Xn(a(t),a(e),i,n,s,o);return o.delete(t),u;case R:if(Dr)return Dr.call(t)==Dr.call(e)}return!1}(t,e,h,i,n,s,o);if(!(1&i)){var p=l&&Tt.call(t,"__wrapped__"),b=f&&Tt.call(e,"__wrapped__");if(p||b){var A=p?t.value():t,S=b?e.value():e;return o||(o=new $r),s(A,S,i,n,o)}}return!!d&&(o||(o=new $r),function(t,e,i,n,s,o){var a=1&i,c=ts(t),h=c.length;if(h!=ts(e).length&&!a)return!1;for(var u=h;u--;){var l=c[u];if(!(a?l in e:Tt.call(e,l)))return!1}var f=o.get(t),d=o.get(e);if(f&&d)return f==e&&d==t;var p=!0;o.set(t,e),o.set(e,t);for(var g=a;++u<h;){var m=t[l=c[u]],y=e[l];if(n)var v=a?n(y,m,l,e,t,o):n(m,y,l,t,e,o);if(!(v===r?m===y||s(m,y,i,n,o):v)){p=!1;break}g||(g="constructor"==l)}if(p&&!g){var w=t.constructor,b=e.constructor;w!=b&&"constructor"in t&&"constructor"in e&&!("function"==typeof w&&w instanceof w&&"function"==typeof b&&b instanceof b)&&(p=!1)}return o.delete(t),o.delete(e),p}(t,e,i,n,s,o))}(t,e,i,n,Ri,s))}function Ni(t,e,i,n){var s=i.length,o=s,a=!n;if(null==t)return!o;for(t=St(t);s--;){var c=i[s];if(a&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++s<o;){var h=(c=i[s])[0],u=t[h],l=c[1];if(a&&c[2]){if(u===r&&!(h in t))return!1}else{var f=new $r;if(n)var d=n(u,l,h,t,e,f);if(!(d===r?Ri(l,u,3,n,f):d))return!1}}return!0}function Ci(t){return!(!Yo(t)||function(t){return!!Bt&&Bt in t}(t))&&(Go(t)?jt:gt).test(Ts(t))}function Oi(t){return"function"==typeof t?t:null==t?tc:"object"==typeof t?zo(t)?ki(t[0],t[1]):Bi(t):hc(t)}function Ui(t){if(!bs(t))return pr(t);var e=[];for(var r in St(t))Tt.call(t,r)&&"constructor"!=r&&e.push(r);return e}function Ti(t,e){return t<e}function Di(t,e){var r=-1,i=Ko(t)?nt(t.length):[];return hi(t,(function(t,n,s){i[++r]=e(t,n,s)})),i}function Bi(t){var e=as(t);return 1==e.length&&e[0][2]?_s(e[0][0],e[0][1]):function(r){return r===t||Ni(r,t,e)}}function ki(t,e){return ys(t)&&As(e)?_s(Us(t),e):function(i){var n=Ea(i,t);return n===r&&n===e?Ia(i,t):Ri(e,n,3)}}function qi(t,e,i,n,s){t!==e&&gi(e,(function(o,a){if(s||(s=new $r),Yo(o))!function(t,e,i,n,s,o,a){var c=Ss(t,i),h=Ss(e,i),u=a.get(h);if(u)Yr(t,i,u);else{var l=o?o(c,h,i+"",t,e,a):r,f=l===r;if(f){var d=zo(h),p=!d&&Vo(h),g=!d&&!p&&oa(h);l=h,d||p||g?zo(c)?l=c:Ho(c)?l=Mn(c):p?(f=!1,l=bn(h,!0)):g?(f=!1,l=_n(h,!0)):l=[]:ea(h)||jo(h)?(l=c,jo(c)?l=pa(c):(!Yo(c)||Go(c))&&(l=ds(h))):f=!1}f&&(a.set(h,l),s(l,h,n,o,a),a.delete(h)),Yr(t,i,l)}}(t,e,a,i,qi,n,s);else{var c=n?n(Ss(t,a),o,a+"",t,e,s):r;c===r&&(c=o),Yr(t,a,c)}}),Ra)}function Li(t,e){var i=t.length;if(i)return gs(e+=e<0?i:0,i)?t[e]:r}function ji(t,e,r){e=e.length?Ce(e,(function(t){return zo(t)?function(e){return bi(e,1===t.length?t[0]:t)}:t})):[tc];var i=-1;e=Ce(e,We(ss()));var n=Di(t,(function(t,r,n){var s=Ce(e,(function(e){return e(t)}));return{criteria:s,index:++i,value:t}}));return function(t){var e=t.length;for(t.sort((function(t,e){return function(t,e,r){for(var i=-1,n=t.criteria,s=e.criteria,o=n.length,a=r.length;++i<o;){var c=En(n[i],s[i]);if(c)return i>=a?c:c*("desc"==r[i]?-1:1)}return t.index-e.index}(t,e,r)}));e--;)t[e]=t[e].value;return t}(n)}function zi(t,e,r){for(var i=-1,n=e.length,s={};++i<n;){var o=e[i],a=bi(t,o);r(a,o)&&Wi(s,mn(o,t),a)}return s}function Fi(t,e,r,i){var n=i?je:Le,s=-1,o=e.length,a=t;for(t===e&&(e=Mn(e)),r&&(a=Ce(t,We(r)));++s<o;)for(var c=0,h=e[s],u=r?r(h):h;(c=n(a,u,c,i))>-1;)a!==t&&Gt.call(a,c,1),Gt.call(t,c,1);return t}function Ki(t,e){for(var r=t?e.length:0,i=r-1;r--;){var n=e[r];if(r==i||n!==s){var s=n;gs(n)?Gt.call(t,n,1):cn(t,n)}}return t}function Hi(t,e){return t+ge(wr()*(e-t+1))}function Vi(t,e){var r="";if(!t||e<1||e>h)return r;do{e%2&&(r+=t),(e=ge(e/2))&&(t+=t)}while(e);return r}function $i(t,e){return xs(Es(t,e,tc),t+"")}function Ji(t){return Gr(ka(t))}function Gi(t,e){var r=ka(t);return Cs(r,ni(e,0,r.length))}function Wi(t,e,i,n){if(!Yo(t))return t;for(var s=-1,o=(e=mn(e,t)).length,a=o-1,c=t;null!=c&&++s<o;){var h=Us(e[s]),u=i;if("__proto__"===h||"constructor"===h||"prototype"===h)return t;if(s!=a){var l=c[h];(u=n?n(l,h,c):r)===r&&(u=Yo(l)?l:gs(e[s+1])?[]:{})}Xr(c,h,u),c=c[h]}return t}var Qi=Pr?function(t,e){return Pr.set(t,e),t}:tc,Yi=oe?function(t,e){return oe(t,"toString",{configurable:!0,enumerable:!1,value:Ya(e),writable:!0})}:tc;function Xi(t){return Cs(ka(t))}function Zi(t,e,r){var i=-1,n=t.length;e<0&&(e=-e>n?0:n+e),(r=r>n?n:r)<0&&(r+=n),n=e>r?0:r-e>>>0,e>>>=0;for(var s=nt(n);++i<n;)s[i]=t[i+e];return s}function tn(t,e){var r;return hi(t,(function(t,i,n){return!(r=e(t,i,n))})),!!r}function en(t,e,r){var i=0,n=null==t?i:t.length;if("number"==typeof e&&e==e&&n<=d){for(;i<n;){var s=i+n>>>1,o=t[s];null!==o&&!sa(o)&&(r?o<=e:o<e)?i=s+1:n=s}return n}return rn(t,e,tc,r)}function rn(t,e,i,n){var s=0,o=null==t?0:t.length;if(0===o)return 0;for(var a=(e=i(e))!=e,c=null===e,h=sa(e),u=e===r;s<o;){var l=ge((s+o)/2),d=i(t[l]),p=d!==r,g=null===d,m=d==d,y=sa(d);if(a)var v=n||m;else v=u?m&&(n||p):c?m&&p&&(n||!g):h?m&&p&&!g&&(n||!y):!g&&!y&&(n?d<=e:d<e);v?s=l+1:o=l}return mr(o,f)}function nn(t,e){for(var r=-1,i=t.length,n=0,s=[];++r<i;){var o=t[r],a=e?e(o):o;if(!r||!ko(a,c)){var c=a;s[n++]=0===o?0:o}}return s}function sn(t){return"number"==typeof t?t:sa(t)?u:+t}function on(t){if("string"==typeof t)return t;if(zo(t))return Ce(t,on)+"";if(sa(t))return Br?Br.call(t):"";var e=t+"";return"0"==e&&1/t==-c?"-0":e}function an(t,e,r){var i=-1,n=Re,s=t.length,o=!0,a=[],c=a;if(r)o=!1,n=Ne;else if(s>=200){var h=e?null:$n(t);if(h)return ar(h);o=!1,n=Ye,c=new Vr}else c=e?[]:a;t:for(;++i<s;){var u=t[i],l=e?e(u):u;if(u=r||0!==u?u:0,o&&l==l){for(var f=c.length;f--;)if(c[f]===l)continue t;e&&c.push(l),a.push(u)}else n(c,l,r)||(c!==a&&c.push(l),a.push(u))}return a}function cn(t,e){return null==(t=Is(t,e=mn(e,t)))||delete t[Us($s(e))]}function hn(t,e,r,i){return Wi(t,e,r(bi(t,e)),i)}function un(t,e,r,i){for(var n=t.length,s=i?n:-1;(i?s--:++s<n)&&e(t[s],s,t););return r?Zi(t,i?0:s,i?s+1:n):Zi(t,i?s+1:0,i?n:s)}function ln(t,e){var r=t;return r instanceof zr&&(r=r.value()),Ue(e,(function(t,e){return e.func.apply(e.thisArg,Oe([t],e.args))}),r)}function fn(t,e,r){var i=t.length;if(i<2)return i?an(t[0]):[];for(var n=-1,s=nt(i);++n<i;)for(var o=t[n],a=-1;++a<i;)a!=n&&(s[n]=ci(s[n]||o,t[a],e,r));return an(pi(s,1),e,r)}function dn(t,e,i){for(var n=-1,s=t.length,o=e.length,a={};++n<s;){var c=n<o?e[n]:r;i(a,t[n],c)}return a}function pn(t){return Ho(t)?t:[]}function gn(t){return"function"==typeof t?t:tc}function mn(t,e){return zo(t)?t:ys(t,e)?[t]:Os(ga(t))}var yn=$i;function vn(t,e,i){var n=t.length;return i=i===r?n:i,!e&&i>=n?t:Zi(t,e,i)}var wn=he||function(t){return le.clearTimeout(t)};function bn(t,e){if(e)return t.slice();var r=t.length,i=Ht?Ht(r):new t.constructor(r);return t.copy(i),i}function An(t){var e=new t.constructor(t.byteLength);return new Kt(e).set(new Kt(t)),e}function _n(t,e){var r=e?An(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}function En(t,e){if(t!==e){var i=t!==r,n=null===t,s=t==t,o=sa(t),a=e!==r,c=null===e,h=e==e,u=sa(e);if(!c&&!u&&!o&&t>e||o&&a&&h&&!c&&!u||n&&a&&h||!i&&h||!s)return 1;if(!n&&!o&&!u&&t<e||u&&i&&s&&!n&&!o||c&&i&&s||!a&&s||!h)return-1}return 0}function In(t,e,r,i){for(var n=-1,s=t.length,o=r.length,a=-1,c=e.length,h=gr(s-o,0),u=nt(c+h),l=!i;++a<c;)u[a]=e[a];for(;++n<o;)(l||n<s)&&(u[r[n]]=t[n]);for(;h--;)u[a++]=t[n++];return u}function Sn(t,e,r,i){for(var n=-1,s=t.length,o=-1,a=r.length,c=-1,h=e.length,u=gr(s-a,0),l=nt(u+h),f=!i;++n<u;)l[n]=t[n];for(var d=n;++c<h;)l[d+c]=e[c];for(;++o<a;)(f||n<s)&&(l[d+r[o]]=t[n++]);return l}function Mn(t,e){var r=-1,i=t.length;for(e||(e=nt(i));++r<i;)e[r]=t[r];return e}function Pn(t,e,i,n){var s=!i;i||(i={});for(var o=-1,a=e.length;++o<a;){var c=e[o],h=n?n(i[c],t[c],c,i,t):r;h===r&&(h=t[c]),s?ri(i,c,h):Xr(i,c,h)}return i}function xn(t,e){return function(r,i){var n=zo(r)?Ie:ti,s=e?e():{};return n(r,t,ss(i,2),s)}}function Rn(t){return $i((function(e,i){var n=-1,s=i.length,o=s>1?i[s-1]:r,a=s>2?i[2]:r;for(o=t.length>3&&"function"==typeof o?(s--,o):r,a&&ms(i[0],i[1],a)&&(o=s<3?r:o,s=1),e=St(e);++n<s;){var c=i[n];c&&t(e,c,n,o)}return e}))}function Nn(t,e){return function(r,i){if(null==r)return r;if(!Ko(r))return t(r,i);for(var n=r.length,s=e?n:-1,o=St(r);(e?s--:++s<n)&&!1!==i(o[s],s,o););return r}}function Cn(t){return function(e,r,i){for(var n=-1,s=St(e),o=i(e),a=o.length;a--;){var c=o[t?a:++n];if(!1===r(s[c],c,s))break}return e}}function On(t){return function(e){var i=ir(e=ga(e))?hr(e):r,n=i?i[0]:e.charAt(0),s=i?vn(i,1).join(""):e.slice(1);return n[t]()+s}}function Un(t){return function(e){return Ue(Ga(ja(e).replace(Qt,"")),t,"")}}function Tn(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var r=qr(t.prototype),i=t.apply(r,e);return Yo(i)?i:r}}function Dn(t){return function(e,i,n){var s=St(e);if(!Ko(e)){var o=ss(i,3);e=xa(e),i=function(t){return o(s[t],t,s)}}var a=t(e,i,n);return a>-1?s[o?e[a]:a]:r}}function Bn(t){return Zn((function(e){var n=e.length,s=n,o=jr.prototype.thru;for(t&&e.reverse();s--;){var a=e[s];if("function"!=typeof a)throw new xt(i);if(o&&!c&&"wrapper"==is(a))var c=new jr([],!0)}for(s=c?s:n;++s<n;){var h=is(a=e[s]),u="wrapper"==h?rs(a):r;c=u&&vs(u[0])&&424==u[1]&&!u[4].length&&1==u[9]?c[is(u[0])].apply(c,u[3]):1==a.length&&vs(a)?c[h]():c.thru(a)}return function(){var t=arguments,r=t[0];if(c&&1==t.length&&zo(r))return c.plant(r).value();for(var i=0,s=n?e[i].apply(this,t):r;++i<n;)s=e[i].call(this,s);return s}}))}function kn(t,e,i,n,s,o,c,h,u,l){var f=e&a,d=1&e,p=2&e,g=24&e,m=512&e,y=p?r:Tn(t);return function a(){for(var v=arguments.length,w=nt(v),b=v;b--;)w[b]=arguments[b];if(g)var A=ns(a),_=function(t,e){for(var r=t.length,i=0;r--;)t[r]===e&&++i;return i}(w,A);if(n&&(w=In(w,n,s,g)),o&&(w=Sn(w,o,c,g)),v-=_,g&&v<l){var E=or(w,A);return Hn(t,e,kn,a.placeholder,i,w,E,h,u,l-v)}var I=d?i:this,S=p?I[t]:t;return v=w.length,h?w=function(t,e){for(var i=t.length,n=mr(e.length,i),s=Mn(t);n--;){var o=e[n];t[n]=gs(o,i)?s[o]:r}return t}(w,h):m&&v>1&&w.reverse(),f&&u<v&&(w.length=u),this&&this!==le&&this instanceof a&&(S=y||Tn(S)),S.apply(I,w)}}function qn(t,e){return function(r,i){return function(t,e,r,i){return yi(t,(function(t,n,s){e(i,r(t),n,s)})),i}(r,t,e(i),{})}}function Ln(t,e){return function(i,n){var s;if(i===r&&n===r)return e;if(i!==r&&(s=i),n!==r){if(s===r)return n;"string"==typeof i||"string"==typeof n?(i=on(i),n=on(n)):(i=sn(i),n=sn(n)),s=t(i,n)}return s}}function jn(t){return Zn((function(e){return e=Ce(e,We(ss())),$i((function(r){var i=this;return t(e,(function(t){return Ee(t,i,r)}))}))}))}function zn(t,e){var i=(e=e===r?" ":on(e)).length;if(i<2)return i?Vi(e,t):e;var n=Vi(e,de(t/cr(e)));return ir(e)?vn(hr(n),0,t).join(""):n.slice(0,t)}function Fn(t){return function(e,i,n){return n&&"number"!=typeof n&&ms(e,i,n)&&(i=n=r),e=ua(e),i===r?(i=e,e=0):i=ua(i),function(t,e,r,i){for(var n=-1,s=gr(de((e-t)/(r||1)),0),o=nt(s);s--;)o[i?s:++n]=t,t+=r;return o}(e,i,n=n===r?e<i?1:-1:ua(n),t)}}function Kn(t){return function(e,r){return"string"==typeof e&&"string"==typeof r||(e=da(e),r=da(r)),t(e,r)}}function Hn(t,e,i,n,s,a,c,h,u,l){var f=8&e;e|=f?o:64,4&(e&=~(f?64:o))||(e&=-4);var d=[t,e,s,f?a:r,f?c:r,f?r:a,f?r:c,h,u,l],p=i.apply(r,d);return vs(t)&&Ms(p,d),p.placeholder=n,Rs(p,t,e)}function Vn(t){var e=It[t];return function(t,r){if(t=da(t),(r=null==r?0:mr(la(r),292))&&He(t)){var i=(ga(t)+"e").split("e");return+((i=(ga(e(i[0]+"e"+(+i[1]+r)))+"e").split("e"))[0]+"e"+(+i[1]-r))}return e(t)}}var $n=Ir&&1/ar(new Ir([,-0]))[1]==c?function(t){return new Ir(t)}:sc;function Jn(t){return function(e){var r=ls(e);return r==_?nr(e):r==P?function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=[t,t]})),r}(e):function(t,e){return Ce(e,(function(e){return[e,t[e]]}))}(e,t(e))}}function Gn(t,e,n,c,h,u,l,f){var d=2&e;if(!d&&"function"!=typeof t)throw new xt(i);var p=c?c.length:0;if(p||(e&=-97,c=h=r),l=l===r?l:gr(la(l),0),f=f===r?f:la(f),p-=h?h.length:0,64&e){var g=c,m=h;c=h=r}var y=d?r:rs(t),v=[t,e,n,c,h,g,m,u,l,f];if(y&&function(t,e){var r=t[1],i=e[1],n=r|i,o=n<131,c=i==a&&8==r||i==a&&256==r&&t[7].length<=e[8]||384==i&&e[7].length<=e[8]&&8==r;if(!o&&!c)return t;1&i&&(t[2]=e[2],n|=1&r?0:4);var h=e[3];if(h){var u=t[3];t[3]=u?In(u,h,e[4]):h,t[4]=u?or(t[3],s):e[4]}(h=e[5])&&(u=t[5],t[5]=u?Sn(u,h,e[6]):h,t[6]=u?or(t[5],s):e[6]),(h=e[7])&&(t[7]=h),i&a&&(t[8]=null==t[8]?e[8]:mr(t[8],e[8])),null==t[9]&&(t[9]=e[9]),t[0]=e[0],t[1]=n}(v,y),t=v[0],e=v[1],n=v[2],c=v[3],h=v[4],!(f=v[9]=v[9]===r?d?0:t.length:gr(v[9]-p,0))&&24&e&&(e&=-25),e&&1!=e)w=8==e||16==e?function(t,e,i){var n=Tn(t);return function s(){for(var o=arguments.length,a=nt(o),c=o,h=ns(s);c--;)a[c]=arguments[c];var u=o<3&&a[0]!==h&&a[o-1]!==h?[]:or(a,h);return(o-=u.length)<i?Hn(t,e,kn,s.placeholder,r,a,u,r,r,i-o):Ee(this&&this!==le&&this instanceof s?n:t,this,a)}}(t,e,f):e!=o&&33!=e||h.length?kn.apply(r,v):function(t,e,r,i){var n=1&e,s=Tn(t);return function e(){for(var o=-1,a=arguments.length,c=-1,h=i.length,u=nt(h+a),l=this&&this!==le&&this instanceof e?s:t;++c<h;)u[c]=i[c];for(;a--;)u[c++]=arguments[++o];return Ee(l,n?r:this,u)}}(t,e,n,c);else var w=function(t,e,r){var i=1&e,n=Tn(t);return function e(){return(this&&this!==le&&this instanceof e?n:t).apply(i?r:this,arguments)}}(t,e,n);return Rs((y?Qi:Ms)(w,v),t,e)}function Wn(t,e,i,n){return t===r||ko(t,Ct[i])&&!Tt.call(n,i)?e:t}function Qn(t,e,i,n,s,o){return Yo(t)&&Yo(e)&&(o.set(e,t),qi(t,e,r,Qn,o),o.delete(e)),t}function Yn(t){return ea(t)?r:t}function Xn(t,e,i,n,s,o){var a=1&i,c=t.length,h=e.length;if(c!=h&&!(a&&h>c))return!1;var u=o.get(t),l=o.get(e);if(u&&l)return u==e&&l==t;var f=-1,d=!0,p=2&i?new Vr:r;for(o.set(t,e),o.set(e,t);++f<c;){var g=t[f],m=e[f];if(n)var y=a?n(m,g,f,e,t,o):n(g,m,f,t,e,o);if(y!==r){if(y)continue;d=!1;break}if(p){if(!De(e,(function(t,e){if(!Ye(p,e)&&(g===t||s(g,t,i,n,o)))return p.push(e)}))){d=!1;break}}else if(g!==m&&!s(g,m,i,n,o)){d=!1;break}}return o.delete(t),o.delete(e),d}function Zn(t){return xs(Es(t,r,zs),t+"")}function ts(t){return Ai(t,xa,hs)}function es(t){return Ai(t,Ra,us)}var rs=Pr?function(t){return Pr.get(t)}:sc;function is(t){for(var e=t.name+"",r=xr[e],i=Tt.call(xr,e)?r.length:0;i--;){var n=r[i],s=n.func;if(null==s||s==t)return n.name}return e}function ns(t){return(Tt.call(kr,"placeholder")?kr:t).placeholder}function ss(){var t=kr.iteratee||ec;return t=t===ec?Oi:t,arguments.length?t(arguments[0],arguments[1]):t}function os(t,e){var r=t.__data__;return function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}(e)?r["string"==typeof e?"string":"hash"]:r.map}function as(t){for(var e=xa(t),r=e.length;r--;){var i=e[r],n=t[i];e[r]=[i,n,As(n)]}return e}function cs(t,e){var i=function(t,e){return null==t?r:t[e]}(t,e);return Ci(i)?i:r}var hs=me?function(t){return null==t?[]:(t=St(t),xe(me(t),(function(e){return Jt.call(t,e)})))}:fc,us=me?function(t){for(var e=[];t;)Oe(e,hs(t)),t=Vt(t);return e}:fc,ls=_i;function fs(t,e,r){for(var i=-1,n=(e=mn(e,t)).length,s=!1;++i<n;){var o=Us(e[i]);if(!(s=null!=t&&r(t,o)))break;t=t[o]}return s||++i!=n?s:!!(n=null==t?0:t.length)&&Qo(n)&&gs(o,n)&&(zo(t)||jo(t))}function ds(t){return"function"!=typeof t.constructor||bs(t)?{}:qr(Vt(t))}function ps(t){return zo(t)||jo(t)||!!(Wt&&t&&t[Wt])}function gs(t,e){var r=typeof t;return!!(e=e??h)&&("number"==r||"symbol"!=r&&yt.test(t))&&t>-1&&t%1==0&&t<e}function ms(t,e,r){if(!Yo(r))return!1;var i=typeof e;return!!("number"==i?Ko(r)&&gs(e,r.length):"string"==i&&e in r)&&ko(r[e],t)}function ys(t,e){if(zo(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!sa(t))||Z.test(t)||!X.test(t)||null!=e&&t in St(e)}function vs(t){var e=is(t),r=kr[e];if("function"!=typeof r||!(e in zr.prototype))return!1;if(t===r)return!0;var i=rs(r);return!!i&&t===i[0]}(Ar&&ls(new Ar(new ArrayBuffer(1)))!=O||_r&&ls(new _r)!=_||Er&&ls(Er.resolve())!=S||Ir&&ls(new Ir)!=P||Sr&&ls(new Sr)!=N)&&(ls=function(t){var e=_i(t),i=e==I?t.constructor:r,n=i?Ts(i):"";if(n)switch(n){case Rr:return O;case Nr:return _;case Cr:return S;case Or:return P;case Ur:return N}return e});var ws=Ot?Go:dc;function bs(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Ct)}function As(t){return t==t&&!Yo(t)}function _s(t,e){return function(i){return null!=i&&i[t]===e&&(e!==r||t in St(i))}}function Es(t,e,i){return e=gr(e===r?t.length-1:e,0),function(){for(var r=arguments,n=-1,s=gr(r.length-e,0),o=nt(s);++n<s;)o[n]=r[e+n];n=-1;for(var a=nt(e+1);++n<e;)a[n]=r[n];return a[e]=i(o),Ee(t,this,a)}}function Is(t,e){return e.length<2?t:bi(t,Zi(e,0,-1))}function Ss(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}var Ms=Ns(Qi),Ps=fe||function(t,e){return le.setTimeout(t,e)},xs=Ns(Yi);function Rs(t,e,r){var i=e+"";return xs(t,function(t,e){var r=e.length;if(!r)return t;var i=r-1;return e[i]=(r>1?"& ":"")+e[i],e=e.join(r>2?", ":" "),t.replace(st,"{\n/* [wrapped with "+e+"] */\n")}(i,function(t,e){return Se(p,(function(r){var i="_."+r[0];e&r[1]&&!Re(t,i)&&t.push(i)})),t.sort()}(function(t){var e=t.match(ot);return e?e[1].split(at):[]}(i),r)))}function Ns(t){var e=0,i=0;return function(){var n=yr(),s=16-(n-i);if(i=n,s>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(r,arguments)}}function Cs(t,e){var i=-1,n=t.length,s=n-1;for(e=e===r?n:e;++i<e;){var o=Hi(i,s),a=t[o];t[o]=t[i],t[i]=a}return t.length=e,t}var Os=function(){var t=Co((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(tt,(function(t,r,i,n){e.push(i?n.replace(ut,"$1"):r||t)})),e}),(function(t){return 500===e.size&&e.clear(),t})),e=t.cache;return t}();function Us(t){if("string"==typeof t||sa(t))return t;var e=t+"";return"0"==e&&1/t==-c?"-0":e}function Ts(t){if(null!=t){try{return Ut.call(t)}catch{}try{return t+""}catch{}}return""}function Ds(t){if(t instanceof zr)return t.clone();var e=new jr(t.__wrapped__,t.__chain__);return e.__actions__=Mn(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}var Bs=$i((function(t,e){return Ho(t)?ci(t,pi(e,1,Ho,!0)):[]})),ks=$i((function(t,e){var i=$s(e);return Ho(i)&&(i=r),Ho(t)?ci(t,pi(e,1,Ho,!0),ss(i,2)):[]})),qs=$i((function(t,e){var i=$s(e);return Ho(i)&&(i=r),Ho(t)?ci(t,pi(e,1,Ho,!0),r,i):[]}));function Ls(t,e,r){var i=null==t?0:t.length;if(!i)return-1;var n=null==r?0:la(r);return n<0&&(n=gr(i+n,0)),qe(t,ss(e,3),n)}function js(t,e,i){var n=null==t?0:t.length;if(!n)return-1;var s=n-1;return i!==r&&(s=la(i),s=i<0?gr(n+s,0):mr(s,n-1)),qe(t,ss(e,3),s,!0)}function zs(t){return null!=t&&t.length?pi(t,1):[]}function Fs(t){return t&&t.length?t[0]:r}var Ks=$i((function(t){var e=Ce(t,pn);return e.length&&e[0]===t[0]?Mi(e):[]})),Hs=$i((function(t){var e=$s(t),i=Ce(t,pn);return e===$s(i)?e=r:i.pop(),i.length&&i[0]===t[0]?Mi(i,ss(e,2)):[]})),Vs=$i((function(t){var e=$s(t),i=Ce(t,pn);return(e="function"==typeof e?e:r)&&i.pop(),i.length&&i[0]===t[0]?Mi(i,r,e):[]}));function $s(t){var e=null==t?0:t.length;return e?t[e-1]:r}var Js=$i(Gs);function Gs(t,e){return t&&t.length&&e&&e.length?Fi(t,e):t}var Ws=Zn((function(t,e){var r=null==t?0:t.length,i=ii(t,e);return Ki(t,Ce(e,(function(t){return gs(t,r)?+t:t})).sort(En)),i}));function Qs(t){return null==t?t:br.call(t)}var Ys=$i((function(t){return an(pi(t,1,Ho,!0))})),Xs=$i((function(t){var e=$s(t);return Ho(e)&&(e=r),an(pi(t,1,Ho,!0),ss(e,2))})),Zs=$i((function(t){var e=$s(t);return e="function"==typeof e?e:r,an(pi(t,1,Ho,!0),r,e)}));function to(t){if(!t||!t.length)return[];var e=0;return t=xe(t,(function(t){if(Ho(t))return e=gr(t.length,e),!0})),Je(e,(function(e){return Ce(t,Ke(e))}))}function eo(t,e){if(!t||!t.length)return[];var i=to(t);return null==e?i:Ce(i,(function(t){return Ee(e,r,t)}))}var ro=$i((function(t,e){return Ho(t)?ci(t,e):[]})),io=$i((function(t){return fn(xe(t,Ho))})),no=$i((function(t){var e=$s(t);return Ho(e)&&(e=r),fn(xe(t,Ho),ss(e,2))})),so=$i((function(t){var e=$s(t);return e="function"==typeof e?e:r,fn(xe(t,Ho),r,e)})),oo=$i(to),ao=$i((function(t){var e=t.length,i=e>1?t[e-1]:r;return i="function"==typeof i?(t.pop(),i):r,eo(t,i)}));function co(t){var e=kr(t);return e.__chain__=!0,e}function ho(t,e){return e(t)}var uo=Zn((function(t){var e=t.length,i=e?t[0]:0,n=this.__wrapped__,s=function(e){return ii(e,t)};return!(e>1||this.__actions__.length)&&n instanceof zr&&gs(i)?((n=n.slice(i,+i+(e?1:0))).__actions__.push({func:ho,args:[s],thisArg:r}),new jr(n,this.__chain__).thru((function(t){return e&&!t.length&&t.push(r),t}))):this.thru(s)})),lo=xn((function(t,e,r){Tt.call(t,r)?++t[r]:ri(t,r,1)})),fo=Dn(Ls),po=Dn(js);function go(t,e){return(zo(t)?Se:hi)(t,ss(e,3))}function mo(t,e){return(zo(t)?Me:ui)(t,ss(e,3))}var yo=xn((function(t,e,r){Tt.call(t,r)?t[r].push(e):ri(t,r,[e])})),vo=$i((function(t,e,r){var i=-1,n="function"==typeof e,s=Ko(t)?nt(t.length):[];return hi(t,(function(t){s[++i]=n?Ee(e,t,r):Pi(t,e,r)})),s})),wo=xn((function(t,e,r){ri(t,r,e)}));function bo(t,e){return(zo(t)?Ce:Di)(t,ss(e,3))}var Ao=xn((function(t,e,r){t[r?0:1].push(e)}),(function(){return[[],[]]})),_o=$i((function(t,e){if(null==t)return[];var r=e.length;return r>1&&ms(t,e[0],e[1])?e=[]:r>2&&ms(e[0],e[1],e[2])&&(e=[e[0]]),ji(t,pi(e,1),[])})),Eo=ue||function(){return le.Date.now()};function Io(t,e,i){return e=i?r:e,e=t&&null==e?t.length:e,Gn(t,a,r,r,r,r,e)}function So(t,e){var n;if("function"!=typeof e)throw new xt(i);return t=la(t),function(){return--t>0&&(n=e.apply(this,arguments)),t<=1&&(e=r),n}}var Mo=$i((function(t,e,r){var i=1;if(r.length){var n=or(r,ns(Mo));i|=o}return Gn(t,i,e,r,n)})),Po=$i((function(t,e,r){var i=3;if(r.length){var n=or(r,ns(Po));i|=o}return Gn(e,i,t,r,n)}));function xo(t,e,n){var s,o,a,c,h,u,l=0,f=!1,d=!1,p=!0;if("function"!=typeof t)throw new xt(i);function g(e){var i=s,n=o;return s=o=r,l=e,c=t.apply(n,i)}function m(t){var i=t-u;return u===r||i>=e||i<0||d&&t-l>=a}function y(){var t=Eo();if(m(t))return v(t);h=Ps(y,function(t){var r=e-(t-u);return d?mr(r,a-(t-l)):r}(t))}function v(t){return h=r,p&&s?g(t):(s=o=r,c)}function w(){var t=Eo(),i=m(t);if(s=arguments,o=this,u=t,i){if(h===r)return function(t){return l=t,h=Ps(y,e),f?g(t):c}(u);if(d)return wn(h),h=Ps(y,e),g(u)}return h===r&&(h=Ps(y,e)),c}return e=da(e)||0,Yo(n)&&(f=!!n.leading,a=(d="maxWait"in n)?gr(da(n.maxWait)||0,e):a,p="trailing"in n?!!n.trailing:p),w.cancel=function(){h!==r&&wn(h),l=0,s=u=o=h=r},w.flush=function(){return h===r?c:v(Eo())},w}var Ro=$i((function(t,e){return ai(t,1,e)})),No=$i((function(t,e,r){return ai(t,da(e)||0,r)}));function Co(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new xt(i);var r=function(){var i=arguments,n=e?e.apply(this,i):i[0],s=r.cache;if(s.has(n))return s.get(n);var o=t.apply(this,i);return r.cache=s.set(n,o)||s,o};return r.cache=new(Co.Cache||Hr),r}function Oo(t){if("function"!=typeof t)throw new xt(i);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}Co.Cache=Hr;var Uo=yn((function(t,e){var r=(e=1==e.length&&zo(e[0])?Ce(e[0],We(ss())):Ce(pi(e,1),We(ss()))).length;return $i((function(i){for(var n=-1,s=mr(i.length,r);++n<s;)i[n]=e[n].call(this,i[n]);return Ee(t,this,i)}))})),To=$i((function(t,e){var i=or(e,ns(To));return Gn(t,o,r,e,i)})),Do=$i((function(t,e){var i=or(e,ns(Do));return Gn(t,64,r,e,i)})),Bo=Zn((function(t,e){return Gn(t,256,r,r,r,e)}));function ko(t,e){return t===e||t!=t&&e!=e}var qo=Kn(Ei),Lo=Kn((function(t,e){return t>=e})),jo=xi(function(){return arguments}())?xi:function(t){return Xo(t)&&Tt.call(t,"callee")&&!Jt.call(t,"callee")},zo=nt.isArray,Fo=ye?We(ye):function(t){return Xo(t)&&_i(t)==C};function Ko(t){return null!=t&&Qo(t.length)&&!Go(t)}function Ho(t){return Xo(t)&&Ko(t)}var Vo=Be||dc,$o=ve?We(ve):function(t){return Xo(t)&&_i(t)==v};function Jo(t){if(!Xo(t))return!1;var e=_i(t);return e==w||"[object DOMException]"==e||"string"==typeof t.message&&"string"==typeof t.name&&!ea(t)}function Go(t){if(!Yo(t))return!1;var e=_i(t);return e==b||e==A||"[object AsyncFunction]"==e||"[object Proxy]"==e}function Wo(t){return"number"==typeof t&&t==la(t)}function Qo(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=h}function Yo(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Xo(t){return null!=t&&"object"==typeof t}var Zo=we?We(we):function(t){return Xo(t)&&ls(t)==_};function ta(t){return"number"==typeof t||Xo(t)&&_i(t)==E}function ea(t){if(!Xo(t)||_i(t)!=I)return!1;var e=Vt(t);if(null===e)return!0;var r=Tt.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&Ut.call(r)==qt}var ra=be?We(be):function(t){return Xo(t)&&_i(t)==M},ia=Ae?We(Ae):function(t){return Xo(t)&&ls(t)==P};function na(t){return"string"==typeof t||!zo(t)&&Xo(t)&&_i(t)==x}function sa(t){return"symbol"==typeof t||Xo(t)&&_i(t)==R}var oa=_e?We(_e):function(t){return Xo(t)&&Qo(t.length)&&!!ne[_i(t)]},aa=Kn(Ti),ca=Kn((function(t,e){return t<=e}));function ha(t){if(!t)return[];if(Ko(t))return na(t)?hr(t):Mn(t);if(Xt&&t[Xt])return function(t){for(var e,r=[];!(e=t.next()).done;)r.push(e.value);return r}(t[Xt]());var e=ls(t);return(e==_?nr:e==P?ar:ka)(t)}function ua(t){return t?(t=da(t))===c||t===-c?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}function la(t){var e=ua(t),r=e%1;return e==e?r?e-r:e:0}function fa(t){return t?ni(la(t),0,l):0}function da(t){if("number"==typeof t)return t;if(sa(t))return u;if(Yo(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Yo(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=Ge(t);var r=pt.test(t);return r||mt.test(t)?ce(t.slice(2),r?2:8):dt.test(t)?u:+t}function pa(t){return Pn(t,Ra(t))}function ga(t){return null==t?"":on(t)}var ma=Rn((function(t,e){if(bs(e)||Ko(e))Pn(e,xa(e),t);else for(var r in e)Tt.call(e,r)&&Xr(t,r,e[r])})),ya=Rn((function(t,e){Pn(e,Ra(e),t)})),va=Rn((function(t,e,r,i){Pn(e,Ra(e),t,i)})),wa=Rn((function(t,e,r,i){Pn(e,xa(e),t,i)})),ba=Zn(ii),Aa=$i((function(t,e){t=St(t);var i=-1,n=e.length,s=n>2?e[2]:r;for(s&&ms(e[0],e[1],s)&&(n=1);++i<n;)for(var o=e[i],a=Ra(o),c=-1,h=a.length;++c<h;){var u=a[c],l=t[u];(l===r||ko(l,Ct[u])&&!Tt.call(t,u))&&(t[u]=o[u])}return t})),_a=$i((function(t){return t.push(r,Qn),Ee(Ca,r,t)}));function Ea(t,e,i){var n=null==t?r:bi(t,e);return n===r?i:n}function Ia(t,e){return null!=t&&fs(t,e,Si)}var Sa=qn((function(t,e,r){null!=e&&"function"!=typeof e.toString&&(e=kt.call(e)),t[e]=r}),Ya(tc)),Ma=qn((function(t,e,r){null!=e&&"function"!=typeof e.toString&&(e=kt.call(e)),Tt.call(t,e)?t[e].push(r):t[e]=[r]}),ss),Pa=$i(Pi);function xa(t){return Ko(t)?Jr(t):Ui(t)}function Ra(t){return Ko(t)?Jr(t,!0):function(t){if(!Yo(t))return function(t){var e=[];if(null!=t)for(var r in St(t))e.push(r);return e}(t);var e=bs(t),r=[];for(var i in t)"constructor"==i&&(e||!Tt.call(t,i))||r.push(i);return r}(t)}var Na=Rn((function(t,e,r){qi(t,e,r)})),Ca=Rn((function(t,e,r,i){qi(t,e,r,i)})),Oa=Zn((function(t,e){var r={};if(null==t)return r;var i=!1;e=Ce(e,(function(e){return e=mn(e,t),i||(i=e.length>1),e})),Pn(t,es(t),r),i&&(r=si(r,7,Yn));for(var n=e.length;n--;)cn(r,e[n]);return r})),Ua=Zn((function(t,e){return null==t?{}:function(t,e){return zi(t,e,(function(e,r){return Ia(t,r)}))}(t,e)}));function Ta(t,e){if(null==t)return{};var r=Ce(es(t),(function(t){return[t]}));return e=ss(e),zi(t,r,(function(t,r){return e(t,r[0])}))}var Da=Jn(xa),Ba=Jn(Ra);function ka(t){return null==t?[]:Qe(t,xa(t))}var qa=Un((function(t,e,r){return e=e.toLowerCase(),t+(r?La(e):e)}));function La(t){return Ja(ga(t).toLowerCase())}function ja(t){return(t=ga(t))&&t.replace(vt,tr).replace(Yt,"")}var za=Un((function(t,e,r){return t+(r?"-":"")+e.toLowerCase()})),Fa=Un((function(t,e,r){return t+(r?" ":"")+e.toLowerCase()})),Ka=On("toLowerCase"),Ha=Un((function(t,e,r){return t+(r?"_":"")+e.toLowerCase()})),Va=Un((function(t,e,r){return t+(r?" ":"")+Ja(e)})),$a=Un((function(t,e,r){return t+(r?" ":"")+e.toUpperCase()})),Ja=On("toUpperCase");function Ga(t,e,i){return t=ga(t),(e=i?r:e)===r?function(t){return ee.test(t)}(t)?function(t){return t.match(Zt)||[]}(t):function(t){return t.match(ct)||[]}(t):t.match(e)||[]}var Wa=$i((function(t,e){try{return Ee(t,r,e)}catch(t){return Jo(t)?t:new _t(t)}})),Qa=Zn((function(t,e){return Se(e,(function(e){e=Us(e),ri(t,e,Mo(t[e],t))})),t}));function Ya(t){return function(){return t}}var Xa=Bn(),Za=Bn(!0);function tc(t){return t}function ec(t){return Oi("function"==typeof t?t:si(t,1))}var rc=$i((function(t,e){return function(r){return Pi(r,t,e)}})),ic=$i((function(t,e){return function(r){return Pi(t,r,e)}}));function nc(t,e,r){var i=xa(e),n=wi(e,i);null==r&&(!Yo(e)||!n.length&&i.length)&&(r=e,e=t,t=this,n=wi(e,xa(e)));var s=!(Yo(r)&&"chain"in r&&!r.chain),o=Go(t);return Se(n,(function(r){var i=e[r];t[r]=i,o&&(t.prototype[r]=function(){var e=this.__chain__;if(s||e){var r=t(this.__wrapped__);return(r.__actions__=Mn(this.__actions__)).push({func:i,args:arguments,thisArg:t}),r.__chain__=e,r}return i.apply(t,Oe([this.value()],arguments))})})),t}function sc(){}var oc=jn(Ce),ac=jn(Pe),cc=jn(De);function hc(t){return ys(t)?Ke(Us(t)):function(t){return function(e){return bi(e,t)}}(t)}var uc=Fn(),lc=Fn(!0);function fc(){return[]}function dc(){return!1}var pc=Ln((function(t,e){return t+e}),0),gc=Vn("ceil"),mc=Ln((function(t,e){return t/e}),1),yc=Vn("floor"),vc=Ln((function(t,e){return t*e}),1),wc=Vn("round"),bc=Ln((function(t,e){return t-e}),0);return kr.after=function(t,e){if("function"!=typeof e)throw new xt(i);return t=la(t),function(){if(--t<1)return e.apply(this,arguments)}},kr.ary=Io,kr.assign=ma,kr.assignIn=ya,kr.assignInWith=va,kr.assignWith=wa,kr.at=ba,kr.before=So,kr.bind=Mo,kr.bindAll=Qa,kr.bindKey=Po,kr.castArray=function(){if(!arguments.length)return[];var t=arguments[0];return zo(t)?t:[t]},kr.chain=co,kr.chunk=function(t,e,i){e=(i?ms(t,e,i):e===r)?1:gr(la(e),0);var n=null==t?0:t.length;if(!n||e<1)return[];for(var s=0,o=0,a=nt(de(n/e));s<n;)a[o++]=Zi(t,s,s+=e);return a},kr.compact=function(t){for(var e=-1,r=null==t?0:t.length,i=0,n=[];++e<r;){var s=t[e];s&&(n[i++]=s)}return n},kr.concat=function(){var t=arguments.length;if(!t)return[];for(var e=nt(t-1),r=arguments[0],i=t;i--;)e[i-1]=arguments[i];return Oe(zo(r)?Mn(r):[r],pi(e,1))},kr.cond=function(t){var e=null==t?0:t.length,r=ss();return t=e?Ce(t,(function(t){if("function"!=typeof t[1])throw new xt(i);return[r(t[0]),t[1]]})):[],$i((function(r){for(var i=-1;++i<e;){var n=t[i];if(Ee(n[0],this,r))return Ee(n[1],this,r)}}))},kr.conforms=function(t){return function(t){var e=xa(t);return function(r){return oi(r,t,e)}}(si(t,1))},kr.constant=Ya,kr.countBy=lo,kr.create=function(t,e){var r=qr(t);return null==e?r:ei(r,e)},kr.curry=function t(e,i,n){var s=Gn(e,8,r,r,r,r,r,i=n?r:i);return s.placeholder=t.placeholder,s},kr.curryRight=function t(e,i,n){var s=Gn(e,16,r,r,r,r,r,i=n?r:i);return s.placeholder=t.placeholder,s},kr.debounce=xo,kr.defaults=Aa,kr.defaultsDeep=_a,kr.defer=Ro,kr.delay=No,kr.difference=Bs,kr.differenceBy=ks,kr.differenceWith=qs,kr.drop=function(t,e,i){var n=null==t?0:t.length;return n?Zi(t,(e=i||e===r?1:la(e))<0?0:e,n):[]},kr.dropRight=function(t,e,i){var n=null==t?0:t.length;return n?Zi(t,0,(e=n-(e=i||e===r?1:la(e)))<0?0:e):[]},kr.dropRightWhile=function(t,e){return t&&t.length?un(t,ss(e,3),!0,!0):[]},kr.dropWhile=function(t,e){return t&&t.length?un(t,ss(e,3),!0):[]},kr.fill=function(t,e,i,n){var s=null==t?0:t.length;return s?(i&&"number"!=typeof i&&ms(t,e,i)&&(i=0,n=s),function(t,e,i,n){var s=t.length;for((i=la(i))<0&&(i=-i>s?0:s+i),(n=n===r||n>s?s:la(n))<0&&(n+=s),n=i>n?0:fa(n);i<n;)t[i++]=e;return t}(t,e,i,n)):[]},kr.filter=function(t,e){return(zo(t)?xe:di)(t,ss(e,3))},kr.flatMap=function(t,e){return pi(bo(t,e),1)},kr.flatMapDeep=function(t,e){return pi(bo(t,e),c)},kr.flatMapDepth=function(t,e,i){return i=i===r?1:la(i),pi(bo(t,e),i)},kr.flatten=zs,kr.flattenDeep=function(t){return null!=t&&t.length?pi(t,c):[]},kr.flattenDepth=function(t,e){return null!=t&&t.length?pi(t,e=e===r?1:la(e)):[]},kr.flip=function(t){return Gn(t,512)},kr.flow=Xa,kr.flowRight=Za,kr.fromPairs=function(t){for(var e=-1,r=null==t?0:t.length,i={};++e<r;){var n=t[e];i[n[0]]=n[1]}return i},kr.functions=function(t){return null==t?[]:wi(t,xa(t))},kr.functionsIn=function(t){return null==t?[]:wi(t,Ra(t))},kr.groupBy=yo,kr.initial=function(t){return null!=t&&t.length?Zi(t,0,-1):[]},kr.intersection=Ks,kr.intersectionBy=Hs,kr.intersectionWith=Vs,kr.invert=Sa,kr.invertBy=Ma,kr.invokeMap=vo,kr.iteratee=ec,kr.keyBy=wo,kr.keys=xa,kr.keysIn=Ra,kr.map=bo,kr.mapKeys=function(t,e){var r={};return e=ss(e,3),yi(t,(function(t,i,n){ri(r,e(t,i,n),t)})),r},kr.mapValues=function(t,e){var r={};return e=ss(e,3),yi(t,(function(t,i,n){ri(r,i,e(t,i,n))})),r},kr.matches=function(t){return Bi(si(t,1))},kr.matchesProperty=function(t,e){return ki(t,si(e,1))},kr.memoize=Co,kr.merge=Na,kr.mergeWith=Ca,kr.method=rc,kr.methodOf=ic,kr.mixin=nc,kr.negate=Oo,kr.nthArg=function(t){return t=la(t),$i((function(e){return Li(e,t)}))},kr.omit=Oa,kr.omitBy=function(t,e){return Ta(t,Oo(ss(e)))},kr.once=function(t){return So(2,t)},kr.orderBy=function(t,e,i,n){return null==t?[]:(zo(e)||(e=null==e?[]:[e]),zo(i=n?r:i)||(i=null==i?[]:[i]),ji(t,e,i))},kr.over=oc,kr.overArgs=Uo,kr.overEvery=ac,kr.overSome=cc,kr.partial=To,kr.partialRight=Do,kr.partition=Ao,kr.pick=Ua,kr.pickBy=Ta,kr.property=hc,kr.propertyOf=function(t){return function(e){return null==t?r:bi(t,e)}},kr.pull=Js,kr.pullAll=Gs,kr.pullAllBy=function(t,e,r){return t&&t.length&&e&&e.length?Fi(t,e,ss(r,2)):t},kr.pullAllWith=function(t,e,i){return t&&t.length&&e&&e.length?Fi(t,e,r,i):t},kr.pullAt=Ws,kr.range=uc,kr.rangeRight=lc,kr.rearg=Bo,kr.reject=function(t,e){return(zo(t)?xe:di)(t,Oo(ss(e,3)))},kr.remove=function(t,e){var r=[];if(!t||!t.length)return r;var i=-1,n=[],s=t.length;for(e=ss(e,3);++i<s;){var o=t[i];e(o,i,t)&&(r.push(o),n.push(i))}return Ki(t,n),r},kr.rest=function(t,e){if("function"!=typeof t)throw new xt(i);return $i(t,e=e===r?e:la(e))},kr.reverse=Qs,kr.sampleSize=function(t,e,i){return e=(i?ms(t,e,i):e===r)?1:la(e),(zo(t)?Wr:Gi)(t,e)},kr.set=function(t,e,r){return null==t?t:Wi(t,e,r)},kr.setWith=function(t,e,i,n){return n="function"==typeof n?n:r,null==t?t:Wi(t,e,i,n)},kr.shuffle=function(t){return(zo(t)?Qr:Xi)(t)},kr.slice=function(t,e,i){var n=null==t?0:t.length;return n?(i&&"number"!=typeof i&&ms(t,e,i)?(e=0,i=n):(e=null==e?0:la(e),i=i===r?n:la(i)),Zi(t,e,i)):[]},kr.sortBy=_o,kr.sortedUniq=function(t){return t&&t.length?nn(t):[]},kr.sortedUniqBy=function(t,e){return t&&t.length?nn(t,ss(e,2)):[]},kr.split=function(t,e,i){return i&&"number"!=typeof i&&ms(t,e,i)&&(e=i=r),(i=i===r?l:i>>>0)?(t=ga(t))&&("string"==typeof e||null!=e&&!ra(e))&&!(e=on(e))&&ir(t)?vn(hr(t),0,i):t.split(e,i):[]},kr.spread=function(t,e){if("function"!=typeof t)throw new xt(i);return e=null==e?0:gr(la(e),0),$i((function(r){var i=r[e],n=vn(r,0,e);return i&&Oe(n,i),Ee(t,this,n)}))},kr.tail=function(t){var e=null==t?0:t.length;return e?Zi(t,1,e):[]},kr.take=function(t,e,i){return t&&t.length?Zi(t,0,(e=i||e===r?1:la(e))<0?0:e):[]},kr.takeRight=function(t,e,i){var n=null==t?0:t.length;return n?Zi(t,(e=n-(e=i||e===r?1:la(e)))<0?0:e,n):[]},kr.takeRightWhile=function(t,e){return t&&t.length?un(t,ss(e,3),!1,!0):[]},kr.takeWhile=function(t,e){return t&&t.length?un(t,ss(e,3)):[]},kr.tap=function(t,e){return e(t),t},kr.throttle=function(t,e,r){var n=!0,s=!0;if("function"!=typeof t)throw new xt(i);return Yo(r)&&(n="leading"in r?!!r.leading:n,s="trailing"in r?!!r.trailing:s),xo(t,e,{leading:n,maxWait:e,trailing:s})},kr.thru=ho,kr.toArray=ha,kr.toPairs=Da,kr.toPairsIn=Ba,kr.toPath=function(t){return zo(t)?Ce(t,Us):sa(t)?[t]:Mn(Os(ga(t)))},kr.toPlainObject=pa,kr.transform=function(t,e,r){var i=zo(t),n=i||Vo(t)||oa(t);if(e=ss(e,4),null==r){var s=t&&t.constructor;r=n?i?new s:[]:Yo(t)&&Go(s)?qr(Vt(t)):{}}return(n?Se:yi)(t,(function(t,i,n){return e(r,t,i,n)})),r},kr.unary=function(t){return Io(t,1)},kr.union=Ys,kr.unionBy=Xs,kr.unionWith=Zs,kr.uniq=function(t){return t&&t.length?an(t):[]},kr.uniqBy=function(t,e){return t&&t.length?an(t,ss(e,2)):[]},kr.uniqWith=function(t,e){return e="function"==typeof e?e:r,t&&t.length?an(t,r,e):[]},kr.unset=function(t,e){return null==t||cn(t,e)},kr.unzip=to,kr.unzipWith=eo,kr.update=function(t,e,r){return null==t?t:hn(t,e,gn(r))},kr.updateWith=function(t,e,i,n){return n="function"==typeof n?n:r,null==t?t:hn(t,e,gn(i),n)},kr.values=ka,kr.valuesIn=function(t){return null==t?[]:Qe(t,Ra(t))},kr.without=ro,kr.words=Ga,kr.wrap=function(t,e){return To(gn(e),t)},kr.xor=io,kr.xorBy=no,kr.xorWith=so,kr.zip=oo,kr.zipObject=function(t,e){return dn(t||[],e||[],Xr)},kr.zipObjectDeep=function(t,e){return dn(t||[],e||[],Wi)},kr.zipWith=ao,kr.entries=Da,kr.entriesIn=Ba,kr.extend=ya,kr.extendWith=va,nc(kr,kr),kr.add=pc,kr.attempt=Wa,kr.camelCase=qa,kr.capitalize=La,kr.ceil=gc,kr.clamp=function(t,e,i){return i===r&&(i=e,e=r),i!==r&&(i=(i=da(i))==i?i:0),e!==r&&(e=(e=da(e))==e?e:0),ni(da(t),e,i)},kr.clone=function(t){return si(t,4)},kr.cloneDeep=function(t){return si(t,5)},kr.cloneDeepWith=function(t,e){return si(t,5,e="function"==typeof e?e:r)},kr.cloneWith=function(t,e){return si(t,4,e="function"==typeof e?e:r)},kr.conformsTo=function(t,e){return null==e||oi(t,e,xa(e))},kr.deburr=ja,kr.defaultTo=function(t,e){return null==t||t!=t?e:t},kr.divide=mc,kr.endsWith=function(t,e,i){t=ga(t),e=on(e);var n=t.length,s=i=i===r?n:ni(la(i),0,n);return(i-=e.length)>=0&&t.slice(i,s)==e},kr.eq=ko,kr.escape=function(t){return(t=ga(t))&&G.test(t)?t.replace($,er):t},kr.escapeRegExp=function(t){return(t=ga(t))&&rt.test(t)?t.replace(et,"\\$&"):t},kr.every=function(t,e,i){var n=zo(t)?Pe:li;return i&&ms(t,e,i)&&(e=r),n(t,ss(e,3))},kr.find=fo,kr.findIndex=Ls,kr.findKey=function(t,e){return ke(t,ss(e,3),yi)},kr.findLast=po,kr.findLastIndex=js,kr.findLastKey=function(t,e){return ke(t,ss(e,3),vi)},kr.floor=yc,kr.forEach=go,kr.forEachRight=mo,kr.forIn=function(t,e){return null==t?t:gi(t,ss(e,3),Ra)},kr.forInRight=function(t,e){return null==t?t:mi(t,ss(e,3),Ra)},kr.forOwn=function(t,e){return t&&yi(t,ss(e,3))},kr.forOwnRight=function(t,e){return t&&vi(t,ss(e,3))},kr.get=Ea,kr.gt=qo,kr.gte=Lo,kr.has=function(t,e){return null!=t&&fs(t,e,Ii)},kr.hasIn=Ia,kr.head=Fs,kr.identity=tc,kr.includes=function(t,e,r,i){t=Ko(t)?t:ka(t),r=r&&!i?la(r):0;var n=t.length;return r<0&&(r=gr(n+r,0)),na(t)?r<=n&&t.indexOf(e,r)>-1:!!n&&Le(t,e,r)>-1},kr.indexOf=function(t,e,r){var i=null==t?0:t.length;if(!i)return-1;var n=null==r?0:la(r);return n<0&&(n=gr(i+n,0)),Le(t,e,n)},kr.inRange=function(t,e,i){return e=ua(e),i===r?(i=e,e=0):i=ua(i),function(t,e,r){return t>=mr(e,r)&&t<gr(e,r)}(t=da(t),e,i)},kr.invoke=Pa,kr.isArguments=jo,kr.isArray=zo,kr.isArrayBuffer=Fo,kr.isArrayLike=Ko,kr.isArrayLikeObject=Ho,kr.isBoolean=function(t){return!0===t||!1===t||Xo(t)&&_i(t)==y},kr.isBuffer=Vo,kr.isDate=$o,kr.isElement=function(t){return Xo(t)&&1===t.nodeType&&!ea(t)},kr.isEmpty=function(t){if(null==t)return!0;if(Ko(t)&&(zo(t)||"string"==typeof t||"function"==typeof t.splice||Vo(t)||oa(t)||jo(t)))return!t.length;var e=ls(t);if(e==_||e==P)return!t.size;if(bs(t))return!Ui(t).length;for(var r in t)if(Tt.call(t,r))return!1;return!0},kr.isEqual=function(t,e){return Ri(t,e)},kr.isEqualWith=function(t,e,i){var n=(i="function"==typeof i?i:r)?i(t,e):r;return n===r?Ri(t,e,r,i):!!n},kr.isError=Jo,kr.isFinite=function(t){return"number"==typeof t&&He(t)},kr.isFunction=Go,kr.isInteger=Wo,kr.isLength=Qo,kr.isMap=Zo,kr.isMatch=function(t,e){return t===e||Ni(t,e,as(e))},kr.isMatchWith=function(t,e,i){return i="function"==typeof i?i:r,Ni(t,e,as(e),i)},kr.isNaN=function(t){return ta(t)&&t!=+t},kr.isNative=function(t){if(ws(t))throw new _t("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Ci(t)},kr.isNil=function(t){return null==t},kr.isNull=function(t){return null===t},kr.isNumber=ta,kr.isObject=Yo,kr.isObjectLike=Xo,kr.isPlainObject=ea,kr.isRegExp=ra,kr.isSafeInteger=function(t){return Wo(t)&&t>=-h&&t<=h},kr.isSet=ia,kr.isString=na,kr.isSymbol=sa,kr.isTypedArray=oa,kr.isUndefined=function(t){return t===r},kr.isWeakMap=function(t){return Xo(t)&&ls(t)==N},kr.isWeakSet=function(t){return Xo(t)&&"[object WeakSet]"==_i(t)},kr.join=function(t,e){return null==t?"":dr.call(t,e)},kr.kebabCase=za,kr.last=$s,kr.lastIndexOf=function(t,e,i){var n=null==t?0:t.length;if(!n)return-1;var s=n;return i!==r&&(s=(s=la(i))<0?gr(n+s,0):mr(s,n-1)),e==e?function(t,e,r){for(var i=r+1;i--;)if(t[i]===e)return i;return i}(t,e,s):qe(t,ze,s,!0)},kr.lowerCase=Fa,kr.lowerFirst=Ka,kr.lt=aa,kr.lte=ca,kr.max=function(t){return t&&t.length?fi(t,tc,Ei):r},kr.maxBy=function(t,e){return t&&t.length?fi(t,ss(e,2),Ei):r},kr.mean=function(t){return Fe(t,tc)},kr.meanBy=function(t,e){return Fe(t,ss(e,2))},kr.min=function(t){return t&&t.length?fi(t,tc,Ti):r},kr.minBy=function(t,e){return t&&t.length?fi(t,ss(e,2),Ti):r},kr.stubArray=fc,kr.stubFalse=dc,kr.stubObject=function(){return{}},kr.stubString=function(){return""},kr.stubTrue=function(){return!0},kr.multiply=vc,kr.nth=function(t,e){return t&&t.length?Li(t,la(e)):r},kr.noConflict=function(){return le._===this&&(le._=Lt),this},kr.noop=sc,kr.now=Eo,kr.pad=function(t,e,r){t=ga(t);var i=(e=la(e))?cr(t):0;if(!e||i>=e)return t;var n=(e-i)/2;return zn(ge(n),r)+t+zn(de(n),r)},kr.padEnd=function(t,e,r){t=ga(t);var i=(e=la(e))?cr(t):0;return e&&i<e?t+zn(e-i,r):t},kr.padStart=function(t,e,r){t=ga(t);var i=(e=la(e))?cr(t):0;return e&&i<e?zn(e-i,r)+t:t},kr.parseInt=function(t,e,r){return r||null==e?e=0:e&&(e=+e),vr(ga(t).replace(it,""),e||0)},kr.random=function(t,e,i){if(i&&"boolean"!=typeof i&&ms(t,e,i)&&(e=i=r),i===r&&("boolean"==typeof e?(i=e,e=r):"boolean"==typeof t&&(i=t,t=r)),t===r&&e===r?(t=0,e=1):(t=ua(t),e===r?(e=t,t=0):e=ua(e)),t>e){var n=t;t=e,e=n}if(i||t%1||e%1){var s=wr();return mr(t+s*(e-t+ae("1e-"+((s+"").length-1))),e)}return Hi(t,e)},kr.reduce=function(t,e,r){var i=zo(t)?Ue:Ve,n=arguments.length<3;return i(t,ss(e,4),r,n,hi)},kr.reduceRight=function(t,e,r){var i=zo(t)?Te:Ve,n=arguments.length<3;return i(t,ss(e,4),r,n,ui)},kr.repeat=function(t,e,i){return e=(i?ms(t,e,i):e===r)?1:la(e),Vi(ga(t),e)},kr.replace=function(){var t=arguments,e=ga(t[0]);return t.length<3?e:e.replace(t[1],t[2])},kr.result=function(t,e,i){var n=-1,s=(e=mn(e,t)).length;for(s||(s=1,t=r);++n<s;){var o=null==t?r:t[Us(e[n])];o===r&&(n=s,o=i),t=Go(o)?o.call(t):o}return t},kr.round=wc,kr.runInContext=t,kr.sample=function(t){return(zo(t)?Gr:Ji)(t)},kr.size=function(t){if(null==t)return 0;if(Ko(t))return na(t)?cr(t):t.length;var e=ls(t);return e==_||e==P?t.size:Ui(t).length},kr.snakeCase=Ha,kr.some=function(t,e,i){var n=zo(t)?De:tn;return i&&ms(t,e,i)&&(e=r),n(t,ss(e,3))},kr.sortedIndex=function(t,e){return en(t,e)},kr.sortedIndexBy=function(t,e,r){return rn(t,e,ss(r,2))},kr.sortedIndexOf=function(t,e){var r=null==t?0:t.length;if(r){var i=en(t,e);if(i<r&&ko(t[i],e))return i}return-1},kr.sortedLastIndex=function(t,e){return en(t,e,!0)},kr.sortedLastIndexBy=function(t,e,r){return rn(t,e,ss(r,2),!0)},kr.sortedLastIndexOf=function(t,e){if(null!=t&&t.length){var r=en(t,e,!0)-1;if(ko(t[r],e))return r}return-1},kr.startCase=Va,kr.startsWith=function(t,e,r){return t=ga(t),r=null==r?0:ni(la(r),0,t.length),e=on(e),t.slice(r,r+e.length)==e},kr.subtract=bc,kr.sum=function(t){return t&&t.length?$e(t,tc):0},kr.sumBy=function(t,e){return t&&t.length?$e(t,ss(e,2)):0},kr.template=function(t,e,i){var n=kr.templateSettings;i&&ms(t,e,i)&&(e=r),t=ga(t),e=va({},e,n,Wn);var s,o,a=va({},e.imports,n.imports,Wn),c=xa(a),h=Qe(a,c),u=0,l=e.interpolate||wt,f="__p += '",d=Mt((e.escape||wt).source+"|"+l.source+"|"+(l===Y?lt:wt).source+"|"+(e.evaluate||wt).source+"|$","g"),p="//# sourceURL="+(Tt.call(e,"sourceURL")?(e.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++ie+"]")+"\n";t.replace(d,(function(e,r,i,n,a,c){return i||(i=n),f+=t.slice(u,c).replace(bt,rr),r&&(s=!0,f+="' +\n__e("+r+") +\n'"),a&&(o=!0,f+="';\n"+a+";\n__p += '"),i&&(f+="' +\n((__t = ("+i+")) == null ? '' : __t) +\n'"),u=c+e.length,e})),f+="';\n";var g=Tt.call(e,"variable")&&e.variable;if(g){if(ht.test(g))throw new _t("Invalid `variable` option passed into `_.template`")}else f="with (obj) {\n"+f+"\n}\n";f=(o?f.replace(F,""):f).replace(K,"$1").replace(H,"$1;"),f="function("+(g||"obj")+") {\n"+(g?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(s?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+f+"return __p\n}";var m=Wa((function(){return Et(c,p+"return "+f).apply(r,h)}));if(m.source=f,Jo(m))throw m;return m},kr.times=function(t,e){if((t=la(t))<1||t>h)return[];var r=l,i=mr(t,l);e=ss(e),t-=l;for(var n=Je(i,e);++r<t;)e(r);return n},kr.toFinite=ua,kr.toInteger=la,kr.toLength=fa,kr.toLower=function(t){return ga(t).toLowerCase()},kr.toNumber=da,kr.toSafeInteger=function(t){return t?ni(la(t),-h,h):0===t?t:0},kr.toString=ga,kr.toUpper=function(t){return ga(t).toUpperCase()},kr.trim=function(t,e,i){if((t=ga(t))&&(i||e===r))return Ge(t);if(!t||!(e=on(e)))return t;var n=hr(t),s=hr(e);return vn(n,Xe(n,s),Ze(n,s)+1).join("")},kr.trimEnd=function(t,e,i){if((t=ga(t))&&(i||e===r))return t.slice(0,ur(t)+1);if(!t||!(e=on(e)))return t;var n=hr(t);return vn(n,0,Ze(n,hr(e))+1).join("")},kr.trimStart=function(t,e,i){if((t=ga(t))&&(i||e===r))return t.replace(it,"");if(!t||!(e=on(e)))return t;var n=hr(t);return vn(n,Xe(n,hr(e))).join("")},kr.truncate=function(t,e){var i=30,n="...";if(Yo(e)){var s="separator"in e?e.separator:s;i="length"in e?la(e.length):i,n="omission"in e?on(e.omission):n}var o=(t=ga(t)).length;if(ir(t)){var a=hr(t);o=a.length}if(i>=o)return t;var c=i-cr(n);if(c<1)return n;var h=a?vn(a,0,c).join(""):t.slice(0,c);if(s===r)return h+n;if(a&&(c+=h.length-c),ra(s)){if(t.slice(c).search(s)){var u,l=h;for(s.global||(s=Mt(s.source,ga(ft.exec(s))+"g")),s.lastIndex=0;u=s.exec(l);)var f=u.index;h=h.slice(0,f===r?c:f)}}else if(t.indexOf(on(s),c)!=c){var d=h.lastIndexOf(s);d>-1&&(h=h.slice(0,d))}return h+n},kr.unescape=function(t){return(t=ga(t))&&J.test(t)?t.replace(V,lr):t},kr.uniqueId=function(t){var e=++Dt;return ga(t)+e},kr.upperCase=$a,kr.upperFirst=Ja,kr.each=go,kr.eachRight=mo,kr.first=Fs,nc(kr,function(){var t={};return yi(kr,(function(e,r){Tt.call(kr.prototype,r)||(t[r]=e)})),t}(),{chain:!1}),kr.VERSION="4.17.21",Se(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(t){kr[t].placeholder=kr})),Se(["drop","take"],(function(t,e){zr.prototype[t]=function(i){i=i===r?1:gr(la(i),0);var n=this.__filtered__&&!e?new zr(this):this.clone();return n.__filtered__?n.__takeCount__=mr(i,n.__takeCount__):n.__views__.push({size:mr(i,l),type:t+(n.__dir__<0?"Right":"")}),n},zr.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),Se(["filter","map","takeWhile"],(function(t,e){var r=e+1,i=1==r||3==r;zr.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:ss(t,3),type:r}),e.__filtered__=e.__filtered__||i,e}})),Se(["head","last"],(function(t,e){var r="take"+(e?"Right":"");zr.prototype[t]=function(){return this[r](1).value()[0]}})),Se(["initial","tail"],(function(t,e){var r="drop"+(e?"":"Right");zr.prototype[t]=function(){return this.__filtered__?new zr(this):this[r](1)}})),zr.prototype.compact=function(){return this.filter(tc)},zr.prototype.find=function(t){return this.filter(t).head()},zr.prototype.findLast=function(t){return this.reverse().find(t)},zr.prototype.invokeMap=$i((function(t,e){return"function"==typeof t?new zr(this):this.map((function(r){return Pi(r,t,e)}))})),zr.prototype.reject=function(t){return this.filter(Oo(ss(t)))},zr.prototype.slice=function(t,e){t=la(t);var i=this;return i.__filtered__&&(t>0||e<0)?new zr(i):(t<0?i=i.takeRight(-t):t&&(i=i.drop(t)),e!==r&&(i=(e=la(e))<0?i.dropRight(-e):i.take(e-t)),i)},zr.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},zr.prototype.toArray=function(){return this.take(l)},yi(zr.prototype,(function(t,e){var i=/^(?:filter|find|map|reject)|While$/.test(e),n=/^(?:head|last)$/.test(e),s=kr[n?"take"+("last"==e?"Right":""):e],o=n||/^find/.test(e);s&&(kr.prototype[e]=function(){var e=this.__wrapped__,a=n?[1]:arguments,c=e instanceof zr,h=a[0],u=c||zo(e),l=function(t){var e=s.apply(kr,Oe([t],a));return n&&f?e[0]:e};u&&i&&"function"==typeof h&&1!=h.length&&(c=u=!1);var f=this.__chain__,d=!!this.__actions__.length,p=o&&!f,g=c&&!d;if(!o&&u){e=g?e:new zr(this);var m=t.apply(e,a);return m.__actions__.push({func:ho,args:[l],thisArg:r}),new jr(m,f)}return p&&g?t.apply(this,a):(m=this.thru(l),p?n?m.value()[0]:m.value():m)})})),Se(["pop","push","shift","sort","splice","unshift"],(function(t){var e=Rt[t],r=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",i=/^(?:pop|shift)$/.test(t);kr.prototype[t]=function(){var t=arguments;if(i&&!this.__chain__){var n=this.value();return e.apply(zo(n)?n:[],t)}return this[r]((function(r){return e.apply(zo(r)?r:[],t)}))}})),yi(zr.prototype,(function(t,e){var r=kr[e];if(r){var i=r.name+"";Tt.call(xr,i)||(xr[i]=[]),xr[i].push({name:e,func:r})}})),xr[kn(r,2).name]=[{name:"wrapper",func:r}],zr.prototype.clone=function(){var t=new zr(this.__wrapped__);return t.__actions__=Mn(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=Mn(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=Mn(this.__views__),t},zr.prototype.reverse=function(){if(this.__filtered__){var t=new zr(this);t.__dir__=-1,t.__filtered__=!0}else(t=this.clone()).__dir__*=-1;return t},zr.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,r=zo(t),i=e<0,n=r?t.length:0,s=function(t,e,r){for(var i=-1,n=r.length;++i<n;){var s=r[i],o=s.size;switch(s.type){case"drop":t+=o;break;case"dropRight":e-=o;break;case"take":e=mr(e,t+o);break;case"takeRight":t=gr(t,e-o)}}return{start:t,end:e}}(0,n,this.__views__),o=s.start,a=s.end,c=a-o,h=i?a:o-1,u=this.__iteratees__,l=u.length,f=0,d=mr(c,this.__takeCount__);if(!r||!i&&n==c&&d==c)return ln(t,this.__actions__);var p=[];t:for(;c--&&f<d;){for(var g=-1,m=t[h+=e];++g<l;){var y=u[g],v=y.iteratee,w=y.type,b=v(m);if(2==w)m=b;else if(!b){if(1==w)continue t;break t}}p[f++]=m}return p},kr.prototype.at=uo,kr.prototype.chain=function(){return co(this)},kr.prototype.commit=function(){return new jr(this.value(),this.__chain__)},kr.prototype.next=function(){this.__values__===r&&(this.__values__=ha(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?r:this.__values__[this.__index__++]}},kr.prototype.plant=function(t){for(var e,i=this;i instanceof Lr;){var n=Ds(i);n.__index__=0,n.__values__=r,e?s.__wrapped__=n:e=n;var s=n;i=i.__wrapped__}return s.__wrapped__=t,e},kr.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof zr){var e=t;return this.__actions__.length&&(e=new zr(this)),(e=e.reverse()).__actions__.push({func:ho,args:[Qs],thisArg:r}),new jr(e,this.__chain__)}return this.thru(Qs)},kr.prototype.toJSON=kr.prototype.valueOf=kr.prototype.value=function(){return ln(this.__wrapped__,this.__actions__)},kr.prototype.first=kr.prototype.head,Xt&&(kr.prototype[Xt]=function(){return this}),kr}();de?((de.exports=fr)._=fr,fe._=fr):le._=fr}).call(Jd)}(Gd,Gd.exports);var Wd=Object.defineProperty,Qd=Object.defineProperties,Yd=Object.getOwnPropertyDescriptors,Xd=Object.getOwnPropertySymbols,Zd=Object.prototype.hasOwnProperty,tp=Object.prototype.propertyIsEnumerable,ep=(t,e,r)=>e in t?Wd(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,rp=(t,e)=>{for(var r in e||(e={}))Zd.call(e,r)&&ep(t,r,e[r]);if(Xd)for(var r of Xd(e))tp.call(e,r)&&ep(t,r,e[r]);return t},ip=(t,e)=>Qd(t,Yd(e));function np(t,e,r){var i;const n=xe(t);return(null==(i=e.rpcMap)?void 0:i[n.reference])||`https://rpc.walletconnect.com/v1/?chainId=${n.namespace}:${n.reference}&projectId=${r}`}function sp(t){return t.includes(":")?t.split(":")[1]:t}function op(t){return t.map((t=>`${t.split(":")[0]}:${t.split(":")[1]}`))}function ap(t={},e={}){const r=cp(t),i=cp(e);return Gd.exports.merge(r,i)}function cp(t){var e,r,i,n;const s={};if(!wa(t))return s;for(const[o,a]of Object.entries(t)){const t=la(o)?[o]:a.chains,c=a.methods||[],h=a.events||[],u=a.rpcMap||{},l=fa(o);s[l]=ip(rp(rp({},s[l]),a),{chains:er(t,null==(e=s[l])?void 0:e.chains),methods:er(c,null==(r=s[l])?void 0:r.methods),events:er(h,null==(i=s[l])?void 0:i.events),rpcMap:rp(rp({},u),null==(n=s[l])?void 0:n.rpcMap)})}return s}function hp(t){return t.includes(":")?t.split(":")[2]:t}function up(t){const e={};for(const[r,i]of Object.entries(t)){const t=i.methods||[],n=i.events||[],s=i.accounts||[],o=la(r)?[r]:i.chains?i.chains:op(i.accounts);e[r]={chains:o,methods:t,events:n,accounts:s}}return e}function lp(t){return"number"==typeof t?t:t.includes("0x")?parseInt(t,16):(t=t.includes(":")?t.split(":")[1]:t,isNaN(Number(t))?t:Number(t))}const fp={},dp=t=>fp[t],pp=(t,e)=>{fp[t]=e};class gp{constructor(t){this.name="polkadot",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit($d,`${this.name}:${t}`)}getAccounts(){const t=this.namespace.accounts;return t&&t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2]))||[]}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{var r;const i=sp(e);t[i]=this.createHttpProvider(i,null==(r=this.namespace.rpcMap)?void 0:r[e])})),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProvider(t,e){const r=e||np(t,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);return new du.F(new Kd.A(r,dp("disableProviderPing")))}}class mp{constructor(t){this.name="eip155",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.httpProviders=this.createHttpProviders(),this.chainId=parseInt(this.getDefaultChain())}async request(t){switch(t.request.method){case"eth_requestAccounts":case"eth_accounts":return this.getAccounts();case"wallet_switchEthereumChain":return await this.handleSwitchChain(t);case"eth_chainId":return parseInt(this.getDefaultChain())}return this.namespace.methods.includes(t.request.method)?await this.client.request(t):this.getHttpProvider().request(t.request)}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(parseInt(t),e),this.chainId=parseInt(t),this.events.emit($d,`${this.name}:${t}`)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId.toString();if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}createHttpProvider(t,e){const r=e||np(`${this.name}:${t}`,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);return new du.F(new Kd.V(r,dp("disableProviderPing")))}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{var r;const i=parseInt(sp(e));t[i]=this.createHttpProvider(i,null==(r=this.namespace.rpcMap)?void 0:r[e])})),t}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2])))]:[]}getHttpProvider(){const t=this.chainId,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}async handleSwitchChain(t){var e,r;let i=t.request.params?null==(e=t.request.params[0])?void 0:e.chainId:"0x0";i=i.startsWith("0x")?i:`0x${i}`;const n=parseInt(i,16);if(this.isChainApproved(n))this.setDefaultChain(`${n}`);else{if(!this.namespace.methods.includes("wallet_switchEthereumChain"))throw new Error(`Failed to switch to chain 'eip155:${n}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`);await this.client.request({topic:t.topic,request:{method:t.request.method,params:[{chainId:i}]},chainId:null==(r=this.namespace.chains)?void 0:r[0]}),this.setDefaultChain(`${n}`)}return null}isChainApproved(t){return this.namespace.chains.includes(`${this.name}:${t}`)}}class yp{constructor(t){this.name="solana",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit($d,`${this.name}:${t}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2])))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{var r;const i=sp(e);t[i]=this.createHttpProvider(i,null==(r=this.namespace.rpcMap)?void 0:r[e])})),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProvider(t,e){const r=e||np(t,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);return new du.F(new Kd.A(r,dp("disableProviderPing")))}}class vp{constructor(t){this.name="cosmos",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit($d,`${this.name}:${this.chainId}`)}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2])))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{var r;const i=sp(e);t[i]=this.createHttpProvider(i,null==(r=this.namespace.rpcMap)?void 0:r[e])})),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProvider(t,e){const r=e||np(t,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);return new du.F(new Kd.A(r,dp("disableProviderPing")))}}class wp{constructor(t){this.name="cip34",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit($d,`${this.name}:${this.chainId}`)}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2])))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{const r=this.getCardanoRPCUrl(e),i=sp(e);t[i]=this.createHttpProvider(i,r)})),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}getCardanoRPCUrl(t){const e=this.namespace.rpcMap;if(e)return e[t]}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProvider(t,e){const r=e||this.getCardanoRPCUrl(t);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);return new du.F(new Kd.A(r,dp("disableProviderPing")))}}class bp{constructor(t){this.name="elrond",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit($d,`${this.name}:${t}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2])))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{var r;const i=sp(e);t[i]=this.createHttpProvider(i,null==(r=this.namespace.rpcMap)?void 0:r[e])})),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProvider(t,e){const r=e||np(t,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);return new du.F(new Kd.A(r,dp("disableProviderPing")))}}class Ap{constructor(t){this.name="multiversx",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit($d,`${this.name}:${t}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2])))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{var r;const i=sp(e);t[i]=this.createHttpProvider(i,null==(r=this.namespace.rpcMap)?void 0:r[e])})),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProvider(t,e){const r=e||np(t,this.namespace,this.client.core.projectId);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);return new du.F(new Kd.A(r,dp("disableProviderPing")))}}class _p{constructor(t){this.name="near",this.namespace=t.namespace,this.events=dp("events"),this.client=dp("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){if(this.chainId=t,!this.httpProviders[t]){const r=e||np(`${this.name}:${t}`,this.namespace);if(!r)throw new Error(`No RPC url provided for chainId: ${t}`);this.setHttpProvider(t,r)}this.events.emit($d,`${this.name}:${this.chainId}`)}getAccounts(){const t=this.namespace.accounts;return t&&t.filter((t=>t.split(":")[1]===this.chainId.toString())).map((t=>t.split(":")[2]))||[]}createHttpProviders(){const t={};return this.namespace.chains.forEach((e=>{var r;t[e]=this.createHttpProvider(e,null==(r=this.namespace.rpcMap)?void 0:r[e])})),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const r=this.createHttpProvider(t,e);r&&(this.httpProviders[t]=r)}createHttpProvider(t,e){const r=e||np(t,this.namespace);return typeof r>"u"?void 0:new du.F(new Kd.A(r,dp("disableProviderPing")))}}var Ep=Object.defineProperty,Ip=Object.defineProperties,Sp=Object.getOwnPropertyDescriptors,Mp=Object.getOwnPropertySymbols,Pp=Object.prototype.hasOwnProperty,xp=Object.prototype.propertyIsEnumerable,Rp=(t,e,r)=>e in t?Ep(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Np=(t,e)=>{for(var r in e||(e={}))Pp.call(e,r)&&Rp(t,r,e[r]);if(Mp)for(var r of Mp(e))xp.call(e,r)&&Rp(t,r,e[r]);return t},Cp=(t,e)=>Ip(t,Sp(e));class Op{constructor(t){this.events=new(O()),this.rpcProviders={},this.shouldAbortPairingAttempt=!1,this.maxPairingAttempts=10,this.disableProviderPing=!1,this.providerOpts=t,this.logger=typeof t?.logger<"u"&&"string"!=typeof t?.logger?t.logger:(0,pc.h6)((0,pc.iP)({level:t?.logger||Hd})),this.disableProviderPing=t?.disableProviderPing||!1}static async init(t){const e=new Op(t);return await e.initialize(),e}async request(t,e,r){const[i,n]=this.validateChain(e);if(!this.session)throw new Error("Please call connect() before request()");return await this.getProvider(i).request({request:Np({},t),chainId:`${i}:${n}`,topic:this.session.topic,expiry:r})}sendAsync(t,e,r,i){const n=(new Date).getTime();this.request(t,r,i).then((t=>e(null,(0,pu.formatJsonRpcResult)(n,t)))).catch((t=>e(t,void 0)))}async enable(){if(!this.client)throw new Error("Sign Client not initialized");return this.session||await this.connect({namespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties}),await this.requestAccounts()}async disconnect(){var t;if(!this.session)throw new Error("Please call connect() before enable()");await this.client.disconnect({topic:null==(t=this.session)?void 0:t.topic,reason:ya("USER_DISCONNECTED")}),await this.cleanup()}async connect(t){if(!this.client)throw new Error("Sign Client not initialized");if(this.setNamespaces(t),await this.cleanupPendingPairings(),!t.skipPairing)return await this.pair(t.pairingTopic)}async authenticate(t){if(!this.client)throw new Error("Sign Client not initialized");this.setNamespaces(t),await this.cleanupPendingPairings();const{uri:e,response:r}=await this.client.authenticate(t);e&&(this.uri=e,this.events.emit("display_uri",e));const i=await r();if(this.session=i.session,this.session){const t=up(this.session.namespaces);this.namespaces=ap(this.namespaces,t),this.persist("namespaces",this.namespaces),this.onConnect()}return i}on(t,e){this.events.on(t,e)}once(t,e){this.events.once(t,e)}removeListener(t,e){this.events.removeListener(t,e)}off(t,e){this.events.off(t,e)}get isWalletConnect(){return!0}async pair(t){this.shouldAbortPairingAttempt=!1;let e=0;do{if(this.shouldAbortPairingAttempt)throw new Error("Pairing aborted");if(e>=this.maxPairingAttempts)throw new Error("Max auto pairing attempts reached");const{uri:r,approval:i}=await this.client.connect({pairingTopic:t,requiredNamespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties});r&&(this.uri=r,this.events.emit("display_uri",r)),await i().then((t=>{this.session=t;const e=up(t.namespaces);this.namespaces=ap(this.namespaces,e),this.persist("namespaces",this.namespaces)})).catch((t=>{if(t.message!==md)throw t;e++}))}while(!this.session);return this.onConnect(),this.session}setDefaultChain(t,e){try{if(!this.session)return;const[r,i]=this.validateChain(t);this.getProvider(r).setDefaultChain(i,e)}catch(t){if(!/Please call connect/.test(t.message))throw t}}async cleanupPendingPairings(t={}){this.logger.info("Cleaning up inactive pairings...");const e=this.client.pairing.getAll();if(va(e)){for(const r of e)t.deletePairings?this.client.core.expirer.set(r.topic,0):await this.client.core.relayer.subscriber.unsubscribe(r.topic);this.logger.info(`Inactive pairings cleared: ${e.length}`)}}abortPairingAttempt(){this.shouldAbortPairingAttempt=!0}async checkStorage(){if(this.namespaces=await this.getFromStore("namespaces"),this.optionalNamespaces=await this.getFromStore("optionalNamespaces")||{},this.client.session.length){const t=this.client.session.keys.length-1;this.session=this.client.session.get(this.client.session.keys[t]),this.createProviders()}}async initialize(){this.logger.trace("Initialized"),await this.createClient(),await this.checkStorage(),this.registerEventListeners()}async createClient(){this.client=this.providerOpts.client||await Fd.init({logger:this.providerOpts.logger||Hd,relayUrl:this.providerOpts.relayUrl||"wss://relay.walletconnect.com",projectId:this.providerOpts.projectId,metadata:this.providerOpts.metadata,storageOptions:this.providerOpts.storageOptions,storage:this.providerOpts.storage,name:this.providerOpts.name}),this.logger.trace("SignClient Initialized")}createProviders(){if(!this.client)throw new Error("Sign Client not initialized");if(!this.session)throw new Error("Session not initialized. Please call connect() before enable()");const t=[...new Set(Object.keys(this.session.namespaces).map((t=>fa(t))))];pp("client",this.client),pp("events",this.events),pp("disableProviderPing",this.disableProviderPing),t.forEach((t=>{if(!this.session)return;const e=function(t,e){const r=Object.keys(e.namespaces).filter((e=>e.includes(t)));if(!r.length)return[];const i=[];return r.forEach((t=>{const r=e.namespaces[t].accounts;i.push(...r)})),i}(t,this.session),r=op(e),i=ap(this.namespaces,this.optionalNamespaces),n=Cp(Np({},i[t]),{accounts:e,chains:r});switch(t){case"eip155":this.rpcProviders[t]=new mp({namespace:n});break;case"solana":this.rpcProviders[t]=new yp({namespace:n});break;case"cosmos":this.rpcProviders[t]=new vp({namespace:n});break;case"polkadot":this.rpcProviders[t]=new gp({namespace:n});break;case"cip34":this.rpcProviders[t]=new wp({namespace:n});break;case"elrond":this.rpcProviders[t]=new bp({namespace:n});break;case"multiversx":this.rpcProviders[t]=new Ap({namespace:n});break;case"near":this.rpcProviders[t]=new _p({namespace:n})}}))}registerEventListeners(){if(typeof this.client>"u")throw new Error("Sign Client is not initialized");this.client.on("session_ping",(t=>{this.events.emit("session_ping",t)})),this.client.on("session_event",(t=>{const{params:e}=t,{event:r}=e;if("accountsChanged"===r.name){const t=r.data;t&&va(t)&&this.events.emit("accountsChanged",t.map(hp))}else if("chainChanged"===r.name){const t=e.chainId,r=e.event.data,i=fa(t),n=lp(t)!==lp(r)?`${i}:${lp(r)}`:t;this.onChainChanged(n)}else this.events.emit(r.name,r.data);this.events.emit("session_event",t)})),this.client.on("session_update",(({topic:t,params:e})=>{var r;const{namespaces:i}=e,n=null==(r=this.client)?void 0:r.session.get(t);this.session=Cp(Np({},n),{namespaces:i}),this.onSessionUpdate(),this.events.emit("session_update",{topic:t,params:e})})),this.client.on("session_delete",(async t=>{await this.cleanup(),this.events.emit("session_delete",t),this.events.emit("disconnect",Cp(Np({},ya("USER_DISCONNECTED")),{data:t.topic}))})),this.on($d,(t=>{this.onChainChanged(t,!0)}))}getProvider(t){if(!this.rpcProviders[t])throw new Error(`Provider not found: ${t}`);return this.rpcProviders[t]}onSessionUpdate(){Object.keys(this.rpcProviders).forEach((t=>{var e;this.getProvider(t).updateNamespace(null==(e=this.session)?void 0:e.namespaces[t])}))}setNamespaces(t){const{namespaces:e,optionalNamespaces:r,sessionProperties:i}=t;e&&Object.keys(e).length&&(this.namespaces=e),r&&Object.keys(r).length&&(this.optionalNamespaces=r),this.sessionProperties=i,this.persist("namespaces",e),this.persist("optionalNamespaces",r)}validateChain(t){const[e,r]=t?.split(":")||["",""];if(!this.namespaces||!Object.keys(this.namespaces).length)return[e,r];if(e&&!Object.keys(this.namespaces||{}).map((t=>fa(t))).includes(e))throw new Error(`Namespace '${e}' is not configured. Please call connect() first with namespace config.`);if(e&&r)return[e,r];const i=fa(Object.keys(this.namespaces)[0]);return[i,this.rpcProviders[i].getDefaultChain()]}async requestAccounts(){const[t]=this.validateChain();return await this.getProvider(t).requestAccounts()}onChainChanged(t,e=!1){if(!this.namespaces)return;const[r,i]=this.validateChain(t);i&&(e||this.getProvider(r).setDefaultChain(i),this.namespaces[r]?this.namespaces[r].defaultChain=i:this.namespaces[`${r}:${i}`]?this.namespaces[`${r}:${i}`].defaultChain=i:this.namespaces[`${r}:${i}`]={defaultChain:i},this.persist("namespaces",this.namespaces),this.events.emit("chainChanged",i))}onConnect(){this.createProviders(),this.events.emit("connect",{session:this.session})}async cleanup(){this.session=void 0,this.namespaces=void 0,this.optionalNamespaces=void 0,this.sessionProperties=void 0,this.persist("namespaces",void 0),this.persist("optionalNamespaces",void 0),this.persist("sessionProperties",void 0),await this.cleanupPendingPairings({deletePairings:!0})}persist(t,e){this.client.core.storage.setItem(`${Vd}/${t}`,e)}async getFromStore(t){return await this.client.core.storage.getItem(`${Vd}/${t}`)}}const Up=Op,Tp=["eth_sendTransaction","personal_sign"],Dp=["eth_accounts","eth_requestAccounts","eth_sendRawTransaction","eth_sign","eth_signTransaction","eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4","eth_sendTransaction","personal_sign","wallet_switchEthereumChain","wallet_addEthereumChain","wallet_getPermissions","wallet_requestPermissions","wallet_registerOnboarding","wallet_watchAsset","wallet_scanQRCode","wallet_sendCalls","wallet_getCapabilities","wallet_getCallsStatus","wallet_showCallsStatus"],Bp=["chainChanged","accountsChanged"],kp=["chainChanged","accountsChanged","message","disconnect","connect"];var qp=Object.defineProperty,Lp=Object.defineProperties,jp=Object.getOwnPropertyDescriptors,zp=Object.getOwnPropertySymbols,Fp=Object.prototype.hasOwnProperty,Kp=Object.prototype.propertyIsEnumerable,Hp=(t,e,r)=>e in t?qp(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Vp=(t,e)=>{for(var r in e||(e={}))Fp.call(e,r)&&Hp(t,r,e[r]);if(zp)for(var r of zp(e))Kp.call(e,r)&&Hp(t,r,e[r]);return t},$p=(t,e)=>Lp(t,jp(e));function Jp(t){return Number(t[0].split(":")[1])}function Gp(t){return`0x${t.toString(16)}`}class Wp{constructor(){this.events=new C.EventEmitter,this.namespace="eip155",this.accounts=[],this.chainId=1,this.STORAGE_KEY="wc@2:ethereum_provider:",this.on=(t,e)=>(this.events.on(t,e),this),this.once=(t,e)=>(this.events.once(t,e),this),this.removeListener=(t,e)=>(this.events.removeListener(t,e),this),this.off=(t,e)=>(this.events.off(t,e),this),this.parseAccount=t=>this.isCompatibleChainId(t)?this.parseAccountId(t).address:t,this.signer={},this.rpc={}}static async init(t){const e=new Wp;return await e.initialize(t),e}async request(t,e){return await this.signer.request(t,this.formatChainId(this.chainId),e)}sendAsync(t,e,r){this.signer.sendAsync(t,e,this.formatChainId(this.chainId),r)}get connected(){return!!this.signer.client&&this.signer.client.core.relayer.connected}get connecting(){return!!this.signer.client&&this.signer.client.core.relayer.connecting}async enable(){return this.session||await this.connect(),await this.request({method:"eth_requestAccounts"})}async connect(t){if(!this.signer.client)throw new Error("Provider not initialized. Call init() first");this.loadConnectOpts(t);const{required:e,optional:r}=function(t){const{chains:e,optionalChains:r,methods:i,optionalMethods:n,events:s,optionalEvents:o,rpcMap:a}=t;if(!va(e))throw new Error("Invalid chains");const c={chains:e,methods:i||Tp,events:s||Bp,rpcMap:Vp({},e.length?{[Jp(e)]:a[Jp(e)]}:{})},h=s?.filter((t=>!Bp.includes(t))),u=i?.filter((t=>!Tp.includes(t)));if(!(r||o||n||null!=h&&h.length||null!=u&&u.length))return{required:e.length?c:void 0};const l={chains:[...new Set(h?.length&&u?.length||!r?c.chains.concat(r||[]):r)],methods:[...new Set(c.methods.concat(null!=n&&n.length?n:Dp))],events:[...new Set(c.events.concat(null!=o&&o.length?o:kp))],rpcMap:a};return{required:e.length?c:void 0,optional:r.length?l:void 0}}(this.rpc);try{const i=await new Promise((async(i,n)=>{var s;this.rpc.showQrModal&&(null==(s=this.modal)||s.subscribeModal((t=>{!t.open&&!this.signer.session&&(this.signer.abortPairingAttempt(),n(new Error("Connection request reset. Please try again.")))}))),await this.signer.connect($p(Vp({namespaces:Vp({},e&&{[this.namespace]:e})},r&&{optionalNamespaces:{[this.namespace]:r}}),{pairingTopic:t?.pairingTopic})).then((t=>{i(t)})).catch((t=>{n(new Error(t.message))}))}));if(!i)return;const n=Re(i.namespaces,[this.namespace]);this.setChainIds(this.rpc.chains.length?this.rpc.chains:n),this.setAccounts(n),this.events.emit("connect",{chainId:Gp(this.chainId)})}catch(t){throw this.signer.logger.error(t),t}finally{this.modal&&this.modal.closeModal()}}async authenticate(t){if(!this.signer.client)throw new Error("Provider not initialized. Call init() first");this.loadConnectOpts({chains:t?.chains});try{const e=await new Promise((async(e,r)=>{var i;this.rpc.showQrModal&&(null==(i=this.modal)||i.subscribeModal((t=>{!t.open&&!this.signer.session&&(this.signer.abortPairingAttempt(),r(new Error("Connection request reset. Please try again.")))}))),await this.signer.authenticate($p(Vp({},t),{chains:this.rpc.chains})).then((t=>{e(t)})).catch((t=>{r(new Error(t.message))}))})),r=e.session;if(r){const t=Re(r.namespaces,[this.namespace]);this.setChainIds(this.rpc.chains.length?this.rpc.chains:t),this.setAccounts(t),this.events.emit("connect",{chainId:Gp(this.chainId)})}return e}catch(t){throw this.signer.logger.error(t),t}finally{this.modal&&this.modal.closeModal()}}async disconnect(){this.session&&await this.signer.disconnect(),this.reset()}get isWalletConnect(){return!0}get session(){return this.signer.session}registerEventListeners(){this.signer.on("session_event",(t=>{const{params:e}=t,{event:r}=e;"accountsChanged"===r.name?(this.accounts=this.parseAccounts(r.data),this.events.emit("accountsChanged",this.accounts)):"chainChanged"===r.name?this.setChainId(this.formatChainId(r.data)):this.events.emit(r.name,r.data),this.events.emit("session_event",t)})),this.signer.on("chainChanged",(t=>{const e=parseInt(t);this.chainId=e,this.events.emit("chainChanged",Gp(this.chainId)),this.persist()})),this.signer.on("session_update",(t=>{this.events.emit("session_update",t)})),this.signer.on("session_delete",(t=>{this.reset(),this.events.emit("session_delete",t),this.events.emit("disconnect",$p(Vp({},ya("USER_DISCONNECTED")),{data:t.topic,name:"USER_DISCONNECTED"}))})),this.signer.on("display_uri",(t=>{var e,r;this.rpc.showQrModal&&(null==(e=this.modal)||e.closeModal(),null==(r=this.modal)||r.openModal({uri:t})),this.events.emit("display_uri",t)}))}switchEthereumChain(t){this.request({method:"wallet_switchEthereumChain",params:[{chainId:t.toString(16)}]})}isCompatibleChainId(t){return"string"==typeof t&&t.startsWith(`${this.namespace}:`)}formatChainId(t){return`${this.namespace}:${t}`}parseChainId(t){return Number(t.split(":")[1])}setChainIds(t){const e=t.filter((t=>this.isCompatibleChainId(t))).map((t=>this.parseChainId(t)));e.length&&(this.chainId=e[0],this.events.emit("chainChanged",Gp(this.chainId)),this.persist())}setChainId(t){if(this.isCompatibleChainId(t)){const e=this.parseChainId(t);this.chainId=e,this.switchEthereumChain(e)}}parseAccountId(t){const[e,r,i]=t.split(":");return{chainId:`${e}:${r}`,address:i}}setAccounts(t){this.accounts=t.filter((t=>this.parseChainId(this.parseAccountId(t).chainId)===this.chainId)).map((t=>this.parseAccountId(t).address)),this.events.emit("accountsChanged",this.accounts)}getRpcConfig(t){var e,r;const i=null!=(e=t?.chains)?e:[],n=null!=(r=t?.optionalChains)?r:[],s=i.concat(n);if(!s.length)throw new Error("No chains specified in either `chains` or `optionalChains`");const o=i.length?t?.methods||Tp:[],a=i.length?t?.events||Bp:[],c=t?.optionalMethods||[],h=t?.optionalEvents||[],u=t?.rpcMap||this.buildRpcMap(s,t.projectId),l=t?.qrModalOptions||void 0;return{chains:i?.map((t=>this.formatChainId(t))),optionalChains:n.map((t=>this.formatChainId(t))),methods:o,events:a,optionalMethods:c,optionalEvents:h,rpcMap:u,showQrModal:!(null==t||!t.showQrModal),qrModalOptions:l,projectId:t.projectId,metadata:t.metadata}}buildRpcMap(t,e){const r={};return t.forEach((t=>{r[t]=this.getRpcUrl(t,e)})),r}async initialize(t){if(this.rpc=this.getRpcConfig(t),this.chainId=this.rpc.chains.length?Jp(this.rpc.chains):Jp(this.rpc.optionalChains),this.signer=await Up.init({projectId:this.rpc.projectId,metadata:this.rpc.metadata,disableProviderPing:t.disableProviderPing,relayUrl:t.relayUrl,storageOptions:t.storageOptions}),this.registerEventListeners(),await this.loadPersistedSession(),this.rpc.showQrModal){let t;try{const{WalletConnectModal:e}=await r.e(940).then(r.bind(r,80940));t=e}catch{throw new Error("To use QR modal, please install @walletconnect/modal package")}if(t)try{this.modal=new t(Vp({projectId:this.rpc.projectId},this.rpc.qrModalOptions))}catch(t){throw this.signer.logger.error(t),new Error("Could not generate WalletConnectModal Instance")}}}loadConnectOpts(t){if(!t)return;const{chains:e,optionalChains:r,rpcMap:i}=t;e&&va(e)&&(this.rpc.chains=e.map((t=>this.formatChainId(t))),e.forEach((t=>{this.rpc.rpcMap[t]=i?.[t]||this.getRpcUrl(t)}))),r&&va(r)&&(this.rpc.optionalChains=[],this.rpc.optionalChains=r?.map((t=>this.formatChainId(t))),r.forEach((t=>{this.rpc.rpcMap[t]=i?.[t]||this.getRpcUrl(t)})))}getRpcUrl(t,e){var r;return(null==(r=this.rpc.rpcMap)?void 0:r[t])||`https://rpc.walletconnect.com/v1/?chainId=eip155:${t}&projectId=${e||this.rpc.projectId}`}async loadPersistedSession(){if(this.session)try{const t=await this.signer.client.core.storage.getItem(`${this.STORAGE_KEY}/chainId`),e=this.session.namespaces[`${this.namespace}:${t}`]?this.session.namespaces[`${this.namespace}:${t}`]:this.session.namespaces[this.namespace];this.setChainIds(t?[this.formatChainId(t)]:e?.accounts),this.setAccounts(e?.accounts)}catch(t){this.signer.logger.error("Failed to load persisted session, clearing state..."),this.signer.logger.error(t),await this.disconnect().catch((t=>this.signer.logger.warn(t)))}}reset(){this.chainId=1,this.accounts=[]}persist(){this.session&&this.signer.client.core.storage.setItem(`${this.STORAGE_KEY}/chainId`,this.chainId)}parseAccounts(t){return"string"==typeof t||t instanceof String?[this.parseAccount(t)]:t.map((t=>this.parseAccount(t)))}}const Qp=Wp},90796:t=>{"use strict";t.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},55665:()=>{},16410:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=r(93798);function n(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>8,e[r+1]=t>>>0,e}function s(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e}function o(t,e){return void 0===e&&(e=0),t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}function a(t,e){return void 0===e&&(e=0),(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}function c(t,e){return void 0===e&&(e=0),t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e]}function h(t,e){return void 0===e&&(e=0),(t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e])>>>0}function u(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>24,e[r+1]=t>>>16,e[r+2]=t>>>8,e[r+3]=t>>>0,e}function l(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e[r+2]=t>>>16,e[r+3]=t>>>24,e}function f(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),u(t/4294967296>>>0,e,r),u(t>>>0,e,r+4),e}function d(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),l(t>>>0,e,r),l(t/4294967296>>>0,e,r+4),e}e.readInt16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])<<16>>16},e.readUint16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])>>>0},e.readInt16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])<<16>>16},e.readUint16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])>>>0},e.writeUint16BE=n,e.writeInt16BE=n,e.writeUint16LE=s,e.writeInt16LE=s,e.readInt32BE=o,e.readUint32BE=a,e.readInt32LE=c,e.readUint32LE=h,e.writeUint32BE=u,e.writeInt32BE=u,e.writeUint32LE=l,e.writeInt32LE=l,e.readInt64BE=function(t,e){void 0===e&&(e=0);var r=o(t,e),i=o(t,e+4);return 4294967296*r+i-4294967296*(i>>31)},e.readUint64BE=function(t,e){return void 0===e&&(e=0),4294967296*a(t,e)+a(t,e+4)},e.readInt64LE=function(t,e){void 0===e&&(e=0);var r=c(t,e);return 4294967296*c(t,e+4)+r-4294967296*(r>>31)},e.readUint64LE=function(t,e){void 0===e&&(e=0);var r=h(t,e);return 4294967296*h(t,e+4)+r},e.writeUint64BE=f,e.writeInt64BE=f,e.writeUint64LE=d,e.writeInt64LE=d,e.readUintBE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintBE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintBE: array is too short for the given bitLength");for(var i=0,n=1,s=t/8+r-1;s>=r;s--)i+=e[s]*n,n*=256;return i},e.readUintLE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintLE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintLE: array is too short for the given bitLength");for(var i=0,n=1,s=r;s<r+t/8;s++)i+=e[s]*n,n*=256;return i},e.writeUintBE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintBE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintBE value must be an integer");for(var s=1,o=t/8+n-1;o>=n;o--)r[o]=e/s&255,s*=256;return r},e.writeUintLE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintLE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintLE value must be an integer");for(var s=1,o=n;o<n+t/8;o++)r[o]=e/s&255,s*=256;return r},e.readFloat32BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e)},e.readFloat32LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e,!0)},e.readFloat64BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e)},e.readFloat64LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e,!0)},e.writeFloat32BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t),e},e.writeFloat32LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t,!0),e},e.writeFloat64BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t),e},e.writeFloat64LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t,!0),e}},37070:(t,e,r)=>{"use strict";e._S=e.K=e.TP=e.wE=e.Ee=void 0;r(74450);const i=r(64974);r(58602);function n(t){const e=new Float64Array(16);if(t)for(let r=0;r<t.length;r++)e[r]=t[r];return e}e.Ee=64,e.wE=64,e.TP=32,new Uint8Array(32)[0]=9;const s=n(),o=n([1]),a=(n([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),n([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222])),c=n([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),h=n([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]);n([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function u(t,e){for(let r=0;r<16;r++)t[r]=0|e[r]}function l(t){let e=1;for(let r=0;r<16;r++){let i=t[r]+e+65535;e=Math.floor(i/65536),t[r]=i-65536*e}t[0]+=e-1+37*(e-1)}function f(t,e,r){const i=~(r-1);for(let r=0;r<16;r++){const n=i&(t[r]^e[r]);t[r]^=n,e[r]^=n}}function d(t,e){const r=n(),i=n();for(let t=0;t<16;t++)i[t]=e[t];l(i),l(i),l(i);for(let t=0;t<2;t++){r[0]=i[0]-65517;for(let t=1;t<15;t++)r[t]=i[t]-65535-(r[t-1]>>16&1),r[t-1]&=65535;r[15]=i[15]-32767-(r[14]>>16&1);const t=r[15]>>16&1;r[14]&=65535,f(i,r,1-t)}for(let e=0;e<16;e++)t[2*e]=255&i[e],t[2*e+1]=i[e]>>8}function p(t){const e=new Uint8Array(32);return d(e,t),1&e[0]}function g(t,e,r){for(let i=0;i<16;i++)t[i]=e[i]+r[i]}function m(t,e,r){for(let i=0;i<16;i++)t[i]=e[i]-r[i]}function y(t,e,r){let i,n,s=0,o=0,a=0,c=0,h=0,u=0,l=0,f=0,d=0,p=0,g=0,m=0,y=0,v=0,w=0,b=0,A=0,_=0,E=0,I=0,S=0,M=0,P=0,x=0,R=0,N=0,C=0,O=0,U=0,T=0,D=0,B=r[0],k=r[1],q=r[2],L=r[3],j=r[4],z=r[5],F=r[6],K=r[7],H=r[8],V=r[9],$=r[10],J=r[11],G=r[12],W=r[13],Q=r[14],Y=r[15];i=e[0],s+=i*B,o+=i*k,a+=i*q,c+=i*L,h+=i*j,u+=i*z,l+=i*F,f+=i*K,d+=i*H,p+=i*V,g+=i*$,m+=i*J,y+=i*G,v+=i*W,w+=i*Q,b+=i*Y,i=e[1],o+=i*B,a+=i*k,c+=i*q,h+=i*L,u+=i*j,l+=i*z,f+=i*F,d+=i*K,p+=i*H,g+=i*V,m+=i*$,y+=i*J,v+=i*G,w+=i*W,b+=i*Q,A+=i*Y,i=e[2],a+=i*B,c+=i*k,h+=i*q,u+=i*L,l+=i*j,f+=i*z,d+=i*F,p+=i*K,g+=i*H,m+=i*V,y+=i*$,v+=i*J,w+=i*G,b+=i*W,A+=i*Q,_+=i*Y,i=e[3],c+=i*B,h+=i*k,u+=i*q,l+=i*L,f+=i*j,d+=i*z,p+=i*F,g+=i*K,m+=i*H,y+=i*V,v+=i*$,w+=i*J,b+=i*G,A+=i*W,_+=i*Q,E+=i*Y,i=e[4],h+=i*B,u+=i*k,l+=i*q,f+=i*L,d+=i*j,p+=i*z,g+=i*F,m+=i*K,y+=i*H,v+=i*V,w+=i*$,b+=i*J,A+=i*G,_+=i*W,E+=i*Q,I+=i*Y,i=e[5],u+=i*B,l+=i*k,f+=i*q,d+=i*L,p+=i*j,g+=i*z,m+=i*F,y+=i*K,v+=i*H,w+=i*V,b+=i*$,A+=i*J,_+=i*G,E+=i*W,I+=i*Q,S+=i*Y,i=e[6],l+=i*B,f+=i*k,d+=i*q,p+=i*L,g+=i*j,m+=i*z,y+=i*F,v+=i*K,w+=i*H,b+=i*V,A+=i*$,_+=i*J,E+=i*G,I+=i*W,S+=i*Q,M+=i*Y,i=e[7],f+=i*B,d+=i*k,p+=i*q,g+=i*L,m+=i*j,y+=i*z,v+=i*F,w+=i*K,b+=i*H,A+=i*V,_+=i*$,E+=i*J,I+=i*G,S+=i*W,M+=i*Q,P+=i*Y,i=e[8],d+=i*B,p+=i*k,g+=i*q,m+=i*L,y+=i*j,v+=i*z,w+=i*F,b+=i*K,A+=i*H,_+=i*V,E+=i*$,I+=i*J,S+=i*G,M+=i*W,P+=i*Q,x+=i*Y,i=e[9],p+=i*B,g+=i*k,m+=i*q,y+=i*L,v+=i*j,w+=i*z,b+=i*F,A+=i*K,_+=i*H,E+=i*V,I+=i*$,S+=i*J,M+=i*G,P+=i*W,x+=i*Q,R+=i*Y,i=e[10],g+=i*B,m+=i*k,y+=i*q,v+=i*L,w+=i*j,b+=i*z,A+=i*F,_+=i*K,E+=i*H,I+=i*V,S+=i*$,M+=i*J,P+=i*G,x+=i*W,R+=i*Q,N+=i*Y,i=e[11],m+=i*B,y+=i*k,v+=i*q,w+=i*L,b+=i*j,A+=i*z,_+=i*F,E+=i*K,I+=i*H,S+=i*V,M+=i*$,P+=i*J,x+=i*G,R+=i*W,N+=i*Q,C+=i*Y,i=e[12],y+=i*B,v+=i*k,w+=i*q,b+=i*L,A+=i*j,_+=i*z,E+=i*F,I+=i*K,S+=i*H,M+=i*V,P+=i*$,x+=i*J,R+=i*G,N+=i*W,C+=i*Q,O+=i*Y,i=e[13],v+=i*B,w+=i*k,b+=i*q,A+=i*L,_+=i*j,E+=i*z,I+=i*F,S+=i*K,M+=i*H,P+=i*V,x+=i*$,R+=i*J,N+=i*G,C+=i*W,O+=i*Q,U+=i*Y,i=e[14],w+=i*B,b+=i*k,A+=i*q,_+=i*L,E+=i*j,I+=i*z,S+=i*F,M+=i*K,P+=i*H,x+=i*V,R+=i*$,N+=i*J,C+=i*G,O+=i*W,U+=i*Q,T+=i*Y,i=e[15],b+=i*B,A+=i*k,_+=i*q,E+=i*L,I+=i*j,S+=i*z,M+=i*F,P+=i*K,x+=i*H,R+=i*V,N+=i*$,C+=i*J,O+=i*G,U+=i*W,T+=i*Q,D+=i*Y,s+=38*A,o+=38*_,a+=38*E,c+=38*I,h+=38*S,u+=38*M,l+=38*P,f+=38*x,d+=38*R,p+=38*N,g+=38*C,m+=38*O,y+=38*U,v+=38*T,w+=38*D,n=1,i=s+n+65535,n=Math.floor(i/65536),s=i-65536*n,i=o+n+65535,n=Math.floor(i/65536),o=i-65536*n,i=a+n+65535,n=Math.floor(i/65536),a=i-65536*n,i=c+n+65535,n=Math.floor(i/65536),c=i-65536*n,i=h+n+65535,n=Math.floor(i/65536),h=i-65536*n,i=u+n+65535,n=Math.floor(i/65536),u=i-65536*n,i=l+n+65535,n=Math.floor(i/65536),l=i-65536*n,i=f+n+65535,n=Math.floor(i/65536),f=i-65536*n,i=d+n+65535,n=Math.floor(i/65536),d=i-65536*n,i=p+n+65535,n=Math.floor(i/65536),p=i-65536*n,i=g+n+65535,n=Math.floor(i/65536),g=i-65536*n,i=m+n+65535,n=Math.floor(i/65536),m=i-65536*n,i=y+n+65535,n=Math.floor(i/65536),y=i-65536*n,i=v+n+65535,n=Math.floor(i/65536),v=i-65536*n,i=w+n+65535,n=Math.floor(i/65536),w=i-65536*n,i=b+n+65535,n=Math.floor(i/65536),b=i-65536*n,s+=n-1+37*(n-1),n=1,i=s+n+65535,n=Math.floor(i/65536),s=i-65536*n,i=o+n+65535,n=Math.floor(i/65536),o=i-65536*n,i=a+n+65535,n=Math.floor(i/65536),a=i-65536*n,i=c+n+65535,n=Math.floor(i/65536),c=i-65536*n,i=h+n+65535,n=Math.floor(i/65536),h=i-65536*n,i=u+n+65535,n=Math.floor(i/65536),u=i-65536*n,i=l+n+65535,n=Math.floor(i/65536),l=i-65536*n,i=f+n+65535,n=Math.floor(i/65536),f=i-65536*n,i=d+n+65535,n=Math.floor(i/65536),d=i-65536*n,i=p+n+65535,n=Math.floor(i/65536),p=i-65536*n,i=g+n+65535,n=Math.floor(i/65536),g=i-65536*n,i=m+n+65535,n=Math.floor(i/65536),m=i-65536*n,i=y+n+65535,n=Math.floor(i/65536),y=i-65536*n,i=v+n+65535,n=Math.floor(i/65536),v=i-65536*n,i=w+n+65535,n=Math.floor(i/65536),w=i-65536*n,i=b+n+65535,n=Math.floor(i/65536),b=i-65536*n,s+=n-1+37*(n-1),t[0]=s,t[1]=o,t[2]=a,t[3]=c,t[4]=h,t[5]=u,t[6]=l,t[7]=f,t[8]=d,t[9]=p,t[10]=g,t[11]=m,t[12]=y,t[13]=v,t[14]=w,t[15]=b}function v(t,e){y(t,e,e)}function w(t,e){const r=n(),i=n(),s=n(),o=n(),c=n(),h=n(),u=n(),l=n(),f=n();m(r,t[1],t[0]),m(f,e[1],e[0]),y(r,r,f),g(i,t[0],t[1]),g(f,e[0],e[1]),y(i,i,f),y(s,t[3],e[3]),y(s,s,a),y(o,t[2],e[2]),g(o,o,o),m(c,i,r),m(h,o,s),g(u,o,s),g(l,i,r),y(t[0],c,h),y(t[1],l,u),y(t[2],u,h),y(t[3],c,l)}function b(t,e,r){for(let i=0;i<4;i++)f(t[i],e[i],r)}function A(t,e){const r=n(),i=n(),s=n();(function(t,e){const r=n();let i;for(i=0;i<16;i++)r[i]=e[i];for(i=253;i>=0;i--)v(r,r),2!==i&&4!==i&&y(r,r,e);for(i=0;i<16;i++)t[i]=r[i]})(s,e[2]),y(r,e[0],s),y(i,e[1],s),d(t,i),t[31]^=p(r)<<7}function _(t,e){const r=[n(),n(),n(),n()];u(r[0],c),u(r[1],h),u(r[2],o),y(r[3],c,h),function(t,e,r){u(t[0],s),u(t[1],o),u(t[2],o),u(t[3],s);for(let i=255;i>=0;--i){const n=r[i/8|0]>>(7&i)&1;b(t,e,n),w(e,t),w(t,t),b(t,e,n)}}(t,r,e)}e.K=function(t){if(t.length!==e.TP)throw new Error(`ed25519: seed must be ${e.TP} bytes`);const r=(0,i.hash)(t);r[0]&=248,r[31]&=127,r[31]|=64;const s=new Uint8Array(32),o=[n(),n(),n(),n()];_(o,r),A(s,o);const a=new Uint8Array(64);return a.set(t),a.set(s,32),{publicKey:s,secretKey:a}};const E=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function I(t,e){let r,i,n,s;for(i=63;i>=32;--i){for(r=0,n=i-32,s=i-12;n<s;++n)e[n]+=r-16*e[i]*E[n-(i-32)],r=Math.floor((e[n]+128)/256),e[n]-=256*r;e[n]+=r,e[i]=0}for(r=0,n=0;n<32;n++)e[n]+=r-(e[31]>>4)*E[n],r=e[n]>>8,e[n]&=255;for(n=0;n<32;n++)e[n]-=r*E[n];for(i=0;i<32;i++)e[i+1]+=e[i]>>8,t[i]=255&e[i]}function S(t){const e=new Float64Array(64);for(let r=0;r<64;r++)e[r]=t[r];for(let e=0;e<64;e++)t[e]=0;I(t,e)}e._S=function(t,e){const r=new Float64Array(64),s=[n(),n(),n(),n()],o=(0,i.hash)(t.subarray(0,32));o[0]&=248,o[31]&=127,o[31]|=64;const a=new Uint8Array(64);a.set(o.subarray(32),32);const c=new i.SHA512;c.update(a.subarray(32)),c.update(e);const h=c.digest();c.clean(),S(h),_(s,h),A(a,s),c.reset(),c.update(a.subarray(0,32)),c.update(t.subarray(32)),c.update(e);const u=c.digest();S(u);for(let t=0;t<32;t++)r[t]=h[t];for(let t=0;t<32;t++)for(let e=0;e<32;e++)r[t+e]+=u[t]*o[e];return I(a.subarray(32),r),a}},93798:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.mul=Math.imul||function(t,e){var r=65535&t,i=65535&e;return r*i+((t>>>16&65535)*i+r*(e>>>16&65535)<<16>>>0)|0},e.add=function(t,e){return t+e|0},e.sub=function(t,e){return t-e|0},e.rotl=function(t,e){return t<<e|t>>>32-e},e.rotr=function(t,e){return t<<32-e|t>>>e},e.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},e.MAX_SAFE_INTEGER=9007199254740991,e.isSafeInteger=function(t){return e.isInteger(t)&&t>=-e.MAX_SAFE_INTEGER&&t<=e.MAX_SAFE_INTEGER}},74450:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.randomStringForEntropy=e.randomString=e.randomUint32=e.randomBytes=e.defaultRandomSource=void 0;const i=r(30446),n=r(16410),s=r(58602);function o(t,r=e.defaultRandomSource){return r.randomBytes(t)}e.defaultRandomSource=new i.SystemRandomSource,e.randomBytes=o,e.randomUint32=function(t=e.defaultRandomSource){const r=o(4,t),i=(0,n.readUint32LE)(r);return(0,s.wipe)(r),i};const a="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";function c(t,r=a,i=e.defaultRandomSource){if(r.length<2)throw new Error("randomString charset is too short");if(r.length>256)throw new Error("randomString charset is too long");let n="";const c=r.length,h=256-256%c;for(;t>0;){const e=o(Math.ceil(256*t/h),i);for(let i=0;i<e.length&&t>0;i++){const s=e[i];s<h&&(n+=r.charAt(s%c),t--)}(0,s.wipe)(e)}return n}e.randomString=c,e.randomStringForEntropy=function(t,r=a,i=e.defaultRandomSource){return c(Math.ceil(t/(Math.log(r.length)/Math.LN2)),r,i)}},67083:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BrowserRandomSource=void 0,e.BrowserRandomSource=class{constructor(){this.isAvailable=!1,this.isInstantiated=!1;const t="undefined"!=typeof self?self.crypto||self.msCrypto:null;t&&void 0!==t.getRandomValues&&(this._crypto=t,this.isAvailable=!0,this.isInstantiated=!0)}randomBytes(t){if(!this.isAvailable||!this._crypto)throw new Error("Browser random byte generator is not available.");const e=new Uint8Array(t);for(let t=0;t<e.length;t+=65536)this._crypto.getRandomValues(e.subarray(t,t+Math.min(e.length-t,65536)));return e}}},21991:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.NodeRandomSource=void 0;const i=r(58602);e.NodeRandomSource=class{constructor(){this.isAvailable=!1,this.isInstantiated=!1;{const t=r(40130);t&&t.randomBytes&&(this._crypto=t,this.isAvailable=!0,this.isInstantiated=!0)}}randomBytes(t){if(!this.isAvailable||!this._crypto)throw new Error("Node.js random byte generator is not available.");let e=this._crypto.randomBytes(t);if(e.length!==t)throw new Error("NodeRandomSource: got fewer bytes than requested");const r=new Uint8Array(t);for(let t=0;t<r.length;t++)r[t]=e[t];return(0,i.wipe)(e),r}}},30446:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SystemRandomSource=void 0;const i=r(67083),n=r(21991);e.SystemRandomSource=class{constructor(){return this.isAvailable=!1,this.name="",this._source=new i.BrowserRandomSource,this._source.isAvailable?(this.isAvailable=!0,void(this.name="Browser")):(this._source=new n.NodeRandomSource,this._source.isAvailable?(this.isAvailable=!0,void(this.name="Node")):void 0)}randomBytes(t){if(!this.isAvailable)throw new Error("System random byte generator is not available.");return this._source.randomBytes(t)}}},58602:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.wipe=function(t){for(var e=0;e<t.length;e++)t[e]=0;return t}},39879:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=r(65291);function n(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>8,e[r+1]=t>>>0,e}function s(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e}function o(t,e){return void 0===e&&(e=0),t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}function a(t,e){return void 0===e&&(e=0),(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}function c(t,e){return void 0===e&&(e=0),t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e]}function h(t,e){return void 0===e&&(e=0),(t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e])>>>0}function u(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>24,e[r+1]=t>>>16,e[r+2]=t>>>8,e[r+3]=t>>>0,e}function l(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e[r+2]=t>>>16,e[r+3]=t>>>24,e}function f(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),u(t/4294967296>>>0,e,r),u(t>>>0,e,r+4),e}function d(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),l(t>>>0,e,r),l(t/4294967296>>>0,e,r+4),e}e.readInt16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])<<16>>16},e.readUint16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])>>>0},e.readInt16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])<<16>>16},e.readUint16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])>>>0},e.writeUint16BE=n,e.writeInt16BE=n,e.writeUint16LE=s,e.writeInt16LE=s,e.readInt32BE=o,e.readUint32BE=a,e.readInt32LE=c,e.readUint32LE=h,e.writeUint32BE=u,e.writeInt32BE=u,e.writeUint32LE=l,e.writeInt32LE=l,e.readInt64BE=function(t,e){void 0===e&&(e=0);var r=o(t,e),i=o(t,e+4);return 4294967296*r+i-4294967296*(i>>31)},e.readUint64BE=function(t,e){return void 0===e&&(e=0),4294967296*a(t,e)+a(t,e+4)},e.readInt64LE=function(t,e){void 0===e&&(e=0);var r=c(t,e);return 4294967296*c(t,e+4)+r-4294967296*(r>>31)},e.readUint64LE=function(t,e){void 0===e&&(e=0);var r=h(t,e);return 4294967296*h(t,e+4)+r},e.writeUint64BE=f,e.writeInt64BE=f,e.writeUint64LE=d,e.writeInt64LE=d,e.readUintBE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintBE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintBE: array is too short for the given bitLength");for(var i=0,n=1,s=t/8+r-1;s>=r;s--)i+=e[s]*n,n*=256;return i},e.readUintLE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintLE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintLE: array is too short for the given bitLength");for(var i=0,n=1,s=r;s<r+t/8;s++)i+=e[s]*n,n*=256;return i},e.writeUintBE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintBE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintBE value must be an integer");for(var s=1,o=t/8+n-1;o>=n;o--)r[o]=e/s&255,s*=256;return r},e.writeUintLE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintLE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintLE value must be an integer");for(var s=1,o=n;o<n+t/8;o++)r[o]=e/s&255,s*=256;return r},e.readFloat32BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e)},e.readFloat32LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e,!0)},e.readFloat64BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e)},e.readFloat64LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e,!0)},e.writeFloat32BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t),e},e.writeFloat32LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t,!0),e},e.writeFloat64BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t),e},e.writeFloat64LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t,!0),e}},65291:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.mul=Math.imul||function(t,e){var r=65535&t,i=65535&e;return r*i+((t>>>16&65535)*i+r*(e>>>16&65535)<<16>>>0)|0},e.add=function(t,e){return t+e|0},e.sub=function(t,e){return t-e|0},e.rotl=function(t,e){return t<<e|t>>>32-e},e.rotr=function(t,e){return t<<32-e|t>>>e},e.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},e.MAX_SAFE_INTEGER=9007199254740991,e.isSafeInteger=function(t){return e.isInteger(t)&&t>=-e.MAX_SAFE_INTEGER&&t<=e.MAX_SAFE_INTEGER}},18359:(t,e,r)=>{"use strict";e.po=e.yE=void 0;const i=r(13329);r(39879),r(19167);function n(t,r=e.yE){return r.randomBytes(t)}e.yE=new i.SystemRandomSource,e.po=n},83142:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BrowserRandomSource=void 0,e.BrowserRandomSource=class{constructor(){this.isAvailable=!1,this.isInstantiated=!1;const t="undefined"!=typeof self?self.crypto||self.msCrypto:null;t&&void 0!==t.getRandomValues&&(this._crypto=t,this.isAvailable=!0,this.isInstantiated=!0)}randomBytes(t){if(!this.isAvailable||!this._crypto)throw new Error("Browser random byte generator is not available.");const e=new Uint8Array(t);for(let t=0;t<e.length;t+=65536)this._crypto.getRandomValues(e.subarray(t,t+Math.min(e.length-t,65536)));return e}}},16268:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.NodeRandomSource=void 0;const i=r(19167);e.NodeRandomSource=class{constructor(){this.isAvailable=!1,this.isInstantiated=!1;{const t=r(21099);t&&t.randomBytes&&(this._crypto=t,this.isAvailable=!0,this.isInstantiated=!0)}}randomBytes(t){if(!this.isAvailable||!this._crypto)throw new Error("Node.js random byte generator is not available.");let e=this._crypto.randomBytes(t);if(e.length!==t)throw new Error("NodeRandomSource: got fewer bytes than requested");const r=new Uint8Array(t);for(let t=0;t<r.length;t++)r[t]=e[t];return(0,i.wipe)(e),r}}},13329:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SystemRandomSource=void 0;const i=r(83142),n=r(16268);e.SystemRandomSource=class{constructor(){return this.isAvailable=!1,this.name="",this._source=new i.BrowserRandomSource,this._source.isAvailable?(this.isAvailable=!0,void(this.name="Browser")):(this._source=new n.NodeRandomSource,this._source.isAvailable?(this.isAvailable=!0,void(this.name="Node")):void 0)}randomBytes(t){if(!this.isAvailable)throw new Error("System random byte generator is not available.");return this._source.randomBytes(t)}}},19167:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.wipe=function(t){for(var e=0;e<t.length;e++)t[e]=0;return t}},50916:(t,e,r)=>{t.exports=self.fetch||(self.fetch=r(76782).default||r(76782))},8142:(t,e,r)=>{t=r.nmd(t);var i="__lodash_hash_undefined__",n=1,s=2,o=9007199254740991,a="[object Arguments]",c="[object Array]",h="[object AsyncFunction]",u="[object Boolean]",l="[object Date]",f="[object Error]",d="[object Function]",p="[object GeneratorFunction]",g="[object Map]",m="[object Number]",y="[object Null]",v="[object Object]",w="[object Promise]",b="[object Proxy]",A="[object RegExp]",_="[object Set]",E="[object String]",I="[object Undefined]",S="[object WeakMap]",M="[object ArrayBuffer]",P="[object DataView]",x=/^\[object .+?Constructor\]$/,R=/^(?:0|[1-9]\d*)$/,N={};N["[object Float32Array]"]=N["[object Float64Array]"]=N["[object Int8Array]"]=N["[object Int16Array]"]=N["[object Int32Array]"]=N["[object Uint8Array]"]=N["[object Uint8ClampedArray]"]=N["[object Uint16Array]"]=N["[object Uint32Array]"]=!0,N[a]=N[c]=N[M]=N[u]=N[P]=N[l]=N[f]=N[d]=N[g]=N[m]=N[v]=N[A]=N[_]=N[E]=N[S]=!1;var C="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,O="object"==typeof self&&self&&self.Object===Object&&self,U=C||O||Function("return this")(),T=e&&!e.nodeType&&e,D=T&&t&&!t.nodeType&&t,B=D&&D.exports===T,k=B&&C.process,q=function(){try{return k&&k.binding&&k.binding("util")}catch(t){}}(),L=q&&q.isTypedArray;function j(t,e){for(var r=-1,i=null==t?0:t.length;++r<i;)if(e(t[r],r,t))return!0;return!1}function z(t){var e=-1,r=Array(t.size);return t.forEach((function(t,i){r[++e]=[i,t]})),r}function F(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}var K,H,V,$=Array.prototype,J=Function.prototype,G=Object.prototype,W=U["__core-js_shared__"],Q=J.toString,Y=G.hasOwnProperty,X=(K=/[^.]+$/.exec(W&&W.keys&&W.keys.IE_PROTO||""))?"Symbol(src)_1."+K:"",Z=G.toString,tt=RegExp("^"+Q.call(Y).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),et=B?U.Buffer:void 0,rt=U.Symbol,it=U.Uint8Array,nt=G.propertyIsEnumerable,st=$.splice,ot=rt?rt.toStringTag:void 0,at=Object.getOwnPropertySymbols,ct=et?et.isBuffer:void 0,ht=(H=Object.keys,V=Object,function(t){return H(V(t))}),ut=Dt(U,"DataView"),lt=Dt(U,"Map"),ft=Dt(U,"Promise"),dt=Dt(U,"Set"),pt=Dt(U,"WeakMap"),gt=Dt(Object,"create"),mt=Lt(ut),yt=Lt(lt),vt=Lt(ft),wt=Lt(dt),bt=Lt(pt),At=rt?rt.prototype:void 0,_t=At?At.valueOf:void 0;function Et(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function It(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function St(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function Mt(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new St;++e<r;)this.add(t[e])}function Pt(t){var e=this.__data__=new It(t);this.size=e.size}function xt(t,e){for(var r=t.length;r--;)if(jt(t[r][0],e))return r;return-1}function Rt(t){return null==t?void 0===t?I:y:ot&&ot in Object(t)?function(t){var e=Y.call(t,ot),r=t[ot];try{t[ot]=void 0;var i=!0}catch(t){}var n=Z.call(t);return i&&(e?t[ot]=r:delete t[ot]),n}(t):function(t){return Z.call(t)}(t)}function Nt(t){return Jt(t)&&Rt(t)==a}function Ct(t,e,r,i,o){return t===e||(null==t||null==e||!Jt(t)&&!Jt(e)?t!=t&&e!=e:function(t,e,r,i,o,h){var d=Ft(t),p=Ft(e),y=d?c:kt(t),w=p?c:kt(e),b=(y=y==a?v:y)==v,I=(w=w==a?v:w)==v,S=y==w;if(S&&Kt(t)){if(!Kt(e))return!1;d=!0,b=!1}if(S&&!b)return h||(h=new Pt),d||Gt(t)?Ot(t,e,r,i,o,h):function(t,e,r,i,o,a,c){switch(r){case P:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case M:return!(t.byteLength!=e.byteLength||!a(new it(t),new it(e)));case u:case l:case m:return jt(+t,+e);case f:return t.name==e.name&&t.message==e.message;case A:case E:return t==e+"";case g:var h=z;case _:var d=i&n;if(h||(h=F),t.size!=e.size&&!d)return!1;var p=c.get(t);if(p)return p==e;i|=s,c.set(t,e);var y=Ot(h(t),h(e),i,o,a,c);return c.delete(t),y;case"[object Symbol]":if(_t)return _t.call(t)==_t.call(e)}return!1}(t,e,y,r,i,o,h);if(!(r&n)){var x=b&&Y.call(t,"__wrapped__"),R=I&&Y.call(e,"__wrapped__");if(x||R){var N=x?t.value():t,C=R?e.value():e;return h||(h=new Pt),o(N,C,r,i,h)}}return!!S&&(h||(h=new Pt),function(t,e,r,i,s,o){var a=r&n,c=Ut(t),h=c.length;if(h!=Ut(e).length&&!a)return!1;for(var u=h;u--;){var l=c[u];if(!(a?l in e:Y.call(e,l)))return!1}var f=o.get(t);if(f&&o.get(e))return f==e;var d=!0;o.set(t,e),o.set(e,t);for(var p=a;++u<h;){var g=t[l=c[u]],m=e[l];if(i)var y=a?i(m,g,l,e,t,o):i(g,m,l,t,e,o);if(!(void 0===y?g===m||s(g,m,r,i,o):y)){d=!1;break}p||(p="constructor"==l)}if(d&&!p){var v=t.constructor,w=e.constructor;v==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof w&&w instanceof w||(d=!1)}return o.delete(t),o.delete(e),d}(t,e,r,i,o,h))}(t,e,r,i,Ct,o))}function Ot(t,e,r,i,o,a){var c=r&n,h=t.length,u=e.length;if(h!=u&&!(c&&u>h))return!1;var l=a.get(t);if(l&&a.get(e))return l==e;var f=-1,d=!0,p=r&s?new Mt:void 0;for(a.set(t,e),a.set(e,t);++f<h;){var g=t[f],m=e[f];if(i)var y=c?i(m,g,f,e,t,a):i(g,m,f,t,e,a);if(void 0!==y){if(y)continue;d=!1;break}if(p){if(!j(e,(function(t,e){if(n=e,!p.has(n)&&(g===t||o(g,t,r,i,a)))return p.push(e);var n}))){d=!1;break}}else if(g!==m&&!o(g,m,r,i,a)){d=!1;break}}return a.delete(t),a.delete(e),d}function Ut(t){return function(t,e,r){var i=e(t);return Ft(t)?i:function(t,e){for(var r=-1,i=e.length,n=t.length;++r<i;)t[n+r]=e[r];return t}(i,r(t))}(t,Wt,Bt)}function Tt(t,e){var r,i,n=t.__data__;return("string"==(i=typeof(r=e))||"number"==i||"symbol"==i||"boolean"==i?"__proto__"!==r:null===r)?n["string"==typeof e?"string":"hash"]:n.map}function Dt(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!$t(t)||function(t){return!!X&&X in t}(t))&&(Ht(t)?tt:x).test(Lt(t))}(r)?r:void 0}Et.prototype.clear=function(){this.__data__=gt?gt(null):{},this.size=0},Et.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Et.prototype.get=function(t){var e=this.__data__;if(gt){var r=e[t];return r===i?void 0:r}return Y.call(e,t)?e[t]:void 0},Et.prototype.has=function(t){var e=this.__data__;return gt?void 0!==e[t]:Y.call(e,t)},Et.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=gt&&void 0===e?i:e,this},It.prototype.clear=function(){this.__data__=[],this.size=0},It.prototype.delete=function(t){var e=this.__data__,r=xt(e,t);return!(r<0||(r==e.length-1?e.pop():st.call(e,r,1),--this.size,0))},It.prototype.get=function(t){var e=this.__data__,r=xt(e,t);return r<0?void 0:e[r][1]},It.prototype.has=function(t){return xt(this.__data__,t)>-1},It.prototype.set=function(t,e){var r=this.__data__,i=xt(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this},St.prototype.clear=function(){this.size=0,this.__data__={hash:new Et,map:new(lt||It),string:new Et}},St.prototype.delete=function(t){var e=Tt(this,t).delete(t);return this.size-=e?1:0,e},St.prototype.get=function(t){return Tt(this,t).get(t)},St.prototype.has=function(t){return Tt(this,t).has(t)},St.prototype.set=function(t,e){var r=Tt(this,t),i=r.size;return r.set(t,e),this.size+=r.size==i?0:1,this},Mt.prototype.add=Mt.prototype.push=function(t){return this.__data__.set(t,i),this},Mt.prototype.has=function(t){return this.__data__.has(t)},Pt.prototype.clear=function(){this.__data__=new It,this.size=0},Pt.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},Pt.prototype.get=function(t){return this.__data__.get(t)},Pt.prototype.has=function(t){return this.__data__.has(t)},Pt.prototype.set=function(t,e){var r=this.__data__;if(r instanceof It){var i=r.__data__;if(!lt||i.length<199)return i.push([t,e]),this.size=++r.size,this;r=this.__data__=new St(i)}return r.set(t,e),this.size=r.size,this};var Bt=at?function(t){return null==t?[]:(t=Object(t),function(e){for(var r=-1,i=null==e?0:e.length,n=0,s=[];++r<i;){var o=e[r];a=o,nt.call(t,a)&&(s[n++]=o)}var a;return s}(at(t)))}:function(){return[]},kt=Rt;function qt(t,e){return!!(e=null==e?o:e)&&("number"==typeof t||R.test(t))&&t>-1&&t%1==0&&t<e}function Lt(t){if(null!=t){try{return Q.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function jt(t,e){return t===e||t!=t&&e!=e}(ut&&kt(new ut(new ArrayBuffer(1)))!=P||lt&&kt(new lt)!=g||ft&&kt(ft.resolve())!=w||dt&&kt(new dt)!=_||pt&&kt(new pt)!=S)&&(kt=function(t){var e=Rt(t),r=e==v?t.constructor:void 0,i=r?Lt(r):"";if(i)switch(i){case mt:return P;case yt:return g;case vt:return w;case wt:return _;case bt:return S}return e});var zt=Nt(function(){return arguments}())?Nt:function(t){return Jt(t)&&Y.call(t,"callee")&&!nt.call(t,"callee")},Ft=Array.isArray,Kt=ct||function(){return!1};function Ht(t){if(!$t(t))return!1;var e=Rt(t);return e==d||e==p||e==h||e==b}function Vt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=o}function $t(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Jt(t){return null!=t&&"object"==typeof t}var Gt=L?function(t){return function(e){return t(e)}}(L):function(t){return Jt(t)&&Vt(t.length)&&!!N[Rt(t)]};function Wt(t){return null!=(e=t)&&Vt(e.length)&&!Ht(e)?function(t,e){var r=Ft(t),i=!r&&zt(t),n=!r&&!i&&Kt(t),s=!r&&!i&&!n&&Gt(t),o=r||i||n||s,a=o?function(t,e){for(var r=-1,i=Array(t);++r<t;)i[r]=e(r);return i}(t.length,String):[],c=a.length;for(var h in t)!e&&!Y.call(t,h)||o&&("length"==h||n&&("offset"==h||"parent"==h)||s&&("buffer"==h||"byteLength"==h||"byteOffset"==h)||qt(h,c))||a.push(h);return a}(t):function(t){if(r=(e=t)&&e.constructor,e!==("function"==typeof r&&r.prototype||G))return ht(t);var e,r,i=[];for(var n in Object(t))Y.call(t,n)&&"constructor"!=n&&i.push(n);return i}(t);var e}t.exports=function(t,e){return Ct(t,e)}},76782:(t,e,r)=>{"use strict";function i(t,e){return e=e||{},new Promise((function(r,i){var n=new XMLHttpRequest,s=[],o=[],a={},c=function(){return{ok:2==(n.status/100|0),statusText:n.statusText,status:n.status,url:n.responseURL,text:function(){return Promise.resolve(n.responseText)},json:function(){return Promise.resolve(n.responseText).then(JSON.parse)},blob:function(){return Promise.resolve(new Blob([n.response]))},clone:c,headers:{keys:function(){return s},entries:function(){return o},get:function(t){return a[t.toLowerCase()]},has:function(t){return t.toLowerCase()in a}}}};for(var h in n.open(e.method||"get",t,!0),n.onload=function(){n.getAllResponseHeaders().replace(/^(.*?):[^\S\n]*([\s\S]*?)$/gm,(function(t,e,r){s.push(e=e.toLowerCase()),o.push([e,r]),a[e]=a[e]?a[e]+","+r:r})),r(c())},n.onerror=i,n.withCredentials="include"==e.credentials,e.headers)n.setRequestHeader(h,e.headers[h]);n.send(e.body||null)}))}r.r(e),r.d(e,{default:()=>i})}}]);