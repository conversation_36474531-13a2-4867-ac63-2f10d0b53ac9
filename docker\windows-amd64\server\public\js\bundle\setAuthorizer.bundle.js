/*! For license information please see setAuthorizer.bundle.js.LICENSE.txt */
(()=>{var __webpack_modules__={75111:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.utils=e.curve25519=e.getSharedSecret=e.sync=e.verify=e.sign=e.getPublicKey=e.Signature=e.Point=e.RistrettoPoint=e.ExtendedPoint=e.CURVE=void 0;const n=r(13086),i=BigInt(0),o=BigInt(1),s=BigInt(2),a=BigInt(8),u=BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),c=Object.freeze({a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),P:BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),l:u,n:u,h:BigInt(8),Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960")});e.CURVE=c;const h=BigInt("0x10000000000000000000000000000000000000000000000000000000000000000"),f=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752"),l=(BigInt("6853475219497561581579357271197624642482790079785650197046958215289687604742"),BigInt("25063068953384623474111414158702152701244531502492656460079210482610430750235")),p=BigInt("54469307008909316920995813868745141605393597292927456921205312896311721017578"),d=BigInt("1159843021668779879193775521855586647937357759715417654439879720876111806838"),y=BigInt("40440834346308536858101042469323190826248399146238708352240133220865137265952");class g{constructor(t,e,r,n){this.x=t,this.y=e,this.z=r,this.t=n}static fromAffine(t){if(!(t instanceof B))throw new TypeError("ExtendedPoint#fromAffine: expected Point");return t.equals(B.ZERO)?g.ZERO:new g(t.x,t.y,o,N(t.x*t.y))}static toAffineBatch(t){const e=function(t,e=c.P){const r=new Array(t.length),n=D(t.reduce(((t,n,o)=>n===i?t:(r[o]=t,N(t*n,e))),o),e);return t.reduceRight(((t,n,o)=>n===i?t:(r[o]=N(t*r[o],e),N(t*n,e))),n),r}(t.map((t=>t.z)));return t.map(((t,r)=>t.toAffine(e[r])))}static normalizeZ(t){return this.toAffineBatch(t).map(this.fromAffine)}equals(t){b(t);const{x:e,y:r,z:n}=this,{x:i,y:o,z:s}=t,a=N(e*s),u=N(i*n),c=N(r*s),h=N(o*n);return a===u&&c===h}negate(){return new g(N(-this.x),this.y,this.z,N(-this.t))}double(){const{x:t,y:e,z:r}=this,{a:n}=c,i=N(t*t),o=N(e*e),a=N(s*N(r*r)),u=N(n*i),h=t+e,f=N(N(h*h)-i-o),l=u+o,p=l-a,d=u-o,y=N(f*p),m=N(l*d),b=N(f*d),v=N(p*l);return new g(y,m,v,b)}add(t){b(t);const{x:e,y:r,z:n,t:o}=this,{x:a,y:u,z:c,t:h}=t,f=N((r-e)*(u+a)),l=N((r+e)*(u-a)),p=N(l-f);if(p===i)return this.double();const d=N(n*s*h),y=N(o*s*c),m=y+d,v=l+f,w=y-d,E=N(m*p),S=N(v*w),B=N(m*w),A=N(p*v);return new g(E,S,A,B)}subtract(t){return this.add(t.negate())}precomputeWindow(t){const e=1+256/t,r=[];let n=this,i=n;for(let o=0;o<e;o++){i=n,r.push(i);for(let e=1;e<2**(t-1);e++)i=i.add(n),r.push(i);n=i.double()}return r}wNAF(t,e){!e&&this.equals(g.BASE)&&(e=B.BASE);const r=e&&e._WINDOW_SIZE||1;if(256%r)throw new Error("Point#wNAF: Invalid precomputation window, must be power of 2");let n=e&&S.get(e);n||(n=this.precomputeWindow(r),e&&1!==r&&(n=g.normalizeZ(n),S.set(e,n)));let i=g.ZERO,s=g.BASE;const a=1+256/r,u=2**(r-1),c=BigInt(2**r-1),h=2**r,f=BigInt(r);for(let e=0;e<a;e++){const r=e*u;let a=Number(t&c);t>>=f,a>u&&(a-=h,t+=o);const l=r,p=r+Math.abs(a)-1,d=e%2!=0,y=a<0;0===a?s=s.add(m(d,n[l])):i=i.add(m(y,n[p]))}return g.normalizeZ([i,s])[0]}multiply(t,e){return this.wNAF(F(t,c.l),e)}multiplyUnsafe(t){let e=F(t,c.l,!1);const r=g.BASE,n=g.ZERO;if(e===i)return n;if(this.equals(n)||e===o)return this;if(this.equals(r))return this.wNAF(e);let s=n,a=this;for(;e>i;)e&o&&(s=s.add(a)),a=a.double(),e>>=o;return s}isSmallOrder(){return this.multiplyUnsafe(c.h).equals(g.ZERO)}isTorsionFree(){let t=this.multiplyUnsafe(c.l/s).double();return c.l%s&&(t=t.add(this)),t.equals(g.ZERO)}toAffine(t){const{x:e,y:r,z:n}=this,i=this.equals(g.ZERO);null==t&&(t=i?a:D(n));const s=N(e*t),u=N(r*t),c=N(n*t);if(i)return B.ZERO;if(c!==o)throw new Error("invZ was invalid");return new B(s,u)}fromRistrettoBytes(){w()}toRistrettoBytes(){w()}fromRistrettoHash(){w()}}function m(t,e){const r=e.negate();return t?r:e}function b(t){if(!(t instanceof g))throw new TypeError("ExtendedPoint expected")}function v(t){if(!(t instanceof E))throw new TypeError("RistrettoPoint expected")}function w(){throw new Error("Legacy method: switch to RistrettoPoint")}e.ExtendedPoint=g,g.BASE=new g(c.Gx,c.Gy,o,N(c.Gx*c.Gy)),g.ZERO=new g(i,o,o,i);class E{constructor(t){this.ep=t}static calcElligatorRistrettoMap(t){const{d:e}=c,r=N(f*t*t),n=N((r+o)*d);let i=BigInt(-1);const s=N((i-e*r)*N(r+e));let{isValid:a,value:u}=M(n,s),h=N(u*t);_(h)||(h=N(-h)),a||(u=h),a||(i=r);const p=N(i*(r-o)*y-s),m=u*u,b=N((u+u)*s),v=N(p*l),w=N(o-m),E=N(o+m);return new g(N(b*E),N(w*v),N(v*E),N(b*w))}static hashToCurve(t){const e=L((t=z(t,64)).slice(0,32)),r=this.calcElligatorRistrettoMap(e),n=L(t.slice(32,64)),i=this.calcElligatorRistrettoMap(n);return new E(r.add(i))}static fromHex(t){t=z(t,32);const{a:e,d:r}=c,n="RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint",s=L(t);if(!function(t,e){if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++)if(t[r]!==e[r])return!1;return!0}(R(s),t)||_(s))throw new Error(n);const a=N(s*s),u=N(o+e*a),h=N(o-e*a),f=N(u*u),l=N(h*h),p=N(e*r*f-l),{isValid:d,value:y}=j(N(p*l)),m=N(y*h),b=N(y*m*p);let v=N((s+s)*m);_(v)&&(v=N(-v));const w=N(u*b),S=N(v*w);if(!d||_(S)||w===i)throw new Error(n);return new E(new g(v,w,o,S))}toRawBytes(){let{x:t,y:e,z:r,t:n}=this.ep;const i=N(N(r+e)*N(r-e)),o=N(t*e),s=N(o*o),{value:a}=j(N(i*s)),u=N(a*i),c=N(a*o),h=N(u*c*n);let l;if(_(n*h)){let r=N(e*f),n=N(t*f);t=r,e=n,l=N(u*p)}else l=c;_(t*h)&&(e=N(-e));let d=N((r-e)*l);return _(d)&&(d=N(-d)),R(d)}toHex(){return I(this.toRawBytes())}toString(){return this.toHex()}equals(t){v(t);const e=this.ep,r=t.ep,n=N(e.x*r.y)===N(e.y*r.x),i=N(e.y*r.y)===N(e.x*r.x);return n||i}add(t){return v(t),new E(this.ep.add(t.ep))}subtract(t){return v(t),new E(this.ep.subtract(t.ep))}multiply(t){return new E(this.ep.multiply(t))}multiplyUnsafe(t){return new E(this.ep.multiplyUnsafe(t))}}e.RistrettoPoint=E,E.BASE=new E(g.BASE),E.ZERO=new E(g.ZERO);const S=new WeakMap;class B{constructor(t,e){this.x=t,this.y=e}_setWindowSize(t){this._WINDOW_SIZE=t,S.delete(this)}static fromHex(t,e=!0){const{d:r,P:n}=c,i=(t=z(t,32)).slice();i[31]=-129&t[31];const s=P(i);if(e&&s>=n)throw new Error("Expected 0 < hex < P");if(!e&&s>=h)throw new Error("Expected 0 < hex < 2**256");const a=N(s*s),u=N(a-o),f=N(r*a+o);let{isValid:l,value:p}=M(u,f);if(!l)throw new Error("Point.fromHex: invalid y coordinate");const d=(p&o)===o;return!!(128&t[31])!==d&&(p=N(-p)),new B(p,s)}static async fromPrivateKey(t){return(await Q(t)).point}toRawBytes(){const t=R(this.y);return t[31]|=this.x&o?128:0,t}toHex(){return I(this.toRawBytes())}toX25519(){const{y:t}=this;return R(N((o+t)*D(o-t)))}isTorsionFree(){return g.fromAffine(this).isTorsionFree()}equals(t){return this.x===t.x&&this.y===t.y}negate(){return new B(N(-this.x),this.y)}add(t){return g.fromAffine(this).add(g.fromAffine(t)).toAffine()}subtract(t){return this.add(t.negate())}multiply(t){return g.fromAffine(this).multiply(t,this).toAffine()}}e.Point=B,B.BASE=new B(c.Gx,c.Gy),B.ZERO=new B(i,o);class A{constructor(t,e){this.r=t,this.s=e,this.assertValidity()}static fromHex(t){const e=z(t,64),r=B.fromHex(e.slice(0,32),!1),n=P(e.slice(32,64));return new A(r,n)}assertValidity(){const{r:t,s:e}=this;if(!(t instanceof B))throw new Error("Expected Point instance");return F(e,c.l,!1),this}toRawBytes(){const t=new Uint8Array(64);return t.set(this.r.toRawBytes()),t.set(R(this.s),32),t}toHex(){return I(this.toRawBytes())}}function C(...t){if(!t.every((t=>t instanceof Uint8Array)))throw new Error("Expected Uint8Array list");if(1===t.length)return t[0];const e=t.reduce(((t,e)=>t+e.length),0),r=new Uint8Array(e);for(let e=0,n=0;e<t.length;e++){const i=t[e];r.set(i,n),n+=i.length}return r}e.Signature=A;const T=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function I(t){if(!(t instanceof Uint8Array))throw new Error("Uint8Array expected");let e="";for(let r=0;r<t.length;r++)e+=T[t[r]];return e}function k(t){if("string"!=typeof t)throw new TypeError("hexToBytes: expected string, got "+typeof t);if(t.length%2)throw new Error("hexToBytes: received invalid unpadded hex");const e=new Uint8Array(t.length/2);for(let r=0;r<e.length;r++){const n=2*r,i=t.slice(n,n+2),o=Number.parseInt(i,16);if(Number.isNaN(o)||o<0)throw new Error("Invalid byte sequence");e[r]=o}return e}function x(t){return k(t.toString(16).padStart(64,"0"))}function R(t){return x(t).reverse()}function _(t){return(N(t)&o)===o}function P(t){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");return BigInt("0x"+I(Uint8Array.from(t).reverse()))}const U=BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff");function L(t){return N(P(t)&U)}function N(t,e=c.P){const r=t%e;return r>=i?r:e+r}function D(t,e=c.P){if(t===i||e<=i)throw new Error(`invert: expected positive integers, got n=${t} mod=${e}`);let r=N(t,e),n=e,s=i,a=o,u=o,h=i;for(;r!==i;){const t=n/r,e=n%r,i=s-u*t,o=a-h*t;n=r,r=e,s=u,a=h,u=i,h=o}if(n!==o)throw new Error("invert: does not exist");return N(s,e)}function O(t,e){const{P:r}=c;let n=t;for(;e-- >i;)n*=n,n%=r;return n}function K(t){const{P:e}=c,r=BigInt(5),n=BigInt(10),i=BigInt(20),a=BigInt(40),u=BigInt(80),h=t*t%e*t%e,f=O(h,s)*h%e,l=O(f,o)*t%e,p=O(l,r)*l%e,d=O(p,n)*p%e,y=O(d,i)*d%e,g=O(y,a)*y%e,m=O(g,u)*g%e,b=O(m,u)*g%e,v=O(b,n)*p%e;return{pow_p_5_8:O(v,s)*t%e,b2:h}}function M(t,e){const r=N(e*e*e),n=N(r*r*e);let i=N(t*r*K(t*n).pow_p_5_8);const o=N(e*i*i),s=i,a=N(i*f),u=o===t,c=o===N(-t),h=o===N(-t*f);return u&&(i=s),(c||h)&&(i=a),_(i)&&(i=N(-i)),{isValid:u||c,value:i}}function j(t){return M(o,t)}function V(t){return N(P(t),c.l)}function z(t,e){const r=t instanceof Uint8Array?Uint8Array.from(t):k(t);if("number"==typeof e&&r.length!==e)throw new Error(`Expected ${e} bytes`);return r}function F(t,e,r=!0){if(!e)throw new TypeError("Specify max value");if("number"==typeof t&&Number.isSafeInteger(t)&&(t=BigInt(t)),"bigint"==typeof t&&t<e)if(r){if(i<t)return t}else if(i<=t)return t;throw new TypeError("Expected valid scalar: 0 < scalar < max")}function q(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}function H(t){if(32!==(t="bigint"==typeof t||"number"==typeof t?x(F(t,h)):z(t)).length)throw new Error("Expected 32 bytes");return t}function G(t){const e=q(t.slice(0,32)),r=t.slice(32,64),n=V(e),i=B.BASE.multiply(n),o=i.toRawBytes();return{head:e,prefix:r,scalar:n,point:i,pointBytes:o}}let W;function $(...t){if("function"!=typeof W)throw new Error("utils.sha512Sync must be set to use sync methods");return W(...t)}async function Q(t){return G(await e.utils.sha512(H(t)))}function Z(t){return G($(H(t)))}function Y(t,e,r){e=z(e),r instanceof B||(r=B.fromHex(r,!1));const{r:n,s:i}=t instanceof A?t.assertValidity():A.fromHex(t);return{r:n,s:i,SB:g.BASE.multiplyUnsafe(i),pub:r,msg:e}}function J(t,e,r,n){const i=V(n),o=g.fromAffine(t).multiplyUnsafe(i);return g.fromAffine(e).add(o).subtract(r).multiplyUnsafe(c.h).equals(g.ZERO)}function X(t,e,r){const n=N(t*(e-r));return[e=N(e-n),r=N(r+n)]}e.getPublicKey=async function(t){return(await Q(t)).pointBytes},e.sign=async function(t,r){t=z(t);const{prefix:n,scalar:i,pointBytes:o}=await Q(r),s=V(await e.utils.sha512(n,t)),a=B.BASE.multiply(s),u=N(s+V(await e.utils.sha512(a.toRawBytes(),o,t))*i,c.l);return new A(a,u).toRawBytes()},e.verify=async function(t,r,n){const{r:i,SB:o,msg:s,pub:a}=Y(t,r,n),u=await e.utils.sha512(i.toRawBytes(),a.toRawBytes(),s);return J(a,i,o,u)},e.sync={getExtendedPublicKey:Z,getPublicKey:function(t){return Z(t).pointBytes},sign:function(t,e){t=z(t);const{prefix:r,scalar:n,pointBytes:i}=Z(e),o=V($(r,t)),s=B.BASE.multiply(o),a=N(o+V($(s.toRawBytes(),i,t))*n,c.l);return new A(s,a).toRawBytes()},verify:function(t,e,r){const{r:n,SB:i,msg:o,pub:s}=Y(t,e,r),a=$(n.toRawBytes(),s.toRawBytes(),o);return J(s,n,i,a)}},e.getSharedSecret=async function(t,r){const{head:n}=await Q(t),i=B.fromHex(r).toX25519();return e.curve25519.scalarMult(n,i)},B.BASE._setWindowSize(8),e.curve25519={BASE_POINT_U:"0900000000000000000000000000000000000000000000000000000000000000",scalarMult(t,e){const r=function(t,e){const{P:r}=c,n=F(t,r),s=F(e,r),a=BigInt(121665),u=n;let h,f=o,l=i,p=n,d=o,y=i;for(let t=BigInt(254);t>=i;t--){const e=s>>t&o;y^=e,h=X(y,f,p),f=h[0],p=h[1],h=X(y,l,d),l=h[0],d=h[1],y=e;const r=f+l,n=N(r*r),i=f-l,c=N(i*i),g=n-c,m=p+d,b=N((p-d)*r),v=N(m*i),w=b+v,E=b-v;p=N(w*w),d=N(u*N(E*E)),f=N(n*c),l=N(g*(n+N(a*g)))}h=X(y,f,p),f=h[0],p=h[1],h=X(y,l,d),l=h[0],d=h[1];const{pow_p_5_8:g,b2:m}=K(l),b=N(O(g,BigInt(3))*m);return N(f*b)}(function(t){const e=z(t,32);return e[31]&=127,P(e)}(e),P(q(z(t,32))));if(r===i)throw new Error("Invalid private or public key received");return R(N(r,c.P))},scalarMultBase:t=>e.curve25519.scalarMult(t,e.curve25519.BASE_POINT_U)};const tt={node:n,web:"object"==typeof self&&"crypto"in self?self.crypto:void 0};e.utils={bytesToHex:I,hexToBytes:k,concatBytes:C,getExtendedPublicKey:Q,mod:N,invert:D,TORSION_SUBGROUP:["0100000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a","0000000000000000000000000000000000000000000000000000000000000080","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05","ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85","0000000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa"],hashToPrivateScalar:t=>{if((t=z(t)).length<40||t.length>1024)throw new Error("Expected 40-1024 bytes of private key as per FIPS 186");return N(P(t),c.l-o)+o},randomBytes:(t=32)=>{if(tt.web)return tt.web.getRandomValues(new Uint8Array(t));if(tt.node){const{randomBytes:e}=tt.node;return new Uint8Array(e(t).buffer)}throw new Error("The environment doesn't have randomBytes function")},randomPrivateKey:()=>e.utils.randomBytes(32),sha512:async(...t)=>{const e=C(...t);if(tt.web){const t=await tt.web.subtle.digest("SHA-512",e.buffer);return new Uint8Array(t)}if(tt.node)return Uint8Array.from(tt.node.createHash("sha512").update(e).digest());throw new Error("The environment doesn't have sha512 function")},precompute(t=8,e=B.BASE){const r=e.equals(B.BASE)?e:new B(e.x,e.y);return r._setWindowSize(t),r.multiply(s),r},sha512Sync:void 0},Object.defineProperties(e.utils,{sha512Sync:{configurable:!1,get:()=>W,set(t){W||(W=t)}}})},9598:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.utils=e.schnorr=e.verify=e.signSync=e.sign=e.getSharedSecret=e.recoverPublicKey=e.getPublicKey=e.Signature=e.Point=e.CURVE=void 0;const n=r(14923),i=BigInt(0),o=BigInt(1),s=BigInt(2),a=BigInt(3),u=BigInt(8),c=Object.freeze({a:i,b:BigInt(7),P:BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),n:BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),h:o,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee")});e.CURVE=c;const h=(t,e)=>(t+e/s)/e,f={beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar(t){const{n:e}=c,r=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-o*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),s=r,a=BigInt("0x100000000000000000000000000000000"),u=h(s*t,e),f=h(-n*t,e);let l=M(t-u*r-f*i,e),p=M(-u*n-f*s,e);const d=l>a,y=p>a;if(d&&(l=e-l),y&&(p=e-p),l>a||p>a)throw new Error("splitScalarEndo: Endomorphism failed, k="+t);return{k1neg:d,k1:l,k2neg:y,k2:p}}},l=32,p=32,d=l+1,y=2*l+1;function g(t){const{a:e,b:r}=c,n=M(t*t),i=M(n*t);return M(i+e*t+r)}const m=c.a===i;class b extends Error{constructor(t){super(t)}}function v(t){if(!(t instanceof w))throw new TypeError("JacobianPoint expected")}class w{constructor(t,e,r){this.x=t,this.y=e,this.z=r}static fromAffine(t){if(!(t instanceof B))throw new TypeError("JacobianPoint#fromAffine: expected Point");return t.equals(B.ZERO)?w.ZERO:new w(t.x,t.y,o)}static toAffineBatch(t){const e=function(t,e=c.P){const r=new Array(t.length),n=V(t.reduce(((t,n,o)=>n===i?t:(r[o]=t,M(t*n,e))),o),e);return t.reduceRight(((t,n,o)=>n===i?t:(r[o]=M(t*r[o],e),M(t*n,e))),n),r}(t.map((t=>t.z)));return t.map(((t,r)=>t.toAffine(e[r])))}static normalizeZ(t){return w.toAffineBatch(t).map(w.fromAffine)}equals(t){v(t);const{x:e,y:r,z:n}=this,{x:i,y:o,z:s}=t,a=M(n*n),u=M(s*s),c=M(e*u),h=M(i*a),f=M(M(r*s)*u),l=M(M(o*n)*a);return c===h&&f===l}negate(){return new w(this.x,M(-this.y),this.z)}double(){const{x:t,y:e,z:r}=this,n=M(t*t),i=M(e*e),o=M(i*i),c=t+i,h=M(s*(M(c*c)-n-o)),f=M(a*n),l=M(f*f),p=M(l-s*h),d=M(f*(h-p)-u*o),y=M(s*e*r);return new w(p,d,y)}add(t){v(t);const{x:e,y:r,z:n}=this,{x:o,y:a,z:u}=t;if(o===i||a===i)return this;if(e===i||r===i)return t;const c=M(n*n),h=M(u*u),f=M(e*h),l=M(o*c),p=M(M(r*u)*h),d=M(M(a*n)*c),y=M(l-f),g=M(d-p);if(y===i)return g===i?this.double():w.ZERO;const m=M(y*y),b=M(y*m),E=M(f*m),S=M(g*g-b-s*E),B=M(g*(E-S)-p*b),A=M(n*u*y);return new w(S,B,A)}subtract(t){return this.add(t.negate())}multiplyUnsafe(t){const e=w.ZERO;if("bigint"==typeof t&&t===i)return e;let r=K(t);if(r===o)return this;if(!m){let t=e,n=this;for(;r>i;)r&o&&(t=t.add(n)),n=n.double(),r>>=o;return t}let{k1neg:n,k1:s,k2neg:a,k2:u}=f.splitScalar(r),c=e,h=e,l=this;for(;s>i||u>i;)s&o&&(c=c.add(l)),u&o&&(h=h.add(l)),l=l.double(),s>>=o,u>>=o;return n&&(c=c.negate()),a&&(h=h.negate()),h=new w(M(h.x*f.beta),h.y,h.z),c.add(h)}precomputeWindow(t){const e=m?128/t+1:256/t+1,r=[];let n=this,i=n;for(let o=0;o<e;o++){i=n,r.push(i);for(let e=1;e<2**(t-1);e++)i=i.add(n),r.push(i);n=i.double()}return r}wNAF(t,e){!e&&this.equals(w.BASE)&&(e=B.BASE);const r=e&&e._WINDOW_SIZE||1;if(256%r)throw new Error("Point#wNAF: Invalid precomputation window, must be power of 2");let n=e&&S.get(e);n||(n=this.precomputeWindow(r),e&&1!==r&&(n=w.normalizeZ(n),S.set(e,n)));let i=w.ZERO,s=w.BASE;const a=1+(m?128/r:256/r),u=2**(r-1),c=BigInt(2**r-1),h=2**r,f=BigInt(r);for(let e=0;e<a;e++){const r=e*u;let a=Number(t&c);t>>=f,a>u&&(a-=h,t+=o);const l=r,p=r+Math.abs(a)-1,d=e%2!=0,y=a<0;0===a?s=s.add(E(d,n[l])):i=i.add(E(y,n[p]))}return{p:i,f:s}}multiply(t,e){let r,n,i=K(t);if(m){const{k1neg:t,k1:o,k2neg:s,k2:a}=f.splitScalar(i);let{p:u,f:c}=this.wNAF(o,e),{p:h,f:l}=this.wNAF(a,e);u=E(t,u),h=E(s,h),h=new w(M(h.x*f.beta),h.y,h.z),r=u.add(h),n=c.add(l)}else{const{p:t,f:o}=this.wNAF(i,e);r=t,n=o}return w.normalizeZ([r,n])[0]}toAffine(t){const{x:e,y:r,z:n}=this,i=this.equals(w.ZERO);null==t&&(t=i?u:V(n));const s=t,a=M(s*s),c=M(a*s),h=M(e*a),f=M(r*c),l=M(n*s);if(i)return B.ZERO;if(l!==o)throw new Error("invZ was invalid");return new B(h,f)}}function E(t,e){const r=e.negate();return t?r:e}w.BASE=new w(c.Gx,c.Gy,o),w.ZERO=new w(i,o,i);const S=new WeakMap;class B{constructor(t,e){this.x=t,this.y=e}_setWindowSize(t){this._WINDOW_SIZE=t,S.delete(this)}hasEvenY(){return this.y%s===i}static fromCompressedHex(t){const e=32===t.length,r=D(e?t:t.subarray(1));if(!W(r))throw new Error("Point is not on curve");let n=function(t){const{P:e}=c,r=BigInt(6),n=BigInt(11),i=BigInt(22),o=BigInt(23),u=BigInt(44),h=BigInt(88),f=t*t*t%e,l=f*f*t%e,p=j(l,a)*l%e,d=j(p,a)*l%e,y=j(d,s)*f%e,g=j(y,n)*y%e,m=j(g,i)*g%e,b=j(m,u)*m%e,v=j(b,h)*b%e,w=j(v,u)*m%e,E=j(w,a)*l%e,S=j(E,o)*g%e,B=j(S,r)*f%e,A=j(B,s);if(A*A%e!==t)throw new Error("Cannot find square root");return A}(g(r));const i=(n&o)===o;e?i&&(n=M(-n)):!(1&~t[0])!==i&&(n=M(-n));const u=new B(r,n);return u.assertValidity(),u}static fromUncompressedHex(t){const e=D(t.subarray(1,l+1)),r=D(t.subarray(l+1,2*l+1)),n=new B(e,r);return n.assertValidity(),n}static fromHex(t){const e=O(t),r=e.length,n=e[0];if(r===l)return this.fromCompressedHex(e);if(r===d&&(2===n||3===n))return this.fromCompressedHex(e);if(r===y&&4===n)return this.fromUncompressedHex(e);throw new Error(`Point.fromHex: received invalid point. Expected 32-${d} compressed bytes or ${y} uncompressed bytes, not ${r}`)}static fromPrivateKey(t){return B.BASE.multiply(Q(t))}static fromSignature(t,e,r){const{r:n,s:i}=Y(e);if(![0,1,2,3].includes(r))throw new Error("Cannot recover: invalid recovery bit");const o=z(O(t)),{n:s}=c,a=2===r||3===r?n+s:n,u=V(a,s),h=M(-o*u,s),f=M(i*u,s),l=1&r?"03":"02",p=B.fromHex(l+_(a)),d=B.BASE.multiplyAndAddUnsafe(p,h,f);if(!d)throw new Error("Cannot recover signature: point at infinify");return d.assertValidity(),d}toRawBytes(t=!1){return N(this.toHex(t))}toHex(t=!1){const e=_(this.x);return t?`${this.hasEvenY()?"02":"03"}${e}`:`04${e}${_(this.y)}`}toHexX(){return this.toHex(!0).slice(2)}toRawX(){return this.toRawBytes(!0).slice(1)}assertValidity(){const t="Point is not on elliptic curve",{x:e,y:r}=this;if(!W(e)||!W(r))throw new Error(t);const n=M(r*r);if(M(n-g(e))!==i)throw new Error(t)}equals(t){return this.x===t.x&&this.y===t.y}negate(){return new B(this.x,M(-this.y))}double(){return w.fromAffine(this).double().toAffine()}add(t){return w.fromAffine(this).add(w.fromAffine(t)).toAffine()}subtract(t){return this.add(t.negate())}multiply(t){return w.fromAffine(this).multiply(t,this).toAffine()}multiplyAndAddUnsafe(t,e,r){const n=w.fromAffine(this),s=e===i||e===o||this!==B.BASE?n.multiplyUnsafe(e):n.multiply(e),a=w.fromAffine(t).multiplyUnsafe(r),u=s.add(a);return u.equals(w.ZERO)?void 0:u.toAffine()}}function A(t){return Number.parseInt(t[0],16)>=8?"00"+t:t}function C(t){if(t.length<2||2!==t[0])throw new Error(`Invalid signature integer tag: ${x(t)}`);const e=t[1],r=t.subarray(2,e+2);if(!e||r.length!==e)throw new Error("Invalid signature integer: wrong length");if(0===r[0]&&r[1]<=127)throw new Error("Invalid signature integer: trailing length");return{data:D(r),left:t.subarray(e+2)}}e.Point=B,B.BASE=new B(c.Gx,c.Gy),B.ZERO=new B(i,i);class T{constructor(t,e){this.r=t,this.s=e,this.assertValidity()}static fromCompact(t){const e=t instanceof Uint8Array,r="Signature.fromCompact";if("string"!=typeof t&&!e)throw new TypeError(`${r}: Expected string or Uint8Array`);const n=e?x(t):t;if(128!==n.length)throw new Error(`${r}: Expected 64-byte hex`);return new T(L(n.slice(0,64)),L(n.slice(64,128)))}static fromDER(t){const e=t instanceof Uint8Array;if("string"!=typeof t&&!e)throw new TypeError("Signature.fromDER: Expected string or Uint8Array");const{r,s:n}=function(t){if(t.length<2||48!=t[0])throw new Error(`Invalid signature tag: ${x(t)}`);if(t[1]!==t.length-2)throw new Error("Invalid signature: incorrect length");const{data:e,left:r}=C(t.subarray(2)),{data:n,left:i}=C(r);if(i.length)throw new Error(`Invalid signature: left bytes after parsing: ${x(i)}`);return{r:e,s:n}}(e?t:N(t));return new T(r,n)}static fromHex(t){return this.fromDER(t)}assertValidity(){const{r:t,s:e}=this;if(!G(t))throw new Error("Invalid Signature: r must be 0 < r < n");if(!G(e))throw new Error("Invalid Signature: s must be 0 < s < n")}hasHighS(){const t=c.n>>o;return this.s>t}normalizeS(){return this.hasHighS()?new T(this.r,M(-this.s,c.n)):this}toDERRawBytes(){return N(this.toDERHex())}toDERHex(){const t=A(U(this.s)),e=A(U(this.r)),r=t.length/2,n=e.length/2,i=U(r),o=U(n);return`30${U(n+r+4)}02${o}${e}02${i}${t}`}toRawBytes(){return this.toDERRawBytes()}toHex(){return this.toDERHex()}toCompactRawBytes(){return N(this.toCompactHex())}toCompactHex(){return _(this.r)+_(this.s)}}function I(...t){if(!t.every((t=>t instanceof Uint8Array)))throw new Error("Uint8Array list expected");if(1===t.length)return t[0];const e=t.reduce(((t,e)=>t+e.length),0),r=new Uint8Array(e);for(let e=0,n=0;e<t.length;e++){const i=t[e];r.set(i,n),n+=i.length}return r}e.Signature=T;const k=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function x(t){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");let e="";for(let r=0;r<t.length;r++)e+=k[t[r]];return e}const R=BigInt("0x10000000000000000000000000000000000000000000000000000000000000000");function _(t){if("bigint"!=typeof t)throw new Error("Expected bigint");if(!(i<=t&&t<R))throw new Error("Expected number 0 <= n < 2^256");return t.toString(16).padStart(64,"0")}function P(t){const e=N(_(t));if(32!==e.length)throw new Error("Error: expected 32 bytes");return e}function U(t){const e=t.toString(16);return 1&e.length?`0${e}`:e}function L(t){if("string"!=typeof t)throw new TypeError("hexToNumber: expected string, got "+typeof t);return BigInt(`0x${t}`)}function N(t){if("string"!=typeof t)throw new TypeError("hexToBytes: expected string, got "+typeof t);if(t.length%2)throw new Error("hexToBytes: received invalid unpadded hex"+t.length);const e=new Uint8Array(t.length/2);for(let r=0;r<e.length;r++){const n=2*r,i=t.slice(n,n+2),o=Number.parseInt(i,16);if(Number.isNaN(o)||o<0)throw new Error("Invalid byte sequence");e[r]=o}return e}function D(t){return L(x(t))}function O(t){return t instanceof Uint8Array?Uint8Array.from(t):N(t)}function K(t){if("number"==typeof t&&Number.isSafeInteger(t)&&t>0)return BigInt(t);if("bigint"==typeof t&&G(t))return t;throw new TypeError("Expected valid private scalar: 0 < scalar < curve.n")}function M(t,e=c.P){const r=t%e;return r>=i?r:e+r}function j(t,e){const{P:r}=c;let n=t;for(;e-- >i;)n*=n,n%=r;return n}function V(t,e=c.P){if(t===i||e<=i)throw new Error(`invert: expected positive integers, got n=${t} mod=${e}`);let r=M(t,e),n=e,s=i,a=o,u=o,h=i;for(;r!==i;){const t=n/r,e=n%r,i=s-u*t,o=a-h*t;n=r,r=e,s=u,a=h,u=i,h=o}if(n!==o)throw new Error("invert: does not exist");return M(s,e)}function z(t,e=!1){const r=function(t){const e=8*t.length-8*p,r=D(t);return e>0?r>>BigInt(e):r}(t);if(e)return r;const{n}=c;return r>=n?r-n:r}let F,q;class H{constructor(t,e){if(this.hashLen=t,this.qByteLen=e,"number"!=typeof t||t<2)throw new Error("hashLen must be a number");if("number"!=typeof e||e<2)throw new Error("qByteLen must be a number");this.v=new Uint8Array(t).fill(1),this.k=new Uint8Array(t).fill(0),this.counter=0}hmac(...t){return e.utils.hmacSha256(this.k,...t)}hmacSync(...t){return q(this.k,...t)}checkSync(){if("function"!=typeof q)throw new b("hmacSha256Sync needs to be set")}incr(){if(this.counter>=1e3)throw new Error("Tried 1,000 k values for sign(), all were invalid");this.counter+=1}async reseed(t=new Uint8Array){this.k=await this.hmac(this.v,Uint8Array.from([0]),t),this.v=await this.hmac(this.v),0!==t.length&&(this.k=await this.hmac(this.v,Uint8Array.from([1]),t),this.v=await this.hmac(this.v))}reseedSync(t=new Uint8Array){this.checkSync(),this.k=this.hmacSync(this.v,Uint8Array.from([0]),t),this.v=this.hmacSync(this.v),0!==t.length&&(this.k=this.hmacSync(this.v,Uint8Array.from([1]),t),this.v=this.hmacSync(this.v))}async generate(){this.incr();let t=0;const e=[];for(;t<this.qByteLen;){this.v=await this.hmac(this.v);const r=this.v.slice();e.push(r),t+=this.v.length}return I(...e)}generateSync(){this.checkSync(),this.incr();let t=0;const e=[];for(;t<this.qByteLen;){this.v=this.hmacSync(this.v);const r=this.v.slice();e.push(r),t+=this.v.length}return I(...e)}}function G(t){return i<t&&t<c.n}function W(t){return i<t&&t<c.P}function $(t,e,r,n=!0){const{n:s}=c,a=z(t,!0);if(!G(a))return;const u=V(a,s),h=B.BASE.multiply(a),f=M(h.x,s);if(f===i)return;const l=M(u*M(e+r*f,s),s);if(l===i)return;let p=new T(f,l),d=(h.x===p.r?0:2)|Number(h.y&o);return n&&p.hasHighS()&&(p=p.normalizeS(),d^=1),{sig:p,recovery:d}}function Q(t){let e;if("bigint"==typeof t)e=t;else if("number"==typeof t&&Number.isSafeInteger(t)&&t>0)e=BigInt(t);else if("string"==typeof t){if(t.length!==2*p)throw new Error("Expected 32 bytes of private key");e=L(t)}else{if(!(t instanceof Uint8Array))throw new TypeError("Expected valid private key");if(t.length!==p)throw new Error("Expected 32 bytes of private key");e=D(t)}if(!G(e))throw new Error("Expected private key: 0 < key < n");return e}function Z(t){return t instanceof B?(t.assertValidity(),t):B.fromHex(t)}function Y(t){if(t instanceof T)return t.assertValidity(),t;try{return T.fromDER(t)}catch(e){return T.fromCompact(t)}}function J(t){const e=t instanceof Uint8Array,r="string"==typeof t,n=(e||r)&&t.length;return e?n===d||n===y:r?n===2*d||n===2*y:t instanceof B}function X(t){return D(t.length>l?t.slice(0,l):t)}function tt(t){const e=X(t),r=M(e,c.n);return et(r<i?e:r)}function et(t){return P(t)}function rt(t,r,n){if(null==t)throw new Error(`sign: expected valid message hash, not "${t}"`);const i=O(t),o=Q(r),s=[et(o),tt(i)];if(null!=n){!0===n&&(n=e.utils.randomBytes(l));const t=O(n);if(t.length!==l)throw new Error(`sign: Expected ${l} bytes of extra data`);s.push(t)}return{seed:I(...s),m:X(i),d:o}}function nt(t,e){const{sig:r,recovery:n}=t,{der:i,recovered:o}=Object.assign({canonical:!0,der:!0},e),s=i?r.toDERRawBytes():r.toCompactRawBytes();return o?[s,n]:s}e.getPublicKey=function(t,e=!1){return B.fromPrivateKey(t).toRawBytes(e)},e.recoverPublicKey=function(t,e,r,n=!1){return B.fromSignature(t,e,r).toRawBytes(n)},e.getSharedSecret=function(t,e,r=!1){if(J(t))throw new TypeError("getSharedSecret: first arg must be private key");if(!J(e))throw new TypeError("getSharedSecret: second arg must be public key");const n=Z(e);return n.assertValidity(),n.multiply(Q(t)).toRawBytes(r)},e.sign=async function(t,e,r={}){const{seed:n,m:i,d:o}=rt(t,e,r.extraEntropy),s=new H(32,p);let a;for(await s.reseed(n);!(a=$(await s.generate(),i,o,r.canonical));)await s.reseed();return nt(a,r)},e.signSync=function(t,e,r={}){const{seed:n,m:i,d:o}=rt(t,e,r.extraEntropy),s=new H(32,p);let a;for(s.reseedSync(n);!(a=$(s.generateSync(),i,o,r.canonical));)s.reseedSync();return nt(a,r)};const it={strict:!0};function ot(t){return M(D(t),c.n)}e.verify=function(t,e,r,n=it){let i;try{i=Y(t),e=O(e)}catch(t){return!1}const{r:o,s}=i;if(n.strict&&i.hasHighS())return!1;const a=z(e);let u;try{u=Z(r)}catch(t){return!1}const{n:h}=c,f=V(s,h),l=M(a*f,h),p=M(o*f,h),d=B.BASE.multiplyAndAddUnsafe(u,l,p);return!!d&&M(d.x,h)===o};class st{constructor(t,e){this.r=t,this.s=e,this.assertValidity()}static fromHex(t){const e=O(t);if(64!==e.length)throw new TypeError(`SchnorrSignature.fromHex: expected 64 bytes, not ${e.length}`);const r=D(e.subarray(0,32)),n=D(e.subarray(32,64));return new st(r,n)}assertValidity(){const{r:t,s:e}=this;if(!W(t)||!G(e))throw new Error("Invalid signature")}toHex(){return _(this.r)+_(this.s)}toRawBytes(){return N(this.toHex())}}class at{constructor(t,r,n=e.utils.randomBytes()){if(null==t)throw new TypeError(`sign: Expected valid message, not "${t}"`);this.m=O(t);const{x:i,scalar:o}=this.getScalar(Q(r));if(this.px=i,this.d=o,this.rand=O(n),32!==this.rand.length)throw new TypeError("sign: Expected 32 bytes of aux randomness")}getScalar(t){const e=B.fromPrivateKey(t),r=e.hasEvenY()?t:c.n-t;return{point:e,scalar:r,x:e.toRawX()}}initNonce(t,e){return P(t^D(e))}finalizeNonce(t){const e=M(D(t),c.n);if(e===i)throw new Error("sign: Creation of signature failed. k is zero");const{point:r,x:n,scalar:o}=this.getScalar(e);return{R:r,rx:n,k:o}}finalizeSig(t,e,r,n){return new st(t.x,M(e+r*n,c.n)).toRawBytes()}error(){throw new Error("sign: Invalid signature produced")}async calc(){const{m:t,d:r,px:n,rand:i}=this,o=e.utils.taggedHash,s=this.initNonce(r,await o(pt.aux,i)),{R:a,rx:u,k:c}=this.finalizeNonce(await o(pt.nonce,s,n,t)),h=ot(await o(pt.challenge,u,n,t)),f=this.finalizeSig(a,c,h,r);return await ht(f,t,n)||this.error(),f}calcSync(){const{m:t,d:r,px:n,rand:i}=this,o=e.utils.taggedHashSync,s=this.initNonce(r,o(pt.aux,i)),{R:a,rx:u,k:c}=this.finalizeNonce(o(pt.nonce,s,n,t)),h=ot(o(pt.challenge,u,n,t)),f=this.finalizeSig(a,c,h,r);return ft(f,t,n)||this.error(),f}}function ut(t,e,r){const n=t instanceof st,i=n?t:st.fromHex(t);return n&&i.assertValidity(),{...i,m:O(e),P:Z(r)}}function ct(t,e,r,n){const i=B.BASE.multiplyAndAddUnsafe(e,Q(r),M(-n,c.n));return!(!i||!i.hasEvenY()||i.x!==t)}async function ht(t,r,n){try{const{r:i,s:o,m:s,P:a}=ut(t,r,n),u=ot(await e.utils.taggedHash(pt.challenge,P(i),a.toRawX(),s));return ct(i,a,o,u)}catch(t){return!1}}function ft(t,r,n){try{const{r:i,s:o,m:s,P:a}=ut(t,r,n),u=ot(e.utils.taggedHashSync(pt.challenge,P(i),a.toRawX(),s));return ct(i,a,o,u)}catch(t){if(t instanceof b)throw t;return!1}}e.schnorr={Signature:st,getPublicKey:function(t){return B.fromPrivateKey(t).toRawX()},sign:async function(t,e,r){return new at(t,e,r).calc()},verify:ht,signSync:function(t,e,r){return new at(t,e,r).calcSync()},verifySync:ft},B.BASE._setWindowSize(8);const lt={node:n,web:"object"==typeof self&&"crypto"in self?self.crypto:void 0},pt={challenge:"BIP0340/challenge",aux:"BIP0340/aux",nonce:"BIP0340/nonce"},dt={};e.utils={bytesToHex:x,hexToBytes:N,concatBytes:I,mod:M,invert:V,isValidPrivateKey(t){try{return Q(t),!0}catch(t){return!1}},_bigintTo32Bytes:P,_normalizePrivateKey:Q,hashToPrivateKey:t=>{t=O(t);const e=p+8;if(t.length<e||t.length>1024)throw new Error("Expected valid bytes of private key as per FIPS 186");return P(M(D(t),c.n-o)+o)},randomBytes:(t=32)=>{if(lt.web)return lt.web.getRandomValues(new Uint8Array(t));if(lt.node){const{randomBytes:e}=lt.node;return Uint8Array.from(e(t))}throw new Error("The environment doesn't have randomBytes function")},randomPrivateKey:()=>e.utils.hashToPrivateKey(e.utils.randomBytes(p+8)),precompute(t=8,e=B.BASE){const r=e===B.BASE?e:new B(e.x,e.y);return r._setWindowSize(t),r.multiply(a),r},sha256:async(...t)=>{if(lt.web){const e=await lt.web.subtle.digest("SHA-256",I(...t));return new Uint8Array(e)}if(lt.node){const{createHash:e}=lt.node,r=e("sha256");return t.forEach((t=>r.update(t))),Uint8Array.from(r.digest())}throw new Error("The environment doesn't have sha256 function")},hmacSha256:async(t,...e)=>{if(lt.web){const r=await lt.web.subtle.importKey("raw",t,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=I(...e),i=await lt.web.subtle.sign("HMAC",r,n);return new Uint8Array(i)}if(lt.node){const{createHmac:r}=lt.node,n=r("sha256",t);return e.forEach((t=>n.update(t))),Uint8Array.from(n.digest())}throw new Error("The environment doesn't have hmac-sha256 function")},sha256Sync:void 0,hmacSha256Sync:void 0,taggedHash:async(t,...r)=>{let n=dt[t];if(void 0===n){const r=await e.utils.sha256(Uint8Array.from(t,(t=>t.charCodeAt(0))));n=I(r,r),dt[t]=n}return e.utils.sha256(n,...r)},taggedHashSync:(t,...e)=>{if("function"!=typeof F)throw new b("sha256Sync is undefined, you need to set it");let r=dt[t];if(void 0===r){const e=F(Uint8Array.from(t,(t=>t.charCodeAt(0))));r=I(e,e),dt[t]=r}return F(r,...e)},_JacobianPoint:w},Object.defineProperties(e.utils,{sha256Sync:{configurable:!1,get:()=>F,set(t){F||(F=t)}},hmacSha256Sync:{configurable:!1,get:()=>q,set(t){q||(q=t)}}})},18045:t=>{"use strict";t.exports=function(t,e){for(var r=new Array(arguments.length-1),n=0,i=2,o=!0;i<arguments.length;)r[n++]=arguments[i++];return new Promise((function(i,s){r[n]=function(t){if(o)if(o=!1,t)s(t);else{for(var e=new Array(arguments.length-1),r=0;r<e.length;)e[r++]=arguments[r];i.apply(null,e)}};try{t.apply(e||null,r)}catch(t){o&&(o=!1,s(t))}}))}},8839:(t,e)=>{"use strict";var r=e;r.length=function(t){var e=t.length;if(!e)return 0;for(var r=0;--e%4>1&&"="===t.charAt(e);)++r;return Math.ceil(3*t.length)/4-r};for(var n=new Array(64),i=new Array(123),o=0;o<64;)i[n[o]=o<26?o+65:o<52?o+71:o<62?o-4:o-59|43]=o++;r.encode=function(t,e,r){for(var i,o=null,s=[],a=0,u=0;e<r;){var c=t[e++];switch(u){case 0:s[a++]=n[c>>2],i=(3&c)<<4,u=1;break;case 1:s[a++]=n[i|c>>4],i=(15&c)<<2,u=2;break;case 2:s[a++]=n[i|c>>6],s[a++]=n[63&c],u=0}a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,s)),a=0)}return u&&(s[a++]=n[i],s[a++]=61,1===u&&(s[a++]=61)),o?(a&&o.push(String.fromCharCode.apply(String,s.slice(0,a))),o.join("")):String.fromCharCode.apply(String,s.slice(0,a))};var s="invalid encoding";r.decode=function(t,e,r){for(var n,o=r,a=0,u=0;u<t.length;){var c=t.charCodeAt(u++);if(61===c&&a>1)break;if(void 0===(c=i[c]))throw Error(s);switch(a){case 0:n=c,a=1;break;case 1:e[r++]=n<<2|(48&c)>>4,n=c,a=2;break;case 2:e[r++]=(15&n)<<4|(60&c)>>2,n=c,a=3;break;case 3:e[r++]=(3&n)<<6|c,a=0}}if(1===a)throw Error(s);return r-o},r.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}},24358:t=>{"use strict";function e(){this._listeners={}}t.exports=e,e.prototype.on=function(t,e,r){return(this._listeners[t]||(this._listeners[t]=[])).push({fn:e,ctx:r||this}),this},e.prototype.off=function(t,e){if(void 0===t)this._listeners={};else if(void 0===e)this._listeners[t]=[];else for(var r=this._listeners[t],n=0;n<r.length;)r[n].fn===e?r.splice(n,1):++n;return this},e.prototype.emit=function(t){var e=this._listeners[t];if(e){for(var r=[],n=1;n<arguments.length;)r.push(arguments[n++]);for(n=0;n<e.length;)e[n].fn.apply(e[n++].ctx,r)}return this}},49410:t=>{"use strict";function e(t){return"undefined"!=typeof Float32Array?function(){var e=new Float32Array([-0]),r=new Uint8Array(e.buffer),n=128===r[3];function i(t,n,i){e[0]=t,n[i]=r[0],n[i+1]=r[1],n[i+2]=r[2],n[i+3]=r[3]}function o(t,n,i){e[0]=t,n[i]=r[3],n[i+1]=r[2],n[i+2]=r[1],n[i+3]=r[0]}function s(t,n){return r[0]=t[n],r[1]=t[n+1],r[2]=t[n+2],r[3]=t[n+3],e[0]}function a(t,n){return r[3]=t[n],r[2]=t[n+1],r[1]=t[n+2],r[0]=t[n+3],e[0]}t.writeFloatLE=n?i:o,t.writeFloatBE=n?o:i,t.readFloatLE=n?s:a,t.readFloatBE=n?a:s}():function(){function e(t,e,r,n){var i=e<0?1:0;if(i&&(e=-e),0===e)t(1/e>0?0:2147483648,r,n);else if(isNaN(e))t(2143289344,r,n);else if(e>34028234663852886e22)t((i<<31|2139095040)>>>0,r,n);else if(e<11754943508222875e-54)t((i<<31|Math.round(e/1401298464324817e-60))>>>0,r,n);else{var o=Math.floor(Math.log(e)/Math.LN2);t((i<<31|o+127<<23|8388607&Math.round(e*Math.pow(2,-o)*8388608))>>>0,r,n)}}function s(t,e,r){var n=t(e,r),i=2*(n>>31)+1,o=n>>>23&255,s=8388607&n;return 255===o?s?NaN:i*(1/0):0===o?1401298464324817e-60*i*s:i*Math.pow(2,o-150)*(s+8388608)}t.writeFloatLE=e.bind(null,r),t.writeFloatBE=e.bind(null,n),t.readFloatLE=s.bind(null,i),t.readFloatBE=s.bind(null,o)}(),"undefined"!=typeof Float64Array?function(){var e=new Float64Array([-0]),r=new Uint8Array(e.buffer),n=128===r[7];function i(t,n,i){e[0]=t,n[i]=r[0],n[i+1]=r[1],n[i+2]=r[2],n[i+3]=r[3],n[i+4]=r[4],n[i+5]=r[5],n[i+6]=r[6],n[i+7]=r[7]}function o(t,n,i){e[0]=t,n[i]=r[7],n[i+1]=r[6],n[i+2]=r[5],n[i+3]=r[4],n[i+4]=r[3],n[i+5]=r[2],n[i+6]=r[1],n[i+7]=r[0]}function s(t,n){return r[0]=t[n],r[1]=t[n+1],r[2]=t[n+2],r[3]=t[n+3],r[4]=t[n+4],r[5]=t[n+5],r[6]=t[n+6],r[7]=t[n+7],e[0]}function a(t,n){return r[7]=t[n],r[6]=t[n+1],r[5]=t[n+2],r[4]=t[n+3],r[3]=t[n+4],r[2]=t[n+5],r[1]=t[n+6],r[0]=t[n+7],e[0]}t.writeDoubleLE=n?i:o,t.writeDoubleBE=n?o:i,t.readDoubleLE=n?s:a,t.readDoubleBE=n?a:s}():function(){function e(t,e,r,n,i,o){var s=n<0?1:0;if(s&&(n=-n),0===n)t(0,i,o+e),t(1/n>0?0:2147483648,i,o+r);else if(isNaN(n))t(0,i,o+e),t(2146959360,i,o+r);else if(n>17976931348623157e292)t(0,i,o+e),t((s<<31|2146435072)>>>0,i,o+r);else{var a;if(n<22250738585072014e-324)t((a=n/5e-324)>>>0,i,o+e),t((s<<31|a/4294967296)>>>0,i,o+r);else{var u=Math.floor(Math.log(n)/Math.LN2);1024===u&&(u=1023),t(4503599627370496*(a=n*Math.pow(2,-u))>>>0,i,o+e),t((s<<31|u+1023<<20|1048576*a&1048575)>>>0,i,o+r)}}}function s(t,e,r,n,i){var o=t(n,i+e),s=t(n,i+r),a=2*(s>>31)+1,u=s>>>20&2047,c=4294967296*(1048575&s)+o;return 2047===u?c?NaN:a*(1/0):0===u?5e-324*a*c:a*Math.pow(2,u-1075)*(c+4503599627370496)}t.writeDoubleLE=e.bind(null,r,0,4),t.writeDoubleBE=e.bind(null,n,4,0),t.readDoubleLE=s.bind(null,i,0,4),t.readDoubleBE=s.bind(null,o,4,0)}(),t}function r(t,e,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24}function n(t,e,r){e[r]=t>>>24,e[r+1]=t>>>16&255,e[r+2]=t>>>8&255,e[r+3]=255&t}function i(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}function o(t,e){return(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}t.exports=e(e)},84153:module=>{"use strict";function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(t){}return null}module.exports=inquire},99390:t=>{"use strict";t.exports=function(t,e,r){var n=r||8192,i=n>>>1,o=null,s=n;return function(r){if(r<1||r>i)return t(r);s+r>n&&(o=t(n),s=0);var a=e.call(o,s,s+=r);return 7&s&&(s=1+(7|s)),a}}},81447:(t,e)=>{"use strict";var r=e;r.length=function(t){for(var e=0,r=0,n=0;n<t.length;++n)(r=t.charCodeAt(n))<128?e+=1:r<2048?e+=2:55296==(64512&r)&&56320==(64512&t.charCodeAt(n+1))?(++n,e+=4):e+=3;return e},r.read=function(t,e,r){if(r-e<1)return"";for(var n,i=null,o=[],s=0;e<r;)(n=t[e++])<128?o[s++]=n:n>191&&n<224?o[s++]=(31&n)<<6|63&t[e++]:n>239&&n<365?(n=((7&n)<<18|(63&t[e++])<<12|(63&t[e++])<<6|63&t[e++])-65536,o[s++]=55296+(n>>10),o[s++]=56320+(1023&n)):o[s++]=(15&n)<<12|(63&t[e++])<<6|63&t[e++],s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,o)),s=0);return i?(s&&i.push(String.fromCharCode.apply(String,o.slice(0,s))),i.join("")):String.fromCharCode.apply(String,o.slice(0,s))},r.write=function(t,e,r){for(var n,i,o=r,s=0;s<t.length;++s)(n=t.charCodeAt(s))<128?e[r++]=n:n<2048?(e[r++]=n>>6|192,e[r++]=63&n|128):55296==(64512&n)&&56320==(64512&(i=t.charCodeAt(s+1)))?(n=65536+((1023&n)<<10)+(1023&i),++s,e[r++]=n>>18|240,e[r++]=n>>12&63|128,e[r++]=n>>6&63|128,e[r++]=63&n|128):(e[r++]=n>>12|224,e[r++]=n>>6&63|128,e[r++]=63&n|128);return r-o}},67526:(t,e)=>{"use strict";e.byteLength=function(t){var e=a(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){var e,r,o=a(t),s=o[0],u=o[1],c=new i(function(t,e,r){return 3*(e+r)/4-r}(0,s,u)),h=0,f=u>0?s-4:s;for(r=0;r<f;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],c[h++]=e>>16&255,c[h++]=e>>8&255,c[h++]=255&e;return 2===u&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,c[h++]=255&e),1===u&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,c[h++]=e>>8&255,c[h++]=255&e),c},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=16383,a=0,c=n-i;a<c;a+=s)o.push(u(t,a,a+s>c?c:a+s));return 1===i?(e=t[n-1],o.push(r[e>>2]+r[e<<4&63]+"==")):2===i&&(e=(t[n-2]<<8)+t[n-1],o.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"=")),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0;s<64;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function a(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function u(t,e,n){for(var i,o,s=[],a=e;a<n;a+=3)i=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),s.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return s.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},48287:(t,e,r)=>{"use strict";const n=r(67526),i=r(251),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.Buffer=u,e.INSPECT_MAX_BYTES=50;const s=2147483647;function a(t){if(t>s)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,u.prototype),e}function u(t,e,r){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return f(t)}return c(t,e,r)}function c(t,e,r){if("string"==typeof t)return function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!u.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const r=0|y(t,e);let n=a(r);const i=n.write(t,e);return i!==r&&(n=n.slice(0,i)),n}(t,e);if(ArrayBuffer.isView(t))return function(t){if(Q(t,Uint8Array)){const e=new Uint8Array(t);return p(e.buffer,e.byteOffset,e.byteLength)}return l(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(Q(t,ArrayBuffer)||t&&Q(t.buffer,ArrayBuffer))return p(t,e,r);if("undefined"!=typeof SharedArrayBuffer&&(Q(t,SharedArrayBuffer)||t&&Q(t.buffer,SharedArrayBuffer)))return p(t,e,r);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');const n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return u.from(n,e,r);const i=function(t){if(u.isBuffer(t)){const e=0|d(t.length),r=a(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||Z(t.length)?a(0):l(t):"Buffer"===t.type&&Array.isArray(t.data)?l(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return u.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function h(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function f(t){return h(t),a(t<0?0:0|d(t))}function l(t){const e=t.length<0?0:0|d(t.length),r=a(e);for(let n=0;n<e;n+=1)r[n]=255&t[n];return r}function p(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');let n;return n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),Object.setPrototypeOf(n,u.prototype),n}function d(t){if(t>=s)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s.toString(16)+" bytes");return 0|t}function y(t,e){if(u.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||Q(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return G(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return W(t).length;default:if(i)return n?-1:G(t).length;e=(""+e).toLowerCase(),i=!0}}function g(t,e,r){let n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return R(this,e,r);case"utf8":case"utf-8":return T(this,e,r);case"ascii":return k(this,e,r);case"latin1":case"binary":return x(this,e,r);case"base64":return C(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function m(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function b(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),Z(r=+r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:v(t,e,r,n,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):v(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function v(t,e,r,n,i){let o,s=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;s=2,a/=2,u/=2,r/=2}function c(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){let n=-1;for(o=r;o<a;o++)if(c(t,o)===c(e,-1===n?0:o-n)){if(-1===n&&(n=o),o-n+1===u)return n*s}else-1!==n&&(o-=o-n),n=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){let r=!0;for(let n=0;n<u;n++)if(c(t,o+n)!==c(e,n)){r=!1;break}if(r)return o}return-1}function w(t,e,r,n){r=Number(r)||0;const i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;const o=e.length;let s;for(n>o/2&&(n=o/2),s=0;s<n;++s){const n=parseInt(e.substr(2*s,2),16);if(Z(n))return s;t[r+s]=n}return s}function E(t,e,r,n){return $(G(e,t.length-r),t,r,n)}function S(t,e,r,n){return $(function(t){const e=[];for(let r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function B(t,e,r,n){return $(W(e),t,r,n)}function A(t,e,r,n){return $(function(t,e){let r,n,i;const o=[];for(let s=0;s<t.length&&!((e-=2)<0);++s)r=t.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function C(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function T(t,e,r){r=Math.min(t.length,r);const n=[];let i=e;for(;i<r;){const e=t[i];let o=null,s=e>239?4:e>223?3:e>191?2:1;if(i+s<=r){let r,n,a,u;switch(s){case 1:e<128&&(o=e);break;case 2:r=t[i+1],128==(192&r)&&(u=(31&e)<<6|63&r,u>127&&(o=u));break;case 3:r=t[i+1],n=t[i+2],128==(192&r)&&128==(192&n)&&(u=(15&e)<<12|(63&r)<<6|63&n,u>2047&&(u<55296||u>57343)&&(o=u));break;case 4:r=t[i+1],n=t[i+2],a=t[i+3],128==(192&r)&&128==(192&n)&&128==(192&a)&&(u=(15&e)<<18|(63&r)<<12|(63&n)<<6|63&a,u>65535&&u<1114112&&(o=u))}}null===o?(o=65533,s=1):o>65535&&(o-=65536,n.push(o>>>10&1023|55296),o=56320|1023&o),n.push(o),i+=s}return function(t){const e=t.length;if(e<=I)return String.fromCharCode.apply(String,t);let r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=I));return r}(n)}u.TYPED_ARRAY_SUPPORT=function(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),u.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(u.prototype,"parent",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.buffer}}),Object.defineProperty(u.prototype,"offset",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.byteOffset}}),u.poolSize=8192,u.from=function(t,e,r){return c(t,e,r)},Object.setPrototypeOf(u.prototype,Uint8Array.prototype),Object.setPrototypeOf(u,Uint8Array),u.alloc=function(t,e,r){return function(t,e,r){return h(t),t<=0?a(t):void 0!==e?"string"==typeof r?a(t).fill(e,r):a(t).fill(e):a(t)}(t,e,r)},u.allocUnsafe=function(t){return f(t)},u.allocUnsafeSlow=function(t){return f(t)},u.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==u.prototype},u.compare=function(t,e){if(Q(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),Q(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let r=t.length,n=e.length;for(let i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);let r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;const n=u.allocUnsafe(e);let i=0;for(r=0;r<t.length;++r){let e=t[r];if(Q(e,Uint8Array))i+e.length>n.length?(u.isBuffer(e)||(e=u.from(e)),e.copy(n,i)):Uint8Array.prototype.set.call(n,e,i);else{if(!u.isBuffer(e))throw new TypeError('"list" argument must be an Array of Buffers');e.copy(n,i)}i+=e.length}return n},u.byteLength=y,u.prototype._isBuffer=!0,u.prototype.swap16=function(){const t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)m(this,e,e+1);return this},u.prototype.swap32=function(){const t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)m(this,e,e+3),m(this,e+1,e+2);return this},u.prototype.swap64=function(){const t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)m(this,e,e+7),m(this,e+1,e+6),m(this,e+2,e+5),m(this,e+3,e+4);return this},u.prototype.toString=function(){const t=this.length;return 0===t?"":0===arguments.length?T(this,0,t):g.apply(this,arguments)},u.prototype.toLocaleString=u.prototype.toString,u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){let t="";const r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(u.prototype[o]=u.prototype.inspect),u.prototype.compare=function(t,e,r,n,i){if(Q(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),!u.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;let o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(e>>>=0);const a=Math.min(o,s),c=this.slice(n,i),h=t.slice(e,r);for(let t=0;t<a;++t)if(c[t]!==h[t]){o=c[t],s=h[t];break}return o<s?-1:s<o?1:0},u.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return b(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return b(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}const i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");let o=!1;for(;;)switch(n){case"hex":return w(this,t,e,r);case"utf8":case"utf-8":return E(this,t,e,r);case"ascii":case"latin1":case"binary":return S(this,t,e,r);case"base64":return B(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const I=4096;function k(t,e,r){let n="";r=Math.min(t.length,r);for(let i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function x(t,e,r){let n="";r=Math.min(t.length,r);for(let i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function R(t,e,r){const n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);let i="";for(let n=e;n<r;++n)i+=Y[t[n]];return i}function _(t,e,r){const n=t.slice(e,r);let i="";for(let t=0;t<n.length-1;t+=2)i+=String.fromCharCode(n[t]+256*n[t+1]);return i}function P(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function U(t,e,r,n,i,o){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function L(t,e,r,n,i){z(e,n,i,t,r,7);let o=Number(e&BigInt(4294967295));t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[r++]=s,s>>=8,t[r++]=s,s>>=8,t[r++]=s,s>>=8,t[r++]=s,r}function N(t,e,r,n,i){z(e,n,i,t,r,7);let o=Number(e&BigInt(4294967295));t[r+7]=o,o>>=8,t[r+6]=o,o>>=8,t[r+5]=o,o>>=8,t[r+4]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[r+3]=s,s>>=8,t[r+2]=s,s>>=8,t[r+1]=s,s>>=8,t[r]=s,r+8}function D(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function O(t,e,r,n,o){return e=+e,r>>>=0,o||D(t,0,r,4),i.write(t,e,r,n,23,4),r+4}function K(t,e,r,n,o){return e=+e,r>>>=0,o||D(t,0,r,8),i.write(t,e,r,n,52,8),r+8}u.prototype.slice=function(t,e){const r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);const n=this.subarray(t,e);return Object.setPrototypeOf(n,u.prototype),n},u.prototype.readUintLE=u.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||P(t,e,this.length);let n=this[t],i=1,o=0;for(;++o<e&&(i*=256);)n+=this[t+o]*i;return n},u.prototype.readUintBE=u.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||P(t,e,this.length);let n=this[t+--e],i=1;for(;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUint8=u.prototype.readUInt8=function(t,e){return t>>>=0,e||P(t,1,this.length),this[t]},u.prototype.readUint16LE=u.prototype.readUInt16LE=function(t,e){return t>>>=0,e||P(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUint16BE=u.prototype.readUInt16BE=function(t,e){return t>>>=0,e||P(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUint32LE=u.prototype.readUInt32LE=function(t,e){return t>>>=0,e||P(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUint32BE=u.prototype.readUInt32BE=function(t,e){return t>>>=0,e||P(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readBigUInt64LE=J((function(t){F(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=e+256*this[++t]+65536*this[++t]+this[++t]*2**24,i=this[++t]+256*this[++t]+65536*this[++t]+r*2**24;return BigInt(n)+(BigInt(i)<<BigInt(32))})),u.prototype.readBigUInt64BE=J((function(t){F(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=e*2**24+65536*this[++t]+256*this[++t]+this[++t],i=this[++t]*2**24+65536*this[++t]+256*this[++t]+r;return(BigInt(n)<<BigInt(32))+BigInt(i)})),u.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||P(t,e,this.length);let n=this[t],i=1,o=0;for(;++o<e&&(i*=256);)n+=this[t+o]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||P(t,e,this.length);let n=e,i=1,o=this[t+--n];for(;n>0&&(i*=256);)o+=this[t+--n]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return t>>>=0,e||P(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){t>>>=0,e||P(t,2,this.length);const r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(t,e){t>>>=0,e||P(t,2,this.length);const r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(t,e){return t>>>=0,e||P(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return t>>>=0,e||P(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readBigInt64LE=J((function(t){F(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=this[t+4]+256*this[t+5]+65536*this[t+6]+(r<<24);return(BigInt(n)<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+this[++t]*2**24)})),u.prototype.readBigInt64BE=J((function(t){F(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=(e<<24)+65536*this[++t]+256*this[++t]+this[++t];return(BigInt(n)<<BigInt(32))+BigInt(this[++t]*2**24+65536*this[++t]+256*this[++t]+r)})),u.prototype.readFloatLE=function(t,e){return t>>>=0,e||P(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return t>>>=0,e||P(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return t>>>=0,e||P(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return t>>>=0,e||P(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUintLE=u.prototype.writeUIntLE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||U(this,t,e,r,Math.pow(2,8*r)-1,0);let i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},u.prototype.writeUintBE=u.prototype.writeUIntBE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||U(this,t,e,r,Math.pow(2,8*r)-1,0);let i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},u.prototype.writeUint8=u.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,1,255,0),this[e]=255&t,e+1},u.prototype.writeUint16LE=u.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeUint16BE=u.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeUint32LE=u.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},u.prototype.writeUint32BE=u.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeBigUInt64LE=J((function(t,e=0){return L(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),u.prototype.writeBigUInt64BE=J((function(t,e=0){return N(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),u.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){const n=Math.pow(2,8*r-1);U(this,t,e,r,n-1,-n)}let i=0,o=1,s=0;for(this[e]=255&t;++i<r&&(o*=256);)t<0&&0===s&&0!==this[e+i-1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){const n=Math.pow(2,8*r-1);U(this,t,e,r,n-1,-n)}let i=r-1,o=1,s=0;for(this[e+i]=255&t;--i>=0&&(o*=256);)t<0&&0===s&&0!==this[e+i+1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},u.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||U(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeBigInt64LE=J((function(t,e=0){return L(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),u.prototype.writeBigInt64BE=J((function(t,e=0){return N(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),u.prototype.writeFloatLE=function(t,e,r){return O(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return O(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return K(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return K(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(!u.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);const i=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),i},u.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){const e=t.charCodeAt(0);("utf8"===n&&e<128||"latin1"===n)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;let i;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{const o=u.isBuffer(t)?t:u.from(t,n),s=o.length;if(0===s)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=o[i%s]}return this};const M={};function j(t,e,r){M[t]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function V(t){let e="",r=t.length;const n="-"===t[0]?1:0;for(;r>=n+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function z(t,e,r,n,i,o){if(t>r||t<e){const n="bigint"==typeof e?"n":"";let i;throw i=o>3?0===e||e===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(o+1)}${n}`:`>= -(2${n} ** ${8*(o+1)-1}${n}) and < 2 ** ${8*(o+1)-1}${n}`:`>= ${e}${n} and <= ${r}${n}`,new M.ERR_OUT_OF_RANGE("value",i,t)}!function(t,e,r){F(e,"offset"),void 0!==t[e]&&void 0!==t[e+r]||q(e,t.length-(r+1))}(n,i,o)}function F(t,e){if("number"!=typeof t)throw new M.ERR_INVALID_ARG_TYPE(e,"number",t)}function q(t,e,r){if(Math.floor(t)!==t)throw F(t,r),new M.ERR_OUT_OF_RANGE(r||"offset","an integer",t);if(e<0)throw new M.ERR_BUFFER_OUT_OF_BOUNDS;throw new M.ERR_OUT_OF_RANGE(r||"offset",`>= ${r?1:0} and <= ${e}`,t)}j("ERR_BUFFER_OUT_OF_BOUNDS",(function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),j("ERR_INVALID_ARG_TYPE",(function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`}),TypeError),j("ERR_OUT_OF_RANGE",(function(t,e,r){let n=`The value of "${t}" is out of range.`,i=r;return Number.isInteger(r)&&Math.abs(r)>2**32?i=V(String(r)):"bigint"==typeof r&&(i=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(i=V(i)),i+="n"),n+=` It must be ${e}. Received ${i}`,n}),RangeError);const H=/[^+/0-9A-Za-z-_]/g;function G(t,e){let r;e=e||1/0;const n=t.length;let i=null;const o=[];for(let s=0;s<n;++s){if(r=t.charCodeAt(s),r>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function W(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(H,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function $(t,e,r,n){let i;for(i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}function Q(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function Z(t){return t!=t}const Y=function(){const t="0123456789abcdef",e=new Array(256);for(let r=0;r<16;++r){const n=16*r;for(let i=0;i<16;++i)e[n+i]=t[r]+t[i]}return e}();function J(t){return"undefined"==typeof BigInt?X:t}function X(){throw new Error("BigInt not supported")}},45998:t=>{"use strict";t.exports=function(t,{className:e,symbolName:r}){const n=Symbol.for(r),i={[e]:class extends t{constructor(...t){super(...t),Object.defineProperty(this,n,{value:!0})}get[Symbol.toStringTag](){return e}}}[e];return i[`is${e}`]=t=>!(!t||!t[n]),i},t.exports.proto=function(t,{className:e,symbolName:r,withoutNew:n}){const i=Symbol.for(r),o={[e]:function(...e){if(n&&!(this instanceof o))return new o(...e);const r=t.call(this,...e)||this;return r&&!r[i]&&Object.defineProperty(r,i,{value:!0}),r}}[e];return o.prototype=Object.create(t.prototype),o.prototype.constructor=o,Object.defineProperty(o.prototype,Symbol.toStringTag,{get:()=>e}),o[`is${e}`]=t=>!(!t||!t[i]),o}},66310:t=>{"use strict";function e(t,e){for(const r in e)Object.defineProperty(t,r,{value:e[r],enumerable:!0,configurable:!0});return t}t.exports=function(t,r,n){if(!t||"string"==typeof t)throw new TypeError("Please pass an Error to err-code");n||(n={}),"object"==typeof r&&(n=r,r=""),r&&(n.code=r);try{return e(t,n)}catch(r){n.message=t.message,n.stack=t.stack;const i=function(){};return i.prototype=Object.create(Object.getPrototypeOf(t)),e(new i,n)}}},251:(t,e)=>{e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,h=-7,f=r?i-1:0,l=r?-1:1,p=t[e+f];for(f+=l,o=p&(1<<-h)-1,p>>=-h,h+=a;h>0;o=256*o+t[e+f],f+=l,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=n;h>0;s=256*s+t[e+f],f+=l,h-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=c}return(p?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,u,c=8*o-i-1,h=(1<<c)-1,f=h>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,d=n?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=h):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),(e+=s+f>=1?l/u:l*Math.pow(2,1-f))*u>=2&&(s++,u/=2),s+f>=h?(a=0,s=h):s+f>=1?(a=(e*u-1)*Math.pow(2,i),s+=f):(a=e*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;t[r+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;t[r+p]=255&s,p+=d,s/=256,c-=8);t[r+p-d]|=128*y}},52579:t=>{"use strict";const e=65536;t.exports=function(t){const r=new Uint8Array(t);let n=0;if(t>0)if(t>e)for(;n<t;)n+e>t?(crypto.getRandomValues(r.subarray(n,n+(t-n))),n+=t-n):(crypto.getRandomValues(r.subarray(n,n+e)),n+=e);else crypto.getRandomValues(r);return r}},64263:(t,e,r)=>{"use strict";const{concat:n}=r(75007),{fromString:i}=r(44117),o=r(77026);t.exports={create:function({algorithm:t="AES-GCM",nonceLength:e=12,keyLength:r=16,digest:s="SHA-256",saltLength:a=16,iterations:u=32767}={}){const c=o.get();return r*=8,{encrypt:async function(o,h){const f=c.getRandomValues(new Uint8Array(a)),l=c.getRandomValues(new Uint8Array(e)),p={name:t,iv:l},d={name:"PBKDF2",salt:f,iterations:u,hash:{name:s}},y=await c.subtle.importKey("raw",i(h),{name:"PBKDF2"},!1,["deriveKey","deriveBits"]),g=await c.subtle.deriveKey(d,y,{name:t,length:r},!0,["encrypt"]),m=await c.subtle.encrypt(p,g,o);return n([f,p.iv,new Uint8Array(m)])},decrypt:async function(n,o){const h=n.slice(0,a),f=n.slice(a,a+e),l=n.slice(a+e),p={name:t,iv:f},d={name:"PBKDF2",salt:h,iterations:u,hash:{name:s}},y=await c.subtle.importKey("raw",i(o),{name:"PBKDF2"},!1,["deriveKey","deriveBits"]),g=await c.subtle.deriveKey(d,y,{name:t,length:r},!0,["decrypt"]),m=await c.subtle.decrypt(p,g,l);return new Uint8Array(m)}}}}},27572:(t,e,r)=>{"use strict";const n=r(77026),i=r(37792),o={SHA1:"SHA-1",SHA256:"SHA-256",SHA512:"SHA-512"};e.create=async function(t,e){const r=o[t],s=await n.get().subtle.importKey("raw",e,{name:"HMAC",hash:{name:r}},!1,["sign"]);return{digest:async t=>(async(t,e)=>{const r=await n.get().subtle.sign({name:"HMAC"},t,e);return new Uint8Array(r,r.byteOffset,r.byteLength)})(s,t),length:i[t]}}},37792:t=>{"use strict";t.exports={SHA1:20,SHA256:32,SHA512:64}},24881:(t,e,r)=>{"use strict";const n=r(66310),i=r(77026),{base64urlToBuffer:o}=r(55981),s=r(50161),{toString:a}=r(27302),{concat:u}=r(75007),{equals:c}=r(18402),h={"P-256":256,"P-384":384,"P-521":521};e.generateEphmeralKeyPair=async function(t){s(Object.keys(h),t);const e=await i.get().subtle.generateKey({name:"ECDH",namedCurve:t},!0,["deriveBits"]);return{key:l(await i.get().subtle.exportKey("jwk",e.publicKey)),genSharedKey:async(r,n)=>{let o;o=n?await i.get().subtle.importKey("jwk",d(t,n),{name:"ECDH",namedCurve:t},!1,["deriveBits"]):e.privateKey;const s=[await i.get().subtle.importKey("jwk",p(t,r),{name:"ECDH",namedCurve:t},!1,[]),o],a=await i.get().subtle.deriveBits({name:"ECDH",namedCurve:t,public:s[0]},s[1],h[t]);return new Uint8Array(a,a.byteOffset,a.byteLength)}}};const f={"P-256":32,"P-384":48,"P-521":66};function l(t){const e=f[t.crv];return u([Uint8Array.from([4]),o(t.x,e),o(t.y,e)],1+2*e)}function p(t,e){const r=f[t];if(c(!e.slice(0,1),Uint8Array.from([4])))throw n(new Error("Cannot unmarshal public key - invalid key format"),"ERR_INVALID_KEY_FORMAT");return{kty:"EC",crv:t,x:a(e.slice(1,r+1),"base64url"),y:a(e.slice(1+r),"base64url"),ext:!0}}const d=(t,e)=>({...p(t,e.public),d:a(e.private,"base64url")})},89522:(t,e,r)=>{"use strict";const n=r(66310),{equals:i}=r(18402),{sha256:o}=r(72190),{base58btc:s}=r(48894),{identity:a}=r(45914),u=r(29279),c=r(29068),h=r(38145);class f{constructor(t){this._key=p(t,u.publicKeyLength)}async verify(t,e){return u.hashAndVerify(this._key,e,t)}marshal(){return this._key}get bytes(){return c.PublicKey.encode({Type:c.KeyType.Ed25519,Data:this.marshal()}).finish()}equals(t){return i(this.bytes,t.bytes)}async hash(){const{bytes:t}=await o.digest(this.bytes);return t}}class l{constructor(t,e){this._key=p(t,u.privateKeyLength),this._publicKey=p(e,u.publicKeyLength)}async sign(t){return u.hashAndSign(this._key,t)}get public(){return new f(this._publicKey)}marshal(){return this._key}get bytes(){return c.PrivateKey.encode({Type:c.KeyType.Ed25519,Data:this.marshal()}).finish()}equals(t){return i(this.bytes,t.bytes)}async hash(){const{bytes:t}=await o.digest(this.bytes);return t}async id(){const t=await a.digest(this.public.bytes);return s.encode(t.bytes).substring(1)}async export(t,e="libp2p-key"){if("libp2p-key"===e)return h.export(this.bytes,t);throw n(new Error(`export format '${e}' is not supported`),"ERR_INVALID_EXPORT_FORMAT")}}function p(t,e){if((t=Uint8Array.from(t||[])).length!==e)throw n(new Error(`Key must be a Uint8Array of length ${e}, got ${t.length}`),"ERR_INVALID_KEY_TYPE");return t}t.exports={Ed25519PublicKey:f,Ed25519PrivateKey:l,unmarshalEd25519PrivateKey:function(t){if(t.length>u.privateKeyLength){const e=(t=p(t,u.privateKeyLength+u.publicKeyLength)).slice(0,u.privateKeyLength),r=t.slice(u.privateKeyLength,t.length);return new l(e,r)}const e=(t=p(t,u.privateKeyLength)).slice(0,u.privateKeyLength),r=t.slice(u.publicKeyLength);return new l(e,r)},unmarshalEd25519PublicKey:function(t){return t=p(t,u.publicKeyLength),new f(t)},generateKeyPair:async function(){const{privateKey:t,publicKey:e}=await u.generateKey();return new l(t,e)},generateKeyPairFromSeed:async function(t){const{privateKey:e,publicKey:r}=await u.generateKeyFromSeed(t);return new l(e,r)}}},29279:(t,e,r)=>{"use strict";const n=r(75111);function i(t,r){const n=new Uint8Array(e.privateKeyLength);for(let e=0;e<32;e++)n[e]=t[e],n[32+e]=r[e];return n}e.publicKeyLength=32,e.privateKeyLength=64,e.generateKey=async function(){const t=n.utils.randomPrivateKey(),e=await n.getPublicKey(t);return{privateKey:i(t,e),publicKey:e}},e.generateKeyFromSeed=async function(t){if(32!==t.length)throw new TypeError('"seed" must be 32 bytes in length.');if(!(t instanceof Uint8Array))throw new TypeError('"seed" must be a node.js Buffer, or Uint8Array.');const e=t,r=await n.getPublicKey(e);return{privateKey:i(e,r),publicKey:r}},e.hashAndSign=function(t,e){const r=t.slice(0,32);return n.sign(e,r)},e.hashAndVerify=function(t,e,r){return n.verify(e,r,t)}},95946:(t,e,r)=>{"use strict";const n=r(24881);t.exports=async t=>n.generateEphmeralKeyPair(t)},38145:(t,e,r)=>{"use strict";const{base64:n}=r(62437),i=r(64263);t.exports={export:async function(t,e){const r=i.create(),o=await r.encrypt(t,e);return n.encode(o)}}},61306:(t,e,r)=>{"use strict";const{base64:n}=r(62437),i=r(64263);t.exports={import:async function(t,e){const r=n.decode(t),o=i.create();return await o.decrypt(r,e)}}},62558:(t,e,r)=>{"use strict";const n=r(29068);r(12746),r(12698);const i=r(50276),o=r(66310),{fromString:s}=r(44117),a=r(61306),u={rsa:r(72297),ed25519:r(89522),secp256k1:r(95139)(n,r(35004))},c="secp256k1 support requires libp2p-crypto-secp256k1 package",h="ERR_MISSING_PACKAGE";function f(t){const e=u[t.toLowerCase()];if(!e){const e=Object.keys(u).join(" / ");throw o(new Error(`invalid or unsupported key type ${t}. Must be ${e}`),"ERR_UNSUPPORTED_KEY_TYPE")}return e}const l=async t=>{const e=n.PrivateKey.decode(t),r=e.Data;switch(e.Type){case n.KeyType.RSA:return u.rsa.unmarshalRsaPrivateKey(r);case n.KeyType.Ed25519:return u.ed25519.unmarshalEd25519PrivateKey(r);case n.KeyType.Secp256k1:if(u.secp256k1)return u.secp256k1.unmarshalSecp256k1PrivateKey(r);throw o(new Error(c),h);default:f(e.Type)}};t.exports={supportedKeys:u,keysPBM:n,keyStretcher:r(91770),generateEphemeralKeyPair:r(95946),generateKeyPair:async(t,e)=>f(t).generateKeyPair(e),generateKeyPairFromSeed:async(t,e,r)=>{const n=f(t);if("ed25519"!==t.toLowerCase())throw o(new Error("Seed key derivation is unimplemented for RSA or secp256k1"),"ERR_UNSUPPORTED_KEY_DERIVATION_TYPE");return n.generateKeyPairFromSeed(e,r)},unmarshalPublicKey:t=>{const e=n.PublicKey.decode(t),r=e.Data;switch(e.Type){case n.KeyType.RSA:return u.rsa.unmarshalRsaPublicKey(r);case n.KeyType.Ed25519:return u.ed25519.unmarshalEd25519PublicKey(r);case n.KeyType.Secp256k1:if(u.secp256k1)return u.secp256k1.unmarshalSecp256k1PublicKey(r);throw o(new Error(c),h);default:f(e.Type)}},marshalPublicKey:(t,e)=>(f(e=(e||"rsa").toLowerCase()),t.bytes),unmarshalPrivateKey:l,marshalPrivateKey:(t,e)=>(f(e=(e||"rsa").toLowerCase()),t.bytes),import:async(t,e)=>{try{const r=await a.import(t,e);return l(r)}catch(t){}const r=i.pki.decryptRsaPrivateKey(t,e);if(null===r)throw o(new Error("Cannot read the key, most likely the password is wrong or not a RSA key"),"ERR_CANNOT_DECRYPT_PEM");let n=i.asn1.toDer(i.pki.privateKeyToAsn1(r));return n=s(n.getBytes(),"ascii"),u.rsa.unmarshalRsaPrivateKey(n)}}},77718:(t,e,r)=>{"use strict";r(15805);const n=r(50276),{base64urlToBigInteger:i}=r(55981);function o(t,e){return e.map((e=>i(t[e])))}t.exports={jwk2pub:function(t){return n.pki.setRsaPublicKey(...o(t,["n","e"]))},jwk2priv:function(t){return n.pki.setRsaPrivateKey(...o(t,["n","e","d","p","q","dp","dq","qi"]))}}},91770:(t,e,r)=>{"use strict";const n=r(66310),{concat:i}=r(75007),{fromString:o}=r(44117),s=r(27572),a={"AES-128":{ivSize:16,keySize:16},"AES-256":{ivSize:16,keySize:32},Blowfish:{ivSize:8,cipherKeySize:32}};t.exports=async(t,e,r)=>{const u=a[t];if(!u){const e=Object.keys(a).join(" / ");throw n(new Error(`unknown cipher type '${t}'. Must be ${e}`),"ERR_INVALID_CIPHER_TYPE")}if(!e)throw n(new Error("missing hash type"),"ERR_MISSING_HASH_TYPE");const c=u.keySize,h=u.ivSize,f=o("key expansion"),l=2*(h+c+20),p=await s.create(e,r);let d=await p.digest(f);const y=[];let g=0;for(;g<l;){const t=await p.digest(i([d,f]));let e=t.length;g+e>l&&(e=l-g),y.push(t),g+=e,d=await p.digest(d)}const m=l/2,b=i(y),v=b.slice(0,m),w=b.slice(m,l),E=t=>({iv:t.slice(0,h),cipherKey:t.slice(h,h+c),macKey:t.slice(h+c)});return{k1:E(v),k2:E(w)}}},29068:(t,e,r)=>{"use strict";var n,i,o=r(26946),s=o.Reader,a=o.Writer,u=o.util,c=o.roots["libp2p-crypto-keys"]||(o.roots["libp2p-crypto-keys"]={});c.KeyType=(n={},(i=Object.create(n))[n[0]="RSA"]=0,i[n[1]="Ed25519"]=1,i[n[2]="Secp256k1"]=2,i),c.PublicKey=function(){function t(t){if(t)for(var e=Object.keys(t),r=0;r<e.length;++r)null!=t[e[r]]&&(this[e[r]]=t[e[r]])}return t.prototype.Type=0,t.prototype.Data=u.newBuffer([]),t.encode=function(t,e){return e||(e=a.create()),e.uint32(8).int32(t.Type),e.uint32(18).bytes(t.Data),e},t.decode=function(t,e){t instanceof s||(t=s.create(t));for(var r=void 0===e?t.len:t.pos+e,n=new c.PublicKey;t.pos<r;){var i=t.uint32();switch(i>>>3){case 1:n.Type=t.int32();break;case 2:n.Data=t.bytes();break;default:t.skipType(7&i)}}if(!n.hasOwnProperty("Type"))throw u.ProtocolError("missing required 'Type'",{instance:n});if(!n.hasOwnProperty("Data"))throw u.ProtocolError("missing required 'Data'",{instance:n});return n},t.fromObject=function(t){if(t instanceof c.PublicKey)return t;var e=new c.PublicKey;switch(t.Type){case"RSA":case 0:e.Type=0;break;case"Ed25519":case 1:e.Type=1;break;case"Secp256k1":case 2:e.Type=2}return null!=t.Data&&("string"==typeof t.Data?u.base64.decode(t.Data,e.Data=u.newBuffer(u.base64.length(t.Data)),0):t.Data.length&&(e.Data=t.Data)),e},t.toObject=function(t,e){e||(e={});var r={};return e.defaults&&(r.Type=e.enums===String?"RSA":0,e.bytes===String?r.Data="":(r.Data=[],e.bytes!==Array&&(r.Data=u.newBuffer(r.Data)))),null!=t.Type&&t.hasOwnProperty("Type")&&(r.Type=e.enums===String?c.KeyType[t.Type]:t.Type),null!=t.Data&&t.hasOwnProperty("Data")&&(r.Data=e.bytes===String?u.base64.encode(t.Data,0,t.Data.length):e.bytes===Array?Array.prototype.slice.call(t.Data):t.Data),r},t.prototype.toJSON=function(){return this.constructor.toObject(this,o.util.toJSONOptions)},t}(),c.PrivateKey=function(){function t(t){if(t)for(var e=Object.keys(t),r=0;r<e.length;++r)null!=t[e[r]]&&(this[e[r]]=t[e[r]])}return t.prototype.Type=0,t.prototype.Data=u.newBuffer([]),t.encode=function(t,e){return e||(e=a.create()),e.uint32(8).int32(t.Type),e.uint32(18).bytes(t.Data),e},t.decode=function(t,e){t instanceof s||(t=s.create(t));for(var r=void 0===e?t.len:t.pos+e,n=new c.PrivateKey;t.pos<r;){var i=t.uint32();switch(i>>>3){case 1:n.Type=t.int32();break;case 2:n.Data=t.bytes();break;default:t.skipType(7&i)}}if(!n.hasOwnProperty("Type"))throw u.ProtocolError("missing required 'Type'",{instance:n});if(!n.hasOwnProperty("Data"))throw u.ProtocolError("missing required 'Data'",{instance:n});return n},t.fromObject=function(t){if(t instanceof c.PrivateKey)return t;var e=new c.PrivateKey;switch(t.Type){case"RSA":case 0:e.Type=0;break;case"Ed25519":case 1:e.Type=1;break;case"Secp256k1":case 2:e.Type=2}return null!=t.Data&&("string"==typeof t.Data?u.base64.decode(t.Data,e.Data=u.newBuffer(u.base64.length(t.Data)),0):t.Data.length&&(e.Data=t.Data)),e},t.toObject=function(t,e){e||(e={});var r={};return e.defaults&&(r.Type=e.enums===String?"RSA":0,e.bytes===String?r.Data="":(r.Data=[],e.bytes!==Array&&(r.Data=u.newBuffer(r.Data)))),null!=t.Type&&t.hasOwnProperty("Type")&&(r.Type=e.enums===String?c.KeyType[t.Type]:t.Type),null!=t.Data&&t.hasOwnProperty("Data")&&(r.Data=e.bytes===String?u.base64.encode(t.Data,0,t.Data.length):e.bytes===Array?Array.prototype.slice.call(t.Data):t.Data),r},t.prototype.toJSON=function(){return this.constructor.toObject(this,o.util.toJSONOptions)},t}(),t.exports=c},17765:(t,e,r)=>{"use strict";const n=r(77026),i=r(35004),{toString:o}=r(27302),{fromString:s}=r(44117);function a(t){return Promise.all([n.get().subtle.exportKey("jwk",t.privateKey),n.get().subtle.exportKey("jwk",t.publicKey)])}e.utils=r(86832),e.generateKey=async function(t){const e=await n.get().subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:t,publicExponent:new Uint8Array([1,0,1]),hash:{name:"SHA-256"}},!0,["sign","verify"]),r=await a(e);return{privateKey:r[0],publicKey:r[1]}},e.unmarshalPrivateKey=async function(t){const e=[await n.get().subtle.importKey("jwk",t,{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!0,["sign"]),await(r=t,n.get().subtle.importKey("jwk",{kty:r.kty,n:r.n,e:r.e},{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!0,["verify"]))];var r;const i=await a({privateKey:e[0],publicKey:e[1]});return{privateKey:i[0],publicKey:i[1]}},e.getRandomValues=i,e.hashAndSign=async function(t,e){const r=await n.get().subtle.importKey("jwk",t,{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!1,["sign"]),i=await n.get().subtle.sign({name:"RSASSA-PKCS1-v1_5"},r,Uint8Array.from(e));return new Uint8Array(i,i.byteOffset,i.byteLength)},e.hashAndVerify=async function(t,e,r){const i=await n.get().subtle.importKey("jwk",t,{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!1,["verify"]);return n.get().subtle.verify({name:"RSASSA-PKCS1-v1_5"},i,e,r)};const{jwk2pub:u,jwk2priv:c}=r(77718);function h(t,e,r,n){const i=e?u(t):c(t),a=n(o(Uint8Array.from(r),"ascii"),i);return s(a,"ascii")}e.encrypt=function(t,e){return h(t,!0,e,((t,e)=>e.encrypt(t)))},e.decrypt=function(t,e){return h(t,!1,e,((t,e)=>e.decrypt(t)))}},72297:(t,e,r)=>{"use strict";const{sha256:n}=r(72190),i=r(66310),{equals:o}=r(18402),{toString:s}=r(27302);r(72313);const a=r(50276),u=r(17765),c=r(29068),h=r(38145);class f{constructor(t){this._key=t}async verify(t,e){return u.hashAndVerify(this._key,e,t)}marshal(){return u.utils.jwkToPkix(this._key)}get bytes(){return c.PublicKey.encode({Type:c.KeyType.RSA,Data:this.marshal()}).finish()}encrypt(t){return u.encrypt(this._key,t)}equals(t){return o(this.bytes,t.bytes)}async hash(){const{bytes:t}=await n.digest(this.bytes);return t}}class l{constructor(t,e){this._key=t,this._publicKey=e}genSecret(){return u.getRandomValues(16)}async sign(t){return u.hashAndSign(this._key,t)}get public(){if(!this._publicKey)throw i(new Error("public key not provided"),"ERR_PUBKEY_NOT_PROVIDED");return new f(this._publicKey)}decrypt(t){return u.decrypt(this._key,t)}marshal(){return u.utils.jwkToPkcs1(this._key)}get bytes(){return c.PrivateKey.encode({Type:c.KeyType.RSA,Data:this.marshal()}).finish()}equals(t){return o(this.bytes,t.bytes)}async hash(){const{bytes:t}=await n.digest(this.bytes);return t}async id(){const t=await this.public.hash();return s(t,"base58btc")}async export(t,e="pkcs-8"){if("pkcs-8"===e){const e=new a.util.ByteBuffer(this.marshal()),r=a.asn1.fromDer(e),n=a.pki.privateKeyFromAsn1(r),i={algorithm:"aes256",count:1e4,saltSize:16,prfAlgorithm:"sha512"};return a.pki.encryptRsaPrivateKey(n,t,i)}if("libp2p-key"===e)return h.export(this.bytes,t);throw i(new Error(`export format '${e}' is not supported`),"ERR_INVALID_EXPORT_FORMAT")}}t.exports={RsaPublicKey:f,RsaPrivateKey:l,unmarshalRsaPublicKey:function(t){const e=u.utils.pkixToJwk(t);return new f(e)},unmarshalRsaPrivateKey:async function(t){const e=u.utils.pkcs1ToJwk(t),r=await u.unmarshalPrivateKey(e);return new l(r.privateKey,r.publicKey)},generateKeyPair:async function(t){const e=await u.generateKey(t);return new l(e.privateKey,e.publicKey)},fromJwk:async function(t){const e=await u.unmarshalPrivateKey(t);return new l(e.privateKey,e.publicKey)}}},86832:(t,e,r)=>{"use strict";r(12746),r(15805);const n=r(50276),{bigIntegerToUintBase64url:i,base64urlToBigInteger:o}=r(55981),{fromString:s}=r(44117),{toString:a}=r(27302);e.pkcs1ToJwk=function(t){const e=n.asn1.fromDer(a(t,"ascii")),r=n.pki.privateKeyFromAsn1(e);return{kty:"RSA",n:i(r.n),e:i(r.e),d:i(r.d),p:i(r.p),q:i(r.q),dp:i(r.dP),dq:i(r.dQ),qi:i(r.qInv),alg:"RS256",kid:"2011-04-29"}},e.jwkToPkcs1=function(t){const e=n.pki.privateKeyToAsn1({n:o(t.n),e:o(t.e),d:o(t.d),p:o(t.p),q:o(t.q),dP:o(t.dp),dQ:o(t.dq),qInv:o(t.qi)});return s(n.asn1.toDer(e).getBytes(),"ascii")},e.pkixToJwk=function(t){const e=n.asn1.fromDer(a(t,"ascii")),r=n.pki.publicKeyFromAsn1(e);return{kty:"RSA",n:i(r.n),e:i(r.e),alg:"RS256",kid:"2011-04-29"}},e.jwkToPkix=function(t){const e=n.pki.publicKeyToAsn1({n:o(t.n),e:o(t.e)});return s(n.asn1.toDer(e).getBytes(),"ascii")}},95139:(t,e,r)=>{"use strict";const{sha256:n}=r(72190),i=r(66310),{equals:o}=r(18402),{toString:s}=r(27302),a=r(38145);t.exports=(t,e,u)=>{u=u||r(86666)();class c{constructor(t){u.validatePublicKey(t),this._key=t}verify(t,e){return u.hashAndVerify(this._key,e,t)}marshal(){return u.compressPublicKey(this._key)}get bytes(){return t.PublicKey.encode({Type:t.KeyType.Secp256k1,Data:this.marshal()}).finish()}equals(t){return o(this.bytes,t.bytes)}async hash(){const{bytes:t}=await n.digest(this.bytes);return t}}class h{constructor(t,e){this._key=t,this._publicKey=e||u.computePublicKey(t),u.validatePrivateKey(this._key),u.validatePublicKey(this._publicKey)}sign(t){return u.hashAndSign(this._key,t)}get public(){return new c(this._publicKey)}marshal(){return this._key}get bytes(){return t.PrivateKey.encode({Type:t.KeyType.Secp256k1,Data:this.marshal()}).finish()}equals(t){return o(this.bytes,t.bytes)}async hash(){const{bytes:t}=await n.digest(this.bytes);return t}async id(){const t=await this.public.hash();return s(t,"base58btc")}async export(t,e="libp2p-key"){if("libp2p-key"===e)return a.export(this.bytes,t);throw i(new Error(`export format '${e}' is not supported`),"ERR_INVALID_EXPORT_FORMAT")}}return{Secp256k1PublicKey:c,Secp256k1PrivateKey:h,unmarshalSecp256k1PrivateKey:function(t){return new h(t)},unmarshalSecp256k1PublicKey:function(t){return new c(t)},generateKeyPair:async function(){const t=await u.generateKey();return new h(t)}}}},86666:(t,e,r)=>{"use strict";const n=r(66310),i=r(9598),{sha256:o}=r(72190);t.exports=()=>({generateKey:function(){return i.utils.randomPrivateKey()},privateKeyLength:32,hashAndSign:async function(t,e){const{digest:r}=await o.digest(e);try{return await i.sign(r,t)}catch(t){throw n(t,"ERR_INVALID_INPUT")}},hashAndVerify:async function(t,e,r){try{const{digest:n}=await o.digest(r);return i.verify(e,n,t)}catch(t){throw n(t,"ERR_INVALID_INPUT")}},compressPublicKey:function(t){return i.Point.fromHex(t).toRawBytes(!0)},decompressPublicKey:function(t){return i.Point.fromHex(t).toRawBytes(!1)},validatePrivateKey:function(t){try{i.getPublicKey(t,!0)}catch(t){throw n(t,"ERR_INVALID_PRIVATE_KEY")}},validatePublicKey:function(t){try{i.Point.fromHex(t)}catch(t){throw n(t,"ERR_INVALID_PUBLIC_KEY")}},computePublicKey:function(t){try{return i.getPublicKey(t,!0)}catch(t){throw n(t,"ERR_INVALID_PRIVATE_KEY")}}})},50161:(t,e,r)=>{"use strict";const n=r(66310);t.exports=function(t,e){if(!t.includes(e)){const r=t.join(" / ");throw n(new Error(`Unknown curve: ${e}. Must be ${r}`),"ERR_INVALID_CURVE")}}},35004:(t,e,r)=>{"use strict";const n=r(52579),i=r(66310);t.exports=function(t){if(isNaN(t)||t<=0)throw i(new Error("random bytes length must be a Number bigger than 0"),"ERR_INVALID_LENGTH");return n(t)}},55981:(t,e,r)=>{"use strict";r(7619),r(43736);const n=r(50276),{fromString:i}=r(44117),{toString:o}=r(27302),{concat:s}=r(75007);e.bigIntegerToUintBase64url=(t,e)=>{let r=Uint8Array.from(t.abs().toByteArray());if(r=0===r[0]?r.slice(1):r,null!=e){if(r.length>e)throw new Error("byte array longer than desired length");r=s([new Uint8Array(e-r.length),r])}return o(r,"base64url")},e.base64urlToBigInteger=t=>{const r=e.base64urlToBuffer(t);return new n.jsbn.BigInteger(o(r,"base16"),16)},e.base64urlToBuffer=(t,e)=>{let r=i(t,"base64urlpad");if(null!=e){if(r.length>e)throw new Error("byte array longer than desired length");r=s([new Uint8Array(e-r.length),r])}return r}},77026:(t,e)=>{"use strict";e.get=(t=globalThis)=>{const e=t.crypto;if(!e||!e.subtle)throw Object.assign(new Error("Missing Web Crypto API. The most likely cause of this error is that this page is being accessed from an insecure context (i.e. not HTTPS). For more information and possible resolutions see https://github.com/libp2p/js-libp2p-crypto/blob/master/README.md#web-crypto-api"),{code:"ERR_MISSING_WEB_CRYPTO"});return e}},39504:(t,e,r)=>{var n=r(50276);function i(t,e){n.cipher.registerAlgorithm(t,(function(){return new n.aes.Algorithm(t,e)}))}r(93900),r(56370),r(7619),t.exports=n.aes=n.aes||{},n.aes.startEncrypting=function(t,e,r,n){var i=y({key:t,output:r,decrypt:!1,mode:n});return i.start(e),i},n.aes.createEncryptionCipher=function(t,e){return y({key:t,output:null,decrypt:!1,mode:e})},n.aes.startDecrypting=function(t,e,r,n){var i=y({key:t,output:r,decrypt:!0,mode:n});return i.start(e),i},n.aes.createDecryptionCipher=function(t,e){return y({key:t,output:null,decrypt:!0,mode:e})},n.aes.Algorithm=function(t,e){h||l();var r=this;r.name=t,r.mode=new e({blockSize:16,cipher:{encrypt:function(t,e){return d(r._w,t,e,!1)},decrypt:function(t,e){return d(r._w,t,e,!0)}}}),r._init=!1},n.aes.Algorithm.prototype.initialize=function(t){if(!this._init){var e,r=t.key;if("string"!=typeof r||16!==r.length&&24!==r.length&&32!==r.length){if(n.util.isArray(r)&&(16===r.length||24===r.length||32===r.length)){e=r,r=n.util.createBuffer();for(var i=0;i<e.length;++i)r.putByte(e[i])}}else r=n.util.createBuffer(r);if(!n.util.isArray(r)){e=r,r=[];var o=e.length();if(16===o||24===o||32===o)for(o>>>=2,i=0;i<o;++i)r.push(e.getInt32())}if(!n.util.isArray(r)||4!==r.length&&6!==r.length&&8!==r.length)throw new Error("Invalid key parameter.");var s=this.mode.name,a=-1!==["CFB","OFB","CTR","GCM"].indexOf(s);this._w=p(r,t.decrypt&&!a),this._init=!0}},n.aes._expandKey=function(t,e){return h||l(),p(t,e)},n.aes._updateBlock=d,i("AES-ECB",n.cipher.modes.ecb),i("AES-CBC",n.cipher.modes.cbc),i("AES-CFB",n.cipher.modes.cfb),i("AES-OFB",n.cipher.modes.ofb),i("AES-CTR",n.cipher.modes.ctr),i("AES-GCM",n.cipher.modes.gcm);var o,s,a,u,c,h=!1,f=4;function l(){h=!0,a=[0,1,2,4,8,16,32,64,128,27,54];for(var t=new Array(256),e=0;e<128;++e)t[e]=e<<1,t[e+128]=e+128<<1^283;for(o=new Array(256),s=new Array(256),u=new Array(4),c=new Array(4),e=0;e<4;++e)u[e]=new Array(256),c[e]=new Array(256);var r,n,i,f,l,p,d,y=0,g=0;for(e=0;e<256;++e){f=(f=g^g<<1^g<<2^g<<3^g<<4)>>8^255&f^99,o[y]=f,s[f]=y,p=(l=t[f])<<24^f<<16^f<<8^f^l,d=((r=t[y])^(n=t[r])^(i=t[n]))<<24^(y^i)<<16^(y^n^i)<<8^y^r^i;for(var m=0;m<4;++m)u[m][y]=p,c[m][f]=d,p=p<<24|p>>>8,d=d<<24|d>>>8;0===y?y=g=1:(y=r^t[t[t[r^i]]],g^=t[t[g]])}}function p(t,e){for(var r,n=t.slice(0),i=1,s=n.length,u=f*(s+6+1),h=s;h<u;++h)r=n[h-1],h%s==0?(r=o[r>>>16&255]<<24^o[r>>>8&255]<<16^o[255&r]<<8^o[r>>>24]^a[i]<<24,i++):s>6&&h%s==4&&(r=o[r>>>24]<<24^o[r>>>16&255]<<16^o[r>>>8&255]<<8^o[255&r]),n[h]=n[h-s]^r;if(e){for(var l,p=c[0],d=c[1],y=c[2],g=c[3],m=n.slice(0),b=(h=0,(u=n.length)-f);h<u;h+=f,b-=f)if(0===h||h===u-f)m[h]=n[b],m[h+1]=n[b+3],m[h+2]=n[b+2],m[h+3]=n[b+1];else for(var v=0;v<f;++v)l=n[b+v],m[h+(3&-v)]=p[o[l>>>24]]^d[o[l>>>16&255]]^y[o[l>>>8&255]]^g[o[255&l]];n=m}return n}function d(t,e,r,n){var i,a,h,f,l,p,d,y,g,m,b,v,w=t.length/4-1;n?(i=c[0],a=c[1],h=c[2],f=c[3],l=s):(i=u[0],a=u[1],h=u[2],f=u[3],l=o),p=e[0]^t[0],d=e[n?3:1]^t[1],y=e[2]^t[2],g=e[n?1:3]^t[3];for(var E=3,S=1;S<w;++S)m=i[p>>>24]^a[d>>>16&255]^h[y>>>8&255]^f[255&g]^t[++E],b=i[d>>>24]^a[y>>>16&255]^h[g>>>8&255]^f[255&p]^t[++E],v=i[y>>>24]^a[g>>>16&255]^h[p>>>8&255]^f[255&d]^t[++E],g=i[g>>>24]^a[p>>>16&255]^h[d>>>8&255]^f[255&y]^t[++E],p=m,d=b,y=v;r[0]=l[p>>>24]<<24^l[d>>>16&255]<<16^l[y>>>8&255]<<8^l[255&g]^t[++E],r[n?3:1]=l[d>>>24]<<24^l[y>>>16&255]<<16^l[g>>>8&255]<<8^l[255&p]^t[++E],r[2]=l[y>>>24]<<24^l[g>>>16&255]<<16^l[p>>>8&255]<<8^l[255&d]^t[++E],r[n?1:3]=l[g>>>24]<<24^l[p>>>16&255]<<16^l[d>>>8&255]<<8^l[255&y]^t[++E]}function y(t){var e,r="AES-"+((t=t||{}).mode||"CBC").toUpperCase(),i=(e=t.decrypt?n.cipher.createDecipher(r,t.key):n.cipher.createCipher(r,t.key)).start;return e.start=function(t,r){var o=null;r instanceof n.util.ByteBuffer&&(o=r,r={}),(r=r||{}).output=o,r.iv=t,i.call(e,r)},e}},12746:(t,e,r)=>{var n=r(50276);r(7619),r(16418);var i=t.exports=n.asn1=n.asn1||{};function o(t,e,r){if(r>e){var n=new Error("Too few bytes to parse DER.");throw n.available=t.length(),n.remaining=e,n.requested=r,n}}function s(t,e,r,n){var a;o(t,e,2);var u=t.getByte();e--;var c=192&u,h=31&u;a=t.length();var f,l,p=function(t,e){var r=t.getByte();if(e--,128!==r){var n;if(128&r){var i=127&r;o(t,e,i),n=t.getInt(i<<3)}else n=r;if(n<0)throw new Error("Negative length: "+n);return n}}(t,e);if(e-=a-t.length(),void 0!==p&&p>e){if(n.strict){var d=new Error("Too few bytes to read ASN.1 value.");throw d.available=t.length(),d.remaining=e,d.requested=p,d}p=e}var y=!(32&~u);if(y)if(f=[],void 0===p)for(;;){if(o(t,e,2),t.bytes(2)===String.fromCharCode(0,0)){t.getBytes(2),e-=2;break}a=t.length(),f.push(s(t,e,r+1,n)),e-=a-t.length()}else for(;p>0;)a=t.length(),f.push(s(t,p,r+1,n)),e-=a-t.length(),p-=a-t.length();if(void 0===f&&c===i.Class.UNIVERSAL&&h===i.Type.BITSTRING&&(l=t.bytes(p)),void 0===f&&n.decodeBitStrings&&c===i.Class.UNIVERSAL&&h===i.Type.BITSTRING&&p>1){var g=t.read,m=e,b=0;if(h===i.Type.BITSTRING&&(o(t,e,1),b=t.getByte(),e--),0===b)try{a=t.length();var v=s(t,e,r+1,{strict:!0,decodeBitStrings:!0}),w=a-t.length();e-=w,h==i.Type.BITSTRING&&w++;var E=v.tagClass;w!==p||E!==i.Class.UNIVERSAL&&E!==i.Class.CONTEXT_SPECIFIC||(f=[v])}catch(t){}void 0===f&&(t.read=g,e=m)}if(void 0===f){if(void 0===p){if(n.strict)throw new Error("Non-constructed ASN.1 object of indefinite length.");p=e}if(h===i.Type.BMPSTRING)for(f="";p>0;p-=2)o(t,e,2),f+=String.fromCharCode(t.getInt16()),e-=2;else f=t.getBytes(p),e-=p}var S=void 0===l?null:{bitStringContents:l};return i.create(c,h,y,f,S)}i.Class={UNIVERSAL:0,APPLICATION:64,CONTEXT_SPECIFIC:128,PRIVATE:192},i.Type={NONE:0,BOOLEAN:1,INTEGER:2,BITSTRING:3,OCTETSTRING:4,NULL:5,OID:6,ODESC:7,EXTERNAL:8,REAL:9,ENUMERATED:10,EMBEDDED:11,UTF8:12,ROID:13,SEQUENCE:16,SET:17,PRINTABLESTRING:19,IA5STRING:22,UTCTIME:23,GENERALIZEDTIME:24,BMPSTRING:30},i.create=function(t,e,r,o,s){if(n.util.isArray(o)){for(var a=[],u=0;u<o.length;++u)void 0!==o[u]&&a.push(o[u]);o=a}var c={tagClass:t,type:e,constructed:r,composed:r||n.util.isArray(o),value:o};return s&&"bitStringContents"in s&&(c.bitStringContents=s.bitStringContents,c.original=i.copy(c)),c},i.copy=function(t,e){var r;if(n.util.isArray(t)){r=[];for(var o=0;o<t.length;++o)r.push(i.copy(t[o],e));return r}return"string"==typeof t?t:(r={tagClass:t.tagClass,type:t.type,constructed:t.constructed,composed:t.composed,value:i.copy(t.value,e)},e&&!e.excludeBitStringContents&&(r.bitStringContents=t.bitStringContents),r)},i.equals=function(t,e,r){if(n.util.isArray(t)){if(!n.util.isArray(e))return!1;if(t.length!==e.length)return!1;for(var o=0;o<t.length;++o)if(!i.equals(t[o],e[o]))return!1;return!0}if(typeof t!=typeof e)return!1;if("string"==typeof t)return t===e;var s=t.tagClass===e.tagClass&&t.type===e.type&&t.constructed===e.constructed&&t.composed===e.composed&&i.equals(t.value,e.value);return r&&r.includeBitStringContents&&(s=s&&t.bitStringContents===e.bitStringContents),s},i.getBerValueLength=function(t){var e=t.getByte();if(128!==e)return 128&e?t.getInt((127&e)<<3):e},i.fromDer=function(t,e){void 0===e&&(e={strict:!0,parseAllBytes:!0,decodeBitStrings:!0}),"boolean"==typeof e&&(e={strict:e,parseAllBytes:!0,decodeBitStrings:!0}),"strict"in e||(e.strict=!0),"parseAllBytes"in e||(e.parseAllBytes=!0),"decodeBitStrings"in e||(e.decodeBitStrings=!0),"string"==typeof t&&(t=n.util.createBuffer(t));var r=t.length(),i=s(t,t.length(),0,e);if(e.parseAllBytes&&0!==t.length()){var o=new Error("Unparsed DER bytes remain after ASN.1 parsing.");throw o.byteCount=r,o.remaining=t.length(),o}return i},i.toDer=function(t){var e=n.util.createBuffer(),r=t.tagClass|t.type,o=n.util.createBuffer(),s=!1;if("bitStringContents"in t&&(s=!0,t.original&&(s=i.equals(t,t.original))),s)o.putBytes(t.bitStringContents);else if(t.composed){t.constructed?r|=32:o.putByte(0);for(var a=0;a<t.value.length;++a)void 0!==t.value[a]&&o.putBuffer(i.toDer(t.value[a]))}else if(t.type===i.Type.BMPSTRING)for(a=0;a<t.value.length;++a)o.putInt16(t.value.charCodeAt(a));else!(t.type===i.Type.INTEGER&&t.value.length>1)||(0!==t.value.charCodeAt(0)||128&t.value.charCodeAt(1))&&(255!==t.value.charCodeAt(0)||128&~t.value.charCodeAt(1))?o.putBytes(t.value):o.putBytes(t.value.substr(1));if(e.putByte(r),o.length()<=127)e.putByte(127&o.length());else{var u=o.length(),c="";do{c+=String.fromCharCode(255&u),u>>>=8}while(u>0);for(e.putByte(128|c.length),a=c.length-1;a>=0;--a)e.putByte(c.charCodeAt(a))}return e.putBuffer(o),e},i.oidToDer=function(t){var e,r,i,o,s=t.split("."),a=n.util.createBuffer();a.putByte(40*parseInt(s[0],10)+parseInt(s[1],10));for(var u=2;u<s.length;++u){e=!0,r=[],i=parseInt(s[u],10);do{o=127&i,i>>>=7,e||(o|=128),r.push(o),e=!1}while(i>0);for(var c=r.length-1;c>=0;--c)a.putByte(r[c])}return a},i.derToOid=function(t){var e;"string"==typeof t&&(t=n.util.createBuffer(t));var r=t.getByte();e=Math.floor(r/40)+"."+r%40;for(var i=0;t.length()>0;)i<<=7,128&(r=t.getByte())?i+=127&r:(e+="."+(i+r),i=0);return e},i.utcTimeToDate=function(t){var e=new Date,r=parseInt(t.substr(0,2),10);r=r>=50?1900+r:2e3+r;var n=parseInt(t.substr(2,2),10)-1,i=parseInt(t.substr(4,2),10),o=parseInt(t.substr(6,2),10),s=parseInt(t.substr(8,2),10),a=0;if(t.length>11){var u=t.charAt(10),c=10;"+"!==u&&"-"!==u&&(a=parseInt(t.substr(10,2),10),c+=2)}if(e.setUTCFullYear(r,n,i),e.setUTCHours(o,s,a,0),c&&("+"===(u=t.charAt(c))||"-"===u)){var h=60*parseInt(t.substr(c+1,2),10)+parseInt(t.substr(c+4,2),10);h*=6e4,"+"===u?e.setTime(+e-h):e.setTime(+e+h)}return e},i.generalizedTimeToDate=function(t){var e=new Date,r=parseInt(t.substr(0,4),10),n=parseInt(t.substr(4,2),10)-1,i=parseInt(t.substr(6,2),10),o=parseInt(t.substr(8,2),10),s=parseInt(t.substr(10,2),10),a=parseInt(t.substr(12,2),10),u=0,c=0,h=!1;"Z"===t.charAt(t.length-1)&&(h=!0);var f=t.length-5,l=t.charAt(f);return"+"!==l&&"-"!==l||(c=60*parseInt(t.substr(f+1,2),10)+parseInt(t.substr(f+4,2),10),c*=6e4,"+"===l&&(c*=-1),h=!0),"."===t.charAt(14)&&(u=1e3*parseFloat(t.substr(14),10)),h?(e.setUTCFullYear(r,n,i),e.setUTCHours(o,s,a,u),e.setTime(+e+c)):(e.setFullYear(r,n,i),e.setHours(o,s,a,u)),e},i.dateToUtcTime=function(t){if("string"==typeof t)return t;var e="",r=[];r.push((""+t.getUTCFullYear()).substr(2)),r.push(""+(t.getUTCMonth()+1)),r.push(""+t.getUTCDate()),r.push(""+t.getUTCHours()),r.push(""+t.getUTCMinutes()),r.push(""+t.getUTCSeconds());for(var n=0;n<r.length;++n)r[n].length<2&&(e+="0"),e+=r[n];return e+"Z"},i.dateToGeneralizedTime=function(t){if("string"==typeof t)return t;var e="",r=[];r.push(""+t.getUTCFullYear()),r.push(""+(t.getUTCMonth()+1)),r.push(""+t.getUTCDate()),r.push(""+t.getUTCHours()),r.push(""+t.getUTCMinutes()),r.push(""+t.getUTCSeconds());for(var n=0;n<r.length;++n)r[n].length<2&&(e+="0"),e+=r[n];return e+"Z"},i.integerToDer=function(t){var e=n.util.createBuffer();if(t>=-128&&t<128)return e.putSignedInt(t,8);if(t>=-32768&&t<32768)return e.putSignedInt(t,16);if(t>=-8388608&&t<8388608)return e.putSignedInt(t,24);if(t>=-2147483648&&t<2147483648)return e.putSignedInt(t,32);var r=new Error("Integer too large; max is 32-bits.");throw r.integer=t,r},i.derToInteger=function(t){"string"==typeof t&&(t=n.util.createBuffer(t));var e=8*t.length();if(e>32)throw new Error("Integer too large; max is 32-bits.");return t.getSignedInt(e)},i.validate=function(t,e,r,o){var s=!1;if(t.tagClass!==e.tagClass&&void 0!==e.tagClass||t.type!==e.type&&void 0!==e.type)o&&(t.tagClass!==e.tagClass&&o.push("["+e.name+'] Expected tag class "'+e.tagClass+'", got "'+t.tagClass+'"'),t.type!==e.type&&o.push("["+e.name+'] Expected type "'+e.type+'", got "'+t.type+'"'));else if(t.constructed===e.constructed||void 0===e.constructed){if(s=!0,e.value&&n.util.isArray(e.value))for(var a=0,u=0;s&&u<e.value.length;++u)s=e.value[u].optional||!1,t.value[a]&&((s=i.validate(t.value[a],e.value[u],r,o))?++a:e.value[u].optional&&(s=!0)),!s&&o&&o.push("["+e.name+'] Tag class "'+e.tagClass+'", type "'+e.type+'" expected value length "'+e.value.length+'", got "'+t.value.length+'"');if(s&&r&&(e.capture&&(r[e.capture]=t.value),e.captureAsn1&&(r[e.captureAsn1]=t),e.captureBitStringContents&&"bitStringContents"in t&&(r[e.captureBitStringContents]=t.bitStringContents),e.captureBitStringValue&&"bitStringContents"in t))if(t.bitStringContents.length<2)r[e.captureBitStringValue]="";else{if(0!==t.bitStringContents.charCodeAt(0))throw new Error("captureBitStringValue only supported for zero unused bits");r[e.captureBitStringValue]=t.bitStringContents.slice(1)}}else o&&o.push("["+e.name+'] Expected constructed "'+e.constructed+'", got "'+t.constructed+'"');return s};var a=/[^\\u0000-\\u00ff]/;i.prettyPrint=function(t,e,r){var o="";r=r||2,(e=e||0)>0&&(o+="\n");for(var s="",u=0;u<e*r;++u)s+=" ";switch(o+=s+"Tag: ",t.tagClass){case i.Class.UNIVERSAL:o+="Universal:";break;case i.Class.APPLICATION:o+="Application:";break;case i.Class.CONTEXT_SPECIFIC:o+="Context-Specific:";break;case i.Class.PRIVATE:o+="Private:"}if(t.tagClass===i.Class.UNIVERSAL)switch(o+=t.type,t.type){case i.Type.NONE:o+=" (None)";break;case i.Type.BOOLEAN:o+=" (Boolean)";break;case i.Type.INTEGER:o+=" (Integer)";break;case i.Type.BITSTRING:o+=" (Bit string)";break;case i.Type.OCTETSTRING:o+=" (Octet string)";break;case i.Type.NULL:o+=" (Null)";break;case i.Type.OID:o+=" (Object Identifier)";break;case i.Type.ODESC:o+=" (Object Descriptor)";break;case i.Type.EXTERNAL:o+=" (External or Instance of)";break;case i.Type.REAL:o+=" (Real)";break;case i.Type.ENUMERATED:o+=" (Enumerated)";break;case i.Type.EMBEDDED:o+=" (Embedded PDV)";break;case i.Type.UTF8:o+=" (UTF8)";break;case i.Type.ROID:o+=" (Relative Object Identifier)";break;case i.Type.SEQUENCE:o+=" (Sequence)";break;case i.Type.SET:o+=" (Set)";break;case i.Type.PRINTABLESTRING:o+=" (Printable String)";break;case i.Type.IA5String:o+=" (IA5String (ASCII))";break;case i.Type.UTCTIME:o+=" (UTC time)";break;case i.Type.GENERALIZEDTIME:o+=" (Generalized time)";break;case i.Type.BMPSTRING:o+=" (BMP String)"}else o+=t.type;if(o+="\n",o+=s+"Constructed: "+t.constructed+"\n",t.composed){var c=0,h="";for(u=0;u<t.value.length;++u)void 0!==t.value[u]&&(c+=1,h+=i.prettyPrint(t.value[u],e+1,r),u+1<t.value.length&&(h+=","));o+=s+"Sub values: "+c+h}else{if(o+=s+"Value: ",t.type===i.Type.OID){var f=i.derToOid(t.value);o+=f,n.pki&&n.pki.oids&&f in n.pki.oids&&(o+=" ("+n.pki.oids[f]+") ")}if(t.type===i.Type.INTEGER)try{o+=i.derToInteger(t.value)}catch(e){o+="0x"+n.util.bytesToHex(t.value)}else if(t.type===i.Type.BITSTRING){if(t.value.length>1?o+="0x"+n.util.bytesToHex(t.value.slice(1)):o+="(none)",t.value.length>0){var l=t.value.charCodeAt(0);1==l?o+=" (1 unused bit shown)":l>1&&(o+=" ("+l+" unused bits shown)")}}else if(t.type===i.Type.OCTETSTRING)a.test(t.value)||(o+="("+t.value+") "),o+="0x"+n.util.bytesToHex(t.value);else if(t.type===i.Type.UTF8)try{o+=n.util.decodeUtf8(t.value)}catch(e){if("URI malformed"!==e.message)throw e;o+="0x"+n.util.bytesToHex(t.value)+" (malformed UTF8)"}else t.type===i.Type.PRINTABLESTRING||t.type===i.Type.IA5String?o+=t.value:a.test(t.value)?o+="0x"+n.util.bytesToHex(t.value):0===t.value.length?o+="[null]":o+=t.value}return o}},44058:t=>{var e={};t.exports=e;var r={};e.encode=function(t,e,r){if("string"!=typeof e)throw new TypeError('"alphabet" must be a string.');if(void 0!==r&&"number"!=typeof r)throw new TypeError('"maxline" must be a number.');var n="";if(t instanceof Uint8Array){var i=0,o=e.length,s=e.charAt(0),a=[0];for(i=0;i<t.length;++i){for(var u=0,c=t[i];u<a.length;++u)c+=a[u]<<8,a[u]=c%o,c=c/o|0;for(;c>0;)a.push(c%o),c=c/o|0}for(i=0;0===t[i]&&i<t.length-1;++i)n+=s;for(i=a.length-1;i>=0;--i)n+=e[a[i]]}else n=function(t,e){var r=0,n=e.length,i=e.charAt(0),o=[0];for(r=0;r<t.length();++r){for(var s=0,a=t.at(r);s<o.length;++s)a+=o[s]<<8,o[s]=a%n,a=a/n|0;for(;a>0;)o.push(a%n),a=a/n|0}var u="";for(r=0;0===t.at(r)&&r<t.length()-1;++r)u+=i;for(r=o.length-1;r>=0;--r)u+=e[o[r]];return u}(t,e);if(r){var h=new RegExp(".{1,"+r+"}","g");n=n.match(h).join("\r\n")}return n},e.decode=function(t,e){if("string"!=typeof t)throw new TypeError('"input" must be a string.');if("string"!=typeof e)throw new TypeError('"alphabet" must be a string.');var n=r[e];if(!n){n=r[e]=[];for(var i=0;i<e.length;++i)n[e.charCodeAt(i)]=i}t=t.replace(/\s/g,"");var o=e.length,s=e.charAt(0),a=[0];for(i=0;i<t.length;i++){var u=n[t.charCodeAt(i)];if(void 0===u)return;for(var c=0,h=u;c<a.length;++c)h+=a[c]*o,a[c]=255&h,h>>=8;for(;h>0;)a.push(255&h),h>>=8}for(var f=0;t[f]===s&&f<t.length-1;++f)a.push(0);return"undefined"!=typeof Buffer?Buffer.from(a.reverse()):new Uint8Array(a.reverse())}},93900:(t,e,r)=>{var n=r(50276);r(7619),t.exports=n.cipher=n.cipher||{},n.cipher.algorithms=n.cipher.algorithms||{},n.cipher.createCipher=function(t,e){var r=t;if("string"==typeof r&&(r=n.cipher.getAlgorithm(r))&&(r=r()),!r)throw new Error("Unsupported algorithm: "+t);return new n.cipher.BlockCipher({algorithm:r,key:e,decrypt:!1})},n.cipher.createDecipher=function(t,e){var r=t;if("string"==typeof r&&(r=n.cipher.getAlgorithm(r))&&(r=r()),!r)throw new Error("Unsupported algorithm: "+t);return new n.cipher.BlockCipher({algorithm:r,key:e,decrypt:!0})},n.cipher.registerAlgorithm=function(t,e){t=t.toUpperCase(),n.cipher.algorithms[t]=e},n.cipher.getAlgorithm=function(t){return(t=t.toUpperCase())in n.cipher.algorithms?n.cipher.algorithms[t]:null};var i=n.cipher.BlockCipher=function(t){this.algorithm=t.algorithm,this.mode=this.algorithm.mode,this.blockSize=this.mode.blockSize,this._finish=!1,this._input=null,this.output=null,this._op=t.decrypt?this.mode.decrypt:this.mode.encrypt,this._decrypt=t.decrypt,this.algorithm.initialize(t)};i.prototype.start=function(t){t=t||{};var e={};for(var r in t)e[r]=t[r];e.decrypt=this._decrypt,this._finish=!1,this._input=n.util.createBuffer(),this.output=t.output||n.util.createBuffer(),this.mode.start(e)},i.prototype.update=function(t){for(t&&this._input.putBuffer(t);!this._op.call(this.mode,this._input,this.output,this._finish)&&!this._finish;);this._input.compact()},i.prototype.finish=function(t){!t||"ECB"!==this.mode.name&&"CBC"!==this.mode.name||(this.mode.pad=function(e){return t(this.blockSize,e,!1)},this.mode.unpad=function(e){return t(this.blockSize,e,!0)});var e={};return e.decrypt=this._decrypt,e.overflow=this._input.length()%this.blockSize,!(!this._decrypt&&this.mode.pad&&!this.mode.pad(this._input,e)||(this._finish=!0,this.update(),this._decrypt&&this.mode.unpad&&!this.mode.unpad(this.output,e)||this.mode.afterFinish&&!this.mode.afterFinish(this.output,e)))}},56370:(t,e,r)=>{var n=r(50276);r(7619),n.cipher=n.cipher||{};var i=t.exports=n.cipher.modes=n.cipher.modes||{};function o(t,e){if("string"==typeof t&&(t=n.util.createBuffer(t)),n.util.isArray(t)&&t.length>4){var r=t;t=n.util.createBuffer();for(var i=0;i<r.length;++i)t.putByte(r[i])}if(t.length()<e)throw new Error("Invalid IV length; got "+t.length()+" bytes and expected "+e+" bytes.");if(!n.util.isArray(t)){var o=[],s=e/4;for(i=0;i<s;++i)o.push(t.getInt32());t=o}return t}function s(t){t[t.length-1]=t[t.length-1]+1&4294967295}function a(t){return[t/4294967296|0,4294967295&t]}i.ecb=function(t){t=t||{},this.name="ECB",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)},i.ecb.prototype.start=function(t){},i.ecb.prototype.encrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=t.getInt32();for(this.cipher.encrypt(this._inBlock,this._outBlock),n=0;n<this._ints;++n)e.putInt32(this._outBlock[n])},i.ecb.prototype.decrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=t.getInt32();for(this.cipher.decrypt(this._inBlock,this._outBlock),n=0;n<this._ints;++n)e.putInt32(this._outBlock[n])},i.ecb.prototype.pad=function(t,e){var r=t.length()===this.blockSize?this.blockSize:this.blockSize-t.length();return t.fillWithByte(r,r),!0},i.ecb.prototype.unpad=function(t,e){if(e.overflow>0)return!1;var r=t.length(),n=t.at(r-1);return!(n>this.blockSize<<2||(t.truncate(n),0))},i.cbc=function(t){t=t||{},this.name="CBC",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)},i.cbc.prototype.start=function(t){if(null===t.iv){if(!this._prev)throw new Error("Invalid IV parameter.");this._iv=this._prev.slice(0)}else{if(!("iv"in t))throw new Error("Invalid IV parameter.");this._iv=o(t.iv,this.blockSize),this._prev=this._iv.slice(0)}},i.cbc.prototype.encrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=this._prev[n]^t.getInt32();for(this.cipher.encrypt(this._inBlock,this._outBlock),n=0;n<this._ints;++n)e.putInt32(this._outBlock[n]);this._prev=this._outBlock},i.cbc.prototype.decrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=t.getInt32();for(this.cipher.decrypt(this._inBlock,this._outBlock),n=0;n<this._ints;++n)e.putInt32(this._prev[n]^this._outBlock[n]);this._prev=this._inBlock.slice(0)},i.cbc.prototype.pad=function(t,e){var r=t.length()===this.blockSize?this.blockSize:this.blockSize-t.length();return t.fillWithByte(r,r),!0},i.cbc.prototype.unpad=function(t,e){if(e.overflow>0)return!1;var r=t.length(),n=t.at(r-1);return!(n>this.blockSize<<2||(t.truncate(n),0))},i.cfb=function(t){t=t||{},this.name="CFB",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialBlock=new Array(this._ints),this._partialOutput=n.util.createBuffer(),this._partialBytes=0},i.cfb.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");this._iv=o(t.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0},i.cfb.prototype.encrypt=function(t,e,r){var n=t.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var i=0;i<this._ints;++i)this._inBlock[i]=t.getInt32()^this._outBlock[i],e.putInt32(this._inBlock[i]);else{var o=(this.blockSize-n)%this.blockSize;for(o>0&&(o=this.blockSize-o),this._partialOutput.clear(),i=0;i<this._ints;++i)this._partialBlock[i]=t.getInt32()^this._outBlock[i],this._partialOutput.putInt32(this._partialBlock[i]);if(o>0)t.read-=this.blockSize;else for(i=0;i<this._ints;++i)this._inBlock[i]=this._partialBlock[i];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),o>0&&!r)return e.putBytes(this._partialOutput.getBytes(o-this._partialBytes)),this._partialBytes=o,!0;e.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}},i.cfb.prototype.decrypt=function(t,e,r){var n=t.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var i=0;i<this._ints;++i)this._inBlock[i]=t.getInt32(),e.putInt32(this._inBlock[i]^this._outBlock[i]);else{var o=(this.blockSize-n)%this.blockSize;for(o>0&&(o=this.blockSize-o),this._partialOutput.clear(),i=0;i<this._ints;++i)this._partialBlock[i]=t.getInt32(),this._partialOutput.putInt32(this._partialBlock[i]^this._outBlock[i]);if(o>0)t.read-=this.blockSize;else for(i=0;i<this._ints;++i)this._inBlock[i]=this._partialBlock[i];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),o>0&&!r)return e.putBytes(this._partialOutput.getBytes(o-this._partialBytes)),this._partialBytes=o,!0;e.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}},i.ofb=function(t){t=t||{},this.name="OFB",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=n.util.createBuffer(),this._partialBytes=0},i.ofb.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");this._iv=o(t.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0},i.ofb.prototype.encrypt=function(t,e,r){var n=t.length();if(0===t.length())return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var i=0;i<this._ints;++i)e.putInt32(t.getInt32()^this._outBlock[i]),this._inBlock[i]=this._outBlock[i];else{var o=(this.blockSize-n)%this.blockSize;for(o>0&&(o=this.blockSize-o),this._partialOutput.clear(),i=0;i<this._ints;++i)this._partialOutput.putInt32(t.getInt32()^this._outBlock[i]);if(o>0)t.read-=this.blockSize;else for(i=0;i<this._ints;++i)this._inBlock[i]=this._outBlock[i];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),o>0&&!r)return e.putBytes(this._partialOutput.getBytes(o-this._partialBytes)),this._partialBytes=o,!0;e.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}},i.ofb.prototype.decrypt=i.ofb.prototype.encrypt,i.ctr=function(t){t=t||{},this.name="CTR",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=n.util.createBuffer(),this._partialBytes=0},i.ctr.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");this._iv=o(t.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0},i.ctr.prototype.encrypt=function(t,e,r){var n=t.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var i=0;i<this._ints;++i)e.putInt32(t.getInt32()^this._outBlock[i]);else{var o=(this.blockSize-n)%this.blockSize;for(o>0&&(o=this.blockSize-o),this._partialOutput.clear(),i=0;i<this._ints;++i)this._partialOutput.putInt32(t.getInt32()^this._outBlock[i]);if(o>0&&(t.read-=this.blockSize),this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),o>0&&!r)return e.putBytes(this._partialOutput.getBytes(o-this._partialBytes)),this._partialBytes=o,!0;e.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}s(this._inBlock)},i.ctr.prototype.decrypt=i.ctr.prototype.encrypt,i.gcm=function(t){t=t||{},this.name="GCM",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints),this._partialOutput=n.util.createBuffer(),this._partialBytes=0,this._R=3774873600},i.gcm.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");var e,r=n.util.createBuffer(t.iv);if(this._cipherLength=0,e="additionalData"in t?n.util.createBuffer(t.additionalData):n.util.createBuffer(),this._tagLength="tagLength"in t?t.tagLength:128,this._tag=null,t.decrypt&&(this._tag=n.util.createBuffer(t.tag).getBytes(),this._tag.length!==this._tagLength/8))throw new Error("Authentication tag does not match tag length.");this._hashBlock=new Array(this._ints),this.tag=null,this._hashSubkey=new Array(this._ints),this.cipher.encrypt([0,0,0,0],this._hashSubkey),this.componentBits=4,this._m=this.generateHashTable(this._hashSubkey,this.componentBits);var i=r.length();if(12===i)this._j0=[r.getInt32(),r.getInt32(),r.getInt32(),1];else{for(this._j0=[0,0,0,0];r.length()>0;)this._j0=this.ghash(this._hashSubkey,this._j0,[r.getInt32(),r.getInt32(),r.getInt32(),r.getInt32()]);this._j0=this.ghash(this._hashSubkey,this._j0,[0,0].concat(a(8*i)))}this._inBlock=this._j0.slice(0),s(this._inBlock),this._partialBytes=0,e=n.util.createBuffer(e),this._aDataLength=a(8*e.length());var o=e.length()%this.blockSize;for(o&&e.fillWithByte(0,this.blockSize-o),this._s=[0,0,0,0];e.length()>0;)this._s=this.ghash(this._hashSubkey,this._s,[e.getInt32(),e.getInt32(),e.getInt32(),e.getInt32()])},i.gcm.prototype.encrypt=function(t,e,r){var n=t.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize){for(var i=0;i<this._ints;++i)e.putInt32(this._outBlock[i]^=t.getInt32());this._cipherLength+=this.blockSize}else{var o=(this.blockSize-n)%this.blockSize;for(o>0&&(o=this.blockSize-o),this._partialOutput.clear(),i=0;i<this._ints;++i)this._partialOutput.putInt32(t.getInt32()^this._outBlock[i]);if(o<=0||r){if(r){var a=n%this.blockSize;this._cipherLength+=a,this._partialOutput.truncate(this.blockSize-a)}else this._cipherLength+=this.blockSize;for(i=0;i<this._ints;++i)this._outBlock[i]=this._partialOutput.getInt32();this._partialOutput.read-=this.blockSize}if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),o>0&&!r)return t.read-=this.blockSize,e.putBytes(this._partialOutput.getBytes(o-this._partialBytes)),this._partialBytes=o,!0;e.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}this._s=this.ghash(this._hashSubkey,this._s,this._outBlock),s(this._inBlock)},i.gcm.prototype.decrypt=function(t,e,r){var n=t.length();if(n<this.blockSize&&!(r&&n>0))return!0;this.cipher.encrypt(this._inBlock,this._outBlock),s(this._inBlock),this._hashBlock[0]=t.getInt32(),this._hashBlock[1]=t.getInt32(),this._hashBlock[2]=t.getInt32(),this._hashBlock[3]=t.getInt32(),this._s=this.ghash(this._hashSubkey,this._s,this._hashBlock);for(var i=0;i<this._ints;++i)e.putInt32(this._outBlock[i]^this._hashBlock[i]);n<this.blockSize?this._cipherLength+=n%this.blockSize:this._cipherLength+=this.blockSize},i.gcm.prototype.afterFinish=function(t,e){var r=!0;e.decrypt&&e.overflow&&t.truncate(this.blockSize-e.overflow),this.tag=n.util.createBuffer();var i=this._aDataLength.concat(a(8*this._cipherLength));this._s=this.ghash(this._hashSubkey,this._s,i);var o=[];this.cipher.encrypt(this._j0,o);for(var s=0;s<this._ints;++s)this.tag.putInt32(this._s[s]^o[s]);return this.tag.truncate(this.tag.length()%(this._tagLength/8)),e.decrypt&&this.tag.bytes()!==this._tag&&(r=!1),r},i.gcm.prototype.multiply=function(t,e){for(var r=[0,0,0,0],n=e.slice(0),i=0;i<128;++i)t[i/32|0]&1<<31-i%32&&(r[0]^=n[0],r[1]^=n[1],r[2]^=n[2],r[3]^=n[3]),this.pow(n,n);return r},i.gcm.prototype.pow=function(t,e){for(var r=1&t[3],n=3;n>0;--n)e[n]=t[n]>>>1|(1&t[n-1])<<31;e[0]=t[0]>>>1,r&&(e[0]^=this._R)},i.gcm.prototype.tableMultiply=function(t){for(var e=[0,0,0,0],r=0;r<32;++r){var n=t[r/8|0]>>>4*(7-r%8)&15,i=this._m[r][n];e[0]^=i[0],e[1]^=i[1],e[2]^=i[2],e[3]^=i[3]}return e},i.gcm.prototype.ghash=function(t,e,r){return e[0]^=r[0],e[1]^=r[1],e[2]^=r[2],e[3]^=r[3],this.tableMultiply(e)},i.gcm.prototype.generateHashTable=function(t,e){for(var r=8/e,n=4*r,i=16*r,o=new Array(i),s=0;s<i;++s){var a=[0,0,0,0],u=(n-1-s%n)*e;a[s/n|0]=1<<e-1<<u,o[s]=this.generateSubHashTable(this.multiply(a,t),e)}return o},i.gcm.prototype.generateSubHashTable=function(t,e){var r=1<<e,n=r>>>1,i=new Array(r);i[n]=t.slice(0);for(var o=n>>>1;o>0;)this.pow(i[2*o],i[o]=[]),o>>=1;for(o=2;o<n;){for(var s=1;s<o;++s){var a=i[o],u=i[s];i[o+s]=[a[0]^u[0],a[1]^u[1],a[2]^u[2],a[3]^u[3]]}o*=2}for(i[0]=[0,0,0,0],o=n+1;o<r;++o){var c=i[o^n];i[o]=[t[0]^c[0],t[1]^c[1],t[2]^c[2],t[3]^c[3]]}return i}},19095:(t,e,r)=>{var n=r(50276);function i(t,e){n.cipher.registerAlgorithm(t,(function(){return new n.des.Algorithm(t,e)}))}r(93900),r(56370),r(7619),t.exports=n.des=n.des||{},n.des.startEncrypting=function(t,e,r,n){var i=d({key:t,output:r,decrypt:!1,mode:n||(null===e?"ECB":"CBC")});return i.start(e),i},n.des.createEncryptionCipher=function(t,e){return d({key:t,output:null,decrypt:!1,mode:e})},n.des.startDecrypting=function(t,e,r,n){var i=d({key:t,output:r,decrypt:!0,mode:n||(null===e?"ECB":"CBC")});return i.start(e),i},n.des.createDecryptionCipher=function(t,e){return d({key:t,output:null,decrypt:!0,mode:e})},n.des.Algorithm=function(t,e){var r=this;r.name=t,r.mode=new e({blockSize:8,cipher:{encrypt:function(t,e){return p(r._keys,t,e,!1)},decrypt:function(t,e){return p(r._keys,t,e,!0)}}}),r._init=!1},n.des.Algorithm.prototype.initialize=function(t){if(!this._init){var e=n.util.createBuffer(t.key);if(0===this.name.indexOf("3DES")&&24!==e.length())throw new Error("Invalid Triple-DES key size: "+8*e.length());this._keys=function(t){for(var e,r=[0,4,536870912,536870916,65536,65540,536936448,536936452,512,516,536871424,536871428,66048,66052,536936960,536936964],n=[0,1,1048576,1048577,67108864,67108865,68157440,68157441,256,257,1048832,1048833,67109120,67109121,68157696,68157697],i=[0,8,2048,2056,16777216,16777224,16779264,16779272,0,8,2048,2056,16777216,16777224,16779264,16779272],o=[0,2097152,134217728,136314880,8192,2105344,134225920,136323072,131072,2228224,134348800,136445952,139264,2236416,134356992,136454144],s=[0,262144,16,262160,0,262144,16,262160,4096,266240,4112,266256,4096,266240,4112,266256],a=[0,1024,32,1056,0,1024,32,1056,33554432,33555456,33554464,33555488,33554432,33555456,33554464,33555488],u=[0,268435456,524288,268959744,2,268435458,524290,268959746,0,268435456,524288,268959744,2,268435458,524290,268959746],c=[0,65536,2048,67584,536870912,536936448,536872960,536938496,131072,196608,133120,198656,537001984,537067520,537004032,537069568],h=[0,262144,0,262144,2,262146,2,262146,33554432,33816576,33554432,33816576,33554434,33816578,33554434,33816578],f=[0,268435456,8,268435464,0,268435456,8,268435464,1024,268436480,1032,268436488,1024,268436480,1032,268436488],l=[0,32,0,32,1048576,1048608,1048576,1048608,8192,8224,8192,8224,1056768,1056800,1056768,1056800],p=[0,16777216,512,16777728,2097152,18874368,2097664,18874880,67108864,83886080,67109376,83886592,69206016,85983232,69206528,85983744],d=[0,4096,134217728,134221824,524288,528384,134742016,134746112,16,4112,134217744,134221840,524304,528400,134742032,134746128],y=[0,4,256,260,0,4,256,260,1,5,257,261,1,5,257,261],g=t.length()>8?3:1,m=[],b=[0,0,1,1,1,1,1,1,0,1,1,1,1,1,1,0],v=0,w=0;w<g;w++){var E=t.getInt32(),S=t.getInt32();E^=(e=252645135&(E>>>4^S))<<4,E^=e=65535&((S^=e)>>>-16^E),E^=(e=858993459&(E>>>2^(S^=e<<-16)))<<2,E^=e=65535&((S^=e)>>>-16^E),E^=(e=1431655765&(E>>>1^(S^=e<<-16)))<<1,E^=e=16711935&((S^=e)>>>8^E),e=(E^=(e=1431655765&(E>>>1^(S^=e<<8)))<<1)<<8|(S^=e)>>>20&240,E=S<<24|S<<8&16711680|S>>>8&65280|S>>>24&240,S=e;for(var B=0;B<b.length;++B){b[B]?(E=E<<2|E>>>26,S=S<<2|S>>>26):(E=E<<1|E>>>27,S=S<<1|S>>>27);var A=r[(E&=-15)>>>28]|n[E>>>24&15]|i[E>>>20&15]|o[E>>>16&15]|s[E>>>12&15]|a[E>>>8&15]|u[E>>>4&15],C=c[(S&=-15)>>>28]|h[S>>>24&15]|f[S>>>20&15]|l[S>>>16&15]|p[S>>>12&15]|d[S>>>8&15]|y[S>>>4&15];e=65535&(C>>>16^A),m[v++]=A^e,m[v++]=C^e<<16}}return m}(e),this._init=!0}},i("DES-ECB",n.cipher.modes.ecb),i("DES-CBC",n.cipher.modes.cbc),i("DES-CFB",n.cipher.modes.cfb),i("DES-OFB",n.cipher.modes.ofb),i("DES-CTR",n.cipher.modes.ctr),i("3DES-ECB",n.cipher.modes.ecb),i("3DES-CBC",n.cipher.modes.cbc),i("3DES-CFB",n.cipher.modes.cfb),i("3DES-OFB",n.cipher.modes.ofb),i("3DES-CTR",n.cipher.modes.ctr);var o=[16843776,0,65536,16843780,16842756,66564,4,65536,1024,16843776,16843780,1024,16778244,16842756,16777216,4,1028,16778240,16778240,66560,66560,16842752,16842752,16778244,65540,16777220,16777220,65540,0,1028,66564,16777216,65536,16843780,4,16842752,16843776,16777216,16777216,1024,16842756,65536,66560,16777220,1024,4,16778244,66564,16843780,65540,16842752,16778244,16777220,1028,66564,16843776,1028,16778240,16778240,0,65540,66560,0,16842756],s=[-2146402272,-2147450880,32768,1081376,1048576,32,-2146435040,-2147450848,-2147483616,-2146402272,-2146402304,-2147483648,-2147450880,1048576,32,-2146435040,1081344,1048608,-2147450848,0,-2147483648,32768,1081376,-2146435072,1048608,-2147483616,0,1081344,32800,-2146402304,-2146435072,32800,0,1081376,-2146435040,1048576,-2147450848,-2146435072,-2146402304,32768,-2146435072,-2147450880,32,-2146402272,1081376,32,32768,-2147483648,32800,-2146402304,1048576,-2147483616,1048608,-2147450848,-2147483616,1048608,1081344,0,-2147450880,32800,-2147483648,-2146435040,-2146402272,1081344],a=[520,134349312,0,134348808,134218240,0,131592,134218240,131080,134217736,134217736,131072,134349320,131080,134348800,520,134217728,8,134349312,512,131584,134348800,134348808,131592,134218248,131584,131072,134218248,8,134349320,512,134217728,134349312,134217728,131080,520,131072,134349312,134218240,0,512,131080,134349320,134218240,134217736,512,0,134348808,134218248,131072,134217728,134349320,8,131592,131584,134217736,134348800,134218248,520,134348800,131592,8,134348808,131584],u=[8396801,8321,8321,128,8396928,8388737,8388609,8193,0,8396800,8396800,8396929,129,0,8388736,8388609,1,8192,8388608,8396801,128,8388608,8193,8320,8388737,1,8320,8388736,8192,8396928,8396929,129,8388736,8388609,8396800,8396929,129,0,0,8396800,8320,8388736,8388737,1,8396801,8321,8321,128,8396929,129,1,8192,8388609,8193,8396928,8388737,8193,8320,8388608,8396801,128,8388608,8192,8396928],c=[256,34078976,34078720,1107296512,524288,256,1073741824,34078720,1074266368,524288,33554688,1074266368,1107296512,1107820544,524544,1073741824,33554432,1074266112,1074266112,0,1073742080,1107820800,1107820800,33554688,1107820544,1073742080,0,1107296256,34078976,33554432,1107296256,524544,524288,1107296512,256,33554432,1073741824,34078720,1107296512,1074266368,33554688,1073741824,1107820544,34078976,1074266368,256,33554432,1107820544,1107820800,524544,1107296256,1107820800,34078720,0,1074266112,1107296256,524544,33554688,1073742080,524288,0,1074266112,34078976,1073742080],h=[536870928,541065216,16384,541081616,541065216,16,541081616,4194304,536887296,4210704,4194304,536870928,4194320,536887296,536870912,16400,0,4194320,536887312,16384,4210688,536887312,16,541065232,541065232,0,4210704,541081600,16400,4210688,541081600,536870912,536887296,16,541065232,4210688,541081616,4194304,16400,536870928,4194304,536887296,536870912,16400,536870928,541081616,4210688,541065216,4210704,541081600,0,541065232,16,16384,541065216,4210704,16384,4194320,536887312,0,541081600,536870912,4194320,536887312],f=[2097152,69206018,67110914,0,2048,67110914,2099202,69208064,69208066,2097152,0,67108866,2,67108864,69206018,2050,67110912,2099202,2097154,67110912,67108866,69206016,69208064,2097154,69206016,2048,2050,69208066,2099200,2,67108864,2099200,67108864,2099200,2097152,67110914,67110914,69206018,69206018,2,2097154,67108864,67110912,2097152,69208064,2050,2099202,69208064,2050,67108866,69208066,69206016,2099200,0,2,69208066,0,2099202,69206016,2048,67108866,67110912,2048,2097154],l=[268439616,4096,262144,268701760,268435456,268439616,64,268435456,262208,268697600,268701760,266240,268701696,266304,4096,64,268697600,268435520,268439552,4160,266240,262208,268697664,268701696,4160,0,0,268697664,268435520,268439552,266304,262144,266304,262144,268701696,4096,64,268697664,4096,266304,268439552,64,268435520,268697600,268697664,268435456,262144,268439616,0,268701760,262208,268435520,268697600,268439552,268439616,0,268701760,266240,266240,4160,4160,262208,268435456,268701696];function p(t,e,r,n){var i,p,d=32===t.length?3:9;i=3===d?n?[30,-2,-2]:[0,32,2]:n?[94,62,-2,32,64,2,30,-2,-2]:[0,32,2,62,30,-2,64,96,2];var y=e[0],g=e[1];y^=(p=252645135&(y>>>4^g))<<4,y^=(p=65535&(y>>>16^(g^=p)))<<16,y^=p=858993459&((g^=p)>>>2^y),y^=p=16711935&((g^=p<<2)>>>8^y),y=(y^=(p=1431655765&(y>>>1^(g^=p<<8)))<<1)<<1|y>>>31,g=(g^=p)<<1|g>>>31;for(var m=0;m<d;m+=3){for(var b=i[m+1],v=i[m+2],w=i[m];w!=b;w+=v){var E=g^t[w],S=(g>>>4|g<<28)^t[w+1];p=y,y=g,g=p^(s[E>>>24&63]|u[E>>>16&63]|h[E>>>8&63]|l[63&E]|o[S>>>24&63]|a[S>>>16&63]|c[S>>>8&63]|f[63&S])}p=y,y=g,g=p}g=g>>>1|g<<31,g^=p=1431655765&((y=y>>>1|y<<31)>>>1^g),g^=(p=16711935&(g>>>8^(y^=p<<1)))<<8,g^=(p=858993459&(g>>>2^(y^=p)))<<2,g^=p=65535&((y^=p)>>>16^g),g^=p=252645135&((y^=p<<16)>>>4^g),y^=p<<4,r[0]=y,r[1]=g}function d(t){var e,r="DES-"+((t=t||{}).mode||"CBC").toUpperCase(),i=(e=t.decrypt?n.cipher.createDecipher(r,t.key):n.cipher.createCipher(r,t.key)).start;return e.start=function(t,r){var o=null;r instanceof n.util.ByteBuffer&&(o=r,r={}),(r=r||{}).output=o,r.iv=t,i.call(e,r)},e}},50276:t=>{t.exports={options:{usePureJavaScript:!1}}},71696:(t,e,r)=>{var n=r(50276);r(8106),r(7619),(t.exports=n.hmac=n.hmac||{}).create=function(){var t=null,e=null,r=null,i=null,o={start:function(o,s){if(null!==o)if("string"==typeof o){if(!((o=o.toLowerCase())in n.md.algorithms))throw new Error('Unknown hash algorithm "'+o+'"');e=n.md.algorithms[o].create()}else e=o;if(null===s)s=t;else{if("string"==typeof s)s=n.util.createBuffer(s);else if(n.util.isArray(s)){var a=s;s=n.util.createBuffer();for(var u=0;u<a.length;++u)s.putByte(a[u])}var c=s.length();for(c>e.blockLength&&(e.start(),e.update(s.bytes()),s=e.digest()),r=n.util.createBuffer(),i=n.util.createBuffer(),c=s.length(),u=0;u<c;++u)a=s.at(u),r.putByte(54^a),i.putByte(92^a);if(c<e.blockLength)for(a=e.blockLength-c,u=0;u<a;++u)r.putByte(54),i.putByte(92);t=s,r=r.bytes(),i=i.bytes()}e.start(),e.update(r)},update:function(t){e.update(t)},getMac:function(){var t=e.digest().bytes();return e.start(),e.update(i),e.update(t),e.digest()}};return o.digest=o.getMac,o}},43736:(t,e,r)=>{var n,i=r(50276);function o(t,e,r){this.data=[],null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function s(){return new o(null)}function a(t,e,r,n,i,o){for(var s=16383&e,a=e>>14;--o>=0;){var u=16383&this.data[t],c=this.data[t++]>>14,h=a*u+c*s;i=((u=s*u+((16383&h)<<14)+r.data[n]+i)>>28)+(h>>14)+a*c,r.data[n++]=268435455&u}return i}t.exports=i.jsbn=i.jsbn||{},i.jsbn.BigInteger=o,"undefined"==typeof navigator?(o.prototype.am=a,n=28):"Microsoft Internet Explorer"==navigator.appName?(o.prototype.am=function(t,e,r,n,i,o){for(var s=32767&e,a=e>>15;--o>=0;){var u=32767&this.data[t],c=this.data[t++]>>15,h=a*u+c*s;i=((u=s*u+((32767&h)<<15)+r.data[n]+(1073741823&i))>>>30)+(h>>>15)+a*c+(i>>>30),r.data[n++]=1073741823&u}return i},n=30):"Netscape"!=navigator.appName?(o.prototype.am=function(t,e,r,n,i,o){for(;--o>=0;){var s=e*this.data[t++]+r.data[n]+i;i=Math.floor(s/67108864),r.data[n++]=67108863&s}return i},n=26):(o.prototype.am=a,n=28),o.prototype.DB=n,o.prototype.DM=(1<<n)-1,o.prototype.DV=1<<n,o.prototype.FV=Math.pow(2,52),o.prototype.F1=52-n,o.prototype.F2=2*n-52;var u,c,h=new Array;for(u="0".charCodeAt(0),c=0;c<=9;++c)h[u++]=c;for(u="a".charCodeAt(0),c=10;c<36;++c)h[u++]=c;for(u="A".charCodeAt(0),c=10;c<36;++c)h[u++]=c;function f(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function l(t,e){var r=h[t.charCodeAt(e)];return null==r?-1:r}function p(t){var e=s();return e.fromInt(t),e}function d(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function y(t){this.m=t}function g(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function m(t,e){return t&e}function b(t,e){return t|e}function v(t,e){return t^e}function w(t,e){return t&~e}function E(t){if(0==t)return-1;var e=0;return 65535&t||(t>>=16,e+=16),255&t||(t>>=8,e+=8),15&t||(t>>=4,e+=4),3&t||(t>>=2,e+=2),1&t||++e,e}function S(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function B(){}function A(t){return t}function C(t){this.r2=s(),this.q3=s(),o.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}y.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},y.prototype.revert=function(t){return t},y.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},y.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},y.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},g.prototype.convert=function(t){var e=s();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(o.ZERO)>0&&this.m.subTo(e,e),e},g.prototype.revert=function(t){var e=s();return t.copyTo(e),this.reduce(e),e},g.prototype.reduce=function(t){for(;t.t<=this.mt2;)t.data[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t.data[e],n=r*this.mpl+((r*this.mph+(t.data[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(r=e+this.m.t,t.data[r]+=this.m.am(0,n,t,e,0,this.m.t);t.data[r]>=t.DV;)t.data[r]-=t.DV,t.data[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},g.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},g.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},o.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t.data[e]=this.data[e];t.t=this.t,t.s=this.s},o.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this.data[0]=t:t<-1?this.data[0]=t+this.DV:this.t=0},o.prototype.fromString=function(t,e){var r;if(16==e)r=4;else if(8==e)r=3;else if(256==e)r=8;else if(2==e)r=1;else if(32==e)r=5;else{if(4!=e)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var n=t.length,i=!1,s=0;--n>=0;){var a=8==r?255&t[n]:l(t,n);a<0?"-"==t.charAt(n)&&(i=!0):(i=!1,0==s?this.data[this.t++]=a:s+r>this.DB?(this.data[this.t-1]|=(a&(1<<this.DB-s)-1)<<s,this.data[this.t++]=a>>this.DB-s):this.data[this.t-1]|=a<<s,(s+=r)>=this.DB&&(s-=this.DB))}8==r&&128&t[0]&&(this.s=-1,s>0&&(this.data[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),i&&o.ZERO.subTo(this,this)},o.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this.data[this.t-1]==t;)--this.t},o.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e.data[r+t]=this.data[r];for(r=t-1;r>=0;--r)e.data[r]=0;e.t=this.t+t,e.s=this.s},o.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e.data[r-t]=this.data[r];e.t=Math.max(this.t-t,0),e.s=this.s},o.prototype.lShiftTo=function(t,e){var r,n=t%this.DB,i=this.DB-n,o=(1<<i)-1,s=Math.floor(t/this.DB),a=this.s<<n&this.DM;for(r=this.t-1;r>=0;--r)e.data[r+s+1]=this.data[r]>>i|a,a=(this.data[r]&o)<<n;for(r=s-1;r>=0;--r)e.data[r]=0;e.data[s]=a,e.t=this.t+s+1,e.s=this.s,e.clamp()},o.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,o=(1<<n)-1;e.data[0]=this.data[r]>>n;for(var s=r+1;s<this.t;++s)e.data[s-r-1]|=(this.data[s]&o)<<i,e.data[s-r]=this.data[s]>>n;n>0&&(e.data[this.t-r-1]|=(this.s&o)<<i),e.t=this.t-r,e.clamp()}},o.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this.data[r]-t.data[r],e.data[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this.data[r],e.data[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t.data[r],e.data[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e.data[r++]=this.DV+n:n>0&&(e.data[r++]=n),e.t=r,e.clamp()},o.prototype.multiplyTo=function(t,e){var r=this.abs(),n=t.abs(),i=r.t;for(e.t=i+n.t;--i>=0;)e.data[i]=0;for(i=0;i<n.t;++i)e.data[i+r.t]=r.am(0,n.data[i],e,i,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&o.ZERO.subTo(e,e)},o.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t.data[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e.data[r],t,2*r,0,1);(t.data[r+e.t]+=e.am(r+1,2*e.data[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t.data[r+e.t]-=e.DV,t.data[r+e.t+1]=1)}t.t>0&&(t.data[t.t-1]+=e.am(r,e.data[r],t,2*r,0,1)),t.s=0,t.clamp()},o.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var i=this.abs();if(i.t<n.t)return null!=e&&e.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=s());var a=s(),u=this.s,c=t.s,h=this.DB-d(n.data[n.t-1]);h>0?(n.lShiftTo(h,a),i.lShiftTo(h,r)):(n.copyTo(a),i.copyTo(r));var f=a.t,l=a.data[f-1];if(0!=l){var p=l*(1<<this.F1)+(f>1?a.data[f-2]>>this.F2:0),y=this.FV/p,g=(1<<this.F1)/p,m=1<<this.F2,b=r.t,v=b-f,w=null==e?s():e;for(a.dlShiftTo(v,w),r.compareTo(w)>=0&&(r.data[r.t++]=1,r.subTo(w,r)),o.ONE.dlShiftTo(f,w),w.subTo(a,a);a.t<f;)a.data[a.t++]=0;for(;--v>=0;){var E=r.data[--b]==l?this.DM:Math.floor(r.data[b]*y+(r.data[b-1]+m)*g);if((r.data[b]+=a.am(0,E,r,v,0,f))<E)for(a.dlShiftTo(v,w),r.subTo(w,r);r.data[b]<--E;)r.subTo(w,r)}null!=e&&(r.drShiftTo(f,e),u!=c&&o.ZERO.subTo(e,e)),r.t=f,r.clamp(),h>0&&r.rShiftTo(h,r),u<0&&o.ZERO.subTo(r,r)}}},o.prototype.invDigit=function(){if(this.t<1)return 0;var t=this.data[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},o.prototype.isEven=function(){return 0==(this.t>0?1&this.data[0]:this.s)},o.prototype.exp=function(t,e){if(t>4294967295||t<1)return o.ONE;var r=s(),n=s(),i=e.convert(this),a=d(t)-1;for(i.copyTo(r);--a>=0;)if(e.sqrTo(r,n),(t&1<<a)>0)e.mulTo(n,i,r);else{var u=r;r=n,n=u}return e.revert(r)},o.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,i=!1,o="",s=this.t,a=this.DB-s*this.DB%e;if(s-- >0)for(a<this.DB&&(r=this.data[s]>>a)>0&&(i=!0,o=f(r));s>=0;)a<e?(r=(this.data[s]&(1<<a)-1)<<e-a,r|=this.data[--s]>>(a+=this.DB-e)):(r=this.data[s]>>(a-=e)&n,a<=0&&(a+=this.DB,--s)),r>0&&(i=!0),i&&(o+=f(r));return i?o:"0"},o.prototype.negate=function(){var t=s();return o.ZERO.subTo(this,t),t},o.prototype.abs=function(){return this.s<0?this.negate():this},o.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this.data[r]-t.data[r]))return e;return 0},o.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+d(this.data[this.t-1]^this.s&this.DM)},o.prototype.mod=function(t){var e=s();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(o.ZERO)>0&&t.subTo(e,e),e},o.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new y(e):new g(e),this.exp(t,r)},o.ZERO=p(0),o.ONE=p(1),B.prototype.convert=A,B.prototype.revert=A,B.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},B.prototype.sqrTo=function(t,e){t.squareTo(e)},C.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=s();return t.copyTo(e),this.reduce(e),e},C.prototype.revert=function(t){return t},C.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},C.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},C.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)};var T=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509],I=(1<<26)/T[T.length-1];o.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},o.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=p(r),i=s(),o=s(),a="";for(this.divRemTo(n,i,o);i.signum()>0;)a=(r+o.intValue()).toString(t).substr(1)+a,i.divRemTo(n,i,o);return o.intValue().toString(t)+a},o.prototype.fromRadix=function(t,e){this.fromInt(0),null==e&&(e=10);for(var r=this.chunkSize(e),n=Math.pow(e,r),i=!1,s=0,a=0,u=0;u<t.length;++u){var c=l(t,u);c<0?"-"==t.charAt(u)&&0==this.signum()&&(i=!0):(a=e*a+c,++s>=r&&(this.dMultiply(n),this.dAddOffset(a,0),s=0,a=0))}s>0&&(this.dMultiply(Math.pow(e,s)),this.dAddOffset(a,0)),i&&o.ZERO.subTo(this,this)},o.prototype.fromNumber=function(t,e,r){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(o.ONE.shiftLeft(t-1),b,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(o.ONE.shiftLeft(t-1),this);else{var n=new Array,i=7&t;n.length=1+(t>>3),e.nextBytes(n),i>0?n[0]&=(1<<i)-1:n[0]=0,this.fromString(n,256)}},o.prototype.bitwiseTo=function(t,e,r){var n,i,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r.data[n]=e(this.data[n],t.data[n]);if(t.t<this.t){for(i=t.s&this.DM,n=o;n<this.t;++n)r.data[n]=e(this.data[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=o;n<t.t;++n)r.data[n]=e(i,t.data[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},o.prototype.changeBit=function(t,e){var r=o.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r},o.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this.data[r]+t.data[r],e.data[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this.data[r],e.data[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t.data[r],e.data[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e.data[r++]=n:n<-1&&(e.data[r++]=this.DV+n),e.t=r,e.clamp()},o.prototype.dMultiply=function(t){this.data[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},o.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this.data[this.t++]=0;for(this.data[e]+=t;this.data[e]>=this.DV;)this.data[e]-=this.DV,++e>=this.t&&(this.data[this.t++]=0),++this.data[e]}},o.prototype.multiplyLowerTo=function(t,e,r){var n,i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r.data[--i]=0;for(n=r.t-this.t;i<n;++i)r.data[i+this.t]=this.am(0,t.data[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t.data[i],r,i,0,e-i);r.clamp()},o.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r.data[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r.data[this.t+n-e]=this.am(e-n,t.data[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},o.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this.data[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this.data[n])%t;return r},o.prototype.millerRabin=function(t){var e=this.subtract(o.ONE),r=e.getLowestSetBit();if(r<=0)return!1;for(var n,i=e.shiftRight(r),s={nextBytes:function(t){for(var e=0;e<t.length;++e)t[e]=Math.floor(256*Math.random())}},a=0;a<t;++a){do{n=new o(this.bitLength(),s)}while(n.compareTo(o.ONE)<=0||n.compareTo(e)>=0);var u=n.modPow(i,this);if(0!=u.compareTo(o.ONE)&&0!=u.compareTo(e)){for(var c=1;c++<r&&0!=u.compareTo(e);)if(0==(u=u.modPowInt(2,this)).compareTo(o.ONE))return!1;if(0!=u.compareTo(e))return!1}}return!0},o.prototype.clone=function(){var t=s();return this.copyTo(t),t},o.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this.data[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this.data[0];if(0==this.t)return 0}return(this.data[1]&(1<<32-this.DB)-1)<<this.DB|this.data[0]},o.prototype.byteValue=function(){return 0==this.t?this.s:this.data[0]<<24>>24},o.prototype.shortValue=function(){return 0==this.t?this.s:this.data[0]<<16>>16},o.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this.data[0]<=0?0:1},o.prototype.toByteArray=function(){var t=this.t,e=new Array;e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(t-- >0)for(n<this.DB&&(r=this.data[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this.data[t]&(1<<n)-1)<<8-n,r|=this.data[--t]>>(n+=this.DB-8)):(r=this.data[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),128&r&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(i>0||r!=this.s)&&(e[i++]=r);return e},o.prototype.equals=function(t){return 0==this.compareTo(t)},o.prototype.min=function(t){return this.compareTo(t)<0?this:t},o.prototype.max=function(t){return this.compareTo(t)>0?this:t},o.prototype.and=function(t){var e=s();return this.bitwiseTo(t,m,e),e},o.prototype.or=function(t){var e=s();return this.bitwiseTo(t,b,e),e},o.prototype.xor=function(t){var e=s();return this.bitwiseTo(t,v,e),e},o.prototype.andNot=function(t){var e=s();return this.bitwiseTo(t,w,e),e},o.prototype.not=function(){for(var t=s(),e=0;e<this.t;++e)t.data[e]=this.DM&~this.data[e];return t.t=this.t,t.s=~this.s,t},o.prototype.shiftLeft=function(t){var e=s();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},o.prototype.shiftRight=function(t){var e=s();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},o.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this.data[t])return t*this.DB+E(this.data[t]);return this.s<0?this.t*this.DB:-1},o.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=S(this.data[r]^e);return t},o.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:!!(this.data[e]&1<<t%this.DB)},o.prototype.setBit=function(t){return this.changeBit(t,b)},o.prototype.clearBit=function(t){return this.changeBit(t,w)},o.prototype.flipBit=function(t){return this.changeBit(t,v)},o.prototype.add=function(t){var e=s();return this.addTo(t,e),e},o.prototype.subtract=function(t){var e=s();return this.subTo(t,e),e},o.prototype.multiply=function(t){var e=s();return this.multiplyTo(t,e),e},o.prototype.divide=function(t){var e=s();return this.divRemTo(t,e,null),e},o.prototype.remainder=function(t){var e=s();return this.divRemTo(t,null,e),e},o.prototype.divideAndRemainder=function(t){var e=s(),r=s();return this.divRemTo(t,e,r),new Array(e,r)},o.prototype.modPow=function(t,e){var r,n,i=t.bitLength(),o=p(1);if(i<=0)return o;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new y(e):e.isEven()?new C(e):new g(e);var a=new Array,u=3,c=r-1,h=(1<<r)-1;if(a[1]=n.convert(this),r>1){var f=s();for(n.sqrTo(a[1],f);u<=h;)a[u]=s(),n.mulTo(f,a[u-2],a[u]),u+=2}var l,m,b=t.t-1,v=!0,w=s();for(i=d(t.data[b])-1;b>=0;){for(i>=c?l=t.data[b]>>i-c&h:(l=(t.data[b]&(1<<i+1)-1)<<c-i,b>0&&(l|=t.data[b-1]>>this.DB+i-c)),u=r;!(1&l);)l>>=1,--u;if((i-=u)<0&&(i+=this.DB,--b),v)a[l].copyTo(o),v=!1;else{for(;u>1;)n.sqrTo(o,w),n.sqrTo(w,o),u-=2;u>0?n.sqrTo(o,w):(m=o,o=w,w=m),n.mulTo(w,a[l],o)}for(;b>=0&&!(t.data[b]&1<<i);)n.sqrTo(o,w),m=o,o=w,w=m,--i<0&&(i=this.DB-1,--b)}return n.revert(o)},o.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return o.ZERO;for(var r=t.clone(),n=this.clone(),i=p(1),s=p(0),a=p(0),u=p(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),e?(i.isEven()&&s.isEven()||(i.addTo(this,i),s.subTo(t,s)),i.rShiftTo(1,i)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;n.isEven();)n.rShiftTo(1,n),e?(a.isEven()&&u.isEven()||(a.addTo(this,a),u.subTo(t,u)),a.rShiftTo(1,a)):u.isEven()||u.subTo(t,u),u.rShiftTo(1,u);r.compareTo(n)>=0?(r.subTo(n,r),e&&i.subTo(a,i),s.subTo(u,s)):(n.subTo(r,n),e&&a.subTo(i,a),u.subTo(s,u))}return 0!=n.compareTo(o.ONE)?o.ZERO:u.compareTo(t)>=0?u.subtract(t):u.signum()<0?(u.addTo(t,u),u.signum()<0?u.add(t):u):u},o.prototype.pow=function(t){return this.exp(t,new B)},o.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(i<o&&(o=i),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},o.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r.data[0]<=T[T.length-1]){for(e=0;e<T.length;++e)if(r.data[0]==T[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<T.length;){for(var n=T[e],i=e+1;i<T.length&&n<I;)n*=T[i++];for(n=r.modInt(n);e<i;)if(n%T[e++]==0)return!1}return r.millerRabin(t)}},8106:(t,e,r)=>{var n=r(50276);t.exports=n.md=n.md||{},n.md.algorithms=n.md.algorithms||{}},16418:(t,e,r)=>{var n=r(50276);n.pki=n.pki||{};var i=t.exports=n.pki.oids=n.oids=n.oids||{};function o(t,e){i[t]=e,i[e]=t}function s(t,e){i[t]=e}o("1.2.840.113549.1.1.1","rsaEncryption"),o("1.2.840.113549.1.1.4","md5WithRSAEncryption"),o("1.2.840.113549.1.1.5","sha1WithRSAEncryption"),o("1.2.840.113549.1.1.7","RSAES-OAEP"),o("1.2.840.113549.1.1.8","mgf1"),o("1.2.840.113549.1.1.9","pSpecified"),o("1.2.840.113549.1.1.10","RSASSA-PSS"),o("1.2.840.113549.1.1.11","sha256WithRSAEncryption"),o("1.2.840.113549.1.1.12","sha384WithRSAEncryption"),o("1.2.840.113549.1.1.13","sha512WithRSAEncryption"),o("1.3.101.112","EdDSA25519"),o("1.2.840.10040.4.3","dsa-with-sha1"),o("1.3.14.3.2.7","desCBC"),o("1.3.14.3.2.26","sha1"),o("1.3.14.3.2.29","sha1WithRSASignature"),o("2.16.840.*********.2.1","sha256"),o("2.16.840.*********.2.2","sha384"),o("2.16.840.*********.2.3","sha512"),o("2.16.840.*********.2.4","sha224"),o("2.16.840.*********.2.5","sha512-224"),o("2.16.840.*********.2.6","sha512-256"),o("1.2.840.113549.2.2","md2"),o("1.2.840.113549.2.5","md5"),o("1.2.840.113549.1.7.1","data"),o("1.2.840.113549.1.7.2","signedData"),o("1.2.840.113549.1.7.3","envelopedData"),o("1.2.840.113549.1.7.4","signedAndEnvelopedData"),o("1.2.840.113549.1.7.5","digestedData"),o("1.2.840.113549.1.7.6","encryptedData"),o("1.2.840.113549.1.9.1","emailAddress"),o("1.2.840.113549.1.9.2","unstructuredName"),o("1.2.840.113549.1.9.3","contentType"),o("1.2.840.113549.1.9.4","messageDigest"),o("1.2.840.113549.1.9.5","signingTime"),o("1.2.840.113549.1.9.6","counterSignature"),o("1.2.840.113549.1.9.7","challengePassword"),o("1.2.840.113549.1.9.8","unstructuredAddress"),o("1.2.840.113549.1.9.14","extensionRequest"),o("1.2.840.113549.1.9.20","friendlyName"),o("1.2.840.113549.1.9.21","localKeyId"),o("1.2.840.113549.********","x509Certificate"),o("1.2.840.113549.*********.1","keyBag"),o("1.2.840.113549.*********.2","pkcs8ShroudedKeyBag"),o("1.2.840.113549.*********.3","certBag"),o("1.2.840.113549.*********.4","crlBag"),o("1.2.840.113549.*********.5","secretBag"),o("1.2.840.113549.*********.6","safeContentsBag"),o("1.2.840.113549.1.5.13","pkcs5PBES2"),o("1.2.840.113549.1.5.12","pkcs5PBKDF2"),o("1.2.840.113549.********","pbeWithSHAAnd128BitRC4"),o("1.2.840.113549.********","pbeWithSHAAnd40BitRC4"),o("1.2.840.113549.********","pbeWithSHAAnd3-KeyTripleDES-CBC"),o("1.2.840.113549.********","pbeWithSHAAnd2-KeyTripleDES-CBC"),o("1.2.840.113549.********","pbeWithSHAAnd128BitRC2-CBC"),o("1.2.840.113549.********","pbewithSHAAnd40BitRC2-CBC"),o("1.2.840.113549.2.7","hmacWithSHA1"),o("1.2.840.113549.2.8","hmacWithSHA224"),o("1.2.840.113549.2.9","hmacWithSHA256"),o("1.2.840.113549.2.10","hmacWithSHA384"),o("1.2.840.113549.2.11","hmacWithSHA512"),o("1.2.840.113549.3.7","des-EDE3-CBC"),o("2.16.840.*********.1.2","aes128-CBC"),o("2.16.840.*********.1.22","aes192-CBC"),o("2.16.840.*********.1.42","aes256-CBC"),o("*******","commonName"),o("*******","surname"),o("*******","serialNumber"),o("*******","countryName"),o("*******","localityName"),o("*******","stateOrProvinceName"),o("*******","streetAddress"),o("********","organizationName"),o("********","organizationalUnitName"),o("********","title"),o("********","description"),o("********","businessCategory"),o("********","postalCode"),o("*******2","givenName"),o("*******.4.1.311.********","jurisdictionOfIncorporationStateOrProvinceName"),o("*******.4.1.311.********","jurisdictionOfIncorporationCountryName"),o("2.16.840.1.113730.1.1","nsCertType"),o("2.16.840.1.113730.1.13","nsComment"),s("********","authorityKeyIdentifier"),s("********","keyAttributes"),s("********","certificatePolicies"),s("********","keyUsageRestriction"),s("********","policyMapping"),s("********","subtreesConstraint"),s("********","subjectAltName"),s("********","issuerAltName"),s("********","subjectDirectoryAttributes"),s("*********","basicConstraints"),s("*********","nameConstraints"),s("*********","policyConstraints"),s("*********","basicConstraints"),o("*********","subjectKeyIdentifier"),o("*********","keyUsage"),s("*********","privateKeyUsagePeriod"),o("*********","subjectAltName"),o("*********","issuerAltName"),o("*********","basicConstraints"),s("*********","cRLNumber"),s("********1","cRLReason"),s("********2","expirationDate"),s("********3","instructionCode"),s("********4","invalidityDate"),s("********5","cRLDistributionPoints"),s("********6","issuingDistributionPoint"),s("********7","deltaCRLIndicator"),s("********8","issuingDistributionPoint"),s("********9","certificateIssuer"),s("********0","nameConstraints"),o("********1","cRLDistributionPoints"),o("********2","certificatePolicies"),s("********3","policyMappings"),s("********4","policyConstraints"),o("********5","authorityKeyIdentifier"),s("********6","policyConstraints"),o("********7","extKeyUsage"),s("********6","freshestCRL"),s("********4","inhibitAnyPolicy"),o("*******.4.1.11129.2.4.2","timestampList"),o("*******.5.5.7.1.1","authorityInfoAccess"),o("*******.5.5.7.3.1","serverAuth"),o("*******.5.5.7.3.2","clientAuth"),o("*******.5.5.7.3.3","codeSigning"),o("*******.5.5.7.3.4","emailProtection"),o("*******.5.5.7.3.8","timeStamping")},12698:(t,e,r)=>{var n=r(50276);if(r(39504),r(12746),r(19095),r(8106),r(16418),r(3254),r(2385),r(89356),r(55124),r(15805),r(7619),void 0===i)var i=n.jsbn.BigInteger;var o=n.asn1,s=n.pki=n.pki||{};t.exports=s.pbe=n.pbe=n.pbe||{};var a=s.oids,u={name:"EncryptedPrivateKeyInfo",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedPrivateKeyInfo.encryptionAlgorithm",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:o.Class.UNIVERSAL,type:o.Type.OID,constructed:!1,capture:"encryptionOid"},{name:"AlgorithmIdentifier.parameters",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,captureAsn1:"encryptionParams"}]},{name:"EncryptedPrivateKeyInfo.encryptedData",tagClass:o.Class.UNIVERSAL,type:o.Type.OCTETSTRING,constructed:!1,capture:"encryptedData"}]},c={name:"PBES2Algorithms",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc.oid",tagClass:o.Class.UNIVERSAL,type:o.Type.OID,constructed:!1,capture:"kdfOid"},{name:"PBES2Algorithms.params",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.params.salt",tagClass:o.Class.UNIVERSAL,type:o.Type.OCTETSTRING,constructed:!1,capture:"kdfSalt"},{name:"PBES2Algorithms.params.iterationCount",tagClass:o.Class.UNIVERSAL,type:o.Type.INTEGER,constructed:!1,capture:"kdfIterationCount"},{name:"PBES2Algorithms.params.keyLength",tagClass:o.Class.UNIVERSAL,type:o.Type.INTEGER,constructed:!1,optional:!0,capture:"keyLength"},{name:"PBES2Algorithms.params.prf",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,optional:!0,value:[{name:"PBES2Algorithms.params.prf.algorithm",tagClass:o.Class.UNIVERSAL,type:o.Type.OID,constructed:!1,capture:"prfOid"}]}]}]},{name:"PBES2Algorithms.encryptionScheme",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.encryptionScheme.oid",tagClass:o.Class.UNIVERSAL,type:o.Type.OID,constructed:!1,capture:"encOid"},{name:"PBES2Algorithms.encryptionScheme.iv",tagClass:o.Class.UNIVERSAL,type:o.Type.OCTETSTRING,constructed:!1,capture:"encIv"}]}]},h={name:"pkcs-12PbeParams",tagClass:o.Class.UNIVERSAL,type:o.Type.SEQUENCE,constructed:!0,value:[{name:"pkcs-12PbeParams.salt",tagClass:o.Class.UNIVERSAL,type:o.Type.OCTETSTRING,constructed:!1,capture:"salt"},{name:"pkcs-12PbeParams.iterations",tagClass:o.Class.UNIVERSAL,type:o.Type.INTEGER,constructed:!1,capture:"iterations"}]};function f(t,e){return t.start().update(e).digest().getBytes()}function l(t){var e;if(t){if(!(e=s.oids[o.derToOid(t)])){var r=new Error("Unsupported PRF OID.");throw r.oid=t,r.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],r}}else e="hmacWithSHA1";return p(e)}function p(t){var e=n.md;switch(t){case"hmacWithSHA224":e=n.md.sha512;case"hmacWithSHA1":case"hmacWithSHA256":case"hmacWithSHA384":case"hmacWithSHA512":t=t.substr(8).toLowerCase();break;default:var r=new Error("Unsupported PRF algorithm.");throw r.algorithm=t,r.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],r}if(!e||!(t in e))throw new Error("Unknown hash algorithm: "+t);return e[t].create()}s.encryptPrivateKeyInfo=function(t,e,r){(r=r||{}).saltSize=r.saltSize||8,r.count=r.count||2048,r.algorithm=r.algorithm||"aes128",r.prfAlgorithm=r.prfAlgorithm||"sha1";var i,u,c,h=n.random.getBytesSync(r.saltSize),f=r.count,l=o.integerToDer(f);if(0===r.algorithm.indexOf("aes")||"des"===r.algorithm){var d,y,g;switch(r.algorithm){case"aes128":i=16,d=16,y=a["aes128-CBC"],g=n.aes.createEncryptionCipher;break;case"aes192":i=24,d=16,y=a["aes192-CBC"],g=n.aes.createEncryptionCipher;break;case"aes256":i=32,d=16,y=a["aes256-CBC"],g=n.aes.createEncryptionCipher;break;case"des":i=8,d=8,y=a.desCBC,g=n.des.createEncryptionCipher;break;default:throw(S=new Error("Cannot encrypt private key. Unknown encryption algorithm.")).algorithm=r.algorithm,S}var m="hmacWith"+r.prfAlgorithm.toUpperCase(),b=p(m),v=n.pkcs5.pbkdf2(e,h,f,i,b),w=n.random.getBytesSync(d);(B=g(v)).start(w),B.update(o.toDer(t)),B.finish(),c=B.output.getBytes();var E=function(t,e,r,i){var a=o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.OCTETSTRING,!1,t),o.create(o.Class.UNIVERSAL,o.Type.INTEGER,!1,e.getBytes())]);return"hmacWithSHA1"!==i&&a.value.push(o.create(o.Class.UNIVERSAL,o.Type.INTEGER,!1,n.util.hexToBytes(r.toString(16))),o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.OID,!1,o.oidToDer(s.oids[i]).getBytes()),o.create(o.Class.UNIVERSAL,o.Type.NULL,!1,"")])),a}(h,l,i,m);u=o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.OID,!1,o.oidToDer(a.pkcs5PBES2).getBytes()),o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.OID,!1,o.oidToDer(a.pkcs5PBKDF2).getBytes()),E]),o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.OID,!1,o.oidToDer(y).getBytes()),o.create(o.Class.UNIVERSAL,o.Type.OCTETSTRING,!1,w)])])])}else{var S;if("3des"!==r.algorithm)throw(S=new Error("Cannot encrypt private key. Unknown encryption algorithm.")).algorithm=r.algorithm,S;i=24;var B,A=new n.util.ByteBuffer(h);v=s.pbe.generatePkcs12Key(e,A,1,f,i),w=s.pbe.generatePkcs12Key(e,A,2,f,i),(B=n.des.createEncryptionCipher(v)).start(w),B.update(o.toDer(t)),B.finish(),c=B.output.getBytes(),u=o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.OID,!1,o.oidToDer(a["pbeWithSHAAnd3-KeyTripleDES-CBC"]).getBytes()),o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[o.create(o.Class.UNIVERSAL,o.Type.OCTETSTRING,!1,h),o.create(o.Class.UNIVERSAL,o.Type.INTEGER,!1,l.getBytes())])])}return o.create(o.Class.UNIVERSAL,o.Type.SEQUENCE,!0,[u,o.create(o.Class.UNIVERSAL,o.Type.OCTETSTRING,!1,c)])},s.decryptPrivateKeyInfo=function(t,e){var r=null,i={},a=[];if(!o.validate(t,u,i,a)){var c=new Error("Cannot read encrypted private key. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw c.errors=a,c}var h=o.derToOid(i.encryptionOid),f=s.pbe.getCipher(h,i.encryptionParams,e),l=n.util.createBuffer(i.encryptedData);return f.update(l),f.finish()&&(r=o.fromDer(f.output)),r},s.encryptedPrivateKeyToPem=function(t,e){var r={type:"ENCRYPTED PRIVATE KEY",body:o.toDer(t).getBytes()};return n.pem.encode(r,{maxline:e})},s.encryptedPrivateKeyFromPem=function(t){var e=n.pem.decode(t)[0];if("ENCRYPTED PRIVATE KEY"!==e.type){var r=new Error('Could not convert encrypted private key from PEM; PEM header type is "ENCRYPTED PRIVATE KEY".');throw r.headerType=e.type,r}if(e.procType&&"ENCRYPTED"===e.procType.type)throw new Error("Could not convert encrypted private key from PEM; PEM is encrypted.");return o.fromDer(e.body)},s.encryptRsaPrivateKey=function(t,e,r){if(!(r=r||{}).legacy){var i=s.wrapRsaPrivateKey(s.privateKeyToAsn1(t));return i=s.encryptPrivateKeyInfo(i,e,r),s.encryptedPrivateKeyToPem(i)}var a,u,c,h;switch(r.algorithm){case"aes128":a="AES-128-CBC",c=16,u=n.random.getBytesSync(16),h=n.aes.createEncryptionCipher;break;case"aes192":a="AES-192-CBC",c=24,u=n.random.getBytesSync(16),h=n.aes.createEncryptionCipher;break;case"aes256":a="AES-256-CBC",c=32,u=n.random.getBytesSync(16),h=n.aes.createEncryptionCipher;break;case"3des":a="DES-EDE3-CBC",c=24,u=n.random.getBytesSync(8),h=n.des.createEncryptionCipher;break;case"des":a="DES-CBC",c=8,u=n.random.getBytesSync(8),h=n.des.createEncryptionCipher;break;default:var f=new Error('Could not encrypt RSA private key; unsupported encryption algorithm "'+r.algorithm+'".');throw f.algorithm=r.algorithm,f}var l=h(n.pbe.opensslDeriveBytes(e,u.substr(0,8),c));l.start(u),l.update(o.toDer(s.privateKeyToAsn1(t))),l.finish();var p={type:"RSA PRIVATE KEY",procType:{version:"4",type:"ENCRYPTED"},dekInfo:{algorithm:a,parameters:n.util.bytesToHex(u).toUpperCase()},body:l.output.getBytes()};return n.pem.encode(p)},s.decryptRsaPrivateKey=function(t,e){var r=null,i=n.pem.decode(t)[0];if("ENCRYPTED PRIVATE KEY"!==i.type&&"PRIVATE KEY"!==i.type&&"RSA PRIVATE KEY"!==i.type)throw(c=new Error('Could not convert private key from PEM; PEM header type is not "ENCRYPTED PRIVATE KEY", "PRIVATE KEY", or "RSA PRIVATE KEY".')).headerType=c,c;if(i.procType&&"ENCRYPTED"===i.procType.type){var a,u;switch(i.dekInfo.algorithm){case"DES-CBC":a=8,u=n.des.createDecryptionCipher;break;case"DES-EDE3-CBC":a=24,u=n.des.createDecryptionCipher;break;case"AES-128-CBC":a=16,u=n.aes.createDecryptionCipher;break;case"AES-192-CBC":a=24,u=n.aes.createDecryptionCipher;break;case"AES-256-CBC":a=32,u=n.aes.createDecryptionCipher;break;case"RC2-40-CBC":a=5,u=function(t){return n.rc2.createDecryptionCipher(t,40)};break;case"RC2-64-CBC":a=8,u=function(t){return n.rc2.createDecryptionCipher(t,64)};break;case"RC2-128-CBC":a=16,u=function(t){return n.rc2.createDecryptionCipher(t,128)};break;default:var c;throw(c=new Error('Could not decrypt private key; unsupported encryption algorithm "'+i.dekInfo.algorithm+'".')).algorithm=i.dekInfo.algorithm,c}var h=n.util.hexToBytes(i.dekInfo.parameters),f=u(n.pbe.opensslDeriveBytes(e,h.substr(0,8),a));if(f.start(h),f.update(n.util.createBuffer(i.body)),!f.finish())return r;r=f.output.getBytes()}else r=i.body;return null!==(r="ENCRYPTED PRIVATE KEY"===i.type?s.decryptPrivateKeyInfo(o.fromDer(r),e):o.fromDer(r))&&(r=s.privateKeyFromAsn1(r)),r},s.pbe.generatePkcs12Key=function(t,e,r,i,o,s){var a,u;if(null==s){if(!("sha1"in n.md))throw new Error('"sha1" hash algorithm unavailable.');s=n.md.sha1.create()}var c=s.digestLength,h=s.blockLength,f=new n.util.ByteBuffer,l=new n.util.ByteBuffer;if(null!=t){for(u=0;u<t.length;u++)l.putInt16(t.charCodeAt(u));l.putInt16(0)}var p=l.length(),d=e.length(),y=new n.util.ByteBuffer;y.fillWithByte(r,h);var g=h*Math.ceil(d/h),m=new n.util.ByteBuffer;for(u=0;u<g;u++)m.putByte(e.at(u%d));var b=h*Math.ceil(p/h),v=new n.util.ByteBuffer;for(u=0;u<b;u++)v.putByte(l.at(u%p));var w=m;w.putBuffer(v);for(var E=Math.ceil(o/c),S=1;S<=E;S++){var B=new n.util.ByteBuffer;B.putBytes(y.bytes()),B.putBytes(w.bytes());for(var A=0;A<i;A++)s.start(),s.update(B.getBytes()),B=s.digest();var C=new n.util.ByteBuffer;for(u=0;u<h;u++)C.putByte(B.at(u%c));var T=Math.ceil(d/h)+Math.ceil(p/h),I=new n.util.ByteBuffer;for(a=0;a<T;a++){var k=new n.util.ByteBuffer(w.getBytes(h)),x=511;for(u=C.length()-1;u>=0;u--)x>>=8,x+=C.at(u)+k.at(u),k.setAt(u,255&x);I.putBuffer(k)}w=I,f.putBuffer(B)}return f.truncate(f.length()-o),f},s.pbe.getCipher=function(t,e,r){switch(t){case s.oids.pkcs5PBES2:return s.pbe.getCipherForPBES2(t,e,r);case s.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:case s.oids["pbewithSHAAnd40BitRC2-CBC"]:return s.pbe.getCipherForPKCS12PBE(t,e,r);default:var n=new Error("Cannot read encrypted PBE data block. Unsupported OID.");throw n.oid=t,n.supportedOids=["pkcs5PBES2","pbeWithSHAAnd3-KeyTripleDES-CBC","pbewithSHAAnd40BitRC2-CBC"],n}},s.pbe.getCipherForPBES2=function(t,e,r){var i,a={},u=[];if(!o.validate(e,c,a,u))throw(i=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.")).errors=u,i;if((t=o.derToOid(a.kdfOid))!==s.oids.pkcs5PBKDF2)throw(i=new Error("Cannot read encrypted private key. Unsupported key derivation function OID.")).oid=t,i.supportedOids=["pkcs5PBKDF2"],i;if((t=o.derToOid(a.encOid))!==s.oids["aes128-CBC"]&&t!==s.oids["aes192-CBC"]&&t!==s.oids["aes256-CBC"]&&t!==s.oids["des-EDE3-CBC"]&&t!==s.oids.desCBC)throw(i=new Error("Cannot read encrypted private key. Unsupported encryption scheme OID.")).oid=t,i.supportedOids=["aes128-CBC","aes192-CBC","aes256-CBC","des-EDE3-CBC","desCBC"],i;var h,f,p=a.kdfSalt,d=n.util.createBuffer(a.kdfIterationCount);switch(d=d.getInt(d.length()<<3),s.oids[t]){case"aes128-CBC":h=16,f=n.aes.createDecryptionCipher;break;case"aes192-CBC":h=24,f=n.aes.createDecryptionCipher;break;case"aes256-CBC":h=32,f=n.aes.createDecryptionCipher;break;case"des-EDE3-CBC":h=24,f=n.des.createDecryptionCipher;break;case"desCBC":h=8,f=n.des.createDecryptionCipher}var y=l(a.prfOid),g=n.pkcs5.pbkdf2(r,p,d,h,y),m=a.encIv,b=f(g);return b.start(m),b},s.pbe.getCipherForPKCS12PBE=function(t,e,r){var i={},a=[];if(!o.validate(e,h,i,a))throw(y=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.")).errors=a,y;var u,c,f,p=n.util.createBuffer(i.salt),d=n.util.createBuffer(i.iterations);switch(d=d.getInt(d.length()<<3),t){case s.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:u=24,c=8,f=n.des.startDecrypting;break;case s.oids["pbewithSHAAnd40BitRC2-CBC"]:u=5,c=8,f=function(t,e){var r=n.rc2.createDecryptionCipher(t,40);return r.start(e,null),r};break;default:var y;throw(y=new Error("Cannot read PKCS #12 PBE data block. Unsupported OID.")).oid=t,y}var g=l(i.prfOid),m=s.pbe.generatePkcs12Key(r,p,1,d,u,g);return g.start(),f(m,s.pbe.generatePkcs12Key(r,p,2,d,c,g))},s.pbe.opensslDeriveBytes=function(t,e,r,i){if(null==i){if(!("md5"in n.md))throw new Error('"md5" hash algorithm unavailable.');i=n.md.md5.create()}null===e&&(e="");for(var o=[f(i,t+e)],s=16,a=1;s<r;++a,s+=16)o.push(f(i,o[a-1]+t+e));return o.join("").substr(0,r)}},3254:(t,e,r)=>{var n=r(50276);r(71696),r(8106),r(7619);var i,o=n.pkcs5=n.pkcs5||{};n.util.isNodejs&&!n.options.usePureJavaScript&&(i=r(50310)),t.exports=n.pbkdf2=o.pbkdf2=function(t,e,r,o,s,a){if("function"==typeof s&&(a=s,s=null),n.util.isNodejs&&!n.options.usePureJavaScript&&i.pbkdf2&&(null===s||"object"!=typeof s)&&(i.pbkdf2Sync.length>4||!s||"sha1"===s))return"string"!=typeof s&&(s="sha1"),t=Buffer.from(t,"binary"),e=Buffer.from(e,"binary"),a?4===i.pbkdf2Sync.length?i.pbkdf2(t,e,r,o,(function(t,e){if(t)return a(t);a(null,e.toString("binary"))})):i.pbkdf2(t,e,r,o,s,(function(t,e){if(t)return a(t);a(null,e.toString("binary"))})):4===i.pbkdf2Sync.length?i.pbkdf2Sync(t,e,r,o).toString("binary"):i.pbkdf2Sync(t,e,r,o,s).toString("binary");if(null==s&&(s="sha1"),"string"==typeof s){if(!(s in n.md.algorithms))throw new Error("Unknown hash algorithm: "+s);s=n.md[s].create()}var u=s.digestLength;if(o>4294967295*u){var c=new Error("Derived key is too long.");if(a)return a(c);throw c}var h=Math.ceil(o/u),f=o-(h-1)*u,l=n.hmac.create();l.start(s,t);var p,d,y,g="";if(!a){for(var m=1;m<=h;++m){l.start(null,null),l.update(e),l.update(n.util.int32ToBytes(m)),p=y=l.digest().getBytes();for(var b=2;b<=r;++b)l.start(null,null),l.update(y),d=l.digest().getBytes(),p=n.util.xorBytes(p,d,u),y=d;g+=m<h?p:p.substr(0,f)}return g}function v(){if(m>h)return a(null,g);l.start(null,null),l.update(e),l.update(n.util.int32ToBytes(m)),p=y=l.digest().getBytes(),b=2,w()}function w(){if(b<=r)return l.start(null,null),l.update(y),d=l.digest().getBytes(),p=n.util.xorBytes(p,d,u),y=d,++b,n.util.setImmediate(w);g+=m<h?p:p.substr(0,f),++m,v()}m=1,v()}},2385:(t,e,r)=>{var n=r(50276);r(7619);var i=t.exports=n.pem=n.pem||{};function o(t){for(var e=t.name+": ",r=[],n=function(t,e){return" "+e},i=0;i<t.values.length;++i)r.push(t.values[i].replace(/^(\S+\r\n)/,n));e+=r.join(",")+"\r\n";var o=0,s=-1;for(i=0;i<e.length;++i,++o)if(o>65&&-1!==s){var a=e[s];","===a?(++s,e=e.substr(0,s)+"\r\n "+e.substr(s)):e=e.substr(0,s)+"\r\n"+a+e.substr(s+1),o=i-s-1,s=-1,++i}else" "!==e[i]&&"\t"!==e[i]&&","!==e[i]||(s=i);return e}function s(t){return t.replace(/^\s+/,"")}i.encode=function(t,e){e=e||{};var r,i="-----BEGIN "+t.type+"-----\r\n";if(t.procType&&(i+=o(r={name:"Proc-Type",values:[String(t.procType.version),t.procType.type]})),t.contentDomain&&(i+=o(r={name:"Content-Domain",values:[t.contentDomain]})),t.dekInfo&&(r={name:"DEK-Info",values:[t.dekInfo.algorithm]},t.dekInfo.parameters&&r.values.push(t.dekInfo.parameters),i+=o(r)),t.headers)for(var s=0;s<t.headers.length;++s)i+=o(t.headers[s]);return t.procType&&(i+="\r\n"),(i+=n.util.encode64(t.body,e.maxline||64)+"\r\n")+"-----END "+t.type+"-----\r\n"},i.decode=function(t){for(var e,r=[],i=/\s*-----BEGIN ([A-Z0-9- ]+)-----\r?\n?([\x21-\x7e\s]+?(?:\r?\n\r?\n))?([:A-Za-z0-9+\/=\s]+?)-----END \1-----/g,o=/([\x21-\x7e]+):\s*([\x21-\x7e\s^:]+)/,a=/\r?\n/;e=i.exec(t);){var u=e[1];"NEW CERTIFICATE REQUEST"===u&&(u="CERTIFICATE REQUEST");var c={type:u,procType:null,contentDomain:null,dekInfo:null,headers:[],body:n.util.decode64(e[3])};if(r.push(c),e[2]){for(var h=e[2].split(a),f=0;e&&f<h.length;){for(var l=h[f].replace(/\s+$/,""),p=f+1;p<h.length;++p){var d=h[p];if(!/\s/.test(d[0]))break;l+=d,f=p}if(e=l.match(o)){for(var y={name:e[1],values:[]},g=e[2].split(","),m=0;m<g.length;++m)y.values.push(s(g[m]));if(c.procType)if(c.contentDomain||"Content-Domain"!==y.name)if(c.dekInfo||"DEK-Info"!==y.name)c.headers.push(y);else{if(0===y.values.length)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must have at least one subfield.');c.dekInfo={algorithm:g[0],parameters:g[1]||null}}else c.contentDomain=g[0]||"";else{if("Proc-Type"!==y.name)throw new Error('Invalid PEM formatted message. The first encapsulated header must be "Proc-Type".');if(2!==y.values.length)throw new Error('Invalid PEM formatted message. The "Proc-Type" header must have two subfields.');c.procType={version:g[0],type:g[1]}}}++f}if("ENCRYPTED"===c.procType&&!c.dekInfo)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must be present if "Proc-Type" is "ENCRYPTED".')}}if(0===r.length)throw new Error("Invalid PEM formatted message.");return r}},47501:(t,e,r)=>{var n=r(50276);r(7619),r(89356),r(21598);var i=t.exports=n.pkcs1=n.pkcs1||{};function o(t,e,r){r||(r=n.md.sha1.create());for(var i="",o=Math.ceil(e/r.digestLength),s=0;s<o;++s){var a=String.fromCharCode(s>>24&255,s>>16&255,s>>8&255,255&s);r.start(),r.update(t+a),i+=r.digest().getBytes()}return i.substring(0,e)}i.encode_rsa_oaep=function(t,e,r){var i,s,a,u;"string"==typeof r?(i=r,s=arguments[3]||void 0,a=arguments[4]||void 0):r&&(i=r.label||void 0,s=r.seed||void 0,a=r.md||void 0,r.mgf1&&r.mgf1.md&&(u=r.mgf1.md)),a?a.start():a=n.md.sha1.create(),u||(u=a);var c=Math.ceil(t.n.bitLength()/8),h=c-2*a.digestLength-2;if(e.length>h)throw(g=new Error("RSAES-OAEP input message length is too long.")).length=e.length,g.maxLength=h,g;i||(i=""),a.update(i,"raw");for(var f=a.digest(),l="",p=h-e.length,d=0;d<p;d++)l+="\0";var y=f.getBytes()+l+""+e;if(s){if(s.length!==a.digestLength){var g;throw(g=new Error("Invalid RSAES-OAEP seed. The seed length must match the digest length.")).seedLength=s.length,g.digestLength=a.digestLength,g}}else s=n.random.getBytes(a.digestLength);var m=o(s,c-a.digestLength-1,u),b=n.util.xorBytes(y,m,y.length),v=o(b,a.digestLength,u);return"\0"+n.util.xorBytes(s,v,s.length)+b},i.decode_rsa_oaep=function(t,e,r){var i,s,a;"string"==typeof r?(i=r,s=arguments[3]||void 0):r&&(i=r.label||void 0,s=r.md||void 0,r.mgf1&&r.mgf1.md&&(a=r.mgf1.md));var u=Math.ceil(t.n.bitLength()/8);if(e.length!==u)throw(m=new Error("RSAES-OAEP encoded message length is invalid.")).length=e.length,m.expectedLength=u,m;if(void 0===s?s=n.md.sha1.create():s.start(),a||(a=s),u<2*s.digestLength+2)throw new Error("RSAES-OAEP key is too short for the hash function.");i||(i=""),s.update(i,"raw");for(var c=s.digest().getBytes(),h=e.charAt(0),f=e.substring(1,s.digestLength+1),l=e.substring(1+s.digestLength),p=o(l,s.digestLength,a),d=o(n.util.xorBytes(f,p,f.length),u-s.digestLength-1,a),y=n.util.xorBytes(l,d,l.length),g=y.substring(0,s.digestLength),m="\0"!==h,b=0;b<s.digestLength;++b)m|=c.charAt(b)!==g.charAt(b);for(var v=1,w=s.digestLength,E=s.digestLength;E<y.length;E++){var S=y.charCodeAt(E);m|=S&(v?65534:0),w+=v&=1&S^1}if(m||1!==y.charCodeAt(w))throw new Error("Invalid RSAES-OAEP padding.");return y.substring(w+1)}},10268:(t,e,r)=>{var n=r(50276);r(7619),r(43736),r(89356),function(){if(n.prime)t.exports=n.prime;else{var e=t.exports=n.prime=n.prime||{},r=n.jsbn.BigInteger,i=[6,4,2,4,2,4,6,2],o=new r(null);o.fromInt(30);var s=function(t,e){return t|e};e.generateProbablePrime=function(t,e,i){"function"==typeof e&&(i=e,e={});var o=(e=e||{}).algorithm||"PRIMEINC";"string"==typeof o&&(o={name:o}),o.options=o.options||{};var s=e.prng||n.random,u={nextBytes:function(t){for(var e=s.getBytesSync(t.length),r=0;r<t.length;++r)t[r]=e.charCodeAt(r)}};if("PRIMEINC"===o.name)return function(t,e,i,o){return"workers"in i?function(t,e,i,o){if("undefined"==typeof Worker)return a(t,e,i,o);var s=c(t,e),u=i.workers,h=i.workLoad||100,f=30*h/8,l=i.workerScript||"forge/prime.worker.js";if(-1===u)return n.util.estimateCores((function(t,e){t&&(e=2),u=e-1,p()}));function p(){u=Math.max(1,u);for(var n=[],i=0;i<u;++i)n[i]=new Worker(l);for(i=0;i<u;++i)n[i].addEventListener("message",p);var a=!1;function p(i){if(!a){var u=i.data;if(u.found){for(var l=0;l<n.length;++l)n[l].terminate();return a=!0,o(null,new r(u.prime,16))}s.bitLength()>t&&(s=c(t,e));var p=s.toString(16);i.target.postMessage({hex:p,workLoad:h}),s.dAddOffset(f,0)}}}p()}(t,e,i,o):a(t,e,i,o)}(t,u,o.options,i);throw new Error("Invalid prime generation algorithm: "+o.name)}}function a(t,e,r,n){var i=c(t,e),o=function(t){return t<=100?27:t<=150?18:t<=200?15:t<=250?12:t<=300?9:t<=350?8:t<=400?7:t<=500?6:t<=600?5:t<=800?4:t<=1250?3:2}(i.bitLength());"millerRabinTests"in r&&(o=r.millerRabinTests);var s=10;"maxBlockTime"in r&&(s=r.maxBlockTime),u(i,t,e,0,o,s,n)}function u(t,e,r,o,s,a,h){var f=+new Date;do{if(t.bitLength()>e&&(t=c(e,r)),t.isProbablePrime(s))return h(null,t);t.dAddOffset(i[o++%8],0)}while(a<0||+new Date-f<a);n.util.setImmediate((function(){u(t,e,r,o,s,a,h)}))}function c(t,e){var n=new r(t,e),i=t-1;return n.testBit(i)||n.bitwiseTo(r.ONE.shiftLeft(i),s,n),n.dAddOffset(31-n.mod(o).byteValue(),0),n}}()},73878:(t,e,r)=>{var n=r(50276);r(7619);var i=null;!n.util.isNodejs||n.options.usePureJavaScript||process.versions["node-webkit"]||(i=r(50310)),(t.exports=n.prng=n.prng||{}).create=function(t){for(var e={plugin:t,key:null,seed:null,time:null,reseeds:0,generated:0,keyBytes:""},r=t.md,o=new Array(32),s=0;s<32;++s)o[s]=r.create();function a(){if(e.pools[0].messageLength>=32)return u();var t=32-e.pools[0].messageLength<<5;e.collect(e.seedFileSync(t)),u()}function u(){e.reseeds=4294967295===e.reseeds?0:e.reseeds+1;var t=e.plugin.md.create();t.update(e.keyBytes);for(var r=1,n=0;n<32;++n)e.reseeds%r==0&&(t.update(e.pools[n].digest().getBytes()),e.pools[n].start()),r<<=1;e.keyBytes=t.digest().getBytes(),t.start(),t.update(e.keyBytes);var i=t.digest().getBytes();e.key=e.plugin.formatKey(e.keyBytes),e.seed=e.plugin.formatSeed(i),e.generated=0}function c(t){var e=null,r=n.util.globalScope,i=r.crypto||r.msCrypto;i&&i.getRandomValues&&(e=function(t){return i.getRandomValues(t)});var o=n.util.createBuffer();if(e)for(;o.length()<t;){var s=Math.max(1,Math.min(t-o.length(),65536)/4),a=new Uint32Array(Math.floor(s));try{e(a);for(var u=0;u<a.length;++u)o.putInt32(a[u])}catch(t){if(!("undefined"!=typeof QuotaExceededError&&t instanceof QuotaExceededError))throw t}}if(o.length()<t)for(var c,h,f,l=Math.floor(65536*Math.random());o.length()<t;)for(h=16807*(65535&l),h+=(32767&(c=16807*(l>>16)))<<16,l=4294967295&(h=(2147483647&(h+=c>>15))+(h>>31)),u=0;u<3;++u)f=l>>>(u<<3),f^=Math.floor(256*Math.random()),o.putByte(255&f);return o.getBytes(t)}return e.pools=o,e.pool=0,e.generate=function(t,r){if(!r)return e.generateSync(t);var i=e.plugin.cipher,o=e.plugin.increment,s=e.plugin.formatKey,a=e.plugin.formatSeed,c=n.util.createBuffer();e.key=null,function h(f){if(f)return r(f);if(c.length()>=t)return r(null,c.getBytes(t));if(e.generated>1048575&&(e.key=null),null===e.key)return n.util.nextTick((function(){!function(t){if(e.pools[0].messageLength>=32)return u(),t();var r=32-e.pools[0].messageLength<<5;e.seedFile(r,(function(r,n){if(r)return t(r);e.collect(n),u(),t()}))}(h)}));var l=i(e.key,e.seed);e.generated+=l.length,c.putBytes(l),e.key=s(i(e.key,o(e.seed))),e.seed=a(i(e.key,e.seed)),n.util.setImmediate(h)}()},e.generateSync=function(t){var r=e.plugin.cipher,i=e.plugin.increment,o=e.plugin.formatKey,s=e.plugin.formatSeed;e.key=null;for(var u=n.util.createBuffer();u.length()<t;){e.generated>1048575&&(e.key=null),null===e.key&&a();var c=r(e.key,e.seed);e.generated+=c.length,u.putBytes(c),e.key=o(r(e.key,i(e.seed))),e.seed=s(r(e.key,e.seed))}return u.getBytes(t)},i?(e.seedFile=function(t,e){i.randomBytes(t,(function(t,r){if(t)return e(t);e(null,r.toString())}))},e.seedFileSync=function(t){return i.randomBytes(t).toString()}):(e.seedFile=function(t,e){try{e(null,c(t))}catch(t){e(t)}},e.seedFileSync=c),e.collect=function(t){for(var r=t.length,n=0;n<r;++n)e.pools[e.pool].update(t.substr(n,1)),e.pool=31===e.pool?0:e.pool+1},e.collectInt=function(t,r){for(var n="",i=0;i<r;i+=8)n+=String.fromCharCode(t>>i&255);e.collect(n)},e.registerWorker=function(t){t===self?e.seedFile=function(t,e){self.addEventListener("message",(function t(r){var n=r.data;n.forge&&n.forge.prng&&(self.removeEventListener("message",t),e(n.forge.prng.err,n.forge.prng.bytes))})),self.postMessage({forge:{prng:{needed:t}}})}:t.addEventListener("message",(function(r){var n=r.data;n.forge&&n.forge.prng&&e.seedFile(n.forge.prng.needed,(function(e,r){t.postMessage({forge:{prng:{err:e,bytes:r}}})}))}))},e}},89356:(t,e,r)=>{var n=r(50276);r(39504),r(30172),r(73878),r(7619),n.random&&n.random.getBytes?t.exports=n.random:function(e){var r={},i=new Array(4),o=n.util.createBuffer();function s(){var t=n.prng.create(r);return t.getBytes=function(e,r){return t.generate(e,r)},t.getBytesSync=function(e){return t.generate(e)},t}r.formatKey=function(t){var e=n.util.createBuffer(t);return(t=new Array(4))[0]=e.getInt32(),t[1]=e.getInt32(),t[2]=e.getInt32(),t[3]=e.getInt32(),n.aes._expandKey(t,!1)},r.formatSeed=function(t){var e=n.util.createBuffer(t);return(t=new Array(4))[0]=e.getInt32(),t[1]=e.getInt32(),t[2]=e.getInt32(),t[3]=e.getInt32(),t},r.cipher=function(t,e){return n.aes._updateBlock(t,e,i,!1),o.putInt32(i[0]),o.putInt32(i[1]),o.putInt32(i[2]),o.putInt32(i[3]),o.getBytes()},r.increment=function(t){return++t[3],t},r.md=n.md.sha256;var a=s(),u=null,c=n.util.globalScope,h=c.crypto||c.msCrypto;if(h&&h.getRandomValues&&(u=function(t){return h.getRandomValues(t)}),n.options.usePureJavaScript||!n.util.isNodejs&&!u){if("undefined"==typeof window||window.document,a.collectInt(+new Date,32),"undefined"!=typeof navigator){var f="";for(var l in navigator)try{"string"==typeof navigator[l]&&(f+=navigator[l])}catch(t){}a.collect(f),f=null}e&&(e().mousemove((function(t){a.collectInt(t.clientX,16),a.collectInt(t.clientY,16)})),e().keypress((function(t){a.collectInt(t.charCode,8)})))}if(n.random)for(var l in a)n.random[l]=a[l];else n.random=a;n.random.createInstance=s,t.exports=n.random}("undefined"!=typeof jQuery?jQuery:null)},55124:(t,e,r)=>{var n=r(50276);r(7619);var i=[217,120,249,196,25,221,181,237,40,233,253,121,74,160,216,157,198,126,55,131,43,118,83,142,98,76,100,136,68,139,251,162,23,154,89,245,135,179,79,19,97,69,109,141,9,129,125,50,189,143,64,235,134,183,123,11,240,149,33,34,92,107,78,130,84,214,101,147,206,96,178,28,115,86,192,20,167,140,241,220,18,117,202,31,59,190,228,209,66,61,212,48,163,60,182,38,111,191,14,218,70,105,7,87,39,242,29,155,188,148,67,3,248,17,199,246,144,239,62,231,6,195,213,47,200,102,30,215,8,232,234,222,128,82,238,247,132,170,114,172,53,77,106,42,150,26,210,113,90,21,73,116,75,159,208,94,4,24,164,236,194,224,65,110,15,81,203,204,36,145,175,80,161,244,112,57,153,124,58,133,35,184,180,122,252,2,54,91,37,85,151,49,45,93,250,152,227,138,146,174,5,223,41,16,103,108,186,201,211,0,230,207,225,158,168,44,99,22,1,63,88,226,137,169,13,56,52,27,171,51,255,176,187,72,12,95,185,177,205,46,197,243,219,71,229,165,156,119,10,166,32,104,254,127,193,173],o=[1,2,3,5],s=function(t,e){return t<<e&65535|(65535&t)>>16-e},a=function(t,e){return(65535&t)>>e|t<<16-e&65535};t.exports=n.rc2=n.rc2||{},n.rc2.expandKey=function(t,e){"string"==typeof t&&(t=n.util.createBuffer(t)),e=e||128;var r,o=t,s=t.length(),a=e,u=Math.ceil(a/8),c=255>>(7&a);for(r=s;r<128;r++)o.putByte(i[o.at(r-1)+o.at(r-s)&255]);for(o.setAt(128-u,i[o.at(128-u)&c]),r=127-u;r>=0;r--)o.setAt(r,i[o.at(r+1)^o.at(r+u)]);return o};var u=function(t,e,r){var i,u,c,h,f=!1,l=null,p=null,d=null,y=[];for(t=n.rc2.expandKey(t,e),c=0;c<64;c++)y.push(t.getInt16Le());r?(i=function(t){for(c=0;c<4;c++)t[c]+=y[h]+(t[(c+3)%4]&t[(c+2)%4])+(~t[(c+3)%4]&t[(c+1)%4]),t[c]=s(t[c],o[c]),h++},u=function(t){for(c=0;c<4;c++)t[c]+=y[63&t[(c+3)%4]]}):(i=function(t){for(c=3;c>=0;c--)t[c]=a(t[c],o[c]),t[c]-=y[h]+(t[(c+3)%4]&t[(c+2)%4])+(~t[(c+3)%4]&t[(c+1)%4]),h--},u=function(t){for(c=3;c>=0;c--)t[c]-=y[63&t[(c+3)%4]]});var g=function(t){var e=[];for(c=0;c<4;c++){var n=l.getInt16Le();null!==d&&(r?n^=d.getInt16Le():d.putInt16Le(n)),e.push(65535&n)}h=r?0:63;for(var i=0;i<t.length;i++)for(var o=0;o<t[i][0];o++)t[i][1](e);for(c=0;c<4;c++)null!==d&&(r?d.putInt16Le(e[c]):e[c]^=d.getInt16Le()),p.putInt16Le(e[c])},m=null;return m={start:function(t,e){t&&"string"==typeof t&&(t=n.util.createBuffer(t)),f=!1,l=n.util.createBuffer(),p=e||new n.util.createBuffer,d=t,m.output=p},update:function(t){for(f||l.putBuffer(t);l.length()>=8;)g([[5,i],[1,u],[6,i],[1,u],[5,i]])},finish:function(t){var e=!0;if(r)if(t)e=t(8,l,!r);else{var n=8===l.length()?8:8-l.length();l.fillWithByte(n,n)}if(e&&(f=!0,m.update()),!r&&(e=0===l.length()))if(t)e=t(8,p,!r);else{var i=p.length(),o=p.at(i-1);o>i?e=!1:p.truncate(o)}return e}}};n.rc2.startEncrypting=function(t,e,r){var i=n.rc2.createEncryptionCipher(t,128);return i.start(e,r),i},n.rc2.createEncryptionCipher=function(t,e){return u(t,e,!0)},n.rc2.startDecrypting=function(t,e,r){var i=n.rc2.createDecryptionCipher(t,128);return i.start(e,r),i},n.rc2.createDecryptionCipher=function(t,e){return u(t,e,!1)}},15805:(t,e,r)=>{var n=r(50276);if(r(12746),r(43736),r(16418),r(47501),r(10268),r(89356),r(7619),void 0===i)var i=n.jsbn.BigInteger;var o=n.util.isNodejs?r(50310):null,s=n.asn1,a=n.util;n.pki=n.pki||{},t.exports=n.pki.rsa=n.rsa=n.rsa||{};var u=n.pki,c=[6,4,2,4,2,4,6,2],h={name:"PrivateKeyInfo",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,value:[{name:"PrivateKeyInfo.version",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.privateKeyAlgorithm",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:s.Class.UNIVERSAL,type:s.Type.OID,constructed:!1,capture:"privateKeyOid"}]},{name:"PrivateKeyInfo",tagClass:s.Class.UNIVERSAL,type:s.Type.OCTETSTRING,constructed:!1,capture:"privateKey"}]},f={name:"RSAPrivateKey",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPrivateKey.version",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"RSAPrivateKey.modulus",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyModulus"},{name:"RSAPrivateKey.publicExponent",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyPublicExponent"},{name:"RSAPrivateKey.privateExponent",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyPrivateExponent"},{name:"RSAPrivateKey.prime1",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyPrime1"},{name:"RSAPrivateKey.prime2",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyPrime2"},{name:"RSAPrivateKey.exponent1",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyExponent1"},{name:"RSAPrivateKey.exponent2",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyExponent2"},{name:"RSAPrivateKey.coefficient",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"privateKeyCoefficient"}]},l={name:"RSAPublicKey",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPublicKey.modulus",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"publicKeyModulus"},{name:"RSAPublicKey.exponent",tagClass:s.Class.UNIVERSAL,type:s.Type.INTEGER,constructed:!1,capture:"publicKeyExponent"}]},p=n.pki.rsa.publicKeyValidator={name:"SubjectPublicKeyInfo",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,captureAsn1:"subjectPublicKeyInfo",value:[{name:"SubjectPublicKeyInfo.AlgorithmIdentifier",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:s.Class.UNIVERSAL,type:s.Type.OID,constructed:!1,capture:"publicKeyOid"}]},{name:"SubjectPublicKeyInfo.subjectPublicKey",tagClass:s.Class.UNIVERSAL,type:s.Type.BITSTRING,constructed:!1,value:[{name:"SubjectPublicKeyInfo.subjectPublicKey.RSAPublicKey",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,optional:!0,captureAsn1:"rsaPublicKey"}]}]},d={name:"DigestInfo",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,value:[{name:"DigestInfo.DigestAlgorithm",tagClass:s.Class.UNIVERSAL,type:s.Type.SEQUENCE,constructed:!0,value:[{name:"DigestInfo.DigestAlgorithm.algorithmIdentifier",tagClass:s.Class.UNIVERSAL,type:s.Type.OID,constructed:!1,capture:"algorithmIdentifier"},{name:"DigestInfo.DigestAlgorithm.parameters",tagClass:s.Class.UNIVERSAL,type:s.Type.NULL,capture:"parameters",optional:!0,constructed:!1}]},{name:"DigestInfo.digest",tagClass:s.Class.UNIVERSAL,type:s.Type.OCTETSTRING,constructed:!1,capture:"digest"}]},y=function(t){var e;if(!(t.algorithm in u.oids)){var r=new Error("Unknown message digest algorithm.");throw r.algorithm=t.algorithm,r}e=u.oids[t.algorithm];var n=s.oidToDer(e).getBytes(),i=s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[]),o=s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[]);o.value.push(s.create(s.Class.UNIVERSAL,s.Type.OID,!1,n)),o.value.push(s.create(s.Class.UNIVERSAL,s.Type.NULL,!1,""));var a=s.create(s.Class.UNIVERSAL,s.Type.OCTETSTRING,!1,t.digest().getBytes());return i.value.push(o),i.value.push(a),s.toDer(i).getBytes()},g=function(t,e,r){if(r)return t.modPow(e.e,e.n);if(!e.p||!e.q)return t.modPow(e.d,e.n);var o;e.dP||(e.dP=e.d.mod(e.p.subtract(i.ONE))),e.dQ||(e.dQ=e.d.mod(e.q.subtract(i.ONE))),e.qInv||(e.qInv=e.q.modInverse(e.p));do{o=new i(n.util.bytesToHex(n.random.getBytes(e.n.bitLength()/8)),16)}while(o.compareTo(e.n)>=0||!o.gcd(e.n).equals(i.ONE));for(var s=(t=t.multiply(o.modPow(e.e,e.n)).mod(e.n)).mod(e.p).modPow(e.dP,e.p),a=t.mod(e.q).modPow(e.dQ,e.q);s.compareTo(a)<0;)s=s.add(e.p);var u=s.subtract(a).multiply(e.qInv).mod(e.p).multiply(e.q).add(a);return u.multiply(o.modInverse(e.n)).mod(e.n)};function m(t,e,r){var i=n.util.createBuffer(),o=Math.ceil(e.n.bitLength()/8);if(t.length>o-11){var s=new Error("Message is too long for PKCS#1 v1.5 padding.");throw s.length=t.length,s.max=o-11,s}i.putByte(0),i.putByte(r);var a,u=o-3-t.length;if(0===r||1===r){a=0===r?0:255;for(var c=0;c<u;++c)i.putByte(a)}else for(;u>0;){var h=0,f=n.random.getBytes(u);for(c=0;c<u;++c)0===(a=f.charCodeAt(c))?++h:i.putByte(a);u=h}return i.putByte(0),i.putBytes(t),i}function b(t,e,r,i){var o=Math.ceil(e.n.bitLength()/8),s=n.util.createBuffer(t),a=s.getByte(),u=s.getByte();if(0!==a||r&&0!==u&&1!==u||!r&&2!=u||r&&0===u&&void 0===i)throw new Error("Encryption block is invalid.");var c=0;if(0===u){c=o-3-i;for(var h=0;h<c;++h)if(0!==s.getByte())throw new Error("Encryption block is invalid.")}else if(1===u)for(c=0;s.length()>1;){if(255!==s.getByte()){--s.read;break}++c}else if(2===u)for(c=0;s.length()>1;){if(0===s.getByte()){--s.read;break}++c}if(0!==s.getByte()||c!==o-3-s.length())throw new Error("Encryption block is invalid.");return s.getBytes()}function v(t){var e=t.toString(16);e[0]>="8"&&(e="00"+e);var r=n.util.hexToBytes(e);return!(r.length>1)||(0!==r.charCodeAt(0)||128&r.charCodeAt(1))&&(255!==r.charCodeAt(0)||128&~r.charCodeAt(1))?r:r.substr(1)}function w(t){return t<=100?27:t<=150?18:t<=200?15:t<=250?12:t<=300?9:t<=350?8:t<=400?7:t<=500?6:t<=600?5:t<=800?4:t<=1250?3:2}function E(t){return n.util.isNodejs&&"function"==typeof o[t]}function S(t){return void 0!==a.globalScope&&"object"==typeof a.globalScope.crypto&&"object"==typeof a.globalScope.crypto.subtle&&"function"==typeof a.globalScope.crypto.subtle[t]}function B(t){return void 0!==a.globalScope&&"object"==typeof a.globalScope.msCrypto&&"object"==typeof a.globalScope.msCrypto.subtle&&"function"==typeof a.globalScope.msCrypto.subtle[t]}function A(t){for(var e=n.util.hexToBytes(t.toString(16)),r=new Uint8Array(e.length),i=0;i<e.length;++i)r[i]=e.charCodeAt(i);return r}u.rsa.encrypt=function(t,e,r){var o,s=r,a=Math.ceil(e.n.bitLength()/8);!1!==r&&!0!==r?(s=2===r,o=m(t,e,r)):(o=n.util.createBuffer()).putBytes(t);for(var u=new i(o.toHex(),16),c=g(u,e,s).toString(16),h=n.util.createBuffer(),f=a-Math.ceil(c.length/2);f>0;)h.putByte(0),--f;return h.putBytes(n.util.hexToBytes(c)),h.getBytes()},u.rsa.decrypt=function(t,e,r,o){var s=Math.ceil(e.n.bitLength()/8);if(t.length!==s){var a=new Error("Encrypted message length is invalid.");throw a.length=t.length,a.expected=s,a}var u=new i(n.util.createBuffer(t).toHex(),16);if(u.compareTo(e.n)>=0)throw new Error("Encrypted message is invalid.");for(var c=g(u,e,r).toString(16),h=n.util.createBuffer(),f=s-Math.ceil(c.length/2);f>0;)h.putByte(0),--f;return h.putBytes(n.util.hexToBytes(c)),!1!==o?b(h.getBytes(),e,r):h.getBytes()},u.rsa.createKeyPairGenerationState=function(t,e,r){"string"==typeof t&&(t=parseInt(t,10)),t=t||2048;var o,s=(r=r||{}).prng||n.random,a={nextBytes:function(t){for(var e=s.getBytesSync(t.length),r=0;r<t.length;++r)t[r]=e.charCodeAt(r)}},u=r.algorithm||"PRIMEINC";if("PRIMEINC"!==u)throw new Error("Invalid key generation algorithm: "+u);return(o={algorithm:u,state:0,bits:t,rng:a,eInt:e||65537,e:new i(null),p:null,q:null,qBits:t>>1,pBits:t-(t>>1),pqState:0,num:null,keys:null}).e.fromInt(o.eInt),o},u.rsa.stepKeyPairGenerationState=function(t,e){"algorithm"in t||(t.algorithm="PRIMEINC");var r=new i(null);r.fromInt(30);for(var n,o=0,s=function(t,e){return t|e},a=+new Date,h=0;null===t.keys&&(e<=0||h<e);){if(0===t.state){var f=null===t.p?t.pBits:t.qBits,l=f-1;0===t.pqState?(t.num=new i(f,t.rng),t.num.testBit(l)||t.num.bitwiseTo(i.ONE.shiftLeft(l),s,t.num),t.num.dAddOffset(31-t.num.mod(r).byteValue(),0),o=0,++t.pqState):1===t.pqState?t.num.bitLength()>f?t.pqState=0:t.num.isProbablePrime(w(t.num.bitLength()))?++t.pqState:t.num.dAddOffset(c[o++%8],0):2===t.pqState?t.pqState=0===t.num.subtract(i.ONE).gcd(t.e).compareTo(i.ONE)?3:0:3===t.pqState&&(t.pqState=0,null===t.p?t.p=t.num:t.q=t.num,null!==t.p&&null!==t.q&&++t.state,t.num=null)}else if(1===t.state)t.p.compareTo(t.q)<0&&(t.num=t.p,t.p=t.q,t.q=t.num),++t.state;else if(2===t.state)t.p1=t.p.subtract(i.ONE),t.q1=t.q.subtract(i.ONE),t.phi=t.p1.multiply(t.q1),++t.state;else if(3===t.state)0===t.phi.gcd(t.e).compareTo(i.ONE)?++t.state:(t.p=null,t.q=null,t.state=0);else if(4===t.state)t.n=t.p.multiply(t.q),t.n.bitLength()===t.bits?++t.state:(t.q=null,t.state=0);else if(5===t.state){var p=t.e.modInverse(t.phi);t.keys={privateKey:u.rsa.setPrivateKey(t.n,t.e,p,t.p,t.q,p.mod(t.p1),p.mod(t.q1),t.q.modInverse(t.p)),publicKey:u.rsa.setPublicKey(t.n,t.e)}}h+=(n=+new Date)-a,a=n}return null!==t.keys},u.rsa.generateKeyPair=function(t,e,r,c){if(1===arguments.length?"object"==typeof t?(r=t,t=void 0):"function"==typeof t&&(c=t,t=void 0):2===arguments.length?"number"==typeof t?"function"==typeof e?(c=e,e=void 0):"number"!=typeof e&&(r=e,e=void 0):(r=t,c=e,t=void 0,e=void 0):3===arguments.length&&("number"==typeof e?"function"==typeof r&&(c=r,r=void 0):(c=r,r=e,e=void 0)),r=r||{},void 0===t&&(t=r.bits||2048),void 0===e&&(e=r.e||65537),!n.options.usePureJavaScript&&!r.prng&&t>=256&&t<=16384&&(65537===e||3===e))if(c){if(E("generateKeyPair"))return o.generateKeyPair("rsa",{modulusLength:t,publicExponent:e,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}},(function(t,e,r){if(t)return c(t);c(null,{privateKey:u.privateKeyFromPem(r),publicKey:u.publicKeyFromPem(e)})}));if(S("generateKey")&&S("exportKey"))return a.globalScope.crypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:t,publicExponent:A(e),hash:{name:"SHA-256"}},!0,["sign","verify"]).then((function(t){return a.globalScope.crypto.subtle.exportKey("pkcs8",t.privateKey)})).then(void 0,(function(t){c(t)})).then((function(t){if(t){var e=u.privateKeyFromAsn1(s.fromDer(n.util.createBuffer(t)));c(null,{privateKey:e,publicKey:u.setRsaPublicKey(e.n,e.e)})}}));if(B("generateKey")&&B("exportKey")){var h=a.globalScope.msCrypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:t,publicExponent:A(e),hash:{name:"SHA-256"}},!0,["sign","verify"]);return h.oncomplete=function(t){var e=t.target.result,r=a.globalScope.msCrypto.subtle.exportKey("pkcs8",e.privateKey);r.oncomplete=function(t){var e=t.target.result,r=u.privateKeyFromAsn1(s.fromDer(n.util.createBuffer(e)));c(null,{privateKey:r,publicKey:u.setRsaPublicKey(r.n,r.e)})},r.onerror=function(t){c(t)}},void(h.onerror=function(t){c(t)})}}else if(E("generateKeyPairSync")){var f=o.generateKeyPairSync("rsa",{modulusLength:t,publicExponent:e,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}});return{privateKey:u.privateKeyFromPem(f.privateKey),publicKey:u.publicKeyFromPem(f.publicKey)}}var l=u.rsa.createKeyPairGenerationState(t,e,r);if(!c)return u.rsa.stepKeyPairGenerationState(l,0),l.keys;!function(t,e,r){"function"==typeof e&&(r=e,e={});var o={algorithm:{name:(e=e||{}).algorithm||"PRIMEINC",options:{workers:e.workers||2,workLoad:e.workLoad||100,workerScript:e.workerScript}}};function s(){a(t.pBits,(function(e,n){return e?r(e):(t.p=n,null!==t.q?c(e,t.q):void a(t.qBits,c))}))}function a(t,e){n.prime.generateProbablePrime(t,o,e)}function c(e,n){if(e)return r(e);if(t.q=n,t.p.compareTo(t.q)<0){var o=t.p;t.p=t.q,t.q=o}if(0!==t.p.subtract(i.ONE).gcd(t.e).compareTo(i.ONE))return t.p=null,void s();if(0!==t.q.subtract(i.ONE).gcd(t.e).compareTo(i.ONE))return t.q=null,void a(t.qBits,c);if(t.p1=t.p.subtract(i.ONE),t.q1=t.q.subtract(i.ONE),t.phi=t.p1.multiply(t.q1),0!==t.phi.gcd(t.e).compareTo(i.ONE))return t.p=t.q=null,void s();if(t.n=t.p.multiply(t.q),t.n.bitLength()!==t.bits)return t.q=null,void a(t.qBits,c);var h=t.e.modInverse(t.phi);t.keys={privateKey:u.rsa.setPrivateKey(t.n,t.e,h,t.p,t.q,h.mod(t.p1),h.mod(t.q1),t.q.modInverse(t.p)),publicKey:u.rsa.setPublicKey(t.n,t.e)},r(null,t.keys)}"prng"in e&&(o.prng=e.prng),s()}(l,r,c)},u.setRsaPublicKey=u.rsa.setPublicKey=function(t,e){var r={n:t,e,encrypt:function(t,e,i){if("string"==typeof e?e=e.toUpperCase():void 0===e&&(e="RSAES-PKCS1-V1_5"),"RSAES-PKCS1-V1_5"===e)e={encode:function(t,e,r){return m(t,e,2).getBytes()}};else if("RSA-OAEP"===e||"RSAES-OAEP"===e)e={encode:function(t,e){return n.pkcs1.encode_rsa_oaep(e,t,i)}};else if(-1!==["RAW","NONE","NULL",null].indexOf(e))e={encode:function(t){return t}};else if("string"==typeof e)throw new Error('Unsupported encryption scheme: "'+e+'".');var o=e.encode(t,r,!0);return u.rsa.encrypt(o,r,!0)},verify:function(t,e,i,o){"string"==typeof i?i=i.toUpperCase():void 0===i&&(i="RSASSA-PKCS1-V1_5"),void 0===o&&(o={_parseAllDigestBytes:!0}),"_parseAllDigestBytes"in o||(o._parseAllDigestBytes=!0),"RSASSA-PKCS1-V1_5"===i?i={verify:function(t,e){e=b(e,r,!0);var i=s.fromDer(e,{parseAllBytes:o._parseAllDigestBytes}),a={},u=[];if(!s.validate(i,d,a,u))throw(c=new Error("ASN.1 object does not contain a valid RSASSA-PKCS1-v1_5 DigestInfo value.")).errors=u,c;var c,h=s.derToOid(a.algorithmIdentifier);if(h!==n.oids.md2&&h!==n.oids.md5&&h!==n.oids.sha1&&h!==n.oids.sha224&&h!==n.oids.sha256&&h!==n.oids.sha384&&h!==n.oids.sha512&&h!==n.oids["sha512-224"]&&h!==n.oids["sha512-256"])throw(c=new Error("Unknown RSASSA-PKCS1-v1_5 DigestAlgorithm identifier.")).oid=h,c;if((h===n.oids.md2||h===n.oids.md5)&&!("parameters"in a))throw new Error("ASN.1 object does not contain a valid RSASSA-PKCS1-v1_5 DigestInfo value. Missing algorithm identifer NULL parameters.");return t===a.digest}}:"NONE"!==i&&"NULL"!==i&&null!==i||(i={verify:function(t,e){return t===b(e,r,!0)}});var a=u.rsa.decrypt(e,r,!0,!1);return i.verify(t,a,r.n.bitLength())}};return r},u.setRsaPrivateKey=u.rsa.setPrivateKey=function(t,e,r,i,o,s,a,c){var h={n:t,e,d:r,p:i,q:o,dP:s,dQ:a,qInv:c,decrypt:function(t,e,r){"string"==typeof e?e=e.toUpperCase():void 0===e&&(e="RSAES-PKCS1-V1_5");var i=u.rsa.decrypt(t,h,!1,!1);if("RSAES-PKCS1-V1_5"===e)e={decode:b};else if("RSA-OAEP"===e||"RSAES-OAEP"===e)e={decode:function(t,e){return n.pkcs1.decode_rsa_oaep(e,t,r)}};else{if(-1===["RAW","NONE","NULL",null].indexOf(e))throw new Error('Unsupported encryption scheme: "'+e+'".');e={decode:function(t){return t}}}return e.decode(i,h,!1)},sign:function(t,e){var r=!1;"string"==typeof e&&(e=e.toUpperCase()),void 0===e||"RSASSA-PKCS1-V1_5"===e?(e={encode:y},r=1):"NONE"!==e&&"NULL"!==e&&null!==e||(e={encode:function(){return t}},r=1);var n=e.encode(t,h.n.bitLength());return u.rsa.encrypt(n,h,r)}};return h},u.wrapRsaPrivateKey=function(t){return s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,s.integerToDer(0).getBytes()),s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[s.create(s.Class.UNIVERSAL,s.Type.OID,!1,s.oidToDer(u.oids.rsaEncryption).getBytes()),s.create(s.Class.UNIVERSAL,s.Type.NULL,!1,"")]),s.create(s.Class.UNIVERSAL,s.Type.OCTETSTRING,!1,s.toDer(t).getBytes())])},u.privateKeyFromAsn1=function(t){var e,r,o,a,c,l,p,d,y={},g=[];if(s.validate(t,h,y,g)&&(t=s.fromDer(n.util.createBuffer(y.privateKey))),y={},g=[],!s.validate(t,f,y,g)){var m=new Error("Cannot read private key. ASN.1 object does not contain an RSAPrivateKey.");throw m.errors=g,m}return e=n.util.createBuffer(y.privateKeyModulus).toHex(),r=n.util.createBuffer(y.privateKeyPublicExponent).toHex(),o=n.util.createBuffer(y.privateKeyPrivateExponent).toHex(),a=n.util.createBuffer(y.privateKeyPrime1).toHex(),c=n.util.createBuffer(y.privateKeyPrime2).toHex(),l=n.util.createBuffer(y.privateKeyExponent1).toHex(),p=n.util.createBuffer(y.privateKeyExponent2).toHex(),d=n.util.createBuffer(y.privateKeyCoefficient).toHex(),u.setRsaPrivateKey(new i(e,16),new i(r,16),new i(o,16),new i(a,16),new i(c,16),new i(l,16),new i(p,16),new i(d,16))},u.privateKeyToAsn1=u.privateKeyToRSAPrivateKey=function(t){return s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,s.integerToDer(0).getBytes()),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.n)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.e)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.d)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.p)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.q)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.dP)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.dQ)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.qInv))])},u.publicKeyFromAsn1=function(t){var e={},r=[];if(s.validate(t,p,e,r)){var o,a=s.derToOid(e.publicKeyOid);if(a!==u.oids.rsaEncryption)throw(o=new Error("Cannot read public key. Unknown OID.")).oid=a,o;t=e.rsaPublicKey}if(r=[],!s.validate(t,l,e,r))throw(o=new Error("Cannot read public key. ASN.1 object does not contain an RSAPublicKey.")).errors=r,o;var c=n.util.createBuffer(e.publicKeyModulus).toHex(),h=n.util.createBuffer(e.publicKeyExponent).toHex();return u.setRsaPublicKey(new i(c,16),new i(h,16))},u.publicKeyToAsn1=u.publicKeyToSubjectPublicKeyInfo=function(t){return s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[s.create(s.Class.UNIVERSAL,s.Type.OID,!1,s.oidToDer(u.oids.rsaEncryption).getBytes()),s.create(s.Class.UNIVERSAL,s.Type.NULL,!1,"")]),s.create(s.Class.UNIVERSAL,s.Type.BITSTRING,!1,[u.publicKeyToRSAPublicKey(t)])])},u.publicKeyToRSAPublicKey=function(t){return s.create(s.Class.UNIVERSAL,s.Type.SEQUENCE,!0,[s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.n)),s.create(s.Class.UNIVERSAL,s.Type.INTEGER,!1,v(t.e))])}},21598:(t,e,r)=>{var n=r(50276);r(8106),r(7619);var i=t.exports=n.sha1=n.sha1||{};n.md.sha1=n.md.algorithms.sha1=i,i.create=function(){s||(o=String.fromCharCode(128),o+=n.util.fillString(String.fromCharCode(0),64),s=!0);var t=null,e=n.util.createBuffer(),r=new Array(80),i={algorithm:"sha1",blockLength:64,digestLength:20,messageLength:0,fullMessageLength:null,messageLengthSize:8,start:function(){i.messageLength=0,i.fullMessageLength=i.messageLength64=[];for(var r=i.messageLengthSize/4,o=0;o<r;++o)i.fullMessageLength.push(0);return e=n.util.createBuffer(),t={h0:1732584193,h1:4023233417,h2:2562383102,h3:271733878,h4:3285377520},i}};return i.start(),i.update=function(o,s){"utf8"===s&&(o=n.util.encodeUtf8(o));var u=o.length;i.messageLength+=u,u=[u/4294967296>>>0,u>>>0];for(var c=i.fullMessageLength.length-1;c>=0;--c)i.fullMessageLength[c]+=u[1],u[1]=u[0]+(i.fullMessageLength[c]/4294967296>>>0),i.fullMessageLength[c]=i.fullMessageLength[c]>>>0,u[0]=u[1]/4294967296>>>0;return e.putBytes(o),a(t,r,e),(e.read>2048||0===e.length())&&e.compact(),i},i.digest=function(){var s=n.util.createBuffer();s.putBytes(e.bytes());var u,c=i.fullMessageLength[i.fullMessageLength.length-1]+i.messageLengthSize&i.blockLength-1;s.putBytes(o.substr(0,i.blockLength-c));for(var h=8*i.fullMessageLength[0],f=0;f<i.fullMessageLength.length-1;++f)h+=(u=8*i.fullMessageLength[f+1])/4294967296>>>0,s.putInt32(h>>>0),h=u>>>0;s.putInt32(h);var l={h0:t.h0,h1:t.h1,h2:t.h2,h3:t.h3,h4:t.h4};a(l,r,s);var p=n.util.createBuffer();return p.putInt32(l.h0),p.putInt32(l.h1),p.putInt32(l.h2),p.putInt32(l.h3),p.putInt32(l.h4),p},i};var o=null,s=!1;function a(t,e,r){for(var n,i,o,s,a,u,c,h=r.length();h>=64;){for(i=t.h0,o=t.h1,s=t.h2,a=t.h3,u=t.h4,c=0;c<16;++c)n=r.getInt32(),e[c]=n,n=(i<<5|i>>>27)+(a^o&(s^a))+u+1518500249+n,u=a,a=s,s=(o<<30|o>>>2)>>>0,o=i,i=n;for(;c<20;++c)n=(n=e[c-3]^e[c-8]^e[c-14]^e[c-16])<<1|n>>>31,e[c]=n,n=(i<<5|i>>>27)+(a^o&(s^a))+u+1518500249+n,u=a,a=s,s=(o<<30|o>>>2)>>>0,o=i,i=n;for(;c<32;++c)n=(n=e[c-3]^e[c-8]^e[c-14]^e[c-16])<<1|n>>>31,e[c]=n,n=(i<<5|i>>>27)+(o^s^a)+u+1859775393+n,u=a,a=s,s=(o<<30|o>>>2)>>>0,o=i,i=n;for(;c<40;++c)n=(n=e[c-6]^e[c-16]^e[c-28]^e[c-32])<<2|n>>>30,e[c]=n,n=(i<<5|i>>>27)+(o^s^a)+u+1859775393+n,u=a,a=s,s=(o<<30|o>>>2)>>>0,o=i,i=n;for(;c<60;++c)n=(n=e[c-6]^e[c-16]^e[c-28]^e[c-32])<<2|n>>>30,e[c]=n,n=(i<<5|i>>>27)+(o&s|a&(o^s))+u+2400959708+n,u=a,a=s,s=(o<<30|o>>>2)>>>0,o=i,i=n;for(;c<80;++c)n=(n=e[c-6]^e[c-16]^e[c-28]^e[c-32])<<2|n>>>30,e[c]=n,n=(i<<5|i>>>27)+(o^s^a)+u+3395469782+n,u=a,a=s,s=(o<<30|o>>>2)>>>0,o=i,i=n;t.h0=t.h0+i|0,t.h1=t.h1+o|0,t.h2=t.h2+s|0,t.h3=t.h3+a|0,t.h4=t.h4+u|0,h-=64}}},30172:(t,e,r)=>{var n=r(50276);r(8106),r(7619);var i=t.exports=n.sha256=n.sha256||{};n.md.sha256=n.md.algorithms.sha256=i,i.create=function(){s||(o=String.fromCharCode(128),o+=n.util.fillString(String.fromCharCode(0),64),a=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],s=!0);var t=null,e=n.util.createBuffer(),r=new Array(64),i={algorithm:"sha256",blockLength:64,digestLength:32,messageLength:0,fullMessageLength:null,messageLengthSize:8,start:function(){i.messageLength=0,i.fullMessageLength=i.messageLength64=[];for(var r=i.messageLengthSize/4,o=0;o<r;++o)i.fullMessageLength.push(0);return e=n.util.createBuffer(),t={h0:1779033703,h1:3144134277,h2:1013904242,h3:2773480762,h4:1359893119,h5:2600822924,h6:528734635,h7:1541459225},i}};return i.start(),i.update=function(o,s){"utf8"===s&&(o=n.util.encodeUtf8(o));var a=o.length;i.messageLength+=a,a=[a/4294967296>>>0,a>>>0];for(var c=i.fullMessageLength.length-1;c>=0;--c)i.fullMessageLength[c]+=a[1],a[1]=a[0]+(i.fullMessageLength[c]/4294967296>>>0),i.fullMessageLength[c]=i.fullMessageLength[c]>>>0,a[0]=a[1]/4294967296>>>0;return e.putBytes(o),u(t,r,e),(e.read>2048||0===e.length())&&e.compact(),i},i.digest=function(){var s=n.util.createBuffer();s.putBytes(e.bytes());var a,c=i.fullMessageLength[i.fullMessageLength.length-1]+i.messageLengthSize&i.blockLength-1;s.putBytes(o.substr(0,i.blockLength-c));for(var h=8*i.fullMessageLength[0],f=0;f<i.fullMessageLength.length-1;++f)h+=(a=8*i.fullMessageLength[f+1])/4294967296>>>0,s.putInt32(h>>>0),h=a>>>0;s.putInt32(h);var l={h0:t.h0,h1:t.h1,h2:t.h2,h3:t.h3,h4:t.h4,h5:t.h5,h6:t.h6,h7:t.h7};u(l,r,s);var p=n.util.createBuffer();return p.putInt32(l.h0),p.putInt32(l.h1),p.putInt32(l.h2),p.putInt32(l.h3),p.putInt32(l.h4),p.putInt32(l.h5),p.putInt32(l.h6),p.putInt32(l.h7),p},i};var o=null,s=!1,a=null;function u(t,e,r){for(var n,i,o,s,u,c,h,f,l,p,d,y,g,m=r.length();m>=64;){for(u=0;u<16;++u)e[u]=r.getInt32();for(;u<64;++u)n=((n=e[u-2])>>>17|n<<15)^(n>>>19|n<<13)^n>>>10,i=((i=e[u-15])>>>7|i<<25)^(i>>>18|i<<14)^i>>>3,e[u]=n+e[u-7]+i+e[u-16]|0;for(c=t.h0,h=t.h1,f=t.h2,l=t.h3,p=t.h4,d=t.h5,y=t.h6,g=t.h7,u=0;u<64;++u)o=(c>>>2|c<<30)^(c>>>13|c<<19)^(c>>>22|c<<10),s=c&h|f&(c^h),n=g+((p>>>6|p<<26)^(p>>>11|p<<21)^(p>>>25|p<<7))+(y^p&(d^y))+a[u]+e[u],g=y,y=d,d=p,p=l+n>>>0,l=f,f=h,h=c,c=n+(i=o+s)>>>0;t.h0=t.h0+c|0,t.h1=t.h1+h|0,t.h2=t.h2+f|0,t.h3=t.h3+l|0,t.h4=t.h4+p|0,t.h5=t.h5+d|0,t.h6=t.h6+y|0,t.h7=t.h7+g|0,m-=64}}},72313:(t,e,r)=>{var n=r(50276);r(8106),r(7619);var i=t.exports=n.sha512=n.sha512||{};n.md.sha512=n.md.algorithms.sha512=i;var o=n.sha384=n.sha512.sha384=n.sha512.sha384||{};o.create=function(){return i.create("SHA-384")},n.md.sha384=n.md.algorithms.sha384=o,n.sha512.sha256=n.sha512.sha256||{create:function(){return i.create("SHA-512/256")}},n.md["sha512/256"]=n.md.algorithms["sha512/256"]=n.sha512.sha256,n.sha512.sha224=n.sha512.sha224||{create:function(){return i.create("SHA-512/224")}},n.md["sha512/224"]=n.md.algorithms["sha512/224"]=n.sha512.sha224,i.create=function(t){if(a||(s=String.fromCharCode(128),s+=n.util.fillString(String.fromCharCode(0),128),u=[[1116352408,3609767458],[1899447441,602891725],[3049323471,3964484399],[3921009573,2173295548],[961987163,4081628472],[1508970993,3053834265],[2453635748,2937671579],[2870763221,3664609560],[3624381080,2734883394],[310598401,1164996542],[607225278,1323610764],[1426881987,3590304994],[1925078388,4068182383],[2162078206,991336113],[2614888103,633803317],[3248222580,3479774868],[3835390401,2666613458],[4022224774,944711139],[264347078,2341262773],[604807628,2007800933],[770255983,1495990901],[1249150122,1856431235],[1555081692,3175218132],[1996064986,2198950837],[2554220882,3999719339],[2821834349,766784016],[2952996808,2566594879],[3210313671,3203337956],[3336571891,1034457026],[3584528711,2466948901],[113926993,3758326383],[338241895,168717936],[666307205,1188179964],[773529912,1546045734],[1294757372,1522805485],[1396182291,2643833823],[1695183700,2343527390],[1986661051,1014477480],[2177026350,1206759142],[2456956037,344077627],[2730485921,1290863460],[2820302411,3158454273],[3259730800,3505952657],[3345764771,106217008],[3516065817,3606008344],[3600352804,1432725776],[4094571909,1467031594],[275423344,851169720],[430227734,3100823752],[506948616,1363258195],[659060556,3750685593],[883997877,3785050280],[958139571,3318307427],[1322822218,3812723403],[1537002063,2003034995],[1747873779,3602036899],[1955562222,1575990012],[2024104815,1125592928],[2227730452,2716904306],[2361852424,442776044],[2428436474,593698344],[2756734187,3733110249],[3204031479,2999351573],[3329325298,3815920427],[3391569614,3928383900],[3515267271,566280711],[3940187606,3454069534],[4118630271,4000239992],[116418474,1914138554],[174292421,2731055270],[289380356,3203993006],[460393269,320620315],[685471733,587496836],[852142971,1086792851],[1017036298,365543100],[1126000580,2618297676],[1288033470,3409855158],[1501505948,4234509866],[1607167915,987167468],[1816402316,1246189591]],(c={})["SHA-512"]=[[1779033703,4089235720],[3144134277,2227873595],[1013904242,4271175723],[2773480762,1595750129],[1359893119,2917565137],[2600822924,725511199],[528734635,4215389547],[1541459225,327033209]],c["SHA-384"]=[[3418070365,3238371032],[1654270250,914150663],[2438529370,812702999],[355462360,4144912697],[1731405415,4290775857],[2394180231,1750603025],[3675008525,1694076839],[1203062813,3204075428]],c["SHA-512/256"]=[[573645204,4230739756],[2673172387,3360449730],[596883563,1867755857],[2520282905,1497426621],[2519219938,2827943907],[3193839141,1401305490],[721525244,746961066],[246885852,2177182882]],c["SHA-512/224"]=[[2352822216,424955298],[1944164710,2312950998],[502970286,855612546],[1738396948,1479516111],[258812777,2077511080],[2011393907,79989058],[1067287976,1780299464],[286451373,2446758561]],a=!0),void 0===t&&(t="SHA-512"),!(t in c))throw new Error("Invalid SHA-512 algorithm: "+t);for(var e=c[t],r=null,i=n.util.createBuffer(),o=new Array(80),f=0;f<80;++f)o[f]=new Array(2);var l=64;switch(t){case"SHA-384":l=48;break;case"SHA-512/256":l=32;break;case"SHA-512/224":l=28}var p={algorithm:t.replace("-","").toLowerCase(),blockLength:128,digestLength:l,messageLength:0,fullMessageLength:null,messageLengthSize:16,start:function(){p.messageLength=0,p.fullMessageLength=p.messageLength128=[];for(var t=p.messageLengthSize/4,o=0;o<t;++o)p.fullMessageLength.push(0);for(i=n.util.createBuffer(),r=new Array(e.length),o=0;o<e.length;++o)r[o]=e[o].slice(0);return p}};return p.start(),p.update=function(t,e){"utf8"===e&&(t=n.util.encodeUtf8(t));var s=t.length;p.messageLength+=s,s=[s/4294967296>>>0,s>>>0];for(var a=p.fullMessageLength.length-1;a>=0;--a)p.fullMessageLength[a]+=s[1],s[1]=s[0]+(p.fullMessageLength[a]/4294967296>>>0),p.fullMessageLength[a]=p.fullMessageLength[a]>>>0,s[0]=s[1]/4294967296>>>0;return i.putBytes(t),h(r,o,i),(i.read>2048||0===i.length())&&i.compact(),p},p.digest=function(){var e=n.util.createBuffer();e.putBytes(i.bytes());var a,u=p.fullMessageLength[p.fullMessageLength.length-1]+p.messageLengthSize&p.blockLength-1;e.putBytes(s.substr(0,p.blockLength-u));for(var c=8*p.fullMessageLength[0],f=0;f<p.fullMessageLength.length-1;++f)c+=(a=8*p.fullMessageLength[f+1])/4294967296>>>0,e.putInt32(c>>>0),c=a>>>0;e.putInt32(c);var l=new Array(r.length);for(f=0;f<r.length;++f)l[f]=r[f].slice(0);h(l,o,e);var d,y=n.util.createBuffer();for(d="SHA-512"===t?l.length:"SHA-384"===t?l.length-2:l.length-4,f=0;f<d;++f)y.putInt32(l[f][0]),f===d-1&&"SHA-512/224"===t||y.putInt32(l[f][1]);return y},p};var s=null,a=!1,u=null,c=null;function h(t,e,r){for(var n,i,o,s,a,c,h,f,l,p,d,y,g,m,b,v,w,E,S,B,A,C,T,I,k,x,R,_,P,U,L,N,D,O=r.length();O>=128;){for(R=0;R<16;++R)e[R][0]=r.getInt32()>>>0,e[R][1]=r.getInt32()>>>0;for(;R<80;++R)n=(((_=(U=e[R-2])[0])>>>19|(P=U[1])<<13)^(P>>>29|_<<3)^_>>>6)>>>0,i=((_<<13|P>>>19)^(P<<3|_>>>29)^(_<<26|P>>>6))>>>0,o=(((_=(N=e[R-15])[0])>>>1|(P=N[1])<<31)^(_>>>8|P<<24)^_>>>7)>>>0,s=((_<<31|P>>>1)^(_<<24|P>>>8)^(_<<25|P>>>7))>>>0,L=e[R-7],D=e[R-16],P=i+L[1]+s+D[1],e[R][0]=n+L[0]+o+D[0]+(P/4294967296>>>0)>>>0,e[R][1]=P>>>0;for(d=t[0][0],y=t[0][1],g=t[1][0],m=t[1][1],b=t[2][0],v=t[2][1],w=t[3][0],E=t[3][1],S=t[4][0],B=t[4][1],A=t[5][0],C=t[5][1],T=t[6][0],I=t[6][1],k=t[7][0],x=t[7][1],R=0;R<80;++R)h=((S>>>14|B<<18)^(S>>>18|B<<14)^(B>>>9|S<<23))>>>0,f=(T^S&(A^T))>>>0,a=((d>>>28|y<<4)^(y>>>2|d<<30)^(y>>>7|d<<25))>>>0,c=((d<<4|y>>>28)^(y<<30|d>>>2)^(y<<25|d>>>7))>>>0,l=(d&g|b&(d^g))>>>0,p=(y&m|v&(y^m))>>>0,P=x+(((S<<18|B>>>14)^(S<<14|B>>>18)^(B<<23|S>>>9))>>>0)+((I^B&(C^I))>>>0)+u[R][1]+e[R][1],n=k+h+f+u[R][0]+e[R][0]+(P/4294967296>>>0)>>>0,i=P>>>0,o=a+l+((P=c+p)/4294967296>>>0)>>>0,s=P>>>0,k=T,x=I,T=A,I=C,A=S,C=B,S=w+n+((P=E+i)/4294967296>>>0)>>>0,B=P>>>0,w=b,E=v,b=g,v=m,g=d,m=y,d=n+o+((P=i+s)/4294967296>>>0)>>>0,y=P>>>0;P=t[0][1]+y,t[0][0]=t[0][0]+d+(P/4294967296>>>0)>>>0,t[0][1]=P>>>0,P=t[1][1]+m,t[1][0]=t[1][0]+g+(P/4294967296>>>0)>>>0,t[1][1]=P>>>0,P=t[2][1]+v,t[2][0]=t[2][0]+b+(P/4294967296>>>0)>>>0,t[2][1]=P>>>0,P=t[3][1]+E,t[3][0]=t[3][0]+w+(P/4294967296>>>0)>>>0,t[3][1]=P>>>0,P=t[4][1]+B,t[4][0]=t[4][0]+S+(P/4294967296>>>0)>>>0,t[4][1]=P>>>0,P=t[5][1]+C,t[5][0]=t[5][0]+A+(P/4294967296>>>0)>>>0,t[5][1]=P>>>0,P=t[6][1]+I,t[6][0]=t[6][0]+T+(P/4294967296>>>0)>>>0,t[6][1]=P>>>0,P=t[7][1]+x,t[7][0]=t[7][0]+k+(P/4294967296>>>0)>>>0,t[7][1]=P>>>0,O-=128}}},7619:(t,e,r)=>{var n=r(50276),i=r(44058),o=t.exports=n.util=n.util||{};function s(t){if(8!==t&&16!==t&&24!==t&&32!==t)throw new Error("Only 8, 16, 24, or 32 bits supported: "+t)}function a(t){if(this.data="",this.read=0,"string"==typeof t)this.data=t;else if(o.isArrayBuffer(t)||o.isArrayBufferView(t))if("undefined"!=typeof Buffer&&t instanceof Buffer)this.data=t.toString("binary");else{var e=new Uint8Array(t);try{this.data=String.fromCharCode.apply(null,e)}catch(t){for(var r=0;r<e.length;++r)this.putByte(e[r])}}else(t instanceof a||"object"==typeof t&&"string"==typeof t.data&&"number"==typeof t.read)&&(this.data=t.data,this.read=t.read);this._constructedStringLength=0}!function(){if("undefined"!=typeof process&&process.nextTick&&!process.browser)return o.nextTick=process.nextTick,void("function"==typeof setImmediate?o.setImmediate=setImmediate:o.setImmediate=o.nextTick);if("function"==typeof setImmediate)return o.setImmediate=function(){return setImmediate.apply(void 0,arguments)},void(o.nextTick=function(t){return setImmediate(t)});if(o.setImmediate=function(t){setTimeout(t,0)},"undefined"!=typeof window&&"function"==typeof window.postMessage){var t="forge.setImmediate",e=[];o.setImmediate=function(r){e.push(r),1===e.length&&window.postMessage(t,"*")},window.addEventListener("message",(function(r){if(r.source===window&&r.data===t){r.stopPropagation();var n=e.slice();e.length=0,n.forEach((function(t){t()}))}}),!0)}if("undefined"!=typeof MutationObserver){var r=Date.now(),n=!0,i=document.createElement("div");e=[],new MutationObserver((function(){var t=e.slice();e.length=0,t.forEach((function(t){t()}))})).observe(i,{attributes:!0});var s=o.setImmediate;o.setImmediate=function(t){Date.now()-r>15?(r=Date.now(),s(t)):(e.push(t),1===e.length&&i.setAttribute("a",n=!n))}}o.nextTick=o.setImmediate}(),o.isNodejs="undefined"!=typeof process&&process.versions&&process.versions.node,o.globalScope=o.isNodejs?r.g:"undefined"==typeof self?window:self,o.isArray=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},o.isArrayBuffer=function(t){return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer},o.isArrayBufferView=function(t){return t&&o.isArrayBuffer(t.buffer)&&void 0!==t.byteLength},o.ByteBuffer=a,o.ByteStringBuffer=a,o.ByteStringBuffer.prototype._optimizeConstructedString=function(t){this._constructedStringLength+=t,this._constructedStringLength>4096&&(this.data.substr(0,1),this._constructedStringLength=0)},o.ByteStringBuffer.prototype.length=function(){return this.data.length-this.read},o.ByteStringBuffer.prototype.isEmpty=function(){return this.length()<=0},o.ByteStringBuffer.prototype.putByte=function(t){return this.putBytes(String.fromCharCode(t))},o.ByteStringBuffer.prototype.fillWithByte=function(t,e){t=String.fromCharCode(t);for(var r=this.data;e>0;)1&e&&(r+=t),(e>>>=1)>0&&(t+=t);return this.data=r,this._optimizeConstructedString(e),this},o.ByteStringBuffer.prototype.putBytes=function(t){return this.data+=t,this._optimizeConstructedString(t.length),this},o.ByteStringBuffer.prototype.putString=function(t){return this.putBytes(o.encodeUtf8(t))},o.ByteStringBuffer.prototype.putInt16=function(t){return this.putBytes(String.fromCharCode(t>>8&255)+String.fromCharCode(255&t))},o.ByteStringBuffer.prototype.putInt24=function(t){return this.putBytes(String.fromCharCode(t>>16&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(255&t))},o.ByteStringBuffer.prototype.putInt32=function(t){return this.putBytes(String.fromCharCode(t>>24&255)+String.fromCharCode(t>>16&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(255&t))},o.ByteStringBuffer.prototype.putInt16Le=function(t){return this.putBytes(String.fromCharCode(255&t)+String.fromCharCode(t>>8&255))},o.ByteStringBuffer.prototype.putInt24Le=function(t){return this.putBytes(String.fromCharCode(255&t)+String.fromCharCode(t>>8&255)+String.fromCharCode(t>>16&255))},o.ByteStringBuffer.prototype.putInt32Le=function(t){return this.putBytes(String.fromCharCode(255&t)+String.fromCharCode(t>>8&255)+String.fromCharCode(t>>16&255)+String.fromCharCode(t>>24&255))},o.ByteStringBuffer.prototype.putInt=function(t,e){s(e);var r="";do{e-=8,r+=String.fromCharCode(t>>e&255)}while(e>0);return this.putBytes(r)},o.ByteStringBuffer.prototype.putSignedInt=function(t,e){return t<0&&(t+=2<<e-1),this.putInt(t,e)},o.ByteStringBuffer.prototype.putBuffer=function(t){return this.putBytes(t.getBytes())},o.ByteStringBuffer.prototype.getByte=function(){return this.data.charCodeAt(this.read++)},o.ByteStringBuffer.prototype.getInt16=function(){var t=this.data.charCodeAt(this.read)<<8^this.data.charCodeAt(this.read+1);return this.read+=2,t},o.ByteStringBuffer.prototype.getInt24=function(){var t=this.data.charCodeAt(this.read)<<16^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2);return this.read+=3,t},o.ByteStringBuffer.prototype.getInt32=function(){var t=this.data.charCodeAt(this.read)<<24^this.data.charCodeAt(this.read+1)<<16^this.data.charCodeAt(this.read+2)<<8^this.data.charCodeAt(this.read+3);return this.read+=4,t},o.ByteStringBuffer.prototype.getInt16Le=function(){var t=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8;return this.read+=2,t},o.ByteStringBuffer.prototype.getInt24Le=function(){var t=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16;return this.read+=3,t},o.ByteStringBuffer.prototype.getInt32Le=function(){var t=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16^this.data.charCodeAt(this.read+3)<<24;return this.read+=4,t},o.ByteStringBuffer.prototype.getInt=function(t){s(t);var e=0;do{e=(e<<8)+this.data.charCodeAt(this.read++),t-=8}while(t>0);return e},o.ByteStringBuffer.prototype.getSignedInt=function(t){var e=this.getInt(t),r=2<<t-2;return e>=r&&(e-=r<<1),e},o.ByteStringBuffer.prototype.getBytes=function(t){var e;return t?(t=Math.min(this.length(),t),e=this.data.slice(this.read,this.read+t),this.read+=t):0===t?e="":(e=0===this.read?this.data:this.data.slice(this.read),this.clear()),e},o.ByteStringBuffer.prototype.bytes=function(t){return void 0===t?this.data.slice(this.read):this.data.slice(this.read,this.read+t)},o.ByteStringBuffer.prototype.at=function(t){return this.data.charCodeAt(this.read+t)},o.ByteStringBuffer.prototype.setAt=function(t,e){return this.data=this.data.substr(0,this.read+t)+String.fromCharCode(e)+this.data.substr(this.read+t+1),this},o.ByteStringBuffer.prototype.last=function(){return this.data.charCodeAt(this.data.length-1)},o.ByteStringBuffer.prototype.copy=function(){var t=o.createBuffer(this.data);return t.read=this.read,t},o.ByteStringBuffer.prototype.compact=function(){return this.read>0&&(this.data=this.data.slice(this.read),this.read=0),this},o.ByteStringBuffer.prototype.clear=function(){return this.data="",this.read=0,this},o.ByteStringBuffer.prototype.truncate=function(t){var e=Math.max(0,this.length()-t);return this.data=this.data.substr(this.read,e),this.read=0,this},o.ByteStringBuffer.prototype.toHex=function(){for(var t="",e=this.read;e<this.data.length;++e){var r=this.data.charCodeAt(e);r<16&&(t+="0"),t+=r.toString(16)}return t},o.ByteStringBuffer.prototype.toString=function(){return o.decodeUtf8(this.bytes())},o.DataBuffer=function(t,e){e=e||{},this.read=e.readOffset||0,this.growSize=e.growSize||1024;var r=o.isArrayBuffer(t),n=o.isArrayBufferView(t);if(r||n)return this.data=r?new DataView(t):new DataView(t.buffer,t.byteOffset,t.byteLength),void(this.write="writeOffset"in e?e.writeOffset:this.data.byteLength);this.data=new DataView(new ArrayBuffer(0)),this.write=0,null!=t&&this.putBytes(t),"writeOffset"in e&&(this.write=e.writeOffset)},o.DataBuffer.prototype.length=function(){return this.write-this.read},o.DataBuffer.prototype.isEmpty=function(){return this.length()<=0},o.DataBuffer.prototype.accommodate=function(t,e){if(this.length()>=t)return this;e=Math.max(e||this.growSize,t);var r=new Uint8Array(this.data.buffer,this.data.byteOffset,this.data.byteLength),n=new Uint8Array(this.length()+e);return n.set(r),this.data=new DataView(n.buffer),this},o.DataBuffer.prototype.putByte=function(t){return this.accommodate(1),this.data.setUint8(this.write++,t),this},o.DataBuffer.prototype.fillWithByte=function(t,e){this.accommodate(e);for(var r=0;r<e;++r)this.data.setUint8(t);return this},o.DataBuffer.prototype.putBytes=function(t,e){if(o.isArrayBufferView(t)){var r=(n=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)).byteLength-n.byteOffset;return this.accommodate(r),new Uint8Array(this.data.buffer,this.write).set(n),this.write+=r,this}if(o.isArrayBuffer(t)){var n=new Uint8Array(t);return this.accommodate(n.byteLength),new Uint8Array(this.data.buffer).set(n,this.write),this.write+=n.byteLength,this}if(t instanceof o.DataBuffer||"object"==typeof t&&"number"==typeof t.read&&"number"==typeof t.write&&o.isArrayBufferView(t.data))return n=new Uint8Array(t.data.byteLength,t.read,t.length()),this.accommodate(n.byteLength),new Uint8Array(t.data.byteLength,this.write).set(n),this.write+=n.byteLength,this;if(t instanceof o.ByteStringBuffer&&(t=t.data,e="binary"),e=e||"binary","string"==typeof t){var i;if("hex"===e)return this.accommodate(Math.ceil(t.length/2)),i=new Uint8Array(this.data.buffer,this.write),this.write+=o.binary.hex.decode(t,i,this.write),this;if("base64"===e)return this.accommodate(3*Math.ceil(t.length/4)),i=new Uint8Array(this.data.buffer,this.write),this.write+=o.binary.base64.decode(t,i,this.write),this;if("utf8"===e&&(t=o.encodeUtf8(t),e="binary"),"binary"===e||"raw"===e)return this.accommodate(t.length),i=new Uint8Array(this.data.buffer,this.write),this.write+=o.binary.raw.decode(i),this;if("utf16"===e)return this.accommodate(2*t.length),i=new Uint16Array(this.data.buffer,this.write),this.write+=o.text.utf16.encode(i),this;throw new Error("Invalid encoding: "+e)}throw Error("Invalid parameter: "+t)},o.DataBuffer.prototype.putBuffer=function(t){return this.putBytes(t),t.clear(),this},o.DataBuffer.prototype.putString=function(t){return this.putBytes(t,"utf16")},o.DataBuffer.prototype.putInt16=function(t){return this.accommodate(2),this.data.setInt16(this.write,t),this.write+=2,this},o.DataBuffer.prototype.putInt24=function(t){return this.accommodate(3),this.data.setInt16(this.write,t>>8&65535),this.data.setInt8(this.write,t>>16&255),this.write+=3,this},o.DataBuffer.prototype.putInt32=function(t){return this.accommodate(4),this.data.setInt32(this.write,t),this.write+=4,this},o.DataBuffer.prototype.putInt16Le=function(t){return this.accommodate(2),this.data.setInt16(this.write,t,!0),this.write+=2,this},o.DataBuffer.prototype.putInt24Le=function(t){return this.accommodate(3),this.data.setInt8(this.write,t>>16&255),this.data.setInt16(this.write,t>>8&65535,!0),this.write+=3,this},o.DataBuffer.prototype.putInt32Le=function(t){return this.accommodate(4),this.data.setInt32(this.write,t,!0),this.write+=4,this},o.DataBuffer.prototype.putInt=function(t,e){s(e),this.accommodate(e/8);do{e-=8,this.data.setInt8(this.write++,t>>e&255)}while(e>0);return this},o.DataBuffer.prototype.putSignedInt=function(t,e){return s(e),this.accommodate(e/8),t<0&&(t+=2<<e-1),this.putInt(t,e)},o.DataBuffer.prototype.getByte=function(){return this.data.getInt8(this.read++)},o.DataBuffer.prototype.getInt16=function(){var t=this.data.getInt16(this.read);return this.read+=2,t},o.DataBuffer.prototype.getInt24=function(){var t=this.data.getInt16(this.read)<<8^this.data.getInt8(this.read+2);return this.read+=3,t},o.DataBuffer.prototype.getInt32=function(){var t=this.data.getInt32(this.read);return this.read+=4,t},o.DataBuffer.prototype.getInt16Le=function(){var t=this.data.getInt16(this.read,!0);return this.read+=2,t},o.DataBuffer.prototype.getInt24Le=function(){var t=this.data.getInt8(this.read)^this.data.getInt16(this.read+1,!0)<<8;return this.read+=3,t},o.DataBuffer.prototype.getInt32Le=function(){var t=this.data.getInt32(this.read,!0);return this.read+=4,t},o.DataBuffer.prototype.getInt=function(t){s(t);var e=0;do{e=(e<<8)+this.data.getInt8(this.read++),t-=8}while(t>0);return e},o.DataBuffer.prototype.getSignedInt=function(t){var e=this.getInt(t),r=2<<t-2;return e>=r&&(e-=r<<1),e},o.DataBuffer.prototype.getBytes=function(t){var e;return t?(t=Math.min(this.length(),t),e=this.data.slice(this.read,this.read+t),this.read+=t):0===t?e="":(e=0===this.read?this.data:this.data.slice(this.read),this.clear()),e},o.DataBuffer.prototype.bytes=function(t){return void 0===t?this.data.slice(this.read):this.data.slice(this.read,this.read+t)},o.DataBuffer.prototype.at=function(t){return this.data.getUint8(this.read+t)},o.DataBuffer.prototype.setAt=function(t,e){return this.data.setUint8(t,e),this},o.DataBuffer.prototype.last=function(){return this.data.getUint8(this.write-1)},o.DataBuffer.prototype.copy=function(){return new o.DataBuffer(this)},o.DataBuffer.prototype.compact=function(){if(this.read>0){var t=new Uint8Array(this.data.buffer,this.read),e=new Uint8Array(t.byteLength);e.set(t),this.data=new DataView(e),this.write-=this.read,this.read=0}return this},o.DataBuffer.prototype.clear=function(){return this.data=new DataView(new ArrayBuffer(0)),this.read=this.write=0,this},o.DataBuffer.prototype.truncate=function(t){return this.write=Math.max(0,this.length()-t),this.read=Math.min(this.read,this.write),this},o.DataBuffer.prototype.toHex=function(){for(var t="",e=this.read;e<this.data.byteLength;++e){var r=this.data.getUint8(e);r<16&&(t+="0"),t+=r.toString(16)}return t},o.DataBuffer.prototype.toString=function(t){var e=new Uint8Array(this.data,this.read,this.length());if("binary"===(t=t||"utf8")||"raw"===t)return o.binary.raw.encode(e);if("hex"===t)return o.binary.hex.encode(e);if("base64"===t)return o.binary.base64.encode(e);if("utf8"===t)return o.text.utf8.decode(e);if("utf16"===t)return o.text.utf16.decode(e);throw new Error("Invalid encoding: "+t)},o.createBuffer=function(t,e){return e=e||"raw",void 0!==t&&"utf8"===e&&(t=o.encodeUtf8(t)),new o.ByteBuffer(t)},o.fillString=function(t,e){for(var r="";e>0;)1&e&&(r+=t),(e>>>=1)>0&&(t+=t);return r},o.xorBytes=function(t,e,r){for(var n="",i="",o="",s=0,a=0;r>0;--r,++s)i=t.charCodeAt(s)^e.charCodeAt(s),a>=10&&(n+=o,o="",a=0),o+=String.fromCharCode(i),++a;return n+o},o.hexToBytes=function(t){var e="",r=0;for(!0&t.length&&(r=1,e+=String.fromCharCode(parseInt(t[0],16)));r<t.length;r+=2)e+=String.fromCharCode(parseInt(t.substr(r,2),16));return e},o.bytesToHex=function(t){return o.createBuffer(t).toHex()},o.int32ToBytes=function(t){return String.fromCharCode(t>>24&255)+String.fromCharCode(t>>16&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(255&t)};var u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",c=[62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,64,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],h="**********************************************************";o.encode64=function(t,e){for(var r,n,i,o="",s="",a=0;a<t.length;)r=t.charCodeAt(a++),n=t.charCodeAt(a++),i=t.charCodeAt(a++),o+=u.charAt(r>>2),o+=u.charAt((3&r)<<4|n>>4),isNaN(n)?o+="==":(o+=u.charAt((15&n)<<2|i>>6),o+=isNaN(i)?"=":u.charAt(63&i)),e&&o.length>e&&(s+=o.substr(0,e)+"\r\n",o=o.substr(e));return s+o},o.decode64=function(t){t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(var e,r,n,i,o="",s=0;s<t.length;)e=c[t.charCodeAt(s++)-43],r=c[t.charCodeAt(s++)-43],n=c[t.charCodeAt(s++)-43],i=c[t.charCodeAt(s++)-43],o+=String.fromCharCode(e<<2|r>>4),64!==n&&(o+=String.fromCharCode((15&r)<<4|n>>2),64!==i&&(o+=String.fromCharCode((3&n)<<6|i)));return o},o.encodeUtf8=function(t){return unescape(encodeURIComponent(t))},o.decodeUtf8=function(t){return decodeURIComponent(escape(t))},o.binary={raw:{},hex:{},base64:{},base58:{},baseN:{encode:i.encode,decode:i.decode}},o.binary.raw.encode=function(t){return String.fromCharCode.apply(null,t)},o.binary.raw.decode=function(t,e,r){var n=e;n||(n=new Uint8Array(t.length));for(var i=r=r||0,o=0;o<t.length;++o)n[i++]=t.charCodeAt(o);return e?i-r:n},o.binary.hex.encode=o.bytesToHex,o.binary.hex.decode=function(t,e,r){var n=e;n||(n=new Uint8Array(Math.ceil(t.length/2)));var i=0,o=r=r||0;for(1&t.length&&(i=1,n[o++]=parseInt(t[0],16));i<t.length;i+=2)n[o++]=parseInt(t.substr(i,2),16);return e?o-r:n},o.binary.base64.encode=function(t,e){for(var r,n,i,o="",s="",a=0;a<t.byteLength;)r=t[a++],n=t[a++],i=t[a++],o+=u.charAt(r>>2),o+=u.charAt((3&r)<<4|n>>4),isNaN(n)?o+="==":(o+=u.charAt((15&n)<<2|i>>6),o+=isNaN(i)?"=":u.charAt(63&i)),e&&o.length>e&&(s+=o.substr(0,e)+"\r\n",o=o.substr(e));return s+o},o.binary.base64.decode=function(t,e,r){var n,i,o,s,a=e;a||(a=new Uint8Array(3*Math.ceil(t.length/4))),t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(var u=0,h=r=r||0;u<t.length;)n=c[t.charCodeAt(u++)-43],i=c[t.charCodeAt(u++)-43],o=c[t.charCodeAt(u++)-43],s=c[t.charCodeAt(u++)-43],a[h++]=n<<2|i>>4,64!==o&&(a[h++]=(15&i)<<4|o>>2,64!==s&&(a[h++]=(3&o)<<6|s));return e?h-r:a.subarray(0,h)},o.binary.base58.encode=function(t,e){return o.binary.baseN.encode(t,h,e)},o.binary.base58.decode=function(t,e){return o.binary.baseN.decode(t,h,e)},o.text={utf8:{},utf16:{}},o.text.utf8.encode=function(t,e,r){t=o.encodeUtf8(t);var n=e;n||(n=new Uint8Array(t.length));for(var i=r=r||0,s=0;s<t.length;++s)n[i++]=t.charCodeAt(s);return e?i-r:n},o.text.utf8.decode=function(t){return o.decodeUtf8(String.fromCharCode.apply(null,t))},o.text.utf16.encode=function(t,e,r){var n=e;n||(n=new Uint8Array(2*t.length));for(var i=new Uint16Array(n.buffer),o=r=r||0,s=r,a=0;a<t.length;++a)i[s++]=t.charCodeAt(a),o+=2;return e?o-r:n},o.text.utf16.decode=function(t){return String.fromCharCode.apply(null,new Uint16Array(t.buffer))},o.deflate=function(t,e,r){if(e=o.decode64(t.deflate(o.encode64(e)).rval),r){var n=2;32&e.charCodeAt(1)&&(n=6),e=e.substring(n,e.length-4)}return e},o.inflate=function(t,e,r){var n=t.inflate(o.encode64(e)).rval;return null===n?null:o.decode64(n)};var f=function(t,e,r){if(!t)throw new Error("WebStorage not available.");var n;if(null===r?n=t.removeItem(e):(r=o.encode64(JSON.stringify(r)),n=t.setItem(e,r)),void 0!==n&&!0!==n.rval){var i=new Error(n.error.message);throw i.id=n.error.id,i.name=n.error.name,i}},l=function(t,e){if(!t)throw new Error("WebStorage not available.");var r=t.getItem(e);if(t.init)if(null===r.rval){if(r.error){var n=new Error(r.error.message);throw n.id=r.error.id,n.name=r.error.name,n}r=null}else r=r.rval;return null!==r&&(r=JSON.parse(o.decode64(r))),r},p=function(t,e,r,n){var i=l(t,e);null===i&&(i={}),i[r]=n,f(t,e,i)},d=function(t,e,r){var n=l(t,e);return null!==n&&(n=r in n?n[r]:null),n},y=function(t,e,r){var n=l(t,e);if(null!==n&&r in n){delete n[r];var i=!0;for(var o in n){i=!1;break}i&&(n=null),f(t,e,n)}},g=function(t,e){f(t,e,null)},m=function(t,e,r){var n,i=null;void 0===r&&(r=["web","flash"]);var o=!1,s=null;for(var a in r){n=r[a];try{if("flash"===n||"both"===n){if(null===e[0])throw new Error("Flash local storage not available.");i=t.apply(this,e),o="flash"===n}"web"!==n&&"both"!==n||(e[0]=localStorage,i=t.apply(this,e),o=!0)}catch(t){s=t}if(o)break}if(!o)throw s;return i};o.setItem=function(t,e,r,n,i){m(p,arguments,i)},o.getItem=function(t,e,r,n){return m(d,arguments,n)},o.removeItem=function(t,e,r,n){m(y,arguments,n)},o.clearItems=function(t,e,r){m(g,arguments,r)},o.isEmpty=function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0},o.format=function(t){for(var e,r,n=/%./g,i=0,o=[],s=0;e=n.exec(t);){(r=t.substring(s,n.lastIndex-2)).length>0&&o.push(r),s=n.lastIndex;var a=e[0][1];switch(a){case"s":case"o":i<arguments.length?o.push(arguments[1+i++]):o.push("<?>");break;case"%":o.push("%");break;default:o.push("<%"+a+"?>")}}return o.push(t.substring(s)),o.join("")},o.formatNumber=function(t,e,r,n){var i=t,o=isNaN(e=Math.abs(e))?2:e,s=void 0===r?",":r,a=void 0===n?".":n,u=i<0?"-":"",c=parseInt(i=Math.abs(+i||0).toFixed(o),10)+"",h=c.length>3?c.length%3:0;return u+(h?c.substr(0,h)+a:"")+c.substr(h).replace(/(\d{3})(?=\d)/g,"$1"+a)+(o?s+Math.abs(i-c).toFixed(o).slice(2):"")},o.formatSize=function(t){return t>=1073741824?o.formatNumber(t/1073741824,2,".","")+" GiB":t>=1048576?o.formatNumber(t/1048576,2,".","")+" MiB":t>=1024?o.formatNumber(t/1024,0)+" KiB":o.formatNumber(t,0)+" bytes"},o.bytesFromIP=function(t){return-1!==t.indexOf(".")?o.bytesFromIPv4(t):-1!==t.indexOf(":")?o.bytesFromIPv6(t):null},o.bytesFromIPv4=function(t){if(4!==(t=t.split(".")).length)return null;for(var e=o.createBuffer(),r=0;r<t.length;++r){var n=parseInt(t[r],10);if(isNaN(n))return null;e.putByte(n)}return e.getBytes()},o.bytesFromIPv6=function(t){for(var e=0,r=2*(8-(t=t.split(":").filter((function(t){return 0===t.length&&++e,!0}))).length+e),n=o.createBuffer(),i=0;i<8;++i)if(t[i]&&0!==t[i].length){var s=o.hexToBytes(t[i]);s.length<2&&n.putByte(0),n.putBytes(s)}else n.fillWithByte(0,r),r=0;return n.getBytes()},o.bytesToIP=function(t){return 4===t.length?o.bytesToIPv4(t):16===t.length?o.bytesToIPv6(t):null},o.bytesToIPv4=function(t){if(4!==t.length)return null;for(var e=[],r=0;r<t.length;++r)e.push(t.charCodeAt(r));return e.join(".")},o.bytesToIPv6=function(t){if(16!==t.length)return null;for(var e=[],r=[],n=0,i=0;i<t.length;i+=2){for(var s=o.bytesToHex(t[i]+t[i+1]);"0"===s[0]&&"0"!==s;)s=s.substr(1);if("0"===s){var a=r[r.length-1],u=e.length;a&&u===a.end+1?(a.end=u,a.end-a.start>r[n].end-r[n].start&&(n=r.length-1)):r.push({start:u,end:u})}e.push(s)}if(r.length>0){var c=r[n];c.end-c.start>0&&(e.splice(c.start,c.end-c.start+1,""),0===c.start&&e.unshift(""),7===c.end&&e.push(""))}return e.join(":")},o.estimateCores=function(t,e){if("function"==typeof t&&(e=t,t={}),t=t||{},"cores"in o&&!t.update)return e(null,o.cores);if("undefined"!=typeof navigator&&"hardwareConcurrency"in navigator&&navigator.hardwareConcurrency>0)return o.cores=navigator.hardwareConcurrency,e(null,o.cores);if("undefined"==typeof Worker)return o.cores=1,e(null,o.cores);if("undefined"==typeof Blob)return o.cores=2,e(null,o.cores);var r=URL.createObjectURL(new Blob(["(",function(){self.addEventListener("message",(function(t){for(var e=Date.now(),r=e+4;Date.now()<r;);self.postMessage({st:e,et:r})}))}.toString(),")()"],{type:"application/javascript"}));!function t(n,i,s){if(0===i){var a=Math.floor(n.reduce((function(t,e){return t+e}),0)/n.length);return o.cores=Math.max(1,a),URL.revokeObjectURL(r),e(null,o.cores)}!function(t,e){for(var n=[],i=[],o=0;o<t;++o){var s=new Worker(r);s.addEventListener("message",(function(r){if(i.push(r.data),i.length===t){for(var o=0;o<t;++o)n[o].terminate();e(0,i)}})),n.push(s)}for(o=0;o<t;++o)n[o].postMessage(o)}(s,(function(e,r){n.push(function(t,e){for(var r=[],n=0;n<t;++n)for(var i=e[n],o=r[n]=[],s=0;s<t;++s)if(n!==s){var a=e[s];(i.st>a.st&&i.st<a.et||a.st>i.st&&a.st<i.et)&&o.push(s)}return r.reduce((function(t,e){return Math.max(t,e.length)}),0)}(s,r)),t(n,i-1,s)}))}([],5,16)}},50216:(t,e,r)=>{"use strict";const{CID:n}=r(94944),i=r(93009),o=r(70573),s=r(32257),a=r(96794),{base58btc:u}=r(32257),{base32:c}=r(93009),{base16:h}=r(58151),f=r(56677),l=r(62558),p=r(45998),{PeerIdProto:d}=r(60754),{equals:y}=r(18402),{fromString:g}=r(44117),{toString:m}=r(27302),{identity:b}=r(24255),v={...i,...o,...s,...a},w=Object.keys(v).reduce(((t,e)=>t.or(v[e])),c.decoder),E=p(class{constructor(t,e,r){if(!(t instanceof Uint8Array))throw new Error("invalid id provided");if(e&&r&&!y(e.public.bytes,r.bytes))throw new Error("inconsistent arguments");this._id=t,this._idB58String=u.encode(this.id).substring(1),this._privKey=e,this._pubKey=r}get id(){return this._id}set id(t){throw new Error("Id is immutable")}get privKey(){return this._privKey}set privKey(t){this._privKey=t}get pubKey(){if(this._pubKey)return this._pubKey;if(this._privKey)return this._privKey.public;try{const t=f.decode(this.id);t.code===b.code&&(this._pubKey=l.unmarshalPublicKey(t.digest))}catch(t){}return this._pubKey}set pubKey(t){this._pubKey=t}marshalPubKey(){if(this.pubKey)return l.marshalPublicKey(this.pubKey)}marshalPrivKey(){if(this.privKey)return l.marshalPrivateKey(this.privKey)}marshal(t){return d.encode({id:this.toBytes(),pubKey:this.marshalPubKey(),privKey:t?null:this.marshalPrivKey()}).finish()}toPrint(){let t=this.toB58String();t.startsWith("Qm")&&(t=t.slice(2));let e=6;return t.length<e&&(e=t.length),"<peer.ID "+t.substr(0,e)+">"}toJSON(){return{id:this.toB58String(),privKey:C(this.marshalPrivKey()),pubKey:C(this.marshalPubKey())}}toHexString(){return h.encode(this.id).substring(1)}toBytes(){return this.id}toB58String(){return this._idB58String}toString(){if(!this._idCIDString){const t=n.createV1(114,f.decode(this.id));Object.defineProperty(this,"_idCIDString",{value:t.toString(),enumerable:!1})}return this._idCIDString}equals(t){if(t instanceof Uint8Array)return y(this.id,t);if(t.id)return y(this.id,t.id);throw new Error("not valid Id")}isEqual(t){return this.equals(t)}isValid(){return Boolean(this.privKey&&this.privKey.public&&this.privKey.public.bytes&&this.pubKey.bytes instanceof Uint8Array&&y(this.privKey.public.bytes,this.pubKey.bytes))}hasInlinePublicKey(){try{if(f.decode(this.id).code===b.code)return!0}catch(t){}return!1}},{className:"PeerId",symbolName:"@libp2p/js-peer-id/PeerId"});e=t.exports=E;const S=t=>t.bytes.length<=42?f.create(b.code,t.bytes).bytes:t.hash(),B=async(t,e)=>{const r=await S(e);return new E(r,t,e)};e.create=async t=>{(t=t||{}).bits=t.bits||2048,t.keyType=t.keyType||"RSA";const e=await l.generateKeyPair(t.keyType,t.bits);return B(e,e.public)},e.createFromHexString=t=>new E(h.decode("f"+t)),e.createFromBytes=t=>{try{const r=n.decode(t);if(!A(r))throw new Error("Supplied PeerID CID is invalid");return e.createFromCID(r)}catch{if(f.decode(t).code!==b.code)throw new Error("Supplied PeerID CID is invalid");return new E(t)}},e.createFromB58String=t=>e.createFromBytes(u.decode("z"+t));const A=t=>114===t.code||112===t.code;function C(t){if(t)return m(t,"base64pad")}e.createFromCID=t=>{if(!(t=n.asCID(t))||!A(t))throw new Error("Supplied PeerID CID is invalid");return new E(t.multihash.bytes)},e.createFromPubKey=async t=>{let e=t;if("string"==typeof e&&(e=g(t,"base64pad")),!(e instanceof Uint8Array))throw new Error("Supplied key is neither a base64 string nor a Uint8Array");const r=await l.unmarshalPublicKey(e);return B(void 0,r)},e.createFromPrivKey=async t=>{if("string"==typeof t&&(t=g(t,"base64pad")),!(t instanceof Uint8Array))throw new Error("Supplied key is neither a base64 string nor a Uint8Array");const e=await l.unmarshalPrivateKey(t);return B(e,e.public)},e.createFromJSON=async t=>{const e=u.decode("z"+t.id),r=t.privKey&&g(t.privKey,"base64pad"),n=t.pubKey&&g(t.pubKey,"base64pad"),i=n&&await l.unmarshalPublicKey(n);if(!r)return new E(e,void 0,i);const o=await l.unmarshalPrivateKey(r),s=await S(o.public);let a;if(i&&(a=await S(i)),i&&!y(s,a))throw new Error("Public and private key do not match");if(e&&!y(s,e))throw new Error("Id and private key do not match");return new E(e,o,i)},e.createFromProtobuf=async t=>{"string"==typeof t&&(t=g(t,"base16"));let e,r,{id:n,privKey:i,pubKey:o}=d.decode(t);if(i=!!i&&await l.unmarshalPrivateKey(i),o=!!o&&await l.unmarshalPublicKey(o),i&&(r=await S(i.public)),o&&(e=await S(o)),i){if(o&&!y(r,e))throw new Error("Public and private key do not match");return new E(r,i,i.public)}if(o)return new E(e,void 0,o);if(n)return new E(n);throw new Error("Protobuf did not contain any usable key material")},e.parse=t=>("1"!==t.charAt(0)&&"Q"!==t.charAt(0)||(t=`z${t}`),e.createFromBytes(w.decode(t))),e.isPeerId=t=>Boolean("object"==typeof t&&t._id&&t._idB58String)},60754:(t,e,r)=>{"use strict";var n=r(26946),i=n.Reader,o=n.Writer,s=n.util,a=n.roots["libp2p-peer-id"]||(n.roots["libp2p-peer-id"]={});a.PeerIdProto=function(){function t(t){if(t)for(var e=Object.keys(t),r=0;r<e.length;++r)null!=t[e[r]]&&(this[e[r]]=t[e[r]])}return t.prototype.id=s.newBuffer([]),t.prototype.pubKey=s.newBuffer([]),t.prototype.privKey=s.newBuffer([]),t.encode=function(t,e){return e||(e=o.create()),e.uint32(10).bytes(t.id),null!=t.pubKey&&Object.hasOwnProperty.call(t,"pubKey")&&e.uint32(18).bytes(t.pubKey),null!=t.privKey&&Object.hasOwnProperty.call(t,"privKey")&&e.uint32(26).bytes(t.privKey),e},t.decode=function(t,e){t instanceof i||(t=i.create(t));for(var r=void 0===e?t.len:t.pos+e,n=new a.PeerIdProto;t.pos<r;){var o=t.uint32();switch(o>>>3){case 1:n.id=t.bytes();break;case 2:n.pubKey=t.bytes();break;case 3:n.privKey=t.bytes();break;default:t.skipType(7&o)}}if(!n.hasOwnProperty("id"))throw s.ProtocolError("missing required 'id'",{instance:n});return n},t.fromObject=function(t){if(t instanceof a.PeerIdProto)return t;var e=new a.PeerIdProto;return null!=t.id&&("string"==typeof t.id?s.base64.decode(t.id,e.id=s.newBuffer(s.base64.length(t.id)),0):t.id.length&&(e.id=t.id)),null!=t.pubKey&&("string"==typeof t.pubKey?s.base64.decode(t.pubKey,e.pubKey=s.newBuffer(s.base64.length(t.pubKey)),0):t.pubKey.length&&(e.pubKey=t.pubKey)),null!=t.privKey&&("string"==typeof t.privKey?s.base64.decode(t.privKey,e.privKey=s.newBuffer(s.base64.length(t.privKey)),0):t.privKey.length&&(e.privKey=t.privKey)),e},t.toObject=function(t,e){e||(e={});var r={};return e.defaults&&(e.bytes===String?r.id="":(r.id=[],e.bytes!==Array&&(r.id=s.newBuffer(r.id))),e.bytes===String?r.pubKey="":(r.pubKey=[],e.bytes!==Array&&(r.pubKey=s.newBuffer(r.pubKey))),e.bytes===String?r.privKey="":(r.privKey=[],e.bytes!==Array&&(r.privKey=s.newBuffer(r.privKey)))),null!=t.id&&t.hasOwnProperty("id")&&(r.id=e.bytes===String?s.base64.encode(t.id,0,t.id.length):e.bytes===Array?Array.prototype.slice.call(t.id):t.id),null!=t.pubKey&&t.hasOwnProperty("pubKey")&&(r.pubKey=e.bytes===String?s.base64.encode(t.pubKey,0,t.pubKey.length):e.bytes===Array?Array.prototype.slice.call(t.pubKey):t.pubKey),null!=t.privKey&&t.hasOwnProperty("privKey")&&(r.privKey=e.bytes===String?s.base64.encode(t.privKey,0,t.privKey.length):e.bytes===Array?Array.prototype.slice.call(t.privKey):t.privKey),r},t.prototype.toJSON=function(){return this.constructor.toObject(this,n.util.toJSONOptions)},t}(),t.exports=a},26946:(t,e,r)=>{"use strict";t.exports=r(24394)},24394:(t,e,r)=>{"use strict";var n=e;function i(){n.util._configure(),n.Writer._configure(n.BufferWriter),n.Reader._configure(n.BufferReader)}n.build="minimal",n.Writer=r(63449),n.BufferWriter=r(60818),n.Reader=r(16237),n.BufferReader=r(33158),n.util=r(93610),n.rpc=r(95047),n.roots=r(64529),n.configure=i,i()},16237:(t,e,r)=>{"use strict";t.exports=u;var n,i=r(93610),o=i.LongBits,s=i.utf8;function a(t,e){return RangeError("index out of range: "+t.pos+" + "+(e||1)+" > "+t.len)}function u(t){this.buf=t,this.pos=0,this.len=t.length}var c,h="undefined"!=typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new u(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new u(t);throw Error("illegal buffer")},f=function(){return i.Buffer?function(t){return(u.create=function(t){return i.Buffer.isBuffer(t)?new n(t):h(t)})(t)}:h};function l(){var t=new o(0,0),e=0;if(!(this.len-this.pos>4)){for(;e<3;++e){if(this.pos>=this.len)throw a(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*e)>>>0,t}for(;e<4;++e)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;if(e=0,this.len-this.pos>4){for(;e<5;++e)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}else for(;e<5;++e){if(this.pos>=this.len)throw a(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function p(t,e){return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0}function d(){if(this.pos+8>this.len)throw a(this,8);return new o(p(this.buf,this.pos+=4),p(this.buf,this.pos+=4))}u.create=f(),u.prototype._slice=i.Array.prototype.subarray||i.Array.prototype.slice,u.prototype.uint32=(c=4294967295,function(){if(c=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return c;if((this.pos+=5)>this.len)throw this.pos=this.len,a(this,10);return c}),u.prototype.int32=function(){return 0|this.uint32()},u.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)},u.prototype.bool=function(){return 0!==this.uint32()},u.prototype.fixed32=function(){if(this.pos+4>this.len)throw a(this,4);return p(this.buf,this.pos+=4)},u.prototype.sfixed32=function(){if(this.pos+4>this.len)throw a(this,4);return 0|p(this.buf,this.pos+=4)},u.prototype.float=function(){if(this.pos+4>this.len)throw a(this,4);var t=i.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},u.prototype.double=function(){if(this.pos+8>this.len)throw a(this,4);var t=i.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},u.prototype.bytes=function(){var t=this.uint32(),e=this.pos,r=this.pos+t;if(r>this.len)throw a(this,t);return this.pos+=t,Array.isArray(this.buf)?this.buf.slice(e,r):e===r?new this.buf.constructor(0):this._slice.call(this.buf,e,r)},u.prototype.string=function(){var t=this.bytes();return s.read(t,0,t.length)},u.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw a(this,t);this.pos+=t}else do{if(this.pos>=this.len)throw a(this)}while(128&this.buf[this.pos++]);return this},u.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(t=7&this.uint32());)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},u._configure=function(t){n=t,u.create=f(),n._configure();var e=i.Long?"toLong":"toNumber";i.merge(u.prototype,{int64:function(){return l.call(this)[e](!1)},uint64:function(){return l.call(this)[e](!0)},sint64:function(){return l.call(this).zzDecode()[e](!1)},fixed64:function(){return d.call(this)[e](!0)},sfixed64:function(){return d.call(this)[e](!1)}})}},33158:(t,e,r)=>{"use strict";t.exports=o;var n=r(16237);(o.prototype=Object.create(n.prototype)).constructor=o;var i=r(93610);function o(t){n.call(this,t)}o._configure=function(){i.Buffer&&(o.prototype._slice=i.Buffer.prototype.slice)},o.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))},o._configure()},64529:t=>{"use strict";t.exports={}},95047:(t,e,r)=>{"use strict";e.Service=r(27595)},27595:(t,e,r)=>{"use strict";t.exports=i;var n=r(93610);function i(t,e,r){if("function"!=typeof t)throw TypeError("rpcImpl must be a function");n.EventEmitter.call(this),this.rpcImpl=t,this.requestDelimited=Boolean(e),this.responseDelimited=Boolean(r)}(i.prototype=Object.create(n.EventEmitter.prototype)).constructor=i,i.prototype.rpcCall=function t(e,r,i,o,s){if(!o)throw TypeError("request must be specified");var a=this;if(!s)return n.asPromise(t,a,e,r,i,o);if(a.rpcImpl)try{return a.rpcImpl(e,r[a.requestDelimited?"encodeDelimited":"encode"](o).finish(),(function(t,r){if(t)return a.emit("error",t,e),s(t);if(null!==r){if(!(r instanceof i))try{r=i[a.responseDelimited?"decodeDelimited":"decode"](r)}catch(t){return a.emit("error",t,e),s(t)}return a.emit("data",r,e),s(null,r)}a.end(!0)}))}catch(t){return a.emit("error",t,e),void setTimeout((function(){s(t)}),0)}else setTimeout((function(){s(Error("already ended"))}),0)},i.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},32239:(t,e,r)=>{"use strict";t.exports=i;var n=r(93610);function i(t,e){this.lo=t>>>0,this.hi=e>>>0}var o=i.zero=new i(0,0);o.toNumber=function(){return 0},o.zzEncode=o.zzDecode=function(){return this},o.length=function(){return 1};var s=i.zeroHash="\0\0\0\0\0\0\0\0";i.fromNumber=function(t){if(0===t)return o;var e=t<0;e&&(t=-t);var r=t>>>0,n=(t-r)/4294967296>>>0;return e&&(n=~n>>>0,r=~r>>>0,++r>4294967295&&(r=0,++n>4294967295&&(n=0))),new i(r,n)},i.from=function(t){if("number"==typeof t)return i.fromNumber(t);if(n.isString(t)){if(!n.Long)return i.fromNumber(parseInt(t,10));t=n.Long.fromString(t)}return t.low||t.high?new i(t.low>>>0,t.high>>>0):o},i.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var e=1+~this.lo>>>0,r=~this.hi>>>0;return e||(r=r+1>>>0),-(e+4294967296*r)}return this.lo+4294967296*this.hi},i.prototype.toLong=function(t){return n.Long?new n.Long(0|this.lo,0|this.hi,Boolean(t)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(t)}};var a=String.prototype.charCodeAt;i.fromHash=function(t){return t===s?o:new i((a.call(t,0)|a.call(t,1)<<8|a.call(t,2)<<16|a.call(t,3)<<24)>>>0,(a.call(t,4)|a.call(t,5)<<8|a.call(t,6)<<16|a.call(t,7)<<24)>>>0)},i.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},i.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},i.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},i.prototype.length=function(){var t=this.lo,e=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===e?t<16384?t<128?1:2:t<2097152?3:4:e<16384?e<128?5:6:e<2097152?7:8:r<128?9:10}},93610:function(t,e,r){"use strict";var n=e;function i(t,e,r){for(var n=Object.keys(e),i=0;i<n.length;++i)void 0!==t[n[i]]&&r||(t[n[i]]=e[n[i]]);return t}function o(t){function e(t,r){if(!(this instanceof e))return new e(t,r);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,e):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),r&&i(this,r)}return(e.prototype=Object.create(Error.prototype)).constructor=e,Object.defineProperty(e.prototype,"name",{get:function(){return t}}),e.prototype.toString=function(){return this.name+": "+this.message},e}n.asPromise=r(18045),n.base64=r(8839),n.EventEmitter=r(24358),n.float=r(49410),n.inquire=r(84153),n.utf8=r(81447),n.pool=r(99390),n.LongBits=r(32239),n.isNode=Boolean(void 0!==r.g&&r.g&&r.g.process&&r.g.process.versions&&r.g.process.versions.node),n.global=n.isNode&&r.g||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,n.emptyArray=Object.freeze?Object.freeze([]):[],n.emptyObject=Object.freeze?Object.freeze({}):{},n.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},n.isString=function(t){return"string"==typeof t||t instanceof String},n.isObject=function(t){return t&&"object"==typeof t},n.isset=n.isSet=function(t,e){var r=t[e];return!(null==r||!t.hasOwnProperty(e))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},n.Buffer=function(){try{var t=n.inquire("buffer").Buffer;return t.prototype.utf8Write?t:null}catch(t){return null}}(),n._Buffer_from=null,n._Buffer_allocUnsafe=null,n.newBuffer=function(t){return"number"==typeof t?n.Buffer?n._Buffer_allocUnsafe(t):new n.Array(t):n.Buffer?n._Buffer_from(t):"undefined"==typeof Uint8Array?t:new Uint8Array(t)},n.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,n.Long=n.global.dcodeIO&&n.global.dcodeIO.Long||n.global.Long||n.inquire("long"),n.key2Re=/^true|false|0|1$/,n.key32Re=/^-?(?:0|[1-9][0-9]*)$/,n.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,n.longToHash=function(t){return t?n.LongBits.from(t).toHash():n.LongBits.zeroHash},n.longFromHash=function(t,e){var r=n.LongBits.fromHash(t);return n.Long?n.Long.fromBits(r.lo,r.hi,e):r.toNumber(Boolean(e))},n.merge=i,n.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)},n.newError=o,n.ProtocolError=o("ProtocolError"),n.oneOfGetter=function(t){for(var e={},r=0;r<t.length;++r)e[t[r]]=1;return function(){for(var t=Object.keys(this),r=t.length-1;r>-1;--r)if(1===e[t[r]]&&void 0!==this[t[r]]&&null!==this[t[r]])return t[r]}},n.oneOfSetter=function(t){return function(e){for(var r=0;r<t.length;++r)t[r]!==e&&delete this[t[r]]}},n.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},n._configure=function(){var t=n.Buffer;t?(n._Buffer_from=t.from!==Uint8Array.from&&t.from||function(e,r){return new t(e,r)},n._Buffer_allocUnsafe=t.allocUnsafe||function(e){return new t(e)}):n._Buffer_from=n._Buffer_allocUnsafe=null}},63449:(t,e,r)=>{"use strict";t.exports=f;var n,i=r(93610),o=i.LongBits,s=i.base64,a=i.utf8;function u(t,e,r){this.fn=t,this.len=e,this.next=void 0,this.val=r}function c(){}function h(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function f(){this.len=0,this.head=new u(c,0,0),this.tail=this.head,this.states=null}var l=function(){return i.Buffer?function(){return(f.create=function(){return new n})()}:function(){return new f}};function p(t,e,r){e[r]=255&t}function d(t,e){this.len=t,this.next=void 0,this.val=e}function y(t,e,r){for(;t.hi;)e[r++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;t.lo>127;)e[r++]=127&t.lo|128,t.lo=t.lo>>>7;e[r++]=t.lo}function g(t,e,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24}f.create=l(),f.alloc=function(t){return new i.Array(t)},i.Array!==Array&&(f.alloc=i.pool(f.alloc,i.Array.prototype.subarray)),f.prototype._push=function(t,e,r){return this.tail=this.tail.next=new u(t,e,r),this.len+=e,this},d.prototype=Object.create(u.prototype),d.prototype.fn=function(t,e,r){for(;t>127;)e[r++]=127&t|128,t>>>=7;e[r]=t},f.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new d((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this},f.prototype.int32=function(t){return t<0?this._push(y,10,o.fromNumber(t)):this.uint32(t)},f.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},f.prototype.uint64=function(t){var e=o.from(t);return this._push(y,e.length(),e)},f.prototype.int64=f.prototype.uint64,f.prototype.sint64=function(t){var e=o.from(t).zzEncode();return this._push(y,e.length(),e)},f.prototype.bool=function(t){return this._push(p,1,t?1:0)},f.prototype.fixed32=function(t){return this._push(g,4,t>>>0)},f.prototype.sfixed32=f.prototype.fixed32,f.prototype.fixed64=function(t){var e=o.from(t);return this._push(g,4,e.lo)._push(g,4,e.hi)},f.prototype.sfixed64=f.prototype.fixed64,f.prototype.float=function(t){return this._push(i.float.writeFloatLE,4,t)},f.prototype.double=function(t){return this._push(i.float.writeDoubleLE,8,t)};var m=i.Array.prototype.set?function(t,e,r){e.set(t,r)}:function(t,e,r){for(var n=0;n<t.length;++n)e[r+n]=t[n]};f.prototype.bytes=function(t){var e=t.length>>>0;if(!e)return this._push(p,1,0);if(i.isString(t)){var r=f.alloc(e=s.length(t));s.decode(t,r,0),t=r}return this.uint32(e)._push(m,e,t)},f.prototype.string=function(t){var e=a.length(t);return e?this.uint32(e)._push(a.write,e,t):this._push(p,1,0)},f.prototype.fork=function(){return this.states=new h(this),this.head=this.tail=new u(c,0,0),this.len=0,this},f.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new u(c,0,0),this.len=0),this},f.prototype.ldelim=function(){var t=this.head,e=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=t.next,this.tail=e,this.len+=r),this},f.prototype.finish=function(){for(var t=this.head.next,e=this.constructor.alloc(this.len),r=0;t;)t.fn(t.val,e,r),r+=t.len,t=t.next;return e},f._configure=function(t){n=t,f.create=l(),n._configure()}},60818:(t,e,r)=>{"use strict";t.exports=o;var n=r(63449);(o.prototype=Object.create(n.prototype)).constructor=o;var i=r(93610);function o(){n.call(this)}function s(t,e,r){t.length<40?i.utf8.write(t,e,r):e.utf8Write?e.utf8Write(t,r):e.write(t,r)}o._configure=function(){o.alloc=i._Buffer_allocUnsafe,o.writeBytesBuffer=i.Buffer&&i.Buffer.prototype instanceof Uint8Array&&"set"===i.Buffer.prototype.set.name?function(t,e,r){e.set(t,r)}:function(t,e,r){if(t.copy)t.copy(e,r,0,t.length);else for(var n=0;n<t.length;)e[r++]=t[n++]}},o.prototype.bytes=function(t){i.isString(t)&&(t=i._Buffer_from(t,"base64"));var e=t.length>>>0;return this.uint32(e),e&&this._push(o.writeBytesBuffer,e,t),this},o.prototype.string=function(t){var e=i.Buffer.byteLength(t);return this.uint32(e),e&&this._push(s,e,t),this},o._configure()},13086:()=>{},14923:()=>{},50310:()=>{},93653:(t,e,r)=>{"use strict";r.d(e,{_Q:()=>h,yE:()=>f});var n=r(5398);class i{constructor(t,e,r){this.name=t,this.prefix=e,this.baseEncode=r}encode(t){if(t instanceof Uint8Array)return`${this.prefix}${this.baseEncode(t)}`;throw Error("Unknown type, must be binary type")}}class o{constructor(t,e,r){if(this.name=t,this.prefix=e,void 0===e.codePointAt(0))throw new Error("Invalid prefix character");this.prefixCodePoint=e.codePointAt(0),this.baseDecode=r}decode(t){if("string"==typeof t){if(t.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(t)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(t.slice(this.prefix.length))}throw Error("Can only multibase decode strings")}or(t){return a(this,t)}}class s{constructor(t){this.decoders=t}or(t){return a(this,t)}decode(t){const e=t[0],r=this.decoders[e];if(r)return r.decode(t);throw RangeError(`Unable to decode multibase string ${JSON.stringify(t)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const a=(t,e)=>new s({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class u{constructor(t,e,r,n){this.name=t,this.prefix=e,this.baseEncode=r,this.baseDecode=n,this.encoder=new i(t,e,r),this.decoder=new o(t,e,n)}encode(t){return this.encoder.encode(t)}decode(t){return this.decoder.decode(t)}}const c=({name:t,prefix:e,encode:r,decode:n})=>new u(t,e,r,n),h=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:o}=function(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var o=t.charAt(i),s=o.charCodeAt(0);if(255!==r[s])throw new TypeError(o+" is ambiguous");r[s]=i}var a=t.length,u=t.charAt(0),c=Math.log(a)/Math.log(256),h=Math.log(256)/Math.log(a);function f(t){if("string"!=typeof t)throw new TypeError("Expected String");if(0===t.length)return new Uint8Array;var e=0;if(" "!==t[e]){for(var n=0,i=0;t[e]===u;)n++,e++;for(var o=(t.length-e)*c+1>>>0,s=new Uint8Array(o);t[e];){var h=r[t.charCodeAt(e)];if(255===h)return;for(var f=0,l=o-1;(0!==h||f<i)&&-1!==l;l--,f++)h+=a*s[l]>>>0,s[l]=h%256>>>0,h=h/256>>>0;if(0!==h)throw new Error("Non-zero carry");i=f,e++}if(" "!==t[e]){for(var p=o-i;p!==o&&0===s[p];)p++;for(var d=new Uint8Array(n+(o-p)),y=n;p!==o;)d[y++]=s[p++];return d}}}return{encode:function(e){if(e instanceof Uint8Array||(ArrayBuffer.isView(e)?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):Array.isArray(e)&&(e=Uint8Array.from(e))),!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===e.length)return"";for(var r=0,n=0,i=0,o=e.length;i!==o&&0===e[i];)i++,r++;for(var s=(o-i)*h+1>>>0,c=new Uint8Array(s);i!==o;){for(var f=e[i],l=0,p=s-1;(0!==f||l<n)&&-1!==p;p--,l++)f+=256*c[p]>>>0,c[p]=f%a>>>0,f=f/a>>>0;if(0!==f)throw new Error("Non-zero carry");n=l,i++}for(var d=s-n;d!==s&&0===c[d];)d++;for(var y=u.repeat(r);d<s;++d)y+=t.charAt(c[d]);return y},decodeUnsafe:f,decode:function(t){var r=f(t);if(r)return r;throw new Error(`Non-${e} character`)}}}(r,e);return c({prefix:t,name:e,encode:i,decode:t=>(0,n.au)(o(t))})},f=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>c({prefix:e,name:t,encode:t=>((t,e,r)=>{const n="="===e[e.length-1],i=(1<<r)-1;let o="",s=0,a=0;for(let n=0;n<t.length;++n)for(a=a<<8|t[n],s+=8;s>r;)s-=r,o+=e[i&a>>s];if(s&&(o+=e[i&a<<r-s]),n)for(;o.length*r&7;)o+="=";return o})(t,n,r),decode:e=>((t,e,r,n)=>{const i={};for(let t=0;t<e.length;++t)i[e[t]]=t;let o=t.length;for(;"="===t[o-1];)--o;const s=new Uint8Array(o*r/8|0);let a=0,u=0,c=0;for(let e=0;e<o;++e){const o=i[t[e]];if(void 0===o)throw new SyntaxError(`Non-${n} character`);u=u<<r|o,a+=r,a>=8&&(a-=8,s[c++]=255&u>>a)}if(a>=r||255&u<<8-a)throw new SyntaxError("Unexpected end of data");return s})(e,n,r,t)})},48894:(t,e,r)=>{"use strict";r.r(e),r.d(e,{base58btc:()=>i,base58flickr:()=>o});var n=r(93653);const i=(0,n._Q)({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),o=(0,n._Q)({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"})},62437:(t,e,r)=>{"use strict";r.r(e),r.d(e,{base64:()=>i,base64pad:()=>o,base64url:()=>s,base64urlpad:()=>a});var n=r(93653);const i=(0,n.yE)({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),o=(0,n.yE)({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),s=(0,n.yE)({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),a=(0,n.yE)({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6})},5398:(t,e,r)=>{"use strict";r.d(e,{au:()=>n}),new Uint8Array(0);const n=t=>{if(t instanceof Uint8Array&&"Uint8Array"===t.constructor.name)return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")}},89562:(t,e,r)=>{"use strict";r.d(e,{vt:()=>v}),r(5398);var n=128,i=-128,o=Math.pow(2,31),s=Math.pow(2,7),a=Math.pow(2,14),u=Math.pow(2,21),c=Math.pow(2,28),h=Math.pow(2,35),f=Math.pow(2,42),l=Math.pow(2,49),p=Math.pow(2,56),d=Math.pow(2,63);const y=function t(e,r,s){r=r||[];for(var a=s=s||0;e>=o;)r[s++]=255&e|n,e/=128;for(;e&i;)r[s++]=255&e|n,e>>>=7;return r[s]=0|e,t.bytes=s-a+1,r},g=function(t){return t<s?1:t<a?2:t<u?3:t<c?4:t<h?5:t<f?6:t<l?7:t<p?8:t<d?9:10},m=(t,e,r=0)=>(y(t,e,r),e),b=t=>g(t),v=(t,e)=>{const r=e.byteLength,n=b(t),i=n+b(r),o=new Uint8Array(i+r);return m(t,o,0),m(r,o,n),o.set(e,i),new w(t,r,e,o)};class w{constructor(t,e,r,n){this.code=t,this.size=e,this.digest=r,this.bytes=n}}},45914:(t,e,r)=>{"use strict";r.r(e),r.d(e,{identity:()=>s});var n=r(5398),i=r(89562);const o=n.au,s={code:0,name:"identity",encode:o,digest:t=>i.vt(0,o(t))}},72190:(t,e,r)=>{"use strict";r.r(e),r.d(e,{sha256:()=>a,sha512:()=>u});var n=r(89562);const i=({name:t,code:e,encode:r})=>new o(t,e,r);class o{constructor(t,e,r){this.name=t,this.code=e,this.encode=r}digest(t){if(t instanceof Uint8Array){const e=this.encode(t);return e instanceof Uint8Array?n.vt(this.code,e):e.then((t=>n.vt(this.code,t)))}throw Error("Unknown type, must be binary type")}}const s=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),a=i({name:"sha2-256",code:18,encode:s("SHA-256")}),u=i({name:"sha2-512",code:19,encode:s("SHA-512")})},14304:(t,e,r)=>{"use strict";r.d(e,{_Q:()=>h,yE:()=>f});var n=r(77487);class i{constructor(t,e,r){this.name=t,this.prefix=e,this.baseEncode=r}encode(t){if(t instanceof Uint8Array)return`${this.prefix}${this.baseEncode(t)}`;throw Error("Unknown type, must be binary type")}}class o{constructor(t,e,r){if(this.name=t,this.prefix=e,void 0===e.codePointAt(0))throw new Error("Invalid prefix character");this.prefixCodePoint=e.codePointAt(0),this.baseDecode=r}decode(t){if("string"==typeof t){if(t.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(t)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(t.slice(this.prefix.length))}throw Error("Can only multibase decode strings")}or(t){return a(this,t)}}class s{constructor(t){this.decoders=t}or(t){return a(this,t)}decode(t){const e=t[0],r=this.decoders[e];if(r)return r.decode(t);throw RangeError(`Unable to decode multibase string ${JSON.stringify(t)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const a=(t,e)=>new s({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class u{constructor(t,e,r,n){this.name=t,this.prefix=e,this.baseEncode=r,this.baseDecode=n,this.encoder=new i(t,e,r),this.decoder=new o(t,e,n)}encode(t){return this.encoder.encode(t)}decode(t){return this.decoder.decode(t)}}const c=({name:t,prefix:e,encode:r,decode:n})=>new u(t,e,r,n),h=({prefix:t,name:e,alphabet:r})=>{const{encode:i,decode:o}=function(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var o=t.charAt(i),s=o.charCodeAt(0);if(255!==r[s])throw new TypeError(o+" is ambiguous");r[s]=i}var a=t.length,u=t.charAt(0),c=Math.log(a)/Math.log(256),h=Math.log(256)/Math.log(a);function f(t){if("string"!=typeof t)throw new TypeError("Expected String");if(0===t.length)return new Uint8Array;var e=0;if(" "!==t[e]){for(var n=0,i=0;t[e]===u;)n++,e++;for(var o=(t.length-e)*c+1>>>0,s=new Uint8Array(o);t[e];){var h=r[t.charCodeAt(e)];if(255===h)return;for(var f=0,l=o-1;(0!==h||f<i)&&-1!==l;l--,f++)h+=a*s[l]>>>0,s[l]=h%256>>>0,h=h/256>>>0;if(0!==h)throw new Error("Non-zero carry");i=f,e++}if(" "!==t[e]){for(var p=o-i;p!==o&&0===s[p];)p++;for(var d=new Uint8Array(n+(o-p)),y=n;p!==o;)d[y++]=s[p++];return d}}}return{encode:function(e){if(e instanceof Uint8Array||(ArrayBuffer.isView(e)?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):Array.isArray(e)&&(e=Uint8Array.from(e))),!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===e.length)return"";for(var r=0,n=0,i=0,o=e.length;i!==o&&0===e[i];)i++,r++;for(var s=(o-i)*h+1>>>0,c=new Uint8Array(s);i!==o;){for(var f=e[i],l=0,p=s-1;(0!==f||l<n)&&-1!==p;p--,l++)f+=256*c[p]>>>0,c[p]=f%a>>>0,f=f/a>>>0;if(0!==f)throw new Error("Non-zero carry");n=l,i++}for(var d=s-n;d!==s&&0===c[d];)d++;for(var y=u.repeat(r);d<s;++d)y+=t.charAt(c[d]);return y},decodeUnsafe:f,decode:function(t){var r=f(t);if(r)return r;throw new Error(`Non-${e} character`)}}}(r,e);return c({prefix:t,name:e,encode:i,decode:t=>(0,n.au)(o(t))})},f=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>c({prefix:e,name:t,encode:t=>((t,e,r)=>{const n="="===e[e.length-1],i=(1<<r)-1;let o="",s=0,a=0;for(let n=0;n<t.length;++n)for(a=a<<8|t[n],s+=8;s>r;)s-=r,o+=e[i&a>>s];if(s&&(o+=e[i&a<<r-s]),n)for(;o.length*r&7;)o+="=";return o})(t,n,r),decode:e=>((t,e,r,n)=>{const i={};for(let t=0;t<e.length;++t)i[e[t]]=t;let o=t.length;for(;"="===t[o-1];)--o;const s=new Uint8Array(o*r/8|0);let a=0,u=0,c=0;for(let e=0;e<o;++e){const o=i[t[e]];if(void 0===o)throw new SyntaxError(`Non-${n} character`);u=u<<r|o,a+=r,a>=8&&(a-=8,s[c++]=255&u>>a)}if(a>=r||255&u<<8-a)throw new SyntaxError("Unexpected end of data");return s})(e,n,r,t)})},58151:(t,e,r)=>{"use strict";r.r(e),r.d(e,{base16:()=>i,base16upper:()=>o});var n=r(14304);const i=(0,n.yE)({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),o=(0,n.yE)({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4})},93009:(t,e,r)=>{"use strict";r.r(e),r.d(e,{base32:()=>i,base32hex:()=>u,base32hexpad:()=>h,base32hexpadupper:()=>f,base32hexupper:()=>c,base32pad:()=>s,base32padupper:()=>a,base32upper:()=>o,base32z:()=>l});var n=r(14304);const i=(0,n.yE)({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),o=(0,n.yE)({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),s=(0,n.yE)({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),a=(0,n.yE)({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),u=(0,n.yE)({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),c=(0,n.yE)({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),h=(0,n.yE)({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),f=(0,n.yE)({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),l=(0,n.yE)({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5})},70573:(t,e,r)=>{"use strict";r.r(e),r.d(e,{base36:()=>i,base36upper:()=>o});var n=r(14304);const i=(0,n._Q)({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),o=(0,n._Q)({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"})},32257:(t,e,r)=>{"use strict";r.r(e),r.d(e,{base58btc:()=>i,base58flickr:()=>o});var n=r(14304);const i=(0,n._Q)({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),o=(0,n._Q)({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"})},96794:(t,e,r)=>{"use strict";r.r(e),r.d(e,{base64:()=>i,base64pad:()=>o,base64url:()=>s,base64urlpad:()=>a});var n=r(14304);const i=(0,n.yE)({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),o=(0,n.yE)({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),s=(0,n.yE)({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),a=(0,n.yE)({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6})},77487:(t,e,r)=>{"use strict";r.d(e,{aI:()=>n,au:()=>i}),new Uint8Array(0);const n=(t,e)=>{if(t===e)return!0;if(t.byteLength!==e.byteLength)return!1;for(let r=0;r<t.byteLength;r++)if(t[r]!==e[r])return!1;return!0},i=t=>{if(t instanceof Uint8Array&&"Uint8Array"===t.constructor.name)return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")}},94944:(t,e,r)=>{"use strict";r.r(e),r.d(e,{CID:()=>u});var n=r(81662),i=r(56677),o=r(32257),s=r(93009),a=r(77487);class u{constructor(t,e,r,n){this.code=e,this.version=t,this.multihash=r,this.bytes=n,this.byteOffset=n.byteOffset,this.byteLength=n.byteLength,this.asCID=this,this._baseCache=new Map,Object.defineProperties(this,{byteOffset:m,byteLength:m,code:g,version:g,multihash:g,bytes:g,_baseCache:m,asCID:m})}toV0(){if(0===this.version)return this;{const{code:t,multihash:e}=this;if(t!==l)throw new Error("Cannot convert a non dag-pb CID to CIDv0");if(e.code!==p)throw new Error("Cannot convert non sha2-256 multihash CID to CIDv0");return u.createV0(e)}}toV1(){switch(this.version){case 0:{const{code:t,digest:e}=this.multihash,r=i.create(t,e);return u.createV1(this.code,r)}case 1:return this;default:throw Error(`Can not convert CID version ${this.version} to version 0. This is a bug please report`)}}equals(t){return t&&this.code===t.code&&this.version===t.version&&i.equals(this.multihash,t.multihash)}toString(t){const{bytes:e,version:r,_baseCache:n}=this;return 0===r?h(e,n,t||o.base58btc.encoder):f(e,n,t||s.base32.encoder)}toJSON(){return{code:this.code,version:this.version,hash:this.multihash.bytes}}get[Symbol.toStringTag](){return"CID"}[Symbol.for("nodejs.util.inspect.custom")](){return"CID("+this.toString()+")"}static isCID(t){return b(/^0\.0/,v),!(!t||!t[y]&&t.asCID!==t)}get toBaseEncodedString(){throw new Error("Deprecated, use .toString()")}get codec(){throw new Error('"codec" property is deprecated, use integer "code" property instead')}get buffer(){throw new Error("Deprecated .buffer property, use .bytes to get Uint8Array instead")}get multibaseName(){throw new Error('"multibaseName" property is deprecated')}get prefix(){throw new Error('"prefix" property is deprecated')}static asCID(t){if(t instanceof u)return t;if(null!=t&&t.asCID===t){const{version:e,code:r,multihash:n,bytes:i}=t;return new u(e,r,n,i||d(e,r,n.bytes))}if(null!=t&&!0===t[y]){const{version:e,multihash:r,code:n}=t,o=i.decode(r);return u.create(e,n,o)}return null}static create(t,e,r){if("number"!=typeof e)throw new Error("String codecs are no longer supported");switch(t){case 0:if(e!==l)throw new Error(`Version 0 CID must use dag-pb (code: ${l}) block encoding`);return new u(t,e,r,r.bytes);case 1:{const n=d(t,e,r.bytes);return new u(t,e,r,n)}default:throw new Error("Invalid version")}}static createV0(t){return u.create(0,l,t)}static createV1(t,e){return u.create(1,t,e)}static decode(t){const[e,r]=u.decodeFirst(t);if(r.length)throw new Error("Incorrect length");return e}static decodeFirst(t){const e=u.inspectBytes(t),r=e.size-e.multihashSize,n=(0,a.au)(t.subarray(r,r+e.multihashSize));if(n.byteLength!==e.multihashSize)throw new Error("Incorrect length");const o=n.subarray(e.multihashSize-e.digestSize),s=new i.Digest(e.multihashCode,e.digestSize,o,n);return[0===e.version?u.createV0(s):u.createV1(e.codec,s),t.subarray(e.size)]}static inspectBytes(t){let e=0;const r=()=>{const[r,i]=n.D4(t.subarray(e));return e+=i,r};let i=r(),o=l;if(18===i?(i=0,e=0):1===i&&(o=r()),0!==i&&1!==i)throw new RangeError(`Invalid CID version ${i}`);const s=e,a=r(),u=r(),c=e+u;return{version:i,codec:o,multihashCode:a,digestSize:u,multihashSize:c-s,size:c}}static parse(t,e){const[r,n]=c(t,e),i=u.decode(n);return i._baseCache.set(r,t),i}}const c=(t,e)=>{switch(t[0]){case"Q":{const r=e||o.base58btc;return[o.base58btc.prefix,r.decode(`${o.base58btc.prefix}${t}`)]}case o.base58btc.prefix:{const r=e||o.base58btc;return[o.base58btc.prefix,r.decode(t)]}case s.base32.prefix:{const r=e||s.base32;return[s.base32.prefix,r.decode(t)]}default:if(null==e)throw Error("To parse non base32 or base58btc encoded CID multibase decoder must be provided");return[t[0],e.decode(t)]}},h=(t,e,r)=>{const{prefix:n}=r;if(n!==o.base58btc.prefix)throw Error(`Cannot string encode V0 in ${r.name} encoding`);const i=e.get(n);if(null==i){const i=r.encode(t).slice(1);return e.set(n,i),i}return i},f=(t,e,r)=>{const{prefix:n}=r,i=e.get(n);if(null==i){const i=r.encode(t);return e.set(n,i),i}return i},l=112,p=18,d=(t,e,r)=>{const i=n.OY(t),o=i+n.OY(e),s=new Uint8Array(o+r.byteLength);return n.s_(t,s,0),n.s_(e,s,i),s.set(r,o),s},y=Symbol.for("@ipld/js-cid/CID"),g={writable:!1,configurable:!1,enumerable:!0},m={writable:!1,enumerable:!1,configurable:!1},b=(t,e)=>{if(!t.test("0.0.0-dev"))throw new Error(e);console.warn(e)},v="CID.isCID(v) is deprecated and will be removed in the next major release.\nFollowing code pattern:\n\nif (CID.isCID(value)) {\n  doSomethingWithCID(value)\n}\n\nIs replaced with:\n\nconst cid = CID.asCID(value)\nif (cid) {\n  // Make sure to use cid instead of value\n  doSomethingWithCID(cid)\n}\n"},56677:(t,e,r)=>{"use strict";r.r(e),r.d(e,{Digest:()=>u,create:()=>o,decode:()=>s,equals:()=>a});var n=r(77487),i=r(81662);const o=(t,e)=>{const r=e.byteLength,n=i.OY(t),o=n+i.OY(r),s=new Uint8Array(o+r);return i.s_(t,s,0),i.s_(r,s,n),s.set(e,o),new u(t,r,e,s)},s=t=>{const e=(0,n.au)(t),[r,o]=i.D4(e),[s,a]=i.D4(e.subarray(o)),c=e.subarray(o+a);if(c.byteLength!==s)throw new Error("Incorrect length");return new u(r,s,c,e)},a=(t,e)=>t===e||t.code===e.code&&t.size===e.size&&(0,n.aI)(t.bytes,e.bytes);class u{constructor(t,e,r,n){this.code=t,this.size=e,this.digest=r,this.bytes=n}}},24255:(t,e,r)=>{"use strict";r.r(e),r.d(e,{identity:()=>s});var n=r(77487),i=r(56677);const o=n.au,s={code:0,name:"identity",encode:o,digest:t=>i.create(0,o(t))}},81662:(t,e,r)=>{"use strict";r.d(e,{D4:()=>b,s_:()=>v,OY:()=>w});var n=128,i=-128,o=Math.pow(2,31),s=128,a=127,u=Math.pow(2,7),c=Math.pow(2,14),h=Math.pow(2,21),f=Math.pow(2,28),l=Math.pow(2,35),p=Math.pow(2,42),d=Math.pow(2,49),y=Math.pow(2,56),g=Math.pow(2,63);const m={encode:function t(e,r,s){r=r||[];for(var a=s=s||0;e>=o;)r[s++]=255&e|n,e/=128;for(;e&i;)r[s++]=255&e|n,e>>>=7;return r[s]=0|e,t.bytes=s-a+1,r},decode:function t(e,r){var n,i=0,o=0,u=r=r||0,c=e.length;do{if(u>=c)throw t.bytes=0,new RangeError("Could not decode varint");n=e[u++],i+=o<28?(n&a)<<o:(n&a)*Math.pow(2,o),o+=7}while(n>=s);return t.bytes=u-r,i},encodingLength:function(t){return t<u?1:t<c?2:t<h?3:t<f?4:t<l?5:t<p?6:t<d?7:t<y?8:t<g?9:10}},b=(t,e=0)=>[m.decode(t,e),m.decode.bytes],v=(t,e,r=0)=>(m.encode(t,e,r),e),w=t=>m.encodingLength(t)},45238:(t,e,r)=>{"use strict";r.d(e,{K:()=>i});var n=r(75930);function i(t=0){return null!=globalThis.Buffer&&null!=globalThis.Buffer.allocUnsafe?(0,n.o)(globalThis.Buffer.allocUnsafe(t)):new Uint8Array(t)}},75007:(t,e,r)=>{"use strict";r.r(e),r.d(e,{concat:()=>o});var n=r(45238),i=r(75930);function o(t,e){e||(e=t.reduce(((t,e)=>t+e.length),0));const r=(0,n.K)(e);let o=0;for(const e of t)r.set(e,o),o+=e.length;return(0,i.o)(r)}},18402:(t,e,r)=>{"use strict";function n(t,e){if(t===e)return!0;if(t.byteLength!==e.byteLength)return!1;for(let r=0;r<t.byteLength;r++)if(t[r]!==e[r])return!1;return!0}r.r(e),r.d(e,{equals:()=>n})},44117:(t,e,r)=>{"use strict";r.r(e),r.d(e,{fromString:()=>o});var n=r(55821),i=r(75930);function o(t,e="utf8"){const r=n.A[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return"utf8"!==e&&"utf-8"!==e||null==globalThis.Buffer||null==globalThis.Buffer.from?r.decoder.decode(`${r.prefix}${t}`):(0,i.o)(globalThis.Buffer.from(t,"utf-8"))}},27302:(t,e,r)=>{"use strict";r.r(e),r.d(e,{toString:()=>i});var n=r(55821);function i(t,e="utf8"){const r=n.A[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return"utf8"!==e&&"utf-8"!==e||null==globalThis.Buffer||null==globalThis.Buffer.from?r.encoder.encode(t).substring(1):globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8")}},75930:(t,e,r)=>{"use strict";function n(t){return null!=globalThis.Buffer?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t}r.d(e,{o:()=>n})},55821:(t,e,r)=>{"use strict";r.d(e,{A:()=>jt});var n={};r.r(n),r.d(n,{identity:()=>I});var i={};r.r(i),r.d(i,{base2:()=>k});var o={};r.r(o),r.d(o,{base8:()=>x});var s={};r.r(s),r.d(s,{base10:()=>R});var a={};r.r(a),r.d(a,{base16:()=>_,base16upper:()=>P});var u={};r.r(u),r.d(u,{base32:()=>U,base32hex:()=>O,base32hexpad:()=>M,base32hexpadupper:()=>j,base32hexupper:()=>K,base32pad:()=>N,base32padupper:()=>D,base32upper:()=>L,base32z:()=>V});var c={};r.r(c),r.d(c,{base36:()=>z,base36upper:()=>F});var h={};r.r(h),r.d(h,{base58btc:()=>q,base58flickr:()=>H});var f={};r.r(f),r.d(f,{base64:()=>G,base64pad:()=>W,base64url:()=>$,base64urlpad:()=>Q});var l={};r.r(l),r.d(l,{base256emoji:()=>X});var p={};r.r(p),r.d(p,{sha256:()=>Et,sha512:()=>St});var d={};r.r(d),r.d(d,{identity:()=>At});var y={};r.r(y),r.d(y,{code:()=>Tt,decode:()=>kt,encode:()=>It,name:()=>Ct});var g={};r.r(g),r.d(g,{code:()=>Pt,decode:()=>Lt,encode:()=>Ut,name:()=>_t});const m=function(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var o=t.charAt(i),s=o.charCodeAt(0);if(255!==r[s])throw new TypeError(o+" is ambiguous");r[s]=i}var a=t.length,u=t.charAt(0),c=Math.log(a)/Math.log(256),h=Math.log(256)/Math.log(a);function f(t){if("string"!=typeof t)throw new TypeError("Expected String");if(0===t.length)return new Uint8Array;var e=0;if(" "!==t[e]){for(var n=0,i=0;t[e]===u;)n++,e++;for(var o=(t.length-e)*c+1>>>0,s=new Uint8Array(o);t[e];){var h=r[t.charCodeAt(e)];if(255===h)return;for(var f=0,l=o-1;(0!==h||f<i)&&-1!==l;l--,f++)h+=a*s[l]>>>0,s[l]=h%256>>>0,h=h/256>>>0;if(0!==h)throw new Error("Non-zero carry");i=f,e++}if(" "!==t[e]){for(var p=o-i;p!==o&&0===s[p];)p++;for(var d=new Uint8Array(n+(o-p)),y=n;p!==o;)d[y++]=s[p++];return d}}}return{encode:function(e){if(e instanceof Uint8Array||(ArrayBuffer.isView(e)?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):Array.isArray(e)&&(e=Uint8Array.from(e))),!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(0===e.length)return"";for(var r=0,n=0,i=0,o=e.length;i!==o&&0===e[i];)i++,r++;for(var s=(o-i)*h+1>>>0,c=new Uint8Array(s);i!==o;){for(var f=e[i],l=0,p=s-1;(0!==f||l<n)&&-1!==p;p--,l++)f+=256*c[p]>>>0,c[p]=f%a>>>0,f=f/a>>>0;if(0!==f)throw new Error("Non-zero carry");n=l,i++}for(var d=s-n;d!==s&&0===c[d];)d++;for(var y=u.repeat(r);d<s;++d)y+=t.charAt(c[d]);return y},decodeUnsafe:f,decode:function(t){var r=f(t);if(r)return r;throw new Error(`Non-${e} character`)}}},b=(new Uint8Array(0),t=>{if(t instanceof Uint8Array&&"Uint8Array"===t.constructor.name)return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")});class v{constructor(t,e,r){this.name=t,this.prefix=e,this.baseEncode=r}encode(t){if(t instanceof Uint8Array)return`${this.prefix}${this.baseEncode(t)}`;throw Error("Unknown type, must be binary type")}}class w{constructor(t,e,r){if(this.name=t,this.prefix=e,void 0===e.codePointAt(0))throw new Error("Invalid prefix character");this.prefixCodePoint=e.codePointAt(0),this.baseDecode=r}decode(t){if("string"==typeof t){if(t.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(t)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(t.slice(this.prefix.length))}throw Error("Can only multibase decode strings")}or(t){return S(this,t)}}class E{constructor(t){this.decoders=t}or(t){return S(this,t)}decode(t){const e=t[0],r=this.decoders[e];if(r)return r.decode(t);throw RangeError(`Unable to decode multibase string ${JSON.stringify(t)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const S=(t,e)=>new E({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class B{constructor(t,e,r,n){this.name=t,this.prefix=e,this.baseEncode=r,this.baseDecode=n,this.encoder=new v(t,e,r),this.decoder=new w(t,e,n)}encode(t){return this.encoder.encode(t)}decode(t){return this.decoder.decode(t)}}const A=({name:t,prefix:e,encode:r,decode:n})=>new B(t,e,r,n),C=({prefix:t,name:e,alphabet:r})=>{const{encode:n,decode:i}=m(r,e);return A({prefix:t,name:e,encode:n,decode:t=>b(i(t))})},T=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>A({prefix:e,name:t,encode:t=>((t,e,r)=>{const n="="===e[e.length-1],i=(1<<r)-1;let o="",s=0,a=0;for(let n=0;n<t.length;++n)for(a=a<<8|t[n],s+=8;s>r;)s-=r,o+=e[i&a>>s];if(s&&(o+=e[i&a<<r-s]),n)for(;o.length*r&7;)o+="=";return o})(t,n,r),decode:e=>((t,e,r,n)=>{const i={};for(let t=0;t<e.length;++t)i[e[t]]=t;let o=t.length;for(;"="===t[o-1];)--o;const s=new Uint8Array(o*r/8|0);let a=0,u=0,c=0;for(let e=0;e<o;++e){const o=i[t[e]];if(void 0===o)throw new SyntaxError(`Non-${n} character`);u=u<<r|o,a+=r,a>=8&&(a-=8,s[c++]=255&u>>a)}if(a>=r||255&u<<8-a)throw new SyntaxError("Unexpected end of data");return s})(e,n,r,t)}),I=A({prefix:"\0",name:"identity",encode:t=>{return e=t,(new TextDecoder).decode(e);var e},decode:t=>(t=>(new TextEncoder).encode(t))(t)}),k=T({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1}),x=T({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3}),R=C({prefix:"9",name:"base10",alphabet:"0123456789"}),_=T({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),P=T({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4}),U=T({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),L=T({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),N=T({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),D=T({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),O=T({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),K=T({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),M=T({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),j=T({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),V=T({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5}),z=C({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),F=C({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"}),q=C({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),H=C({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"}),G=T({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),W=T({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),$=T({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),Q=T({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6}),Z=Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂"),Y=Z.reduce(((t,e,r)=>(t[r]=e,t)),[]),J=Z.reduce(((t,e,r)=>(t[e.codePointAt(0)]=r,t)),[]),X=A({prefix:"🚀",name:"base256emoji",encode:function(t){return t.reduce(((t,e)=>t+Y[e]),"")},decode:function(t){const e=[];for(const r of t){const t=J[r.codePointAt(0)];if(void 0===t)throw new Error(`Non-base256emoji character: ${r}`);e.push(t)}return new Uint8Array(e)}});var tt=128,et=-128,rt=Math.pow(2,31),nt=Math.pow(2,7),it=Math.pow(2,14),ot=Math.pow(2,21),st=Math.pow(2,28),at=Math.pow(2,35),ut=Math.pow(2,42),ct=Math.pow(2,49),ht=Math.pow(2,56),ft=Math.pow(2,63);const lt=function t(e,r,n){r=r||[];for(var i=n=n||0;e>=rt;)r[n++]=255&e|tt,e/=128;for(;e&et;)r[n++]=255&e|tt,e>>>=7;return r[n]=0|e,t.bytes=n-i+1,r},pt=function(t){return t<nt?1:t<it?2:t<ot?3:t<st?4:t<at?5:t<ut?6:t<ct?7:t<ht?8:t<ft?9:10},dt=(t,e,r=0)=>(lt(t,e,r),e),yt=t=>pt(t),gt=(t,e)=>{const r=e.byteLength,n=yt(t),i=n+yt(r),o=new Uint8Array(i+r);return dt(t,o,0),dt(r,o,n),o.set(e,i),new mt(t,r,e,o)};class mt{constructor(t,e,r,n){this.code=t,this.size=e,this.digest=r,this.bytes=n}}const bt=({name:t,code:e,encode:r})=>new vt(t,e,r);class vt{constructor(t,e,r){this.name=t,this.code=e,this.encode=r}digest(t){if(t instanceof Uint8Array){const e=this.encode(t);return e instanceof Uint8Array?gt(this.code,e):e.then((t=>gt(this.code,t)))}throw Error("Unknown type, must be binary type")}}const wt=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),Et=bt({name:"sha2-256",code:18,encode:wt("SHA-256")}),St=bt({name:"sha2-512",code:19,encode:wt("SHA-512")}),Bt=b,At={code:0,name:"identity",encode:Bt,digest:t=>gt(0,Bt(t))},Ct="raw",Tt=85,It=t=>b(t),kt=t=>b(t),xt=new TextEncoder,Rt=new TextDecoder,_t="json",Pt=512,Ut=t=>xt.encode(JSON.stringify(t)),Lt=t=>JSON.parse(Rt.decode(t));Symbol.toStringTag,Symbol.for("nodejs.util.inspect.custom"),Symbol.for("@ipld/js-cid/CID");const Nt={...n,...i,...o,...s,...a,...u,...c,...h,...f,...l};var Dt=r(45238);function Ot(t,e,r,n){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:n}}}const Kt=Ot("utf8","u",(t=>"u"+new TextDecoder("utf8").decode(t)),(t=>(new TextEncoder).encode(t.substring(1)))),Mt=Ot("ascii","a",(t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}),(t=>{t=t.substring(1);const e=(0,Dt.K)(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e})),jt={utf8:Kt,"utf-8":Kt,hex:Nt.base16,latin1:Mt,ascii:Mt,binary:Mt,...Nt}}},__webpack_module_cache__={};function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var r=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t].call(r.exports,r,r.exports,__webpack_require__),r.exports}__webpack_require__.d=(t,e)=>{for(var r in e)__webpack_require__.o(e,r)&&!__webpack_require__.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),__webpack_require__.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),__webpack_require__.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var __webpack_exports__={};(()=>{"use strict";var t={};function e(t,e){return function(){return t.apply(e,arguments)}}__webpack_require__.r(t),__webpack_require__.d(t,{hasBrowserEnv:()=>it,hasStandardBrowserEnv:()=>ot,hasStandardBrowserWebWorkerEnv:()=>at,origin:()=>ut});const{toString:r}=Object.prototype,{getPrototypeOf:n}=Object,i=(o=Object.create(null),t=>{const e=r.call(t);return o[e]||(o[e]=e.slice(8,-1).toLowerCase())});var o;const s=t=>(t=t.toLowerCase(),e=>i(e)===t),a=t=>e=>typeof e===t,{isArray:u}=Array,c=a("undefined"),h=s("ArrayBuffer"),f=a("string"),l=a("function"),p=a("number"),d=t=>null!==t&&"object"==typeof t,y=t=>{if("object"!==i(t))return!1;const e=n(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||Symbol.toStringTag in t||Symbol.iterator in t)},g=s("Date"),m=s("File"),b=s("Blob"),v=s("FileList"),w=s("URLSearchParams"),[E,S,B,A]=["ReadableStream","Request","Response","Headers"].map(s);function C(t,e,{allOwnKeys:r=!1}={}){if(null==t)return;let n,i;if("object"!=typeof t&&(t=[t]),u(t))for(n=0,i=t.length;n<i;n++)e.call(null,t[n],n,t);else{const i=r?Object.getOwnPropertyNames(t):Object.keys(t),o=i.length;let s;for(n=0;n<o;n++)s=i[n],e.call(null,t[s],s,t)}}function T(t,e){e=e.toLowerCase();const r=Object.keys(t);let n,i=r.length;for(;i-- >0;)if(n=r[i],e===n.toLowerCase())return n;return null}const I="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,k=t=>!c(t)&&t!==I,x=(R="undefined"!=typeof Uint8Array&&n(Uint8Array),t=>R&&t instanceof R);var R;const _=s("HTMLFormElement"),P=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),U=s("RegExp"),L=(t,e)=>{const r=Object.getOwnPropertyDescriptors(t),n={};C(r,((r,i)=>{let o;!1!==(o=e(r,i,t))&&(n[i]=o||r)})),Object.defineProperties(t,n)},N="abcdefghijklmnopqrstuvwxyz",D="0123456789",O={DIGIT:D,ALPHA:N,ALPHA_DIGIT:N+N.toUpperCase()+D},K=s("AsyncFunction"),M={isArray:u,isArrayBuffer:h,isBuffer:function(t){return null!==t&&!c(t)&&null!==t.constructor&&!c(t.constructor)&&l(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||l(t.append)&&("formdata"===(e=i(t))||"object"===e&&l(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&h(t.buffer),e},isString:f,isNumber:p,isBoolean:t=>!0===t||!1===t,isObject:d,isPlainObject:y,isReadableStream:E,isRequest:S,isResponse:B,isHeaders:A,isUndefined:c,isDate:g,isFile:m,isBlob:b,isRegExp:U,isFunction:l,isStream:t=>d(t)&&l(t.pipe),isURLSearchParams:w,isTypedArray:x,isFileList:v,forEach:C,merge:function t(){const{caseless:e}=k(this)&&this||{},r={},n=(n,i)=>{const o=e&&T(r,i)||i;y(r[o])&&y(n)?r[o]=t(r[o],n):y(n)?r[o]=t({},n):u(n)?r[o]=n.slice():r[o]=n};for(let t=0,e=arguments.length;t<e;t++)arguments[t]&&C(arguments[t],n);return r},extend:(t,r,n,{allOwnKeys:i}={})=>(C(r,((r,i)=>{n&&l(r)?t[i]=e(r,n):t[i]=r}),{allOwnKeys:i}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&Object.assign(t.prototype,r)},toFlatObject:(t,e,r,i)=>{let o,s,a;const u={};if(e=e||{},null==t)return e;do{for(o=Object.getOwnPropertyNames(t),s=o.length;s-- >0;)a=o[s],i&&!i(a,t,e)||u[a]||(e[a]=t[a],u[a]=!0);t=!1!==r&&n(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},kindOf:i,kindOfTest:s,endsWith:(t,e,r)=>{t=String(t),(void 0===r||r>t.length)&&(r=t.length),r-=e.length;const n=t.indexOf(e,r);return-1!==n&&n===r},toArray:t=>{if(!t)return null;if(u(t))return t;let e=t.length;if(!p(e))return null;const r=new Array(e);for(;e-- >0;)r[e]=t[e];return r},forEachEntry:(t,e)=>{const r=(t&&t[Symbol.iterator]).call(t);let n;for(;(n=r.next())&&!n.done;){const r=n.value;e.call(t,r[0],r[1])}},matchAll:(t,e)=>{let r;const n=[];for(;null!==(r=t.exec(e));)n.push(r);return n},isHTMLForm:_,hasOwnProperty:P,hasOwnProp:P,reduceDescriptors:L,freezeMethods:t=>{L(t,((e,r)=>{if(l(t)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;const n=t[r];l(n)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")}))}))},toObjectSet:(t,e)=>{const r={},n=t=>{t.forEach((t=>{r[t]=!0}))};return u(t)?n(t):n(String(t).split(e)),r},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(t,e,r){return e.toUpperCase()+r})),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:T,global:I,isContextDefined:k,ALPHABET:O,generateString:(t=16,e=O.ALPHA_DIGIT)=>{let r="";const{length:n}=e;for(;t--;)r+=e[Math.random()*n|0];return r},isSpecCompliantForm:function(t){return!!(t&&l(t.append)&&"FormData"===t[Symbol.toStringTag]&&t[Symbol.iterator])},toJSONObject:t=>{const e=new Array(10),r=(t,n)=>{if(d(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[n]=t;const i=u(t)?[]:{};return C(t,((t,e)=>{const o=r(t,n+1);!c(o)&&(i[e]=o)})),e[n]=void 0,i}}return t};return r(t,0)},isAsyncFn:K,isThenable:t=>t&&(d(t)||l(t))&&l(t.then)&&l(t.catch)};function j(t,e,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i)}M.inherits(j,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:M.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const V=j.prototype,z={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((t=>{z[t]={value:t}})),Object.defineProperties(j,z),Object.defineProperty(V,"isAxiosError",{value:!0}),j.from=(t,e,r,n,i,o)=>{const s=Object.create(V);return M.toFlatObject(t,s,(function(t){return t!==Error.prototype}),(t=>"isAxiosError"!==t)),j.call(s,t.message,e,r,n,i),s.cause=t,s.name=t.name,o&&Object.assign(s,o),s};const F=j;function q(t){return M.isPlainObject(t)||M.isArray(t)}function H(t){return M.endsWith(t,"[]")?t.slice(0,-2):t}function G(t,e,r){return t?t.concat(e).map((function(t,e){return t=H(t),!r&&e?"["+t+"]":t})).join(r?".":""):e}const W=M.toFlatObject(M,{},null,(function(t){return/^is[A-Z]/.test(t)})),$=function(t,e,r){if(!M.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const n=(r=M.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(t,e){return!M.isUndefined(e[t])}))).metaTokens,i=r.visitor||c,o=r.dots,s=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&M.isSpecCompliantForm(e);if(!M.isFunction(i))throw new TypeError("visitor must be a function");function u(t){if(null===t)return"";if(M.isDate(t))return t.toISOString();if(!a&&M.isBlob(t))throw new F("Blob is not supported. Use a Buffer instead.");return M.isArrayBuffer(t)||M.isTypedArray(t)?a&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function c(t,r,i){let a=t;if(t&&!i&&"object"==typeof t)if(M.endsWith(r,"{}"))r=n?r:r.slice(0,-2),t=JSON.stringify(t);else if(M.isArray(t)&&function(t){return M.isArray(t)&&!t.some(q)}(t)||(M.isFileList(t)||M.endsWith(r,"[]"))&&(a=M.toArray(t)))return r=H(r),a.forEach((function(t,n){!M.isUndefined(t)&&null!==t&&e.append(!0===s?G([r],n,o):null===s?r:r+"[]",u(t))})),!1;return!!q(t)||(e.append(G(i,r,o),u(t)),!1)}const h=[],f=Object.assign(W,{defaultVisitor:c,convertValue:u,isVisitable:q});if(!M.isObject(t))throw new TypeError("data must be an object");return function t(r,n){if(!M.isUndefined(r)){if(-1!==h.indexOf(r))throw Error("Circular reference detected in "+n.join("."));h.push(r),M.forEach(r,(function(r,o){!0===(!(M.isUndefined(r)||null===r)&&i.call(e,r,M.isString(o)?o.trim():o,n,f))&&t(r,n?n.concat(o):[o])})),h.pop()}}(t),e};function Q(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,(function(t){return e[t]}))}function Z(t,e){this._pairs=[],t&&$(t,this,e)}const Y=Z.prototype;Y.append=function(t,e){this._pairs.push([t,e])},Y.toString=function(t){const e=t?function(e){return t.call(this,e,Q)}:Q;return this._pairs.map((function(t){return e(t[0])+"="+e(t[1])}),"").join("&")};const J=Z;function X(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function tt(t,e,r){if(!e)return t;const n=r&&r.encode||X,i=r&&r.serialize;let o;if(o=i?i(e,r):M.isURLSearchParams(e)?e.toString():new J(e,r).toString(n),o){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+o}return t}const et=class{constructor(){this.handlers=[]}use(t,e,r){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){M.forEach(this.handlers,(function(e){null!==e&&t(e)}))}},rt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},nt={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:J,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},it="undefined"!=typeof window&&"undefined"!=typeof document,ot=(st="undefined"!=typeof navigator&&navigator.product,it&&["ReactNative","NativeScript","NS"].indexOf(st)<0);var st;const at="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ut=it&&window.location.href||"http://localhost",ct={...t,...nt},ht=function(t){function e(t,r,n,i){let o=t[i++];if("__proto__"===o)return!0;const s=Number.isFinite(+o),a=i>=t.length;return o=!o&&M.isArray(n)?n.length:o,a?(M.hasOwnProp(n,o)?n[o]=[n[o],r]:n[o]=r,!s):(n[o]&&M.isObject(n[o])||(n[o]=[]),e(t,r,n[o],i)&&M.isArray(n[o])&&(n[o]=function(t){const e={},r=Object.keys(t);let n;const i=r.length;let o;for(n=0;n<i;n++)o=r[n],e[o]=t[o];return e}(n[o])),!s)}if(M.isFormData(t)&&M.isFunction(t.entries)){const r={};return M.forEachEntry(t,((t,n)=>{e(function(t){return M.matchAll(/\w+|\[(\w*)]/g,t).map((t=>"[]"===t[0]?"":t[1]||t[0]))}(t),n,r,0)})),r}return null},ft={transitional:rt,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const r=e.getContentType()||"",n=r.indexOf("application/json")>-1,i=M.isObject(t);if(i&&M.isHTMLForm(t)&&(t=new FormData(t)),M.isFormData(t))return n?JSON.stringify(ht(t)):t;if(M.isArrayBuffer(t)||M.isBuffer(t)||M.isStream(t)||M.isFile(t)||M.isBlob(t)||M.isReadableStream(t))return t;if(M.isArrayBufferView(t))return t.buffer;if(M.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return $(t,new ct.classes.URLSearchParams,Object.assign({visitor:function(t,e,r,n){return ct.isNode&&M.isBuffer(t)?(this.append(e,t.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((o=M.isFileList(t))||r.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return $(o?{"files[]":t}:t,e&&new e,this.formSerializer)}}return i||n?(e.setContentType("application/json",!1),function(t){if(M.isString(t))try{return(0,JSON.parse)(t),M.trim(t)}catch(t){if("SyntaxError"!==t.name)throw t}return(0,JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||ft.transitional,r=e&&e.forcedJSONParsing,n="json"===this.responseType;if(M.isResponse(t)||M.isReadableStream(t))return t;if(t&&M.isString(t)&&(r&&!this.responseType||n)){const r=!(e&&e.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(t){if(r){if("SyntaxError"===t.name)throw F.from(t,F.ERR_BAD_RESPONSE,this,null,this.response);throw t}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ct.classes.FormData,Blob:ct.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};M.forEach(["delete","get","head","post","put","patch"],(t=>{ft.headers[t]={}}));const lt=ft,pt=M.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),dt=Symbol("internals");function yt(t){return t&&String(t).trim().toLowerCase()}function gt(t){return!1===t||null==t?t:M.isArray(t)?t.map(gt):String(t)}function mt(t,e,r,n,i){return M.isFunction(n)?n.call(this,e,r):(i&&(e=r),M.isString(e)?M.isString(n)?-1!==e.indexOf(n):M.isRegExp(n)?n.test(e):void 0:void 0)}class bt{constructor(t){t&&this.set(t)}set(t,e,r){const n=this;function i(t,e,r){const i=yt(e);if(!i)throw new Error("header name must be a non-empty string");const o=M.findKey(n,i);(!o||void 0===n[o]||!0===r||void 0===r&&!1!==n[o])&&(n[o||e]=gt(t))}const o=(t,e)=>M.forEach(t,((t,r)=>i(t,r,e)));if(M.isPlainObject(t)||t instanceof this.constructor)o(t,e);else if(M.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim()))o((t=>{const e={};let r,n,i;return t&&t.split("\n").forEach((function(t){i=t.indexOf(":"),r=t.substring(0,i).trim().toLowerCase(),n=t.substring(i+1).trim(),!r||e[r]&&pt[r]||("set-cookie"===r?e[r]?e[r].push(n):e[r]=[n]:e[r]=e[r]?e[r]+", "+n:n)})),e})(t),e);else if(M.isHeaders(t))for(const[e,n]of t.entries())i(n,e,r);else null!=t&&i(e,t,r);return this}get(t,e){if(t=yt(t)){const r=M.findKey(this,t);if(r){const t=this[r];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(t);)e[n[1]]=n[2];return e}(t);if(M.isFunction(e))return e.call(this,t,r);if(M.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=yt(t)){const r=M.findKey(this,t);return!(!r||void 0===this[r]||e&&!mt(0,this[r],r,e))}return!1}delete(t,e){const r=this;let n=!1;function i(t){if(t=yt(t)){const i=M.findKey(r,t);!i||e&&!mt(0,r[i],i,e)||(delete r[i],n=!0)}}return M.isArray(t)?t.forEach(i):i(t),n}clear(t){const e=Object.keys(this);let r=e.length,n=!1;for(;r--;){const i=e[r];t&&!mt(0,this[i],i,t,!0)||(delete this[i],n=!0)}return n}normalize(t){const e=this,r={};return M.forEach(this,((n,i)=>{const o=M.findKey(r,i);if(o)return e[o]=gt(n),void delete e[i];const s=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((t,e,r)=>e.toUpperCase()+r))}(i):String(i).trim();s!==i&&delete e[i],e[s]=gt(n),r[s]=!0})),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return M.forEach(this,((r,n)=>{null!=r&&!1!==r&&(e[n]=t&&M.isArray(r)?r.join(", "):r)})),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([t,e])=>t+": "+e)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const r=new this(t);return e.forEach((t=>r.set(t))),r}static accessor(t){const e=(this[dt]=this[dt]={accessors:{}}).accessors,r=this.prototype;function n(t){const n=yt(t);e[n]||(function(t,e){const r=M.toCamelCase(" "+e);["get","set","has"].forEach((n=>{Object.defineProperty(t,n+r,{value:function(t,r,i){return this[n].call(this,e,t,r,i)},configurable:!0})}))}(r,t),e[n]=!0)}return M.isArray(t)?t.forEach(n):n(t),this}}bt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),M.reduceDescriptors(bt.prototype,(({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[r]=t}}})),M.freezeMethods(bt);const vt=bt;function wt(t,e){const r=this||lt,n=e||r,i=vt.from(n.headers);let o=n.data;return M.forEach(t,(function(t){o=t.call(r,o,i.normalize(),e?e.status:void 0)})),i.normalize(),o}function Et(t){return!(!t||!t.__CANCEL__)}function St(t,e,r){F.call(this,null==t?"canceled":t,F.ERR_CANCELED,e,r),this.name="CanceledError"}M.inherits(St,F,{__CANCEL__:!0});const Bt=St;function At(t,e,r){const n=r.config.validateStatus;r.status&&n&&!n(r.status)?e(new F("Request failed with status code "+r.status,[F.ERR_BAD_REQUEST,F.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r)):t(r)}const Ct=(t,e,r=3)=>{let n=0;const i=function(t,e){t=t||10;const r=new Array(t),n=new Array(t);let i,o=0,s=0;return e=void 0!==e?e:1e3,function(a){const u=Date.now(),c=n[s];i||(i=u),r[o]=a,n[o]=u;let h=s,f=0;for(;h!==o;)f+=r[h++],h%=t;if(o=(o+1)%t,o===s&&(s=(s+1)%t),u-i<e)return;const l=c&&u-c;return l?Math.round(1e3*f/l):void 0}}(50,250);return function(t,e){let r=0;const n=1e3/e;let i=null;return function(){const e=!0===this,o=Date.now();if(e||o-r>n)return i&&(clearTimeout(i),i=null),r=o,t.apply(null,arguments);i||(i=setTimeout((()=>(i=null,r=Date.now(),t.apply(null,arguments))),n-(o-r)))}}((r=>{const o=r.loaded,s=r.lengthComputable?r.total:void 0,a=o-n,u=i(a);n=o;const c={loaded:o,total:s,progress:s?o/s:void 0,bytes:a,rate:u||void 0,estimated:u&&s&&o<=s?(s-o)/u:void 0,event:r,lengthComputable:null!=s};c[e?"download":"upload"]=!0,t(c)}),r)},Tt=ct.hasStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),e=document.createElement("a");let r;function n(r){let n=r;return t&&(e.setAttribute("href",n),n=e.href),e.setAttribute("href",n),{href:e.href,protocol:e.protocol?e.protocol.replace(/:$/,""):"",host:e.host,search:e.search?e.search.replace(/^\?/,""):"",hash:e.hash?e.hash.replace(/^#/,""):"",hostname:e.hostname,port:e.port,pathname:"/"===e.pathname.charAt(0)?e.pathname:"/"+e.pathname}}return r=n(window.location.href),function(t){const e=M.isString(t)?n(t):t;return e.protocol===r.protocol&&e.host===r.host}}():function(){return!0},It=ct.hasStandardBrowserEnv?{write(t,e,r,n,i,o){const s=[t+"="+encodeURIComponent(e)];M.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),M.isString(n)&&s.push("path="+n),M.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function kt(t,e){return t&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const xt=t=>t instanceof vt?{...t}:t;function Rt(t,e){e=e||{};const r={};function n(t,e,r){return M.isPlainObject(t)&&M.isPlainObject(e)?M.merge.call({caseless:r},t,e):M.isPlainObject(e)?M.merge({},e):M.isArray(e)?e.slice():e}function i(t,e,r){return M.isUndefined(e)?M.isUndefined(t)?void 0:n(void 0,t,r):n(t,e,r)}function o(t,e){if(!M.isUndefined(e))return n(void 0,e)}function s(t,e){return M.isUndefined(e)?M.isUndefined(t)?void 0:n(void 0,t):n(void 0,e)}function a(r,i,o){return o in e?n(r,i):o in t?n(void 0,r):void 0}const u={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(t,e)=>i(xt(t),xt(e),!0)};return M.forEach(Object.keys(Object.assign({},t,e)),(function(n){const o=u[n]||i,s=o(t[n],e[n],n);M.isUndefined(s)&&o!==a||(r[n]=s)})),r}const _t=t=>{const e=Rt({},t);let r,{data:n,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:a,auth:u}=e;if(e.headers=a=vt.from(a),e.url=tt(kt(e.baseURL,e.url),t.params,t.paramsSerializer),u&&a.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),M.isFormData(n))if(ct.hasStandardBrowserEnv||ct.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(r=a.getContentType())){const[t,...e]=r?r.split(";").map((t=>t.trim())).filter(Boolean):[];a.setContentType([t||"multipart/form-data",...e].join("; "))}if(ct.hasStandardBrowserEnv&&(i&&M.isFunction(i)&&(i=i(e)),i||!1!==i&&Tt(e.url))){const t=o&&s&&It.read(s);t&&a.set(o,t)}return e},Pt="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise((function(e,r){const n=_t(t);let i=n.data;const o=vt.from(n.headers).normalize();let s,{responseType:a}=n;function u(){n.cancelToken&&n.cancelToken.unsubscribe(s),n.signal&&n.signal.removeEventListener("abort",s)}let c=new XMLHttpRequest;function h(){if(!c)return;const n=vt.from("getAllResponseHeaders"in c&&c.getAllResponseHeaders());At((function(t){e(t),u()}),(function(t){r(t),u()}),{data:a&&"text"!==a&&"json"!==a?c.response:c.responseText,status:c.status,statusText:c.statusText,headers:n,config:t,request:c}),c=null}c.open(n.method.toUpperCase(),n.url,!0),c.timeout=n.timeout,"onloadend"in c?c.onloadend=h:c.onreadystatechange=function(){c&&4===c.readyState&&(0!==c.status||c.responseURL&&0===c.responseURL.indexOf("file:"))&&setTimeout(h)},c.onabort=function(){c&&(r(new F("Request aborted",F.ECONNABORTED,n,c)),c=null)},c.onerror=function(){r(new F("Network Error",F.ERR_NETWORK,n,c)),c=null},c.ontimeout=function(){let t=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const e=n.transitional||rt;n.timeoutErrorMessage&&(t=n.timeoutErrorMessage),r(new F(t,e.clarifyTimeoutError?F.ETIMEDOUT:F.ECONNABORTED,n,c)),c=null},void 0===i&&o.setContentType(null),"setRequestHeader"in c&&M.forEach(o.toJSON(),(function(t,e){c.setRequestHeader(e,t)})),M.isUndefined(n.withCredentials)||(c.withCredentials=!!n.withCredentials),a&&"json"!==a&&(c.responseType=n.responseType),"function"==typeof n.onDownloadProgress&&c.addEventListener("progress",Ct(n.onDownloadProgress,!0)),"function"==typeof n.onUploadProgress&&c.upload&&c.upload.addEventListener("progress",Ct(n.onUploadProgress)),(n.cancelToken||n.signal)&&(s=e=>{c&&(r(!e||e.type?new Bt(null,t,c):e),c.abort(),c=null)},n.cancelToken&&n.cancelToken.subscribe(s),n.signal&&(n.signal.aborted?s():n.signal.addEventListener("abort",s)));const f=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(n.url);f&&-1===ct.protocols.indexOf(f)?r(new F("Unsupported protocol "+f+":",F.ERR_BAD_REQUEST,t)):c.send(i||null)}))},Ut=(t,e)=>{let r,n=new AbortController;const i=function(t){if(!r){r=!0,s();const e=t instanceof Error?t:this.reason;n.abort(e instanceof F?e:new Bt(e instanceof Error?e.message:e))}};let o=e&&setTimeout((()=>{i(new F(`timeout ${e} of ms exceeded`,F.ETIMEDOUT))}),e);const s=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach((t=>{t&&(t.removeEventListener?t.removeEventListener("abort",i):t.unsubscribe(i))})),t=null)};t.forEach((t=>t&&t.addEventListener&&t.addEventListener("abort",i)));const{signal:a}=n;return a.unsubscribe=s,[a,()=>{o&&clearTimeout(o),o=null}]},Lt=function*(t,e){let r=t.byteLength;if(!e||r<e)return void(yield t);let n,i=0;for(;i<r;)n=i+e,yield t.slice(i,n),i=n},Nt=(t,e,r,n,i)=>{const o=async function*(t,e,r){for await(const n of t)yield*Lt(ArrayBuffer.isView(n)?n:await r(String(n)),e)}(t,e,i);let s=0;return new ReadableStream({type:"bytes",async pull(t){const{done:e,value:i}=await o.next();if(e)return t.close(),void n();let a=i.byteLength;r&&r(s+=a),t.enqueue(new Uint8Array(i))},cancel:t=>(n(t),o.return())},{highWaterMark:2})},Dt=(t,e)=>{const r=null!=t;return n=>setTimeout((()=>e({lengthComputable:r,total:t,loaded:n})))},Ot="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Kt=Ot&&"function"==typeof ReadableStream,Mt=Ot&&("function"==typeof TextEncoder?(jt=new TextEncoder,t=>jt.encode(t)):async t=>new Uint8Array(await new Response(t).arrayBuffer()));var jt;const Vt=Kt&&(()=>{let t=!1;const e=new Request(ct.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e})(),zt=Kt&&!!(()=>{try{return M.isReadableStream(new Response("").body)}catch(t){}})(),Ft={stream:zt&&(t=>t.body)};var qt;Ot&&(qt=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((t=>{!Ft[t]&&(Ft[t]=M.isFunction(qt[t])?e=>e[t]():(e,r)=>{throw new F(`Response type '${t}' is not supported`,F.ERR_NOT_SUPPORT,r)})})));const Ht={http:null,xhr:Pt,fetch:Ot&&(async t=>{let{url:e,method:r,data:n,signal:i,cancelToken:o,timeout:s,onDownloadProgress:a,onUploadProgress:u,responseType:c,headers:h,withCredentials:f="same-origin",fetchOptions:l}=_t(t);c=c?(c+"").toLowerCase():"text";let p,d,[y,g]=i||o||s?Ut([i,o],s):[];const m=()=>{!p&&setTimeout((()=>{y&&y.unsubscribe()})),p=!0};let b;try{if(u&&Vt&&"get"!==r&&"head"!==r&&0!==(b=await(async(t,e)=>{const r=M.toFiniteNumber(t.getContentLength());return null==r?(async t=>null==t?0:M.isBlob(t)?t.size:M.isSpecCompliantForm(t)?(await new Request(t).arrayBuffer()).byteLength:M.isArrayBufferView(t)?t.byteLength:(M.isURLSearchParams(t)&&(t+=""),M.isString(t)?(await Mt(t)).byteLength:void 0))(e):r})(h,n))){let t,r=new Request(e,{method:"POST",body:n,duplex:"half"});M.isFormData(n)&&(t=r.headers.get("content-type"))&&h.setContentType(t),r.body&&(n=Nt(r.body,65536,Dt(b,Ct(u)),null,Mt))}M.isString(f)||(f=f?"cors":"omit"),d=new Request(e,{...l,signal:y,method:r.toUpperCase(),headers:h.normalize().toJSON(),body:n,duplex:"half",withCredentials:f});let i=await fetch(d);const o=zt&&("stream"===c||"response"===c);if(zt&&(a||o)){const t={};["status","statusText","headers"].forEach((e=>{t[e]=i[e]}));const e=M.toFiniteNumber(i.headers.get("content-length"));i=new Response(Nt(i.body,65536,a&&Dt(e,Ct(a,!0)),o&&m,Mt),t)}c=c||"text";let s=await Ft[M.findKey(Ft,c)||"text"](i,t);return!o&&m(),g&&g(),await new Promise(((e,r)=>{At(e,r,{data:s,headers:vt.from(i.headers),status:i.status,statusText:i.statusText,config:t,request:d})}))}catch(e){if(m(),e&&"TypeError"===e.name&&/fetch/i.test(e.message))throw Object.assign(new F("Network Error",F.ERR_NETWORK,t,d),{cause:e.cause||e});throw F.from(e,e&&e.code,t,d)}})};M.forEach(Ht,((t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(t){}Object.defineProperty(t,"adapterName",{value:e})}}));const Gt=t=>`- ${t}`,Wt=t=>M.isFunction(t)||null===t||!1===t,$t=t=>{t=M.isArray(t)?t:[t];const{length:e}=t;let r,n;const i={};for(let o=0;o<e;o++){let e;if(r=t[o],n=r,!Wt(r)&&(n=Ht[(e=String(r)).toLowerCase()],void 0===n))throw new F(`Unknown adapter '${e}'`);if(n)break;i[e||"#"+o]=n}if(!n){const t=Object.entries(i).map((([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build")));let r=e?t.length>1?"since :\n"+t.map(Gt).join("\n"):" "+Gt(t[0]):"as no adapter specified";throw new F("There is no suitable adapter to dispatch the request "+r,"ERR_NOT_SUPPORT")}return n};function Qt(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Bt(null,t)}function Zt(t){return Qt(t),t.headers=vt.from(t.headers),t.data=wt.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1),$t(t.adapter||lt.adapter)(t).then((function(e){return Qt(t),e.data=wt.call(t,t.transformResponse,e),e.headers=vt.from(e.headers),e}),(function(e){return Et(e)||(Qt(t),e&&e.response&&(e.response.data=wt.call(t,t.transformResponse,e.response),e.response.headers=vt.from(e.response.headers))),Promise.reject(e)}))}const Yt={};["object","boolean","number","function","string","symbol"].forEach(((t,e)=>{Yt[t]=function(r){return typeof r===t||"a"+(e<1?"n ":" ")+t}}));const Jt={};Yt.transitional=function(t,e,r){function n(t,e){return"[Axios v1.7.2] Transitional option '"+t+"'"+e+(r?". "+r:"")}return(r,i,o)=>{if(!1===t)throw new F(n(i," has been removed"+(e?" in "+e:"")),F.ERR_DEPRECATED);return e&&!Jt[i]&&(Jt[i]=!0,console.warn(n(i," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(r,i,o)}};const Xt={assertOptions:function(t,e,r){if("object"!=typeof t)throw new F("options must be an object",F.ERR_BAD_OPTION_VALUE);const n=Object.keys(t);let i=n.length;for(;i-- >0;){const o=n[i],s=e[o];if(s){const e=t[o],r=void 0===e||s(e,o,t);if(!0!==r)throw new F("option "+o+" must be "+r,F.ERR_BAD_OPTION_VALUE)}else if(!0!==r)throw new F("Unknown option "+o,F.ERR_BAD_OPTION)}},validators:Yt},te=Xt.validators;class ee{constructor(t){this.defaults=t,this.interceptors={request:new et,response:new et}}async request(t,e){try{return await this._request(t,e)}catch(t){if(t instanceof Error){let e;Error.captureStackTrace?Error.captureStackTrace(e={}):e=new Error;const r=e.stack?e.stack.replace(/^.+\n/,""):"";try{t.stack?r&&!String(t.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(t.stack+="\n"+r):t.stack=r}catch(t){}}throw t}}_request(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=Rt(this.defaults,e);const{transitional:r,paramsSerializer:n,headers:i}=e;void 0!==r&&Xt.assertOptions(r,{silentJSONParsing:te.transitional(te.boolean),forcedJSONParsing:te.transitional(te.boolean),clarifyTimeoutError:te.transitional(te.boolean)},!1),null!=n&&(M.isFunction(n)?e.paramsSerializer={serialize:n}:Xt.assertOptions(n,{encode:te.function,serialize:te.function},!0)),e.method=(e.method||this.defaults.method||"get").toLowerCase();let o=i&&M.merge(i.common,i[e.method]);i&&M.forEach(["delete","get","head","post","put","patch","common"],(t=>{delete i[t]})),e.headers=vt.concat(o,i);const s=[];let a=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(a=a&&t.synchronous,s.unshift(t.fulfilled,t.rejected))}));const u=[];let c;this.interceptors.response.forEach((function(t){u.push(t.fulfilled,t.rejected)}));let h,f=0;if(!a){const t=[Zt.bind(this),void 0];for(t.unshift.apply(t,s),t.push.apply(t,u),h=t.length,c=Promise.resolve(e);f<h;)c=c.then(t[f++],t[f++]);return c}h=s.length;let l=e;for(f=0;f<h;){const t=s[f++],e=s[f++];try{l=t(l)}catch(t){e.call(this,t);break}}try{c=Zt.call(this,l)}catch(t){return Promise.reject(t)}for(f=0,h=u.length;f<h;)c=c.then(u[f++],u[f++]);return c}getUri(t){return tt(kt((t=Rt(this.defaults,t)).baseURL,t.url),t.params,t.paramsSerializer)}}M.forEach(["delete","get","head","options"],(function(t){ee.prototype[t]=function(e,r){return this.request(Rt(r||{},{method:t,url:e,data:(r||{}).data}))}})),M.forEach(["post","put","patch"],(function(t){function e(e){return function(r,n,i){return this.request(Rt(i||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}ee.prototype[t]=e(),ee.prototype[t+"Form"]=e(!0)}));const re=ee;class ne{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise((function(t){e=t}));const r=this;this.promise.then((t=>{if(!r._listeners)return;let e=r._listeners.length;for(;e-- >0;)r._listeners[e](t);r._listeners=null})),this.promise.then=t=>{let e;const n=new Promise((t=>{r.subscribe(t),e=t})).then(t);return n.cancel=function(){r.unsubscribe(e)},n},t((function(t,n,i){r.reason||(r.reason=new Bt(t,n,i),e(r.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}static source(){let t;return{token:new ne((function(e){t=e})),cancel:t}}}const ie=ne,oe={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(oe).forEach((([t,e])=>{oe[e]=t}));const se=oe,ae=function t(r){const n=new re(r),i=e(re.prototype.request,n);return M.extend(i,re.prototype,n,{allOwnKeys:!0}),M.extend(i,n,null,{allOwnKeys:!0}),i.create=function(e){return t(Rt(r,e))},i}(lt);ae.Axios=re,ae.CanceledError=Bt,ae.CancelToken=ie,ae.isCancel=Et,ae.VERSION="1.7.2",ae.toFormData=$,ae.AxiosError=F,ae.Cancel=ae.CanceledError,ae.all=function(t){return Promise.all(t)},ae.spread=function(t){return function(e){return t.apply(null,e)}},ae.isAxiosError=function(t){return M.isObject(t)&&!0===t.isAxiosError},ae.mergeConfig=Rt,ae.AxiosHeaders=vt,ae.formToJSON=t=>ht(M.isHTMLForm(t)?new FormData(t):t),ae.getAdapter=$t,ae.HttpStatusCode=se,ae.default=ae;const ue=ae;__webpack_require__(50216);var ce=__webpack_require__(48287);function he(t,e){var r="".concat(t).concat(e);return ce.Buffer.from(r).toString("base64")}function fe(t){return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fe(t)}function le(){le=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof m?e:m,s=Object.create(o.prototype),a=new R(n||[]);return i(s,"_invoke",{value:T(t,r,a)}),s}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var l="suspendedStart",p="suspendedYield",d="executing",y="completed",g={};function m(){}function b(){}function v(){}var w={};c(w,s,(function(){return this}));var E=Object.getPrototypeOf,S=E&&E(E(_([])));S&&S!==r&&n.call(S,s)&&(w=S);var B=v.prototype=m.prototype=Object.create(w);function A(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(i,o,s,a){var u=f(t[i],t,o);if("throw"!==u.type){var c=u.arg,h=c.value;return h&&"object"==fe(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){r("next",t,s,a)}),(function(t){r("throw",t,s,a)})):e.resolve(h).then((function(t){c.value=t,s(c)}),(function(t){return r("throw",t,s,a)}))}a(u.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return o=o?o.then(i,i):i()}})}function T(e,r,n){var i=l;return function(o,s){if(i===d)throw Error("Generator is already running");if(i===y){if("throw"===o)throw s;return{value:t,done:!0}}for(n.method=o,n.arg=s;;){var a=n.delegate;if(a){var u=I(a,n);if(u){if(u===g)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===l)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=d;var c=f(e,r,n);if("normal"===c.type){if(i=n.done?y:p,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=y,n.method="throw",n.arg=c.arg)}}}function I(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,I(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=f(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var s=o.arg;return s?s.done?(r[e.resultName]=s.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function R(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function _(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(fe(e)+" is not iterable")}return b.prototype=v,i(B,"constructor",{value:v,configurable:!0}),i(v,"constructor",{value:b,configurable:!0}),b.displayName=c(v,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,c(t,u,"GeneratorFunction")),t.prototype=Object.create(B),t},e.awrap=function(t){return{__await:t}},A(C.prototype),c(C.prototype,a,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var s=new C(h(t,r,n,i),o);return e.isGeneratorFunction(r)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},A(B),c(B,u,"Generator"),c(B,s,(function(){return this})),c(B,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=_,R.prototype={constructor:R,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return a.type="throw",a.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var u=n.call(s,"catchLoc"),c=n.call(s,"finallyLoc");if(u&&c){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(u){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=t,s.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),x(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;x(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:_(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function pe(t,e,r,n,i,o,s){try{var a=t[o](s),u=a.value}catch(t){return void r(t)}a.done?e(u):Promise.resolve(u).then(n,i)}function de(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function s(t){pe(o,n,i,s,a,"next",t)}function a(t){pe(o,n,i,s,a,"throw",t)}s(void 0)}))}}function ye(){var t=localStorage.getItem("credentials");return t?JSON.parse(t):null}function ge(){return me.apply(this,arguments)}function me(){return(me=de(le().mark((function t(){var e,r,n,i,o,s,a,u,c,h;return le().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,e=ye()){t.next=4;break}throw new Error("Missing saved credentials. Please go back and sign in again.");case 4:return r=e.password,n=e.signatureData,console.log({password:r,signatureData:n}),i=he(n,r),console.log({seed:i}),t.next=10,ue.post("/api/peer/generate-identity",{seed:i});case 10:return o=t.sent,s=o.data,a=s.peer_id,u=s.seed,console.log("Generated Peer ID:",a),document.getElementById("new-peer-id-text").textContent=a,t.next=16,be(a,u);case 16:c=t.sent,console.log("Blox Peer ID:",c),document.getElementById("new-blox-peer-id-text").textContent=c,localStorage.setItem("bloxPeerId",c),localStorage.setItem("appPeerId",a),(h=document.getElementById("next-button")).disabled=!1,h.classList.remove("disabled"),t.next=30;break;case 26:t.prev=26,t.t0=t.catch(0),console.error("Error:",t.t0),alert("Error: ".concat(t.t0.message));case 30:case"end":return t.stop()}}),t,null,[[0,26]])})))).apply(this,arguments)}function be(t,e){return ve.apply(this,arguments)}function ve(){return(ve=de(le().mark((function t(e,r){var n,i;return le().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,ue.post("/api/peer/exchange",{PeerID:e,seed:r});case 3:if(n=t.sent,i=n.data.peer_id.match(/12D\w+/)){t.next=7;break}throw new Error("Peer ID format is incorrect");case 7:return t.abrupt("return",i[0]);case 10:throw t.prev=10,t.t0=t.catch(0),new Error("Failed to exchange config: ".concat(t.t0.message));case 13:case"end":return t.stop()}}),t,null,[[0,10]])})))).apply(this,arguments)}function we(){return Ee.apply(this,arguments)}function Ee(){return(Ee=de(le().mark((function t(){return le().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:try{localStorage.setItem("authorizer_set","true"),window.location.href="/webui/pools"}catch(t){console.error("Error:",t),alert("Error: ".concat(t.message))}case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}document.addEventListener("DOMContentLoaded",(function(){var t=document.getElementById("next-button"),e=document.getElementById("set-authorizer-button");if(function(){var t=localStorage.getItem("appPeerId"),e=localStorage.getItem("bloxPeerId");return t&&e}()){e.disabled=!0,e.classList.add("disabled"),t.disabled=!1,t.classList.remove("disabled");var r=localStorage.getItem("appPeerId"),n=localStorage.getItem("bloxPeerId");document.getElementById("new-peer-id-text").textContent=r,document.getElementById("new-blox-peer-id-text").textContent=n}else t.disabled=!0,t.classList.add("disabled");e.addEventListener("click",ge),t.addEventListener("click",we)}))})()})();