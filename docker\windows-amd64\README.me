# Build

```
cd server
npm run build
npm run make
```

Then open `Inno Setup Compiler` and compile the `setup.iss`.

## Sign
```
"%PROGRAMFILES(X86)%\Windows Kits\10\bin\x64"\signTool sign /fd SHA512 /a /v /f cert/functionland.pfx /p "password" /tr http://timestamp.digicert.com "D:\Github\fula-ota\docker\windows-amd64\FulaSetup.exe"
```

## silent mode
```
FulaSetup.exe /SILENT
```
or
```
FulaSetup.exe /VERYSILENT
```

