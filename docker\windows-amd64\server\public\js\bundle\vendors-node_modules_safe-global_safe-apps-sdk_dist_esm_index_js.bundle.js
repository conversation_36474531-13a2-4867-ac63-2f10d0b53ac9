"use strict";
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkfula_webui"] = self["webpackChunkfula_webui"] || []).push([["vendors-node_modules_safe-global_safe-apps-sdk_dist_esm_index_js"],{

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_BASE_URL = void 0;\nexports.DEFAULT_BASE_URL = 'https://safe-client.safe.global';\n//# sourceMappingURL=config.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getEndpoint = exports.deleteEndpoint = exports.putEndpoint = exports.postEndpoint = void 0;\nconst utils_1 = __webpack_require__(/*! ./utils */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\");\nfunction makeUrl(baseUrl, path, pathParams, query) {\n    const pathname = (0, utils_1.insertParams)(path, pathParams);\n    const search = (0, utils_1.stringifyQuery)(query);\n    return `${baseUrl}${pathname}${search}`;\n}\nfunction postEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'POST', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nexports.postEndpoint = postEndpoint;\nfunction putEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'PUT', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nexports.putEndpoint = putEndpoint;\nfunction deleteEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'DELETE', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nexports.deleteEndpoint = deleteEndpoint;\nfunction getEndpoint(baseUrl, path, params, rawUrl) {\n    if (rawUrl) {\n        return (0, utils_1.getData)(rawUrl, undefined, params === null || params === void 0 ? void 0 : params.credentials);\n    }\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.getData)(url, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nexports.getEndpoint = getEndpoint;\n//# sourceMappingURL=endpoint.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.deleteAccount = exports.getAccount = exports.createAccount = exports.verifyAuth = exports.getAuthNonce = exports.getContract = exports.getSafeOverviews = exports.unsubscribeAll = exports.unsubscribeSingle = exports.registerRecoveryModule = exports.deleteRegisteredEmail = exports.getRegisteredEmail = exports.verifyEmail = exports.resendEmailVerificationCode = exports.changeEmail = exports.registerEmail = exports.unregisterDevice = exports.unregisterSafe = exports.registerDevice = exports.getDelegates = exports.confirmSafeMessage = exports.proposeSafeMessage = exports.getSafeMessage = exports.getSafeMessages = exports.getDecodedData = exports.getMasterCopies = exports.getSafeApps = exports.getChainConfig = exports.getChainsConfig = exports.getConfirmationView = exports.proposeTransaction = exports.getNonces = exports.postSafeGasEstimation = exports.deleteTransaction = exports.getTransactionDetails = exports.getTransactionQueue = exports.getTransactionHistory = exports.getCollectiblesPage = exports.getCollectibles = exports.getAllOwnedSafes = exports.getOwnedSafes = exports.getFiatCurrencies = exports.getBalances = exports.getMultisigTransactions = exports.getModuleTransactions = exports.getIncomingTransfers = exports.getSafeInfo = exports.getRelayCount = exports.relayTransaction = exports.setBaseUrl = void 0;\nexports.putAccountDataSettings = exports.getAccountDataSettings = exports.getAccountDataTypes = void 0;\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\");\nconst config_1 = __webpack_require__(/*! ./config */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\");\n__exportStar(__webpack_require__(/*! ./types/safe-info */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-apps */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/transactions */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/chains */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/common */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/master-copies */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/decoded-data */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-messages */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/notifications */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/relay */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\"), exports);\n// Can be set externally to a different CGW host\nlet baseUrl = config_1.DEFAULT_BASE_URL;\n/**\n * Set the base CGW URL\n */\nconst setBaseUrl = (url) => {\n    baseUrl = url;\n};\nexports.setBaseUrl = setBaseUrl;\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Relay a transaction from a Safe\n */\nfunction relayTransaction(chainId, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/relay', { path: { chainId }, body });\n}\nexports.relayTransaction = relayTransaction;\n/**\n * Get the relay limit and number of remaining relays remaining\n */\nfunction getRelayCount(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/relay/{address}', { path: { chainId, address } });\n}\nexports.getRelayCount = getRelayCount;\n/**\n * Get basic information about a Safe. E.g. owners, modules, version etc\n */\nfunction getSafeInfo(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}', { path: { chainId, address } });\n}\nexports.getSafeInfo = getSafeInfo;\n/**\n * Get filterable list of incoming transactions\n */\nfunction getIncomingTransfers(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/incoming-transfers/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\nexports.getIncomingTransfers = getIncomingTransfers;\n/**\n * Get filterable list of module transactions\n */\nfunction getModuleTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/module-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\nexports.getModuleTransactions = getModuleTransactions;\n/**\n * Get filterable list of multisig transactions\n */\nfunction getMultisigTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/multisig-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\nexports.getMultisigTransactions = getMultisigTransactions;\n/**\n * Get the total balance and all assets stored in a Safe\n */\nfunction getBalances(chainId, address, currency = 'usd', query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/balances/{currency}', {\n        path: { chainId, address, currency },\n        query,\n    });\n}\nexports.getBalances = getBalances;\n/**\n * Get a list of supported fiat currencies (e.g. USD, EUR etc)\n */\nfunction getFiatCurrencies() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/balances/supported-fiat-codes');\n}\nexports.getFiatCurrencies = getFiatCurrencies;\n/**\n * Get the addresses of all Safes belonging to an owner\n */\nfunction getOwnedSafes(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/owners/{address}/safes', { path: { chainId, address } });\n}\nexports.getOwnedSafes = getOwnedSafes;\n/**\n * Get the addresses of all Safes belonging to an owner on all chains\n */\nfunction getAllOwnedSafes(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/owners/{address}/safes', { path: { address } });\n}\nexports.getAllOwnedSafes = getAllOwnedSafes;\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectibles(chainId, address, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/collectibles', {\n        path: { chainId, address },\n        query,\n    });\n}\nexports.getCollectibles = getCollectibles;\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectiblesPage(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{address}/collectibles', { path: { chainId, address }, query }, pageUrl);\n}\nexports.getCollectiblesPage = getCollectiblesPage;\n/**\n * Get a list of past Safe transactions\n */\nfunction getTransactionHistory(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/history', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\nexports.getTransactionHistory = getTransactionHistory;\n/**\n * Get the list of pending transactions\n */\nfunction getTransactionQueue(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/queued', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\nexports.getTransactionQueue = getTransactionQueue;\n/**\n * Get the details of an individual transaction by its id\n */\nfunction getTransactionDetails(chainId, transactionId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{transactionId}', {\n        path: { chainId, transactionId },\n    });\n}\nexports.getTransactionDetails = getTransactionDetails;\n/**\n * Delete a transaction by its safeTxHash\n */\nfunction deleteTransaction(chainId, safeTxHash, signature) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safeTxHash}', {\n        path: { chainId, safeTxHash },\n        body: { signature },\n    });\n}\nexports.deleteTransaction = deleteTransaction;\n/**\n * Request a gas estimate & recommmended tx nonce for a created transaction\n */\nfunction postSafeGasEstimation(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{safe_address}/multisig-transactions/estimations', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nexports.postSafeGasEstimation = postSafeGasEstimation;\nfunction getNonces(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/nonces', {\n        path: { chainId, safe_address: address },\n    });\n}\nexports.getNonces = getNonces;\n/**\n * Propose a new transaction for other owners to sign/execute\n */\nfunction proposeTransaction(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/propose', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nexports.proposeTransaction = proposeTransaction;\n/**\n * Returns decoded data\n */\nfunction getConfirmationView(chainId, safeAddress, encodedData, to) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/views/transaction-confirmation', {\n        path: { chainId: chainId, safe_address: safeAddress },\n        body: { data: encodedData, to },\n    });\n}\nexports.getConfirmationView = getConfirmationView;\n/**\n * Returns all defined chain configs\n */\nfunction getChainsConfig(query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains', {\n        query,\n    });\n}\nexports.getChainsConfig = getChainsConfig;\n/**\n * Returns a chain config\n */\nfunction getChainConfig(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}', {\n        path: { chainId: chainId },\n    });\n}\nexports.getChainConfig = getChainConfig;\n/**\n * Returns Safe Apps List\n */\nfunction getSafeApps(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safe-apps', {\n        path: { chainId: chainId },\n        query,\n    });\n}\nexports.getSafeApps = getSafeApps;\n/**\n * Returns list of Master Copies\n */\nfunction getMasterCopies(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/master-copies', {\n        path: { chainId: chainId },\n    });\n}\nexports.getMasterCopies = getMasterCopies;\n/**\n * Returns decoded data\n */\nfunction getDecodedData(chainId, encodedData, to) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/data-decoder', {\n        path: { chainId: chainId },\n        body: { data: encodedData, to },\n    });\n}\nexports.getDecodedData = getDecodedData;\n/**\n * Returns list of `SafeMessage`s\n */\nfunction getSafeMessages(chainId, address, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', { path: { chainId, safe_address: address }, query: {} }, pageUrl);\n}\nexports.getSafeMessages = getSafeMessages;\n/**\n * Returns a `SafeMessage`\n */\nfunction getSafeMessage(chainId, messageHash) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}', {\n        path: { chainId, message_hash: messageHash },\n    });\n}\nexports.getSafeMessage = getSafeMessage;\n/**\n * Propose a new `SafeMessage` for other owners to sign\n */\nfunction proposeSafeMessage(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nexports.proposeSafeMessage = proposeSafeMessage;\n/**\n * Add a confirmation to a `SafeMessage`\n */\nfunction confirmSafeMessage(chainId, messageHash, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}/signatures', {\n        path: { chainId, message_hash: messageHash },\n        body,\n    });\n}\nexports.confirmSafeMessage = confirmSafeMessage;\n/**\n * Returns a list of delegates\n */\nfunction getDelegates(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/delegates', {\n        path: { chainId },\n        query,\n    });\n}\nexports.getDelegates = getDelegates;\n/**\n * Registers a device/Safe for notifications\n */\nfunction registerDevice(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/register/notifications', {\n        body,\n    });\n}\nexports.registerDevice = registerDevice;\n/**\n * Unregisters a Safe from notifications\n */\nfunction unregisterSafe(chainId, address, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}/safes/{safe_address}', {\n        path: { chainId, safe_address: address, uuid },\n    });\n}\nexports.unregisterSafe = unregisterSafe;\n/**\n * Unregisters a device from notifications\n */\nfunction unregisterDevice(chainId, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}', {\n        path: { chainId, uuid },\n    });\n}\nexports.unregisterDevice = unregisterDevice;\n/**\n * Registers a email address for a safe signer.\n *\n * The signer wallet has to sign a message of format: `email-register-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param body Signer address and email address\n * @param headers Signature and Signature timestamp\n * @returns 200 if signature matches the data\n */\nfunction registerEmail(chainId, safeAddress, body, headers) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n        headers,\n    });\n}\nexports.registerEmail = registerEmail;\n/**\n * Changes an already registered email address for a safe signer. The new email address still needs to be verified.\n *\n * The signer wallet has to sign a message of format: `email-edit-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param body New email address\n * @param headers Signature and Signature timestamp\n * @returns 202 if signature matches the data\n */\nfunction changeEmail(chainId, safeAddress, signerAddress, body, headers) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n        headers,\n    });\n}\nexports.changeEmail = changeEmail;\n/**\n * Resends an email verification code.\n */\nfunction resendEmailVerificationCode(chainId, safeAddress, signerAddress) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify-resend', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body: '',\n    });\n}\nexports.resendEmailVerificationCode = resendEmailVerificationCode;\n/**\n * Verifies a pending email address registration.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address who signed the email registration\n * @param body Verification code\n */\nfunction verifyEmail(chainId, safeAddress, signerAddress, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n    });\n}\nexports.verifyEmail = verifyEmail;\n/**\n * Gets the registered email address of the signer\n *\n * The signer wallet will have to sign a message of format: `email-retrieval-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address of the owner of the Safe\n *\n * @returns email address and verified flag\n */\nfunction getRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\nexports.getRegisteredEmail = getRegisteredEmail;\n/**\n * Delete a registered email address for the signer\n *\n * The signer wallet will have to sign a message of format: `email-delete-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param headers\n */\nfunction deleteRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\nexports.deleteRegisteredEmail = deleteRegisteredEmail;\n/**\n * Register a recovery module for receiving alerts\n * @param chainId\n * @param safeAddress\n * @param body - { moduleAddress: string }\n */\nfunction registerRecoveryModule(chainId, safeAddress, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/recovery', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n    });\n}\nexports.registerRecoveryModule = registerRecoveryModule;\n/**\n * Delete email subscription for a single category\n * @param query\n */\nfunction unsubscribeSingle(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions', { query });\n}\nexports.unsubscribeSingle = unsubscribeSingle;\n/**\n * Delete email subscription for all categories\n * @param query\n */\nfunction unsubscribeAll(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions/all', { query });\n}\nexports.unsubscribeAll = unsubscribeAll;\n/**\n * Get Safe overviews per address\n */\nfunction getSafeOverviews(safes, query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/safes', {\n        query: Object.assign(Object.assign({}, query), { safes: safes.join(',') }),\n    });\n}\nexports.getSafeOverviews = getSafeOverviews;\nfunction getContract(chainId, contractAddress) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/contracts/{contractAddress}', {\n        path: {\n            chainId: chainId,\n            contractAddress: contractAddress,\n        },\n    });\n}\nexports.getContract = getContract;\nfunction getAuthNonce() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/auth/nonce', { credentials: 'include' });\n}\nexports.getAuthNonce = getAuthNonce;\nfunction verifyAuth(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/auth/verify', {\n        body,\n        credentials: 'include',\n    });\n}\nexports.verifyAuth = verifyAuth;\nfunction createAccount(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/accounts', {\n        body,\n        credentials: 'include',\n    });\n}\nexports.createAccount = createAccount;\nfunction getAccount(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nexports.getAccount = getAccount;\nfunction deleteAccount(address) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nexports.deleteAccount = deleteAccount;\nfunction getAccountDataTypes() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/data-types');\n}\nexports.getAccountDataTypes = getAccountDataTypes;\nfunction getAccountDataSettings(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nexports.getAccountDataSettings = getAccountDataSettings;\nfunction putAccountDataSettings(address, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        body,\n        credentials: 'include',\n    });\n}\nexports.putAccountDataSettings = putAccountDataSettings;\n/* eslint-enable @typescript-eslint/explicit-module-boundary-types */\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FEATURES = exports.GAS_PRICE_TYPE = exports.RPC_AUTHENTICATION = void 0;\nvar RPC_AUTHENTICATION;\n(function (RPC_AUTHENTICATION) {\n    RPC_AUTHENTICATION[\"API_KEY_PATH\"] = \"API_KEY_PATH\";\n    RPC_AUTHENTICATION[\"NO_AUTHENTICATION\"] = \"NO_AUTHENTICATION\";\n    RPC_AUTHENTICATION[\"UNKNOWN\"] = \"UNKNOWN\";\n})(RPC_AUTHENTICATION = exports.RPC_AUTHENTICATION || (exports.RPC_AUTHENTICATION = {}));\nvar GAS_PRICE_TYPE;\n(function (GAS_PRICE_TYPE) {\n    GAS_PRICE_TYPE[\"ORACLE\"] = \"ORACLE\";\n    GAS_PRICE_TYPE[\"FIXED\"] = \"FIXED\";\n    GAS_PRICE_TYPE[\"FIXED_1559\"] = \"FIXED1559\";\n    GAS_PRICE_TYPE[\"UNKNOWN\"] = \"UNKNOWN\";\n})(GAS_PRICE_TYPE = exports.GAS_PRICE_TYPE || (exports.GAS_PRICE_TYPE = {}));\nvar FEATURES;\n(function (FEATURES) {\n    FEATURES[\"ERC721\"] = \"ERC721\";\n    FEATURES[\"SAFE_APPS\"] = \"SAFE_APPS\";\n    FEATURES[\"CONTRACT_INTERACTION\"] = \"CONTRACT_INTERACTION\";\n    FEATURES[\"DOMAIN_LOOKUP\"] = \"DOMAIN_LOOKUP\";\n    FEATURES[\"SPENDING_LIMIT\"] = \"SPENDING_LIMIT\";\n    FEATURES[\"EIP1559\"] = \"EIP1559\";\n    FEATURES[\"SAFE_TX_GAS_OPTIONAL\"] = \"SAFE_TX_GAS_OPTIONAL\";\n    FEATURES[\"TX_SIMULATION\"] = \"TX_SIMULATION\";\n    FEATURES[\"EIP1271\"] = \"EIP1271\";\n})(FEATURES = exports.FEATURES || (exports.FEATURES = {}));\n//# sourceMappingURL=chains.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TokenType = void 0;\nvar TokenType;\n(function (TokenType) {\n    TokenType[\"ERC20\"] = \"ERC20\";\n    TokenType[\"ERC721\"] = \"ERC721\";\n    TokenType[\"NATIVE_TOKEN\"] = \"NATIVE_TOKEN\";\n})(TokenType = exports.TokenType || (exports.TokenType = {}));\n//# sourceMappingURL=common.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConfirmationViewTypes = void 0;\nvar ConfirmationViewTypes;\n(function (ConfirmationViewTypes) {\n    ConfirmationViewTypes[\"COW_SWAP_ORDER\"] = \"COW_SWAP_ORDER\";\n    ConfirmationViewTypes[\"COW_SWAP_TWAP_ORDER\"] = \"COW_SWAP_TWAP_ORDER\";\n})(ConfirmationViewTypes = exports.ConfirmationViewTypes || (exports.ConfirmationViewTypes = {}));\n//# sourceMappingURL=decoded-data.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=master-copies.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeviceType = void 0;\nvar DeviceType;\n(function (DeviceType) {\n    DeviceType[\"ANDROID\"] = \"ANDROID\";\n    DeviceType[\"IOS\"] = \"IOS\";\n    DeviceType[\"WEB\"] = \"WEB\";\n})(DeviceType = exports.DeviceType || (exports.DeviceType = {}));\n//# sourceMappingURL=notifications.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=relay.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppSocialPlatforms = exports.SafeAppFeatures = exports.SafeAppAccessPolicyTypes = void 0;\nvar SafeAppAccessPolicyTypes;\n(function (SafeAppAccessPolicyTypes) {\n    SafeAppAccessPolicyTypes[\"NoRestrictions\"] = \"NO_RESTRICTIONS\";\n    SafeAppAccessPolicyTypes[\"DomainAllowlist\"] = \"DOMAIN_ALLOWLIST\";\n})(SafeAppAccessPolicyTypes = exports.SafeAppAccessPolicyTypes || (exports.SafeAppAccessPolicyTypes = {}));\nvar SafeAppFeatures;\n(function (SafeAppFeatures) {\n    SafeAppFeatures[\"BATCHED_TRANSACTIONS\"] = \"BATCHED_TRANSACTIONS\";\n})(SafeAppFeatures = exports.SafeAppFeatures || (exports.SafeAppFeatures = {}));\nvar SafeAppSocialPlatforms;\n(function (SafeAppSocialPlatforms) {\n    SafeAppSocialPlatforms[\"TWITTER\"] = \"TWITTER\";\n    SafeAppSocialPlatforms[\"GITHUB\"] = \"GITHUB\";\n    SafeAppSocialPlatforms[\"DISCORD\"] = \"DISCORD\";\n})(SafeAppSocialPlatforms = exports.SafeAppSocialPlatforms || (exports.SafeAppSocialPlatforms = {}));\n//# sourceMappingURL=safe-apps.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ImplementationVersionState = void 0;\nvar ImplementationVersionState;\n(function (ImplementationVersionState) {\n    ImplementationVersionState[\"UP_TO_DATE\"] = \"UP_TO_DATE\";\n    ImplementationVersionState[\"OUTDATED\"] = \"OUTDATED\";\n    ImplementationVersionState[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ImplementationVersionState = exports.ImplementationVersionState || (exports.ImplementationVersionState = {}));\n//# sourceMappingURL=safe-info.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeMessageStatus = exports.SafeMessageListItemType = void 0;\nvar SafeMessageListItemType;\n(function (SafeMessageListItemType) {\n    SafeMessageListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n    SafeMessageListItemType[\"MESSAGE\"] = \"MESSAGE\";\n})(SafeMessageListItemType = exports.SafeMessageListItemType || (exports.SafeMessageListItemType = {}));\nvar SafeMessageStatus;\n(function (SafeMessageStatus) {\n    SafeMessageStatus[\"NEEDS_CONFIRMATION\"] = \"NEEDS_CONFIRMATION\";\n    SafeMessageStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n})(SafeMessageStatus = exports.SafeMessageStatus || (exports.SafeMessageStatus = {}));\n//# sourceMappingURL=safe-messages.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LabelValue = exports.StartTimeValue = exports.DurationType = exports.DetailedExecutionInfoType = exports.TransactionListItemType = exports.ConflictType = exports.TransactionInfoType = exports.SettingsInfoType = exports.TransactionTokenType = exports.TransferDirection = exports.TransactionStatus = exports.Operation = void 0;\nvar Operation;\n(function (Operation) {\n    Operation[Operation[\"CALL\"] = 0] = \"CALL\";\n    Operation[Operation[\"DELEGATE\"] = 1] = \"DELEGATE\";\n})(Operation = exports.Operation || (exports.Operation = {}));\nvar TransactionStatus;\n(function (TransactionStatus) {\n    TransactionStatus[\"AWAITING_CONFIRMATIONS\"] = \"AWAITING_CONFIRMATIONS\";\n    TransactionStatus[\"AWAITING_EXECUTION\"] = \"AWAITING_EXECUTION\";\n    TransactionStatus[\"CANCELLED\"] = \"CANCELLED\";\n    TransactionStatus[\"FAILED\"] = \"FAILED\";\n    TransactionStatus[\"SUCCESS\"] = \"SUCCESS\";\n})(TransactionStatus = exports.TransactionStatus || (exports.TransactionStatus = {}));\nvar TransferDirection;\n(function (TransferDirection) {\n    TransferDirection[\"INCOMING\"] = \"INCOMING\";\n    TransferDirection[\"OUTGOING\"] = \"OUTGOING\";\n    TransferDirection[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TransferDirection = exports.TransferDirection || (exports.TransferDirection = {}));\nvar TransactionTokenType;\n(function (TransactionTokenType) {\n    TransactionTokenType[\"ERC20\"] = \"ERC20\";\n    TransactionTokenType[\"ERC721\"] = \"ERC721\";\n    TransactionTokenType[\"NATIVE_COIN\"] = \"NATIVE_COIN\";\n})(TransactionTokenType = exports.TransactionTokenType || (exports.TransactionTokenType = {}));\nvar SettingsInfoType;\n(function (SettingsInfoType) {\n    SettingsInfoType[\"SET_FALLBACK_HANDLER\"] = \"SET_FALLBACK_HANDLER\";\n    SettingsInfoType[\"ADD_OWNER\"] = \"ADD_OWNER\";\n    SettingsInfoType[\"REMOVE_OWNER\"] = \"REMOVE_OWNER\";\n    SettingsInfoType[\"SWAP_OWNER\"] = \"SWAP_OWNER\";\n    SettingsInfoType[\"CHANGE_THRESHOLD\"] = \"CHANGE_THRESHOLD\";\n    SettingsInfoType[\"CHANGE_IMPLEMENTATION\"] = \"CHANGE_IMPLEMENTATION\";\n    SettingsInfoType[\"ENABLE_MODULE\"] = \"ENABLE_MODULE\";\n    SettingsInfoType[\"DISABLE_MODULE\"] = \"DISABLE_MODULE\";\n    SettingsInfoType[\"SET_GUARD\"] = \"SET_GUARD\";\n    SettingsInfoType[\"DELETE_GUARD\"] = \"DELETE_GUARD\";\n})(SettingsInfoType = exports.SettingsInfoType || (exports.SettingsInfoType = {}));\nvar TransactionInfoType;\n(function (TransactionInfoType) {\n    TransactionInfoType[\"TRANSFER\"] = \"Transfer\";\n    TransactionInfoType[\"SETTINGS_CHANGE\"] = \"SettingsChange\";\n    TransactionInfoType[\"CUSTOM\"] = \"Custom\";\n    TransactionInfoType[\"CREATION\"] = \"Creation\";\n    TransactionInfoType[\"SWAP_ORDER\"] = \"SwapOrder\";\n    TransactionInfoType[\"TWAP_ORDER\"] = \"TwapOrder\";\n    TransactionInfoType[\"SWAP_TRANSFER\"] = \"SwapTransfer\";\n})(TransactionInfoType = exports.TransactionInfoType || (exports.TransactionInfoType = {}));\nvar ConflictType;\n(function (ConflictType) {\n    ConflictType[\"NONE\"] = \"None\";\n    ConflictType[\"HAS_NEXT\"] = \"HasNext\";\n    ConflictType[\"END\"] = \"End\";\n})(ConflictType = exports.ConflictType || (exports.ConflictType = {}));\nvar TransactionListItemType;\n(function (TransactionListItemType) {\n    TransactionListItemType[\"TRANSACTION\"] = \"TRANSACTION\";\n    TransactionListItemType[\"LABEL\"] = \"LABEL\";\n    TransactionListItemType[\"CONFLICT_HEADER\"] = \"CONFLICT_HEADER\";\n    TransactionListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n})(TransactionListItemType = exports.TransactionListItemType || (exports.TransactionListItemType = {}));\nvar DetailedExecutionInfoType;\n(function (DetailedExecutionInfoType) {\n    DetailedExecutionInfoType[\"MULTISIG\"] = \"MULTISIG\";\n    DetailedExecutionInfoType[\"MODULE\"] = \"MODULE\";\n})(DetailedExecutionInfoType = exports.DetailedExecutionInfoType || (exports.DetailedExecutionInfoType = {}));\nvar DurationType;\n(function (DurationType) {\n    DurationType[\"AUTO\"] = \"AUTO\";\n    DurationType[\"LIMIT_DURATION\"] = \"LIMIT_DURATION\";\n})(DurationType = exports.DurationType || (exports.DurationType = {}));\nvar StartTimeValue;\n(function (StartTimeValue) {\n    StartTimeValue[\"AT_MINING_TIME\"] = \"AT_MINING_TIME\";\n    StartTimeValue[\"AT_EPOCH\"] = \"AT_EPOCH\";\n})(StartTimeValue = exports.StartTimeValue || (exports.StartTimeValue = {}));\nvar LabelValue;\n(function (LabelValue) {\n    LabelValue[\"Queued\"] = \"Queued\";\n    LabelValue[\"Next\"] = \"Next\";\n})(LabelValue = exports.LabelValue || (exports.LabelValue = {}));\n//# sourceMappingURL=transactions.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getData = exports.fetchData = exports.stringifyQuery = exports.insertParams = void 0;\nconst isErrorResponse = (data) => {\n    const isObject = typeof data === 'object' && data !== null;\n    return isObject && 'code' in data && 'message' in data;\n};\nfunction replaceParam(str, key, value) {\n    return str.replace(new RegExp(`\\\\{${key}\\\\}`, 'g'), value);\n}\nfunction insertParams(template, params) {\n    return params\n        ? Object.keys(params).reduce((result, key) => {\n            return replaceParam(result, key, String(params[key]));\n        }, template)\n        : template;\n}\nexports.insertParams = insertParams;\nfunction stringifyQuery(query) {\n    if (!query) {\n        return '';\n    }\n    const searchParams = new URLSearchParams();\n    Object.keys(query).forEach((key) => {\n        if (query[key] != null) {\n            searchParams.append(key, String(query[key]));\n        }\n    });\n    const searchString = searchParams.toString();\n    return searchString ? `?${searchString}` : '';\n}\nexports.stringifyQuery = stringifyQuery;\nfunction parseResponse(resp) {\n    return __awaiter(this, void 0, void 0, function* () {\n        let json;\n        try {\n            json = yield resp.json();\n        }\n        catch (_a) {\n            json = {};\n        }\n        if (!resp.ok) {\n            const errTxt = isErrorResponse(json)\n                ? `CGW error - ${json.code}: ${json.message}`\n                : `CGW error - status ${resp.statusText}`;\n            throw new Error(errTxt);\n        }\n        return json;\n    });\n}\nfunction fetchData(url, method, body, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const requestHeaders = Object.assign({ 'Content-Type': 'application/json' }, headers);\n        const options = {\n            method: method !== null && method !== void 0 ? method : 'POST',\n            headers: requestHeaders,\n        };\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        if (body != null) {\n            options.body = typeof body === 'string' ? body : JSON.stringify(body);\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\nexports.fetchData = fetchData;\nfunction getData(url, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const options = {\n            method: 'GET',\n        };\n        if (headers) {\n            options['headers'] = Object.assign(Object.assign({}, headers), { 'Content-Type': 'application/json' });\n        }\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\nexports.getData = getData;\n//# sourceMappingURL=utils.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.RestrictedMethods),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messageFormatter.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./methods.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PostMessageCommunicator);\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* binding */ MessageFormatter)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\");\n\n\nclass MessageFormatter {\n}\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0,_version_js__WEBPACK_IMPORTED_MODULE_0__.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n\n//# sourceMappingURL=messageFormatter.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* binding */ Methods),\n/* harmony export */   RestrictedMethods: () => (/* binding */ RestrictedMethods)\n/* harmony export */ });\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateRequestId: () => (/* binding */ generateRequestId)\n/* harmony export */ });\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\n\n//# sourceMappingURL=utils.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wallet/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (requirePermission);\n//# sourceMappingURL=requirePermissions.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RPC_CALLS: () => (/* binding */ RPC_CALLS)\n/* harmony export */ });\nconst RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eth: () => (/* binding */ Eth)\n/* harmony export */ });\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../eth/constants.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* reexport safe */ _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__.MessageFormatter),\n/* harmony export */   Methods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.Methods),\n/* harmony export */   Operation: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.Operation),\n/* harmony export */   RPC_CALLS: () => (/* reexport safe */ _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__.RPC_CALLS),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.RestrictedMethods),\n/* harmony export */   TokenType: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransferDirection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSDKVersion: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_4__.getSDKVersion),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./communication/methods.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communication/messageFormatter.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./eth/constants.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_sdk_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Safe: () => (/* binding */ Safe)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"./node_modules/viem/_esm/utils/abi/encodeFunctionData.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"./node_modules/viem/_esm/utils/signature/hashMessage.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"./node_modules/viem/_esm/utils/signature/hashTypedData.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../eth/constants.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../types/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../decorators/requirePermissions.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\");\nvar __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\n\n\n\n\n\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0,viem__WEBPACK_IMPORTED_MODULE_6__.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0,viem__WEBPACK_IMPORTED_MODULE_7__.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0,_types_index_js__WEBPACK_IMPORTED_MODULE_3__.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\n__decorate([\n    (0,_decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n], Safe.prototype, \"requestAddressBook\", null);\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAGIC_VALUE: () => (/* binding */ MAGIC_VALUE),\n/* harmony export */   MAGIC_VALUE_BYTES: () => (/* binding */ MAGIC_VALUE_BYTES)\n/* harmony export */ });\nconst MAGIC_VALUE = '0x1626ba7e';\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\n\n//# sourceMappingURL=signatures.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _communication_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./communication/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\");\n/* harmony import */ var _txs_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./txs/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\");\n/* harmony import */ var _eth_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./eth/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\");\n/* harmony import */ var _safe_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./safe/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\");\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./wallet/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n\n\n\n\n\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new _communication_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](allowedDomains, debug);\n        this.eth = new _eth_index_js__WEBPACK_IMPORTED_MODULE_2__.Eth(this.communicator);\n        this.txs = new _txs_index_js__WEBPACK_IMPORTED_MODULE_1__.TXs(this.communicator);\n        this.safe = new _safe_index_js__WEBPACK_IMPORTED_MODULE_3__.Safe(this.communicator);\n        this.wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__.Wallet(this.communicator);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeAppsSDK);\n//# sourceMappingURL=sdk.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TXs: () => (/* binding */ TXs)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/index.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n\n\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0,_types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransferDirection)\n/* harmony export */ });\n/* harmony import */ var _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\n\n//# sourceMappingURL=gateway.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransferDirection),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _sdk_js__WEBPACK_IMPORTED_MODULE_0__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\");\n/* harmony import */ var _rpc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rpc.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\");\n/* harmony import */ var _gateway_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gateway.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\");\n/* harmony import */ var _messaging_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messaging.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n//# sourceMappingURL=messaging.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS_REQUEST_REJECTED: () => (/* binding */ PERMISSIONS_REQUEST_REJECTED),\n/* harmony export */   PermissionsError: () => (/* binding */ PermissionsError)\n/* harmony export */ });\nconst PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\n//# sourceMappingURL=permissions.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=rpc.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectEIP712TypedData: () => (/* binding */ isObjectEIP712TypedData)\n/* harmony export */ });\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\n//# sourceMappingURL=sdk.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSDKVersion: () => (/* binding */ getSDKVersion)\n/* harmony export */ });\nconst getSDKVersion = () => '9.1.0';\n//# sourceMappingURL=version.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Wallet: () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions request is invalid', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\n\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/constants/strings.js":
/*!*****************************************************!*\
  !*** ./node_modules/viem/_esm/constants/strings.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   presignMessagePrefix: () => (/* binding */ presignMessagePrefix)\n/* harmony export */ });\nconst presignMessagePrefix = '\\x19Ethereum Signed Message:\\n';\n//# sourceMappingURL=strings.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/constants/strings.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/utils/regex.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/utils/regex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayRegex: () => (/* binding */ arrayRegex),\n/* harmony export */   bytesRegex: () => (/* binding */ bytesRegex),\n/* harmony export */   integerRegex: () => (/* binding */ integerRegex)\n/* harmony export */ });\nconst arrayRegex = /^(.*)\\[([0-9]*)\\]$/;\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nconst bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nconst integerRegex = /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;\n//# sourceMappingURL=regex.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/utils/regex.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/utils/signature/hashMessage.js":
/*!***************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/signature/hashMessage.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hashMessage: () => (/* binding */ hashMessage)\n/* harmony export */ });\n/* harmony import */ var _hash_keccak256_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../hash/keccak256.js */ \"./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var _toPrefixedMessage_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toPrefixedMessage.js */ \"./node_modules/viem/_esm/utils/signature/toPrefixedMessage.js\");\n\n\nfunction hashMessage(message, to_) {\n    return (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_0__.keccak256)((0,_toPrefixedMessage_js__WEBPACK_IMPORTED_MODULE_1__.toPrefixedMessage)(message), to_);\n}\n//# sourceMappingURL=hashMessage.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/utils/signature/hashMessage.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/utils/signature/hashTypedData.js":
/*!*****************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/signature/hashTypedData.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeType: () => (/* binding */ encodeType),\n/* harmony export */   hashDomain: () => (/* binding */ hashDomain),\n/* harmony export */   hashStruct: () => (/* binding */ hashStruct),\n/* harmony export */   hashTypedData: () => (/* binding */ hashTypedData)\n/* harmony export */ });\n/* harmony import */ var _abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../abi/encodeAbiParameters.js */ \"./node_modules/viem/_esm/utils/abi/encodeAbiParameters.js\");\n/* harmony import */ var _data_concat_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../data/concat.js */ \"./node_modules/viem/_esm/utils/data/concat.js\");\n/* harmony import */ var _encoding_toHex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../encoding/toHex.js */ \"./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../hash/keccak256.js */ \"./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var _typedData_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../typedData.js */ \"./node_modules/viem/_esm/utils/typedData.js\");\n// Implementation forked and adapted from https://github.com/MetaMask/eth-sig-util/blob/main/src/sign-typed-data.ts\n\n\n\n\n\nfunction hashTypedData(parameters) {\n    const { domain = {}, message, primaryType, } = parameters;\n    const types = {\n        EIP712Domain: (0,_typedData_js__WEBPACK_IMPORTED_MODULE_0__.getTypesForEIP712Domain)({ domain }),\n        ...parameters.types,\n    };\n    // Need to do a runtime validation check on addresses, byte ranges, integer ranges, etc\n    // as we can't statically check this with TypeScript.\n    (0,_typedData_js__WEBPACK_IMPORTED_MODULE_0__.validateTypedData)({\n        domain,\n        message,\n        primaryType,\n        types,\n    });\n    const parts = ['0x1901'];\n    if (domain)\n        parts.push(hashDomain({\n            domain,\n            types: types,\n        }));\n    if (primaryType !== 'EIP712Domain')\n        parts.push(hashStruct({\n            data: message,\n            primaryType,\n            types: types,\n        }));\n    return (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)((0,_data_concat_js__WEBPACK_IMPORTED_MODULE_2__.concat)(parts));\n}\nfunction hashDomain({ domain, types, }) {\n    return hashStruct({\n        data: domain,\n        primaryType: 'EIP712Domain',\n        types,\n    });\n}\nfunction hashStruct({ data, primaryType, types, }) {\n    const encoded = encodeData({\n        data,\n        primaryType,\n        types,\n    });\n    return (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)(encoded);\n}\nfunction encodeData({ data, primaryType, types, }) {\n    const encodedTypes = [{ type: 'bytes32' }];\n    const encodedValues = [hashType({ primaryType, types })];\n    for (const field of types[primaryType]) {\n        const [type, value] = encodeField({\n            types,\n            name: field.name,\n            type: field.type,\n            value: data[field.name],\n        });\n        encodedTypes.push(type);\n        encodedValues.push(value);\n    }\n    return (0,_abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_3__.encodeAbiParameters)(encodedTypes, encodedValues);\n}\nfunction hashType({ primaryType, types, }) {\n    const encodedHashType = (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_4__.toHex)(encodeType({ primaryType, types }));\n    return (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)(encodedHashType);\n}\nfunction encodeType({ primaryType, types, }) {\n    let result = '';\n    const unsortedDeps = findTypeDependencies({ primaryType, types });\n    unsortedDeps.delete(primaryType);\n    const deps = [primaryType, ...Array.from(unsortedDeps).sort()];\n    for (const type of deps) {\n        result += `${type}(${types[type]\n            .map(({ name, type: t }) => `${t} ${name}`)\n            .join(',')})`;\n    }\n    return result;\n}\nfunction findTypeDependencies({ primaryType: primaryType_, types, }, results = new Set()) {\n    const match = primaryType_.match(/^\\w*/u);\n    const primaryType = match?.[0];\n    if (results.has(primaryType) || types[primaryType] === undefined) {\n        return results;\n    }\n    results.add(primaryType);\n    for (const field of types[primaryType]) {\n        findTypeDependencies({ primaryType: field.type, types }, results);\n    }\n    return results;\n}\nfunction encodeField({ types, name, type, value, }) {\n    if (types[type] !== undefined) {\n        return [\n            { type: 'bytes32' },\n            (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)(encodeData({ data: value, primaryType: type, types })),\n        ];\n    }\n    if (type === 'bytes') {\n        const prepend = value.length % 2 ? '0' : '';\n        value = `0x${prepend + value.slice(2)}`;\n        return [{ type: 'bytes32' }, (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)(value)];\n    }\n    if (type === 'string')\n        return [{ type: 'bytes32' }, (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)((0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_4__.toHex)(value))];\n    if (type.lastIndexOf(']') === type.length - 1) {\n        const parsedType = type.slice(0, type.lastIndexOf('['));\n        const typeValuePairs = value.map((item) => encodeField({\n            name,\n            type: parsedType,\n            types,\n            value: item,\n        }));\n        return [\n            { type: 'bytes32' },\n            (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)((0,_abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_3__.encodeAbiParameters)(typeValuePairs.map(([t]) => t), typeValuePairs.map(([, v]) => v))),\n        ];\n    }\n    return [{ type }, value];\n}\n//# sourceMappingURL=hashTypedData.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/utils/signature/hashTypedData.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/utils/signature/toPrefixedMessage.js":
/*!*********************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/signature/toPrefixedMessage.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toPrefixedMessage: () => (/* binding */ toPrefixedMessage)\n/* harmony export */ });\n/* harmony import */ var _constants_strings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../constants/strings.js */ \"./node_modules/viem/_esm/constants/strings.js\");\n/* harmony import */ var _data_concat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../data/concat.js */ \"./node_modules/viem/_esm/utils/data/concat.js\");\n/* harmony import */ var _data_size_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../data/size.js */ \"./node_modules/viem/_esm/utils/data/size.js\");\n/* harmony import */ var _encoding_toHex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../encoding/toHex.js */ \"./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\n\n\nfunction toPrefixedMessage(message_) {\n    const message = (() => {\n        if (typeof message_ === 'string')\n            return (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_0__.stringToHex)(message_);\n        if (typeof message_.raw === 'string')\n            return message_.raw;\n        return (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(message_.raw);\n    })();\n    const prefix = (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_0__.stringToHex)(`${_constants_strings_js__WEBPACK_IMPORTED_MODULE_1__.presignMessagePrefix}${(0,_data_size_js__WEBPACK_IMPORTED_MODULE_2__.size)(message)}`);\n    return (0,_data_concat_js__WEBPACK_IMPORTED_MODULE_3__.concat)([prefix, message]);\n}\n//# sourceMappingURL=toPrefixedMessage.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/utils/signature/toPrefixedMessage.js?");

/***/ }),

/***/ "./node_modules/viem/_esm/utils/typedData.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/utils/typedData.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   domainSeparator: () => (/* binding */ domainSeparator),\n/* harmony export */   getTypesForEIP712Domain: () => (/* binding */ getTypesForEIP712Domain),\n/* harmony export */   serializeTypedData: () => (/* binding */ serializeTypedData),\n/* harmony export */   validateTypedData: () => (/* binding */ validateTypedData)\n/* harmony export */ });\n/* harmony import */ var _errors_abi_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/abi.js */ \"./node_modules/viem/_esm/errors/abi.js\");\n/* harmony import */ var _errors_address_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/address.js */ \"./node_modules/viem/_esm/errors/address.js\");\n/* harmony import */ var _address_isAddress_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./address/isAddress.js */ \"./node_modules/viem/_esm/utils/address/isAddress.js\");\n/* harmony import */ var _data_size_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./data/size.js */ \"./node_modules/viem/_esm/utils/data/size.js\");\n/* harmony import */ var _encoding_toHex_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./encoding/toHex.js */ \"./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./regex.js */ \"./node_modules/viem/_esm/utils/regex.js\");\n/* harmony import */ var _signature_hashTypedData_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./signature/hashTypedData.js */ \"./node_modules/viem/_esm/utils/signature/hashTypedData.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"./node_modules/viem/_esm/utils/stringify.js\");\n\n\n\n\n\n\n\n\nfunction serializeTypedData(parameters) {\n    const { domain: domain_, message: message_, primaryType, types, } = parameters;\n    const normalizeData = (struct, data_) => {\n        const data = { ...data_ };\n        for (const param of struct) {\n            const { name, type } = param;\n            if (type === 'address')\n                data[name] = data[name].toLowerCase();\n        }\n        return data;\n    };\n    const domain = (() => {\n        if (!types.EIP712Domain)\n            return {};\n        if (!domain_)\n            return {};\n        return normalizeData(types.EIP712Domain, domain_);\n    })();\n    const message = (() => {\n        if (primaryType === 'EIP712Domain')\n            return undefined;\n        return normalizeData(types[primaryType], message_);\n    })();\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_0__.stringify)({ domain, message, primaryType, types });\n}\nfunction validateTypedData(parameters) {\n    const { domain, message, primaryType, types } = parameters;\n    const validateData = (struct, data) => {\n        for (const param of struct) {\n            const { name, type } = param;\n            const value = data[name];\n            const integerMatch = type.match(_regex_js__WEBPACK_IMPORTED_MODULE_1__.integerRegex);\n            if (integerMatch &&\n                (typeof value === 'number' || typeof value === 'bigint')) {\n                const [_type, base, size_] = integerMatch;\n                // If number cannot be cast to a sized hex value, it is out of range\n                // and will throw.\n                (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_2__.numberToHex)(value, {\n                    signed: base === 'int',\n                    size: Number.parseInt(size_) / 8,\n                });\n            }\n            if (type === 'address' && typeof value === 'string' && !(0,_address_isAddress_js__WEBPACK_IMPORTED_MODULE_3__.isAddress)(value))\n                throw new _errors_address_js__WEBPACK_IMPORTED_MODULE_4__.InvalidAddressError({ address: value });\n            const bytesMatch = type.match(_regex_js__WEBPACK_IMPORTED_MODULE_1__.bytesRegex);\n            if (bytesMatch) {\n                const [_type, size_] = bytesMatch;\n                if (size_ && (0,_data_size_js__WEBPACK_IMPORTED_MODULE_5__.size)(value) !== Number.parseInt(size_))\n                    throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_6__.BytesSizeMismatchError({\n                        expectedSize: Number.parseInt(size_),\n                        givenSize: (0,_data_size_js__WEBPACK_IMPORTED_MODULE_5__.size)(value),\n                    });\n            }\n            const struct = types[type];\n            if (struct)\n                validateData(struct, value);\n        }\n    };\n    // Validate domain types.\n    if (types.EIP712Domain && domain)\n        validateData(types.EIP712Domain, domain);\n    // Validate message types.\n    if (primaryType !== 'EIP712Domain')\n        validateData(types[primaryType], message);\n}\nfunction getTypesForEIP712Domain({ domain, }) {\n    return [\n        typeof domain?.name === 'string' && { name: 'name', type: 'string' },\n        domain?.version && { name: 'version', type: 'string' },\n        typeof domain?.chainId === 'number' && {\n            name: 'chainId',\n            type: 'uint256',\n        },\n        domain?.verifyingContract && {\n            name: 'verifyingContract',\n            type: 'address',\n        },\n        domain?.salt && { name: 'salt', type: 'bytes32' },\n    ].filter(Boolean);\n}\nfunction domainSeparator({ domain }) {\n    return (0,_signature_hashTypedData_js__WEBPACK_IMPORTED_MODULE_7__.hashDomain)({\n        domain,\n        types: {\n            EIP712Domain: getTypesForEIP712Domain({ domain }),\n        },\n    });\n}\n//# sourceMappingURL=typedData.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/viem/_esm/utils/typedData.js?");

/***/ })

}]);