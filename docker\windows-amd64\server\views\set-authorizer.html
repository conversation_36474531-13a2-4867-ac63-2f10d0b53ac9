<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Blox Authorizer</title>
    <link rel="stylesheet" href="/webui/public/css/styles.css">
</head>
<body>
    <div class="container">
        <h1>Set Blox Owner</h1>
        <div id="content" style="display: block;">
            <div id="new-peer-id-section" class="section">
                <h2>The Blox App Peer ID</h2>
                <p id="new-peer-id-text">Click on Set authorizer to generate...</p>
            </div>
            <div id="new-blox-peer-id-section" class="section">
                <h2>Your Blox Peer ID</h2>
                <p id="new-blox-peer-id-text">Click on Set authorizer to generate...</p>
            </div>
            <div id="buttons-section" class="button-container">
                <button id="set-authorizer-button" class="button">Set Authorizer</button>
                <button id="next-button" class="button disabled" style="display: block;">Next</button>
            </div>
        </div>
    </div>
    <footer class="footer">
        <p>Version: <span id="version-number"></span>
            <button id="reset-button" class="reset-button">&#8635;</button>
        </p>
    </footer>    
    <script src="/webui/public/js/bundle/setAuthorizer.bundle.js"></script>
    <script src="/webui/public/js/bundle/common.bundle.js"></script>
</body>
</html>
