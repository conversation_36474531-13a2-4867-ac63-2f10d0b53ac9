body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

h2 {
    color: #4CAF50;
    margin-bottom: 10px;
}

p {
    font-size: 16px;
    color: #666;
}

.section {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
}

#search-section {
    text-align: center;
    margin-bottom: 20px;
}

#search-input {
    width: 70%;
    padding: 10px;
    margin-right: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

#search-button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
}

#search-button:hover {
    background-color: #45a049;
}

#sync-status-section {
    text-align: center;
    margin-bottom: 20px;
}

.pool {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
    text-align: center;
}

.pool h3 {
    margin-top: 0;
    color: #333;
}

.pool p {
    color: #666;
}

.pool button {
    padding: 10px 20px;
    margin-top: 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
}

.pool button:hover {
    background-color: #45a049;
}

/* General styles for the buttons */
.button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    margin: 5px;
    display: inline-block;
}

.button:hover {
    background-color: #45a049;
}

/* Additional styles for better UX */

.radio-buttons {
    margin-bottom: 20px;
}

.button.inverted {
    background-color: #fff;
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.button.disabled {
    background-color: #b1b1b1;
    color: white;
    border: 1px solid #b1b1b1;
    cursor: not-allowed;
}

.button.inverted:hover {
    background-color: #4CAF50;
    color: white;
}

.button.disabled:hover {
    background-color: #b1b1b1;
    color: white;
    border: 1px solid #b1b1b1;
}

.button-container {
    text-align: center;
}

.card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    background-color: #fff;
}

.card h3 {
    margin-top: 0;
}

.card .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.card .row .title {
    font-weight: bold;
}

.hint {
    cursor: pointer;
    margin-left: 8px;
    color: #ff9800;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 14px;
    height: 14px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Style the table */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

/* Style the table headers */
table th {
    background-color: #4CAF50;
    color: white;
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
}

/* Style the table rows */
table td {
    padding: 10px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
}

/* Add hover effect for table rows */
table tbody tr:hover {
    background-color: #f1f1f1;
}


/* Style the input field */
input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 16px;
}

/* Style the checkboxes */
.radio-buttons label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
}

.radio-buttons input[type="checkbox"] {
    margin-right: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.radio-buttons input[type="checkbox"]:checked {
    background-color: #4CAF50;
    border: 1px solid #4CAF50;
}

/* Additional styles for the warning text */
.warning-text {
    color: #ff9800;
    font-size: 14px;
    margin-bottom: 20px;
}

/* Add some padding and border radius to the section */
#password-section {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.info-box {
    padding: 20px;
    background-color: #f0f8ff;
    border: 1px solid #ccc;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: left;
}

.info-box p {
    margin: 10px 0;
}

.info-box a {
    color: #4CAF50;
    text-decoration: none;
}

.info-box a:hover {
    text-decoration: underline;
}

.clickable {
    color: #333;
    background-color: #e7e7e7;
    padding: 5px;
    border-radius: 5px;
    cursor: pointer;
}

.clickable:hover {
    background-color: #ccc;
}

.footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #f5f5f5;
    color: #888;
    text-align: center;
    padding: 10px 0;
    font-size: 14px;
}

.footer p {
    margin: 0;
}
