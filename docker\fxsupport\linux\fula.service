[Unit]
Description=%i Fula service with docker compose
PartOf=docker.service
After=docker.service uniondrive.service firewall.service
Requires=docker.service uniondrive.service
Wants=firewall.service

[Service]
Type=simple
RemainAfterExit=true
WorkingDirectory=/usr/bin/fula/
EnvironmentFile=/usr/bin/fula/.env
ExecStartPre=/bin/sleep 60
ExecStart=/bin/bash -c '. /usr/bin/fula/.env && chmod +x /usr/bin/fula/fula.sh && if ! [ -s /usr/bin/fula/fula.sh ] || ! bash /usr/bin/fula/fula.sh start; then  echo "failed";  docker run --name fula_fxsupport --rm $FX_SUPPROT sleep 20 & sleep 2 && docker cp fula_fxsupport:/linux/. /usr/bin/fula/;  sync ;exit 1;  fi'
ExecStop=/bin/bash -c '/usr/bin/fula/fula.sh stop'
Restart=always
RestartSec=30
User=root
Group=root

[Install]
WantedBy=multi-user.target
