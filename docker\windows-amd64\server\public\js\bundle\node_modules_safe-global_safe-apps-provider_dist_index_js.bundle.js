"use strict";
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkfula_webui"] = self["webpackChunkfula_webui"] || []).push([["node_modules_safe-global_safe-apps-provider_dist_index_js"],{

/***/ "./node_modules/@safe-global/safe-apps-provider/dist/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nvar provider_1 = __webpack_require__(/*! ./provider */ \"./node_modules/@safe-global/safe-apps-provider/dist/provider.js\");\nObject.defineProperty(exports, \"SafeAppProvider\", ({ enumerable: true, get: function () { return provider_1.SafeAppProvider; } }));\n//# sourceMappingURL=index.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-provider/dist/index.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-provider/dist/provider.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/provider.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"./node_modules/events/events.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"./node_modules/@safe-global/safe-apps-provider/dist/utils.js\");\n// The API is based on Ethereum JavaScript API Provider Standard. Link: https://eips.ethereum.org/EIPS/eip-1193\nclass SafeAppProvider extends events_1.EventEmitter {\n    constructor(safe, sdk) {\n        super();\n        this.submittedTxs = new Map();\n        this.safe = safe;\n        this.sdk = sdk;\n    }\n    async connect() {\n        this.emit('connect', { chainId: this.chainId });\n        return;\n    }\n    async disconnect() {\n        return;\n    }\n    get chainId() {\n        return this.safe.chainId;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async request(request) {\n        const { method, params = [] } = request;\n        switch (method) {\n            case 'eth_accounts':\n                return [this.safe.safeAddress];\n            case 'net_version':\n            case 'eth_chainId':\n                return `0x${this.chainId.toString(16)}`;\n            case 'personal_sign': {\n                const [message, address] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(message);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sign': {\n                const [address, messageHash] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase() || !messageHash.startsWith('0x')) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(messageHash);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_signTypedData':\n            case 'eth_signTypedData_v4': {\n                const [address, typedData] = params;\n                const parsedTypedData = typeof typedData === 'string' ? JSON.parse(typedData) : typedData;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address is invalid');\n                }\n                const response = await this.sdk.txs.signTypedMessage(parsedTypedData);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sendTransaction':\n                // `value` or `data` can be explicitly set as `undefined` for example in Viem. The spread will overwrite the fallback value.\n                const tx = {\n                    ...params[0],\n                    value: params[0].value || '0',\n                    data: params[0].data || '0x',\n                };\n                // Some ethereum libraries might pass the gas as a hex-encoded string\n                // We need to convert it to a number because the SDK expects a number and our backend only supports\n                // Decimal numbers\n                if (typeof tx.gas === 'string' && tx.gas.startsWith('0x')) {\n                    tx.gas = parseInt(tx.gas, 16);\n                }\n                const resp = await this.sdk.txs.send({\n                    txs: [tx],\n                    params: { safeTxGas: tx.gas },\n                });\n                // Store fake transaction\n                this.submittedTxs.set(resp.safeTxHash, {\n                    from: this.safe.safeAddress,\n                    hash: resp.safeTxHash,\n                    gas: 0,\n                    gasPrice: '0x00',\n                    nonce: 0,\n                    input: tx.data,\n                    value: tx.value,\n                    to: tx.to,\n                    blockHash: null,\n                    blockNumber: null,\n                    transactionIndex: null,\n                });\n                return resp.safeTxHash;\n            case 'eth_blockNumber':\n                const block = await this.sdk.eth.getBlockByNumber(['latest']);\n                return block.number;\n            case 'eth_getBalance':\n                return this.sdk.eth.getBalance([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getCode':\n                return this.sdk.eth.getCode([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getTransactionCount':\n                return this.sdk.eth.getTransactionCount([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getStorageAt':\n                return this.sdk.eth.getStorageAt([(0, utils_1.getLowerCase)(params[0]), params[1], params[2]]);\n            case 'eth_getBlockByNumber':\n                return this.sdk.eth.getBlockByNumber([params[0], params[1]]);\n            case 'eth_getBlockByHash':\n                return this.sdk.eth.getBlockByHash([params[0], params[1]]);\n            case 'eth_getTransactionByHash':\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                // Use fake transaction if we don't have a real tx hash\n                if (this.submittedTxs.has(txHash)) {\n                    return this.submittedTxs.get(txHash);\n                }\n                return this.sdk.eth.getTransactionByHash([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.hash = params[0];\n                    }\n                    return tx;\n                });\n            case 'eth_getTransactionReceipt': {\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                return this.sdk.eth.getTransactionReceipt([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.transactionHash = params[0];\n                    }\n                    return tx;\n                });\n            }\n            case 'eth_estimateGas': {\n                return this.sdk.eth.getEstimateGas(params[0]);\n            }\n            case 'eth_call': {\n                return this.sdk.eth.call([params[0], params[1]]);\n            }\n            case 'eth_getLogs':\n                return this.sdk.eth.getPastLogs([params[0]]);\n            case 'eth_gasPrice':\n                return this.sdk.eth.getGasPrice();\n            case 'wallet_getPermissions':\n                return this.sdk.wallet.getPermissions();\n            case 'wallet_requestPermissions':\n                return this.sdk.wallet.requestPermissions(params[0]);\n            case 'safe_setSettings':\n                return this.sdk.eth.setSafeSettings([params[0]]);\n            default:\n                throw Error(`\"${request.method}\" not implemented`);\n        }\n    }\n    // this method is needed for ethers v4\n    // https://github.com/ethers-io/ethers.js/blob/427e16826eb15d52d25c4f01027f8db22b74b76c/src.ts/providers/web3-provider.ts#L41-L55\n    send(request, callback) {\n        if (!request)\n            callback('Undefined request');\n        this.request(request)\n            .then((result) => callback(null, { jsonrpc: '2.0', id: request.id, result }))\n            .catch((error) => callback(error, null));\n    }\n}\nexports.SafeAppProvider = SafeAppProvider;\n//# sourceMappingURL=provider.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-provider/dist/provider.js?");

/***/ }),

/***/ "./node_modules/@safe-global/safe-apps-provider/dist/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getLowerCase = void 0;\nfunction getLowerCase(value) {\n    if (value) {\n        return value.toLowerCase();\n    }\n    return value;\n}\nexports.getLowerCase = getLowerCase;\n//# sourceMappingURL=utils.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@safe-global/safe-apps-provider/dist/utils.js?");

/***/ })

}]);