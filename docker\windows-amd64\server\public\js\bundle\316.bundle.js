"use strict";(self.webpackChunkfula_webui=self.webpackChunkfula_webui||[]).push([[316],{31316:(e,a,t)=>{t.d(a,{offchainLookup:()=>g,offchainLookupSignature:()=>y});var r=t(50853),s=t(18463),n=t(26329),o=t(61526);class c extends n.C{constructor({callbackSelector:e,cause:a,data:t,extraData:r,sender:s,urls:n}){super(a.shortMessage||"An error occurred while fetching for an offchain result.",{cause:a,metaMessages:[...a.metaMessages||[],a.metaMessages?.length?"":[],"Offchain Gateway Call:",n&&["  Gateway URL(s):",...n.map((e=>`    ${(0,o.ID)(e)}`))],`  Sender: ${s}`,`  Data: ${t}`,`  Callback selector: ${e}`,`  Extra data: ${r}`].flat()}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"OffchainLookupError"})}}class u extends n.C{constructor({result:e,url:a}){super("Offchain gateway response is malformed. Response data must be a hex value.",{metaMessages:[`Gateway URL: ${(0,o.ID)(a)}`,`Response: ${(0,s.A)(e)}`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"OffchainLookupResponseMalformedError"})}}class d extends n.C{constructor({sender:e,to:a}){super("Reverted sender address does not match target contract address (`to`).",{metaMessages:[`Contract address: ${a}`,`OffchainLookup sender address: ${e}`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"OffchainLookupSenderMismatchError"})}}var l=t(76595),i=t(15462),f=t(94531),h=t(14306),p=t(29873),b=t(25419),w=t(46394);const y="0x556f1830",m={name:"OffchainLookup",type:"error",inputs:[{name:"sender",type:"address"},{name:"urls",type:"string[]"},{name:"callData",type:"bytes"},{name:"callbackFunction",type:"bytes4"},{name:"extraData",type:"bytes"}]};async function g(e,{blockNumber:a,blockTag:t,data:s,to:n}){const{args:o}=(0,i.W)({data:s,abi:[m]}),[u,l,w,y,g]=o,{ccipRead:C}=e,O=C&&"function"==typeof C?.request?C.request:k;try{if(!function(e,a){if(!(0,p.P)(e,{strict:!1}))throw new h.M({address:e});if(!(0,p.P)(a,{strict:!1}))throw new h.M({address:a});return e.toLowerCase()===a.toLowerCase()}(n,u))throw new d({sender:u,to:n});const s=await O({data:w,sender:u,urls:l}),{data:o}=await(0,r.T)(e,{blockNumber:a,blockTag:t,data:(0,b.xW)([y,(0,f.h)([{type:"bytes"},{type:"bytes"}],[s,g])]),to:n});return o}catch(e){throw new c({callbackSelector:y,cause:e,data:s,extraData:g,sender:u,urls:l})}}async function k({data:e,sender:a,urls:t}){let r=new Error("An unknown error occurred.");for(let n=0;n<t.length;n++){const o=t[n],c=o.includes("{data}")?"GET":"POST",d="POST"===c?{data:e,sender:a}:void 0;try{const t=await fetch(o.replace("{sender}",a).replace("{data}",e),{body:JSON.stringify(d),method:c});let n;if(n=t.headers.get("Content-Type")?.startsWith("application/json")?(await t.json()).data:await t.text(),!t.ok){r=new l.Ci({body:d,details:n?.error?(0,s.A)(n.error):t.statusText,headers:t.headers,status:t.status,url:o});continue}if(!(0,w.q)(n)){r=new u({result:n,url:o});continue}return n}catch(e){r=new l.Ci({body:d,details:e.message,url:o})}}throw r}}}]);