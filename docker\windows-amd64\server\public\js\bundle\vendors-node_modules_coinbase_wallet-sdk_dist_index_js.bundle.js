/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkfula_webui"] = self["webpackChunkfula_webui"] || []).push([["vendors-node_modules_coinbase_wallet-sdk_dist_index_js"],{

/***/ "./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CoinbaseWalletProvider = void 0;\nconst eventemitter3_1 = __importDefault(__webpack_require__(/*! eventemitter3 */ \"./node_modules/eventemitter3/index.js\"));\nconst error_1 = __webpack_require__(/*! ./core/error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nconst serialize_1 = __webpack_require__(/*! ./core/error/serialize */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js\");\nconst type_1 = __webpack_require__(/*! ./core/type */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js\");\nconst util_1 = __webpack_require__(/*! ./core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nconst util_2 = __webpack_require__(/*! ./sign/util */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/util.js\");\nconst provider_1 = __webpack_require__(/*! ./util/provider */ \"./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\");\nconst Communicator_1 = __webpack_require__(/*! ./core/communicator/Communicator */ \"./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js\");\nconst method_1 = __webpack_require__(/*! ./core/provider/method */ \"./node_modules/@coinbase/wallet-sdk/dist/core/provider/method.js\");\nconst ScopedLocalStorage_1 = __webpack_require__(/*! ./util/ScopedLocalStorage */ \"./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js\");\nclass CoinbaseWalletProvider extends eventemitter3_1.default {\n    constructor(_a) {\n        var _b, _c;\n        var { metadata } = _a, _d = _a.preference, { keysUrl } = _d, preference = __rest(_d, [\"keysUrl\"]);\n        super();\n        this.accounts = [];\n        this.handlers = {\n            // eth_requestAccounts\n            handshake: async (_) => {\n                try {\n                    if (this.connected) {\n                        this.emit('connect', { chainId: (0, util_1.hexStringFromIntNumber)((0, type_1.IntNumber)(this.chain.id)) });\n                        return this.accounts;\n                    }\n                    const signerType = await this.requestSignerSelection();\n                    const signer = this.initSigner(signerType);\n                    const accounts = await signer.handshake();\n                    this.signer = signer;\n                    (0, util_2.storeSignerType)(signerType);\n                    this.emit('connect', { chainId: (0, util_1.hexStringFromIntNumber)((0, type_1.IntNumber)(this.chain.id)) });\n                    return accounts;\n                }\n                catch (error) {\n                    this.handleUnauthorizedError(error);\n                    throw error;\n                }\n            },\n            sign: async (request) => {\n                if (!this.connected || !this.signer) {\n                    throw error_1.standardErrors.provider.unauthorized(\"Must call 'eth_requestAccounts' before other methods\");\n                }\n                try {\n                    return await this.signer.request(request);\n                }\n                catch (error) {\n                    this.handleUnauthorizedError(error);\n                    throw error;\n                }\n            },\n            fetch: (request) => (0, provider_1.fetchRPCRequest)(request, this.chain),\n            state: (request) => {\n                const getConnectedAccounts = () => {\n                    if (this.connected)\n                        return this.accounts;\n                    throw error_1.standardErrors.provider.unauthorized(\"Must call 'eth_requestAccounts' before other methods\");\n                };\n                switch (request.method) {\n                    case 'eth_chainId':\n                        return (0, util_1.hexStringFromIntNumber)((0, type_1.IntNumber)(this.chain.id));\n                    case 'net_version':\n                        return this.chain.id;\n                    case 'eth_accounts':\n                        return getConnectedAccounts();\n                    case 'eth_coinbase':\n                        return getConnectedAccounts()[0];\n                    default:\n                        return this.handlers.unsupported(request);\n                }\n            },\n            deprecated: ({ method }) => {\n                throw error_1.standardErrors.rpc.methodNotSupported(`Method ${method} is deprecated.`);\n            },\n            unsupported: ({ method }) => {\n                throw error_1.standardErrors.rpc.methodNotSupported(`Method ${method} is not supported.`);\n            },\n        };\n        this.isCoinbaseWallet = true;\n        this.updateListener = {\n            onAccountsUpdate: ({ accounts, source }) => {\n                if ((0, util_1.areAddressArraysEqual)(this.accounts, accounts))\n                    return;\n                this.accounts = accounts;\n                if (source === 'storage')\n                    return;\n                this.emit('accountsChanged', this.accounts);\n            },\n            onChainUpdate: ({ chain, source }) => {\n                if (chain.id === this.chain.id && chain.rpcUrl === this.chain.rpcUrl)\n                    return;\n                this.chain = chain;\n                if (source === 'storage')\n                    return;\n                this.emit('chainChanged', (0, util_1.hexStringFromIntNumber)((0, type_1.IntNumber)(chain.id)));\n            },\n        };\n        this.metadata = metadata;\n        this.preference = preference;\n        this.communicator = new Communicator_1.Communicator(keysUrl);\n        this.chain = {\n            id: (_c = (_b = metadata.appChainIds) === null || _b === void 0 ? void 0 : _b[0]) !== null && _c !== void 0 ? _c : 1,\n        };\n        // Load states from storage\n        const signerType = (0, util_2.loadSignerType)();\n        this.signer = signerType ? this.initSigner(signerType) : null;\n    }\n    get connected() {\n        return this.accounts.length > 0;\n    }\n    async request(args) {\n        var _a;\n        try {\n            const invalidArgsError = (0, provider_1.checkErrorForInvalidRequestArgs)(args);\n            if (invalidArgsError)\n                throw invalidArgsError;\n            // unrecognized methods are treated as fetch requests\n            const category = (_a = (0, method_1.determineMethodCategory)(args.method)) !== null && _a !== void 0 ? _a : 'fetch';\n            return this.handlers[category](args);\n        }\n        catch (error) {\n            return Promise.reject((0, serialize_1.serializeError)(error, args.method));\n        }\n    }\n    handleUnauthorizedError(error) {\n        const e = error;\n        if (e.code === error_1.standardErrorCodes.provider.unauthorized)\n            this.disconnect();\n    }\n    /** @deprecated Use `.request({ method: 'eth_requestAccounts' })` instead. */\n    async enable() {\n        console.warn(`.enable() has been deprecated. Please use .request({ method: \"eth_requestAccounts\" }) instead.`);\n        return await this.request({\n            method: 'eth_requestAccounts',\n        });\n    }\n    async disconnect() {\n        this.accounts = [];\n        this.chain = { id: 1 };\n        ScopedLocalStorage_1.ScopedLocalStorage.clearAll();\n        this.emit('disconnect', error_1.standardErrors.provider.disconnected('User initiated disconnection'));\n    }\n    requestSignerSelection() {\n        return (0, util_2.fetchSignerType)({\n            communicator: this.communicator,\n            preference: this.preference,\n            metadata: this.metadata,\n        });\n    }\n    initSigner(signerType) {\n        return (0, util_2.createSigner)({\n            signerType,\n            metadata: this.metadata,\n            communicator: this.communicator,\n            updateListener: this.updateListener,\n        });\n    }\n}\nexports.CoinbaseWalletProvider = CoinbaseWalletProvider;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletSDK.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletSDK.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// Copyright (c) 2018-2024 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CoinbaseWalletSDK = void 0;\nconst wallet_logo_1 = __webpack_require__(/*! ./assets/wallet-logo */ \"./node_modules/@coinbase/wallet-sdk/dist/assets/wallet-logo.js\");\nconst CoinbaseWalletProvider_1 = __webpack_require__(/*! ./CoinbaseWalletProvider */ \"./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js\");\nconst ScopedLocalStorage_1 = __webpack_require__(/*! ./util/ScopedLocalStorage */ \"./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js\");\nconst version_1 = __webpack_require__(/*! ./version */ \"./node_modules/@coinbase/wallet-sdk/dist/version.js\");\nconst util_1 = __webpack_require__(/*! ./core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nconst provider_1 = __webpack_require__(/*! ./util/provider */ \"./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\");\nclass CoinbaseWalletSDK {\n    constructor(metadata) {\n        this.metadata = {\n            appName: metadata.appName || 'Dapp',\n            appLogoUrl: metadata.appLogoUrl || (0, util_1.getFavicon)(),\n            appChainIds: metadata.appChainIds || [],\n        };\n        this.storeLatestVersion();\n    }\n    makeWeb3Provider(preference = { options: 'all' }) {\n        var _a;\n        const params = { metadata: this.metadata, preference };\n        return (_a = (0, provider_1.getCoinbaseInjectedProvider)(params)) !== null && _a !== void 0 ? _a : new CoinbaseWalletProvider_1.CoinbaseWalletProvider(params);\n    }\n    /**\n     * Official Coinbase Wallet logo for developers to use on their frontend\n     * @param type Type of wallet logo: \"standard\" | \"circle\" | \"text\" | \"textWithLogo\" | \"textLight\" | \"textWithLogoLight\"\n     * @param width Width of the logo (Optional)\n     * @returns SVG Data URI\n     */\n    getCoinbaseWalletLogo(type, width = 240) {\n        return (0, wallet_logo_1.walletLogo)(type, width);\n    }\n    storeLatestVersion() {\n        const versionStorage = new ScopedLocalStorage_1.ScopedLocalStorage('CBWSDK');\n        versionStorage.setItem('VERSION', version_1.LIB_VERSION);\n    }\n}\nexports.CoinbaseWalletSDK = CoinbaseWalletSDK;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletSDK.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/assets/wallet-logo.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/assets/wallet-logo.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.walletLogo = void 0;\nconst walletLogo = (type, width) => {\n    let height;\n    switch (type) {\n        case 'standard':\n            height = width;\n            return `data:image/svg+xml,%3Csvg width='${width}' height='${height}' viewBox='0 0 1024 1024' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Crect width='1024' height='1024' fill='%230052FF'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M152 512C152 710.823 313.177 872 512 872C710.823 872 872 710.823 872 512C872 313.177 710.823 152 512 152C313.177 152 152 313.177 152 512ZM420 396C406.745 396 396 406.745 396 420V604C396 617.255 406.745 628 420 628H604C617.255 628 628 617.255 628 604V420C628 406.745 617.255 396 604 396H420Z' fill='white'/%3E %3C/svg%3E `;\n        case 'circle':\n            height = width;\n            return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${width}' height='${height}' viewBox='0 0 999.81 999.81'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052fe;%7D.cls-2%7Bfill:%23fefefe;%7D.cls-3%7Bfill:%230152fe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M655-115.9h56c.83,1.59,2.36.88,3.56,1a478,478,0,0,1,75.06,10.42C891.4-81.76,978.33-32.58,1049.19,44q116.7,126,131.94,297.61c.38,4.14-.34,8.53,1.78,12.45v59c-1.58.84-.91,2.35-1,3.56a482.05,482.05,0,0,1-10.38,74.05c-24,106.72-76.64,196.76-158.83,268.93s-178.18,112.82-287.2,122.6c-4.83.43-9.86-.25-14.51,1.77H654c-1-1.68-2.69-.91-4.06-1a496.89,496.89,0,0,1-105.9-18.59c-93.54-27.42-172.78-77.59-236.91-150.94Q199.34,590.1,184.87,426.58c-.47-5.19.25-10.56-1.77-15.59V355c1.68-1,.91-2.7,1-4.06a498.12,498.12,0,0,1,18.58-105.9c26-88.75,72.64-164.9,140.6-227.57q126-116.27,297.21-131.61C645.32-114.57,650.35-113.88,655-115.9Zm377.92,500c0-192.44-156.31-349.49-347.56-350.15-194.13-.68-350.94,155.13-352.29,347.42-1.37,194.55,155.51,352.1,348.56,352.47C876.15,734.23,1032.93,577.84,1032.93,384.11Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-2' d='M1032.93,384.11c0,193.73-156.78,350.12-351.29,349.74-193-.37-349.93-157.92-348.56-352.47C334.43,189.09,491.24,33.28,685.37,34,876.62,34.62,1032.94,191.67,1032.93,384.11ZM683,496.81q43.74,0,87.48,0c15.55,0,25.32-9.72,25.33-25.21q0-87.48,0-175c0-15.83-9.68-25.46-25.59-25.46H595.77c-15.88,0-25.57,9.64-25.58,25.46q0,87.23,0,174.45c0,16.18,9.59,25.7,25.84,25.71Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-3' d='M683,496.81H596c-16.25,0-25.84-9.53-25.84-25.71q0-87.23,0-174.45c0-15.82,9.7-25.46,25.58-25.46H770.22c15.91,0,25.59,9.63,25.59,25.46q0,87.47,0,175c0,15.49-9.78,25.2-25.33,25.21Q726.74,496.84,683,496.81Z' transform='translate(-183.1 115.9)'/%3E%3C/svg%3E`;\n        case 'text':\n            height = (0.1 * width).toFixed(2);\n            return `data:image/svg+xml,%3Csvg width='${width}' height='${height}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;\n        case 'textWithLogo':\n            height = (0.25 * width).toFixed(2);\n            return `data:image/svg+xml,%3Csvg width='${width}' height='${height}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`;\n        case 'textLight':\n            height = (0.1 * width).toFixed(2);\n            return `data:image/svg+xml,%3Csvg width='${width}' height='${height}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;\n        case 'textWithLogoLight':\n            height = (0.25 * width).toFixed(2);\n            return `data:image/svg+xml,%3Csvg width='${width}' height='${height}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`;\n        default:\n            height = width;\n            return `data:image/svg+xml,%3Csvg width='${width}' height='${height}' viewBox='0 0 1024 1024' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Crect width='1024' height='1024' fill='%230052FF'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M152 512C152 710.823 313.177 872 512 872C710.823 872 872 710.823 872 512C872 313.177 710.823 152 512 152C313.177 152 152 313.177 152 512ZM420 396C406.745 396 396 406.745 396 420V604C396 617.255 406.745 628 420 628H604C617.255 628 628 617.255 628 604V420C628 406.745 617.255 396 604 396H420Z' fill='white'/%3E %3C/svg%3E `;\n    }\n};\nexports.walletLogo = walletLogo;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/assets/wallet-logo.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Communicator = void 0;\nconst version_1 = __webpack_require__(/*! ../../version */ \"./node_modules/@coinbase/wallet-sdk/dist/version.js\");\nconst util_1 = __webpack_require__(/*! ./util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/communicator/util.js\");\nconst constants_1 = __webpack_require__(/*! ../constants */ \"./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\");\nconst error_1 = __webpack_require__(/*! ../error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\n/**\n * Communicates with a popup window for Coinbase keys.coinbase.com (or another url)\n * to send and receive messages.\n *\n * This class is responsible for opening a popup window, posting messages to it,\n * and listening for responses.\n *\n * It also handles cleanup of event listeners and the popup window itself when necessary.\n */\nclass Communicator {\n    constructor(url = constants_1.CB_KEYS_URL) {\n        this.popup = null;\n        this.listeners = new Map();\n        /**\n         * Posts a message to the popup window\n         */\n        this.postMessage = async (message) => {\n            const popup = await this.waitForPopupLoaded();\n            popup.postMessage(message, this.url.origin);\n        };\n        /**\n         * Posts a request to the popup window and waits for a response\n         */\n        this.postRequestAndWaitForResponse = async (request) => {\n            const responsePromise = this.onMessage(({ requestId }) => requestId === request.id);\n            this.postMessage(request);\n            return await responsePromise;\n        };\n        /**\n         * Listens for messages from the popup window that match a given predicate.\n         */\n        this.onMessage = async (predicate) => {\n            return new Promise((resolve, reject) => {\n                const listener = (event) => {\n                    if (event.origin !== this.url.origin)\n                        return; // origin validation\n                    const message = event.data;\n                    if (predicate(message)) {\n                        resolve(message);\n                        window.removeEventListener('message', listener);\n                        this.listeners.delete(listener);\n                    }\n                };\n                window.addEventListener('message', listener);\n                this.listeners.set(listener, { reject });\n            });\n        };\n        /**\n         * Closes the popup, rejects all requests and clears the listeners\n         */\n        this.disconnect = () => {\n            (0, util_1.closePopup)(this.popup);\n            this.popup = null;\n            this.listeners.forEach(({ reject }, listener) => {\n                reject(error_1.standardErrors.provider.userRejectedRequest('Request rejected'));\n                window.removeEventListener('message', listener);\n            });\n            this.listeners.clear();\n        };\n        /**\n         * Waits for the popup window to fully load and then sends a version message.\n         */\n        this.waitForPopupLoaded = async () => {\n            if (this.popup && !this.popup.closed) {\n                // In case the user un-focused the popup between requests, focus it again\n                this.popup.focus();\n                return this.popup;\n            }\n            this.popup = (0, util_1.openPopup)(this.url);\n            this.onMessage(({ event }) => event === 'PopupUnload')\n                .then(this.disconnect)\n                .catch(() => { });\n            return this.onMessage(({ event }) => event === 'PopupLoaded')\n                .then((message) => {\n                this.postMessage({\n                    requestId: message.id,\n                    data: { version: version_1.LIB_VERSION },\n                });\n            })\n                .then(() => {\n                if (!this.popup)\n                    throw error_1.standardErrors.rpc.internal();\n                return this.popup;\n            });\n        };\n        this.url = new URL(url);\n    }\n}\nexports.Communicator = Communicator;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/communicator/util.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/communicator/util.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.closePopup = exports.openPopup = void 0;\nconst error_1 = __webpack_require__(/*! ../error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nconst POPUP_WIDTH = 420;\nconst POPUP_HEIGHT = 540;\n// Window Management\nfunction openPopup(url) {\n    const left = (window.innerWidth - POPUP_WIDTH) / 2 + window.screenX;\n    const top = (window.innerHeight - POPUP_HEIGHT) / 2 + window.screenY;\n    const popup = window.open(url, 'Smart Wallet', `width=${POPUP_WIDTH}, height=${POPUP_HEIGHT}, left=${left}, top=${top}`);\n    popup === null || popup === void 0 ? void 0 : popup.focus();\n    if (!popup) {\n        throw error_1.standardErrors.rpc.internal('Pop up window failed to open');\n    }\n    return popup;\n}\nexports.openPopup = openPopup;\nfunction closePopup(popup) {\n    if (popup && !popup.closed) {\n        popup.close();\n    }\n}\nexports.closePopup = closePopup;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/communicator/util.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/constants.js":
/*!******************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/constants.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CBW_MOBILE_DEEPLINK_URL = exports.WALLETLINK_URL = exports.CB_KEYS_URL = void 0;\nexports.CB_KEYS_URL = 'https://keys.coinbase.com/connect';\nexports.WALLETLINK_URL = 'https://www.walletlink.org';\nexports.CBW_MOBILE_DEEPLINK_URL = 'https://go.cb-w.com/walletlink';\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/constants.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js":
/*!************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.errorValues = exports.standardErrorCodes = void 0;\nexports.standardErrorCodes = {\n    rpc: {\n        invalidInput: -32000,\n        resourceNotFound: -32001,\n        resourceUnavailable: -32002,\n        transactionRejected: -32003,\n        methodNotSupported: -32004,\n        limitExceeded: -32005,\n        parse: -32700,\n        invalidRequest: -32600,\n        methodNotFound: -32601,\n        invalidParams: -32602,\n        internal: -32603,\n    },\n    provider: {\n        userRejectedRequest: 4001,\n        unauthorized: 4100,\n        unsupportedMethod: 4200,\n        disconnected: 4900,\n        chainDisconnected: 4901,\n        unsupportedChain: 4902,\n    },\n};\nexports.errorValues = {\n    '-32700': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n    },\n    '-32600': {\n        standard: 'JSON RPC 2.0',\n        message: 'The JSON sent is not a valid Request object.',\n    },\n    '-32601': {\n        standard: 'JSON RPC 2.0',\n        message: 'The method does not exist / is not available.',\n    },\n    '-32602': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid method parameter(s).',\n    },\n    '-32603': {\n        standard: 'JSON RPC 2.0',\n        message: 'Internal JSON-RPC error.',\n    },\n    '-32000': {\n        standard: 'EIP-1474',\n        message: 'Invalid input.',\n    },\n    '-32001': {\n        standard: 'EIP-1474',\n        message: 'Resource not found.',\n    },\n    '-32002': {\n        standard: 'EIP-1474',\n        message: 'Resource unavailable.',\n    },\n    '-32003': {\n        standard: 'EIP-1474',\n        message: 'Transaction rejected.',\n    },\n    '-32004': {\n        standard: 'EIP-1474',\n        message: 'Method not supported.',\n    },\n    '-32005': {\n        standard: 'EIP-1474',\n        message: 'Request limit exceeded.',\n    },\n    '4001': {\n        standard: 'EIP-1193',\n        message: 'User rejected the request.',\n    },\n    '4100': {\n        standard: 'EIP-1193',\n        message: 'The requested account and/or method has not been authorized by the user.',\n    },\n    '4200': {\n        standard: 'EIP-1193',\n        message: 'The requested method is not supported by this Ethereum provider.',\n    },\n    '4900': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from all chains.',\n    },\n    '4901': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from the specified chain.',\n    },\n    '4902': {\n        standard: 'EIP-3085',\n        message: 'Unrecognized chain ID.',\n    },\n};\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.standardErrors = void 0;\nconst constants_1 = __webpack_require__(/*! ./constants */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js\");\nexports.standardErrors = {\n    rpc: {\n        parse: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.parse, arg),\n        invalidRequest: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.invalidRequest, arg),\n        invalidParams: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.invalidParams, arg),\n        methodNotFound: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.methodNotFound, arg),\n        internal: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.internal, arg),\n        server: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum RPC Server errors must provide single object argument.');\n            }\n            const { code } = opts;\n            if (!Number.isInteger(code) || code > -32005 || code < -32099) {\n                throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');\n            }\n            return getEthJsonRpcError(code, opts);\n        },\n        invalidInput: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.invalidInput, arg),\n        resourceNotFound: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.resourceNotFound, arg),\n        resourceUnavailable: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.resourceUnavailable, arg),\n        transactionRejected: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.transactionRejected, arg),\n        methodNotSupported: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.methodNotSupported, arg),\n        limitExceeded: (arg) => getEthJsonRpcError(constants_1.standardErrorCodes.rpc.limitExceeded, arg),\n    },\n    provider: {\n        userRejectedRequest: (arg) => {\n            return getEthProviderError(constants_1.standardErrorCodes.provider.userRejectedRequest, arg);\n        },\n        unauthorized: (arg) => {\n            return getEthProviderError(constants_1.standardErrorCodes.provider.unauthorized, arg);\n        },\n        unsupportedMethod: (arg) => {\n            return getEthProviderError(constants_1.standardErrorCodes.provider.unsupportedMethod, arg);\n        },\n        disconnected: (arg) => {\n            return getEthProviderError(constants_1.standardErrorCodes.provider.disconnected, arg);\n        },\n        chainDisconnected: (arg) => {\n            return getEthProviderError(constants_1.standardErrorCodes.provider.chainDisconnected, arg);\n        },\n        unsupportedChain: (arg) => {\n            return getEthProviderError(constants_1.standardErrorCodes.provider.unsupportedChain, arg);\n        },\n        custom: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum Provider custom errors must provide single object argument.');\n            }\n            const { code, message, data } = opts;\n            if (!message || typeof message !== 'string') {\n                throw new Error('\"message\" must be a nonempty string');\n            }\n            return new EthereumProviderError(code, message, data);\n        },\n    },\n};\n// Internal\nfunction getEthJsonRpcError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new EthereumRpcError(code, message || (0, utils_1.getMessageFromCode)(code), data);\n}\nfunction getEthProviderError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new EthereumProviderError(code, message || (0, utils_1.getMessageFromCode)(code), data);\n}\nfunction parseOpts(arg) {\n    if (arg) {\n        if (typeof arg === 'string') {\n            return [arg];\n        }\n        else if (typeof arg === 'object' && !Array.isArray(arg)) {\n            const { message, data } = arg;\n            if (message && typeof message !== 'string') {\n                throw new Error('Must specify string message.');\n            }\n            return [message || undefined, data];\n        }\n    }\n    return [];\n}\nclass EthereumRpcError extends Error {\n    constructor(code, message, data) {\n        if (!Number.isInteger(code)) {\n            throw new Error('\"code\" must be an integer.');\n        }\n        if (!message || typeof message !== 'string') {\n            throw new Error('\"message\" must be a nonempty string.');\n        }\n        super(message);\n        this.code = code;\n        if (data !== undefined) {\n            this.data = data;\n        }\n    }\n}\nclass EthereumProviderError extends EthereumRpcError {\n    /**\n     * Create an Ethereum Provider JSON-RPC error.\n     * `code` must be an integer in the 1000 <= 4999 range.\n     */\n    constructor(code, message, data) {\n        if (!isValidEthProviderCode(code)) {\n            throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');\n        }\n        super(code, message, data);\n    }\n}\nfunction isValidEthProviderCode(code) {\n    return Number.isInteger(code) && code >= 1000 && code <= 4999;\n}\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.standardErrors = exports.standardErrorCodes = void 0;\nvar constants_1 = __webpack_require__(/*! ./constants */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\nObject.defineProperty(exports, \"standardErrorCodes\", ({ enumerable: true, get: function () { return constants_1.standardErrorCodes; } }));\nvar errors_1 = __webpack_require__(/*! ./errors */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\nObject.defineProperty(exports, \"standardErrors\", ({ enumerable: true, get: function () { return errors_1.standardErrors; } }));\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js":
/*!************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.serializeError = void 0;\n// TODO: error should not depend on walletlink. revisit this.\nconst Web3Response_1 = __webpack_require__(/*! ../../sign/walletlink/relay/type/Web3Response */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js\");\nconst version_1 = __webpack_require__(/*! ../../version */ \"./node_modules/@coinbase/wallet-sdk/dist/version.js\");\nconst constants_1 = __webpack_require__(/*! ./constants */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js\");\n/**\n * Serializes an error to a format that is compatible with the Ethereum JSON RPC error format.\n * See https://docs.cloud.coinbase.com/wallet-sdk/docs/errors\n * for more information.\n */\nfunction serializeError(error, requestOrMethod) {\n    const serialized = (0, utils_1.serialize)(getErrorObject(error), {\n        shouldIncludeStack: true,\n    });\n    const docUrl = new URL('https://docs.cloud.coinbase.com/wallet-sdk/docs/errors');\n    docUrl.searchParams.set('version', version_1.LIB_VERSION);\n    docUrl.searchParams.set('code', serialized.code.toString());\n    const method = getMethod(serialized.data, requestOrMethod);\n    if (method) {\n        docUrl.searchParams.set('method', method);\n    }\n    docUrl.searchParams.set('message', serialized.message);\n    return Object.assign(Object.assign({}, serialized), { docUrl: docUrl.href });\n}\nexports.serializeError = serializeError;\n/**\n * Converts an error to a serializable object.\n */\nfunction getErrorObject(error) {\n    if (typeof error === 'string') {\n        return {\n            message: error,\n            code: constants_1.standardErrorCodes.rpc.internal,\n        };\n    }\n    else if ((0, Web3Response_1.isErrorResponse)(error)) {\n        return Object.assign(Object.assign({}, error), { message: error.errorMessage, code: error.errorCode, data: { method: error.method } });\n    }\n    return error;\n}\n/**\n * Gets the method name from the serialized data or the request.\n */\nfunction getMethod(serializedData, request) {\n    const methodInData = serializedData === null || serializedData === void 0 ? void 0 : serializedData.method;\n    if (methodInData) {\n        return methodInData;\n    }\n    if (request === undefined) {\n        return undefined;\n    }\n    else if (typeof request === 'string') {\n        return request;\n    }\n    else if (!Array.isArray(request)) {\n        return request.method;\n    }\n    else if (request.length > 0) {\n        return request[0].method;\n    }\n    return undefined;\n}\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.serialize = exports.getErrorCode = exports.isValidCode = exports.getMessageFromCode = exports.JSON_RPC_SERVER_ERROR_MESSAGE = void 0;\nconst constants_1 = __webpack_require__(/*! ./constants */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\nconst FALLBACK_MESSAGE = 'Unspecified error message.';\nexports.JSON_RPC_SERVER_ERROR_MESSAGE = 'Unspecified server error.';\n/**\n * Gets the message for a given code, or a fallback message if the code has\n * no corresponding message.\n */\nfunction getMessageFromCode(code, fallbackMessage = FALLBACK_MESSAGE) {\n    if (code && Number.isInteger(code)) {\n        const codeString = code.toString();\n        if (hasKey(constants_1.errorValues, codeString)) {\n            return constants_1.errorValues[codeString].message;\n        }\n        if (isJsonRpcServerError(code)) {\n            return exports.JSON_RPC_SERVER_ERROR_MESSAGE;\n        }\n    }\n    return fallbackMessage;\n}\nexports.getMessageFromCode = getMessageFromCode;\n/**\n * Returns whether the given code is valid.\n * A code is only valid if it has a message.\n */\nfunction isValidCode(code) {\n    if (!Number.isInteger(code)) {\n        return false;\n    }\n    const codeString = code.toString();\n    if (constants_1.errorValues[codeString]) {\n        return true;\n    }\n    if (isJsonRpcServerError(code)) {\n        return true;\n    }\n    return false;\n}\nexports.isValidCode = isValidCode;\n/**\n * Returns the error code from an error object.\n */\nfunction getErrorCode(error) {\n    var _a;\n    if (typeof error === 'number') {\n        return error;\n    }\n    else if (isErrorWithCode(error)) {\n        return (_a = error.code) !== null && _a !== void 0 ? _a : error.errorCode;\n    }\n    return undefined;\n}\nexports.getErrorCode = getErrorCode;\nfunction isErrorWithCode(error) {\n    return (typeof error === 'object' &&\n        error !== null &&\n        (typeof error.code === 'number' ||\n            typeof error.errorCode === 'number'));\n}\nfunction serialize(error, { shouldIncludeStack = false } = {}) {\n    const serialized = {};\n    if (error &&\n        typeof error === 'object' &&\n        !Array.isArray(error) &&\n        hasKey(error, 'code') &&\n        isValidCode(error.code)) {\n        const _error = error;\n        serialized.code = _error.code;\n        if (_error.message && typeof _error.message === 'string') {\n            serialized.message = _error.message;\n            if (hasKey(_error, 'data')) {\n                serialized.data = _error.data;\n            }\n        }\n        else {\n            serialized.message = getMessageFromCode(serialized.code);\n            serialized.data = { originalError: assignOriginalError(error) };\n        }\n    }\n    else {\n        serialized.code = constants_1.standardErrorCodes.rpc.internal;\n        serialized.message = hasStringProperty(error, 'message') ? error.message : FALLBACK_MESSAGE;\n        serialized.data = { originalError: assignOriginalError(error) };\n    }\n    if (shouldIncludeStack) {\n        serialized.stack = hasStringProperty(error, 'stack') ? error.stack : undefined;\n    }\n    return serialized;\n}\nexports.serialize = serialize;\n// Internal\nfunction isJsonRpcServerError(code) {\n    return code >= -32099 && code <= -32000;\n}\nfunction assignOriginalError(error) {\n    if (error && typeof error === 'object' && !Array.isArray(error)) {\n        return Object.assign({}, error);\n    }\n    return error;\n}\nfunction hasKey(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction hasStringProperty(obj, prop) {\n    return (typeof obj === 'object' && obj !== null && prop in obj && typeof obj[prop] === 'string');\n}\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/provider/method.js":
/*!************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/provider/method.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.determineMethodCategory = void 0;\nconst mapping = {\n    handshake: ['eth_requestAccounts'],\n    sign: [\n        'eth_ecRecover',\n        'personal_sign',\n        'personal_ecRecover',\n        'eth_signTransaction',\n        'eth_sendTransaction',\n        'eth_signTypedData_v1',\n        'eth_signTypedData_v3',\n        'eth_signTypedData_v4',\n        'eth_signTypedData',\n        'wallet_addEthereumChain',\n        'wallet_switchEthereumChain',\n        'wallet_watchAsset',\n        'wallet_getCapabilities',\n        'wallet_sendCalls',\n        'wallet_showCallsStatus',\n    ],\n    state: [\n        // internal state\n        'eth_chainId',\n        'eth_accounts',\n        'eth_coinbase',\n        'net_version',\n    ],\n    deprecated: ['eth_sign', 'eth_signTypedData_v2'],\n    unsupported: ['eth_subscribe', 'eth_unsubscribe'],\n    fetch: [],\n};\nfunction determineMethodCategory(method) {\n    for (const c in mapping) {\n        const category = c;\n        if (mapping[category].includes(method)) {\n            return category;\n        }\n    }\n    return undefined;\n}\nexports.determineMethodCategory = determineMethodCategory;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/provider/method.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RegExpString = exports.IntNumber = exports.BigIntString = exports.AddressString = exports.HexString = exports.OpaqueType = void 0;\nfunction OpaqueType() {\n    return (value) => value;\n}\nexports.OpaqueType = OpaqueType;\nexports.HexString = OpaqueType();\nexports.AddressString = OpaqueType();\nexports.BigIntString = OpaqueType();\nfunction IntNumber(num) {\n    return Math.floor(num);\n}\nexports.IntNumber = IntNumber;\nexports.RegExpString = OpaqueType();\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js":
/*!******************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.areAddressArraysEqual = exports.getFavicon = exports.range = exports.isBigNumber = exports.ensureParsedJSONObject = exports.ensureBigInt = exports.ensureRegExpString = exports.ensureIntNumber = exports.ensureBuffer = exports.ensureAddressString = exports.ensureEvenLengthHexString = exports.ensureHexString = exports.isHexString = exports.prepend0x = exports.strip0x = exports.has0xPrefix = exports.hexStringFromIntNumber = exports.intNumberFromHexString = exports.bigIntStringFromBigInt = exports.hexStringFromBuffer = exports.hexStringToUint8Array = exports.uint8ArrayToHex = exports.randomBytesHex = void 0;\nconst error_1 = __webpack_require__(/*! ../error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nconst _1 = __webpack_require__(/*! . */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js\");\nconst INT_STRING_REGEX = /^[0-9]*$/;\nconst HEXADECIMAL_STRING_REGEX = /^[a-f0-9]*$/;\n/**\n * @param length number of bytes\n */\nfunction randomBytesHex(length) {\n    return uint8ArrayToHex(crypto.getRandomValues(new Uint8Array(length)));\n}\nexports.randomBytesHex = randomBytesHex;\nfunction uint8ArrayToHex(value) {\n    return [...value].map((b) => b.toString(16).padStart(2, '0')).join('');\n}\nexports.uint8ArrayToHex = uint8ArrayToHex;\nfunction hexStringToUint8Array(hexString) {\n    return new Uint8Array(hexString.match(/.{1,2}/g).map((byte) => parseInt(byte, 16)));\n}\nexports.hexStringToUint8Array = hexStringToUint8Array;\nfunction hexStringFromBuffer(buf, includePrefix = false) {\n    const hex = buf.toString('hex');\n    return (0, _1.HexString)(includePrefix ? `0x${hex}` : hex);\n}\nexports.hexStringFromBuffer = hexStringFromBuffer;\nfunction bigIntStringFromBigInt(bi) {\n    return (0, _1.BigIntString)(bi.toString(10));\n}\nexports.bigIntStringFromBigInt = bigIntStringFromBigInt;\nfunction intNumberFromHexString(hex) {\n    return (0, _1.IntNumber)(Number(BigInt(ensureEvenLengthHexString(hex, true))));\n}\nexports.intNumberFromHexString = intNumberFromHexString;\nfunction hexStringFromIntNumber(num) {\n    return (0, _1.HexString)(`0x${BigInt(num).toString(16)}`);\n}\nexports.hexStringFromIntNumber = hexStringFromIntNumber;\nfunction has0xPrefix(str) {\n    return str.startsWith('0x') || str.startsWith('0X');\n}\nexports.has0xPrefix = has0xPrefix;\nfunction strip0x(hex) {\n    if (has0xPrefix(hex)) {\n        return hex.slice(2);\n    }\n    return hex;\n}\nexports.strip0x = strip0x;\nfunction prepend0x(hex) {\n    if (has0xPrefix(hex)) {\n        return `0x${hex.slice(2)}`;\n    }\n    return `0x${hex}`;\n}\nexports.prepend0x = prepend0x;\nfunction isHexString(hex) {\n    if (typeof hex !== 'string') {\n        return false;\n    }\n    const s = strip0x(hex).toLowerCase();\n    return HEXADECIMAL_STRING_REGEX.test(s);\n}\nexports.isHexString = isHexString;\nfunction ensureHexString(hex, includePrefix = false) {\n    if (typeof hex === 'string') {\n        const s = strip0x(hex).toLowerCase();\n        if (HEXADECIMAL_STRING_REGEX.test(s)) {\n            return (0, _1.HexString)(includePrefix ? `0x${s}` : s);\n        }\n    }\n    throw error_1.standardErrors.rpc.invalidParams(`\"${String(hex)}\" is not a hexadecimal string`);\n}\nexports.ensureHexString = ensureHexString;\nfunction ensureEvenLengthHexString(hex, includePrefix = false) {\n    let h = ensureHexString(hex, false);\n    if (h.length % 2 === 1) {\n        h = (0, _1.HexString)(`0${h}`);\n    }\n    return includePrefix ? (0, _1.HexString)(`0x${h}`) : h;\n}\nexports.ensureEvenLengthHexString = ensureEvenLengthHexString;\nfunction ensureAddressString(str) {\n    if (typeof str === 'string') {\n        const s = strip0x(str).toLowerCase();\n        if (isHexString(s) && s.length === 40) {\n            return (0, _1.AddressString)(prepend0x(s));\n        }\n    }\n    throw error_1.standardErrors.rpc.invalidParams(`Invalid Ethereum address: ${String(str)}`);\n}\nexports.ensureAddressString = ensureAddressString;\nfunction ensureBuffer(str) {\n    if (Buffer.isBuffer(str)) {\n        return str;\n    }\n    if (typeof str === 'string') {\n        if (isHexString(str)) {\n            const s = ensureEvenLengthHexString(str, false);\n            return Buffer.from(s, 'hex');\n        }\n        return Buffer.from(str, 'utf8');\n    }\n    throw error_1.standardErrors.rpc.invalidParams(`Not binary data: ${String(str)}`);\n}\nexports.ensureBuffer = ensureBuffer;\nfunction ensureIntNumber(num) {\n    if (typeof num === 'number' && Number.isInteger(num)) {\n        return (0, _1.IntNumber)(num);\n    }\n    if (typeof num === 'string') {\n        if (INT_STRING_REGEX.test(num)) {\n            return (0, _1.IntNumber)(Number(num));\n        }\n        if (isHexString(num)) {\n            return (0, _1.IntNumber)(Number(BigInt(ensureEvenLengthHexString(num, true))));\n        }\n    }\n    throw error_1.standardErrors.rpc.invalidParams(`Not an integer: ${String(num)}`);\n}\nexports.ensureIntNumber = ensureIntNumber;\nfunction ensureRegExpString(regExp) {\n    if (regExp instanceof RegExp) {\n        return (0, _1.RegExpString)(regExp.toString());\n    }\n    throw error_1.standardErrors.rpc.invalidParams(`Not a RegExp: ${String(regExp)}`);\n}\nexports.ensureRegExpString = ensureRegExpString;\nfunction ensureBigInt(val) {\n    if (val !== null && (typeof val === 'bigint' || isBigNumber(val))) {\n        return BigInt(val.toString(10));\n    }\n    if (typeof val === 'number') {\n        return BigInt(ensureIntNumber(val));\n    }\n    if (typeof val === 'string') {\n        if (INT_STRING_REGEX.test(val)) {\n            return BigInt(val);\n        }\n        if (isHexString(val)) {\n            return BigInt(ensureEvenLengthHexString(val, true));\n        }\n    }\n    throw error_1.standardErrors.rpc.invalidParams(`Not an integer: ${String(val)}`);\n}\nexports.ensureBigInt = ensureBigInt;\nfunction ensureParsedJSONObject(val) {\n    if (typeof val === 'string') {\n        return JSON.parse(val);\n    }\n    if (typeof val === 'object') {\n        return val;\n    }\n    throw error_1.standardErrors.rpc.invalidParams(`Not a JSON string or an object: ${String(val)}`);\n}\nexports.ensureParsedJSONObject = ensureParsedJSONObject;\nfunction isBigNumber(val) {\n    if (val == null || typeof val.constructor !== 'function') {\n        return false;\n    }\n    const { constructor } = val;\n    return typeof constructor.config === 'function' && typeof constructor.EUCLID === 'number';\n}\nexports.isBigNumber = isBigNumber;\nfunction range(start, stop) {\n    return Array.from({ length: stop - start }, (_, i) => start + i);\n}\nexports.range = range;\nfunction getFavicon() {\n    const el = document.querySelector('link[sizes=\"192x192\"]') ||\n        document.querySelector('link[sizes=\"180x180\"]') ||\n        document.querySelector('link[rel=\"icon\"]') ||\n        document.querySelector('link[rel=\"shortcut icon\"]');\n    const { protocol, host } = document.location;\n    const href = el ? el.getAttribute('href') : null;\n    if (!href || href.startsWith('javascript:') || href.startsWith('vbscript:')) {\n        return null;\n    }\n    if (href.startsWith('http://') || href.startsWith('https://') || href.startsWith('data:')) {\n        return href;\n    }\n    if (href.startsWith('//')) {\n        return protocol + href;\n    }\n    return `${protocol}//${host}${href}`;\n}\nexports.getFavicon = getFavicon;\nfunction areAddressArraysEqual(arr1, arr2) {\n    return arr1.length === arr2.length && arr1.every((value, index) => value === arr2[index]);\n}\nexports.areAddressArraysEqual = areAddressArraysEqual;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CoinbaseWalletSDK = void 0;\n// Copyright (c) 2018-2024 Coinbase, Inc. <https://www.coinbase.com/>\nconst CoinbaseWalletSDK_1 = __webpack_require__(/*! ./CoinbaseWalletSDK */ \"./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletSDK.js\");\nexports[\"default\"] = CoinbaseWalletSDK_1.CoinbaseWalletSDK;\nvar CoinbaseWalletSDK_2 = __webpack_require__(/*! ./CoinbaseWalletSDK */ \"./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletSDK.js\");\nObject.defineProperty(exports, \"CoinbaseWalletSDK\", ({ enumerable: true, get: function () { return CoinbaseWalletSDK_2.CoinbaseWalletSDK; } }));\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/index.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SCWKeyManager = void 0;\nconst cipher_1 = __webpack_require__(/*! ../../util/cipher */ \"./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js\");\nconst ScopedLocalStorage_1 = __webpack_require__(/*! ../../util/ScopedLocalStorage */ \"./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js\");\nconst OWN_PRIVATE_KEY = {\n    storageKey: 'ownPrivateKey',\n    keyType: 'private',\n};\nconst OWN_PUBLIC_KEY = {\n    storageKey: 'ownPublicKey',\n    keyType: 'public',\n};\nconst PEER_PUBLIC_KEY = {\n    storageKey: 'peerPublicKey',\n    keyType: 'public',\n};\nclass SCWKeyManager {\n    constructor() {\n        this.storage = new ScopedLocalStorage_1.ScopedLocalStorage('CBWSDK', 'SCWKeyManager');\n        this.ownPrivateKey = null;\n        this.ownPublicKey = null;\n        this.peerPublicKey = null;\n        this.sharedSecret = null;\n    }\n    async getOwnPublicKey() {\n        await this.loadKeysIfNeeded();\n        return this.ownPublicKey;\n    }\n    // returns null if the shared secret is not yet derived\n    async getSharedSecret() {\n        await this.loadKeysIfNeeded();\n        return this.sharedSecret;\n    }\n    async setPeerPublicKey(key) {\n        this.sharedSecret = null;\n        this.peerPublicKey = key;\n        await this.storeKey(PEER_PUBLIC_KEY, key);\n        await this.loadKeysIfNeeded();\n    }\n    async clear() {\n        this.ownPrivateKey = null;\n        this.ownPublicKey = null;\n        this.peerPublicKey = null;\n        this.sharedSecret = null;\n        this.storage.removeItem(OWN_PUBLIC_KEY.storageKey);\n        this.storage.removeItem(OWN_PRIVATE_KEY.storageKey);\n        this.storage.removeItem(PEER_PUBLIC_KEY.storageKey);\n    }\n    async generateKeyPair() {\n        const newKeyPair = await (0, cipher_1.generateKeyPair)();\n        this.ownPrivateKey = newKeyPair.privateKey;\n        this.ownPublicKey = newKeyPair.publicKey;\n        await this.storeKey(OWN_PRIVATE_KEY, newKeyPair.privateKey);\n        await this.storeKey(OWN_PUBLIC_KEY, newKeyPair.publicKey);\n    }\n    async loadKeysIfNeeded() {\n        if (this.ownPrivateKey === null) {\n            this.ownPrivateKey = await this.loadKey(OWN_PRIVATE_KEY);\n        }\n        if (this.ownPublicKey === null) {\n            this.ownPublicKey = await this.loadKey(OWN_PUBLIC_KEY);\n        }\n        if (this.ownPrivateKey === null || this.ownPublicKey === null) {\n            await this.generateKeyPair();\n        }\n        if (this.peerPublicKey === null) {\n            this.peerPublicKey = await this.loadKey(PEER_PUBLIC_KEY);\n        }\n        if (this.sharedSecret === null) {\n            if (this.ownPrivateKey === null || this.peerPublicKey === null)\n                return;\n            this.sharedSecret = await (0, cipher_1.deriveSharedSecret)(this.ownPrivateKey, this.peerPublicKey);\n        }\n    }\n    // storage methods\n    async loadKey(item) {\n        const key = this.storage.getItem(item.storageKey);\n        if (!key)\n            return null;\n        return (0, cipher_1.importKeyFromHexString)(item.keyType, key);\n    }\n    async storeKey(item, key) {\n        const hexString = await (0, cipher_1.exportKeyToHexString)(item.keyType, key);\n        this.storage.setItem(item.storageKey, hexString);\n    }\n}\nexports.SCWKeyManager = SCWKeyManager;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SCWSigner = void 0;\nconst SCWKeyManager_1 = __webpack_require__(/*! ./SCWKeyManager */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js\");\nconst SCWStateManager_1 = __webpack_require__(/*! ./SCWStateManager */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWStateManager.js\");\nconst error_1 = __webpack_require__(/*! ../../core/error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nconst util_1 = __webpack_require__(/*! ../../core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nconst cipher_1 = __webpack_require__(/*! ../../util/cipher */ \"./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js\");\nclass SCWSigner {\n    constructor(params) {\n        this.metadata = params.metadata;\n        this.communicator = params.communicator;\n        this.keyManager = new SCWKeyManager_1.SCWKeyManager();\n        this.stateManager = new SCWStateManager_1.SCWStateManager({\n            appChainIds: this.metadata.appChainIds,\n            updateListener: params.updateListener,\n        });\n        this.handshake = this.handshake.bind(this);\n        this.request = this.request.bind(this);\n        this.createRequestMessage = this.createRequestMessage.bind(this);\n        this.decryptResponseMessage = this.decryptResponseMessage.bind(this);\n    }\n    async handshake() {\n        const handshakeMessage = await this.createRequestMessage({\n            handshake: {\n                method: 'eth_requestAccounts',\n                params: this.metadata,\n            },\n        });\n        const response = await this.communicator.postRequestAndWaitForResponse(handshakeMessage);\n        // store peer's public key\n        if ('failure' in response.content)\n            throw response.content.failure;\n        const peerPublicKey = await (0, cipher_1.importKeyFromHexString)('public', response.sender);\n        await this.keyManager.setPeerPublicKey(peerPublicKey);\n        const decrypted = await this.decryptResponseMessage(response);\n        this.updateInternalState({ method: 'eth_requestAccounts' }, decrypted);\n        const result = decrypted.result;\n        if ('error' in result)\n            throw result.error;\n        return this.stateManager.accounts;\n    }\n    async request(request) {\n        const localResult = this.tryLocalHandling(request);\n        if (localResult !== undefined) {\n            if (localResult instanceof Error)\n                throw localResult;\n            return localResult;\n        }\n        // Open the popup before constructing the request message.\n        // This is to ensure that the popup is not blocked by some browsers (i.e. Safari)\n        await this.communicator.waitForPopupLoaded();\n        const response = await this.sendEncryptedRequest(request);\n        const decrypted = await this.decryptResponseMessage(response);\n        this.updateInternalState(request, decrypted);\n        const result = decrypted.result;\n        if ('error' in result)\n            throw result.error;\n        return result.value;\n    }\n    async disconnect() {\n        this.stateManager.clear();\n        await this.keyManager.clear();\n    }\n    tryLocalHandling(request) {\n        var _a;\n        switch (request.method) {\n            case 'wallet_switchEthereumChain': {\n                const params = request.params;\n                if (!params || !((_a = params[0]) === null || _a === void 0 ? void 0 : _a.chainId)) {\n                    throw error_1.standardErrors.rpc.invalidParams();\n                }\n                const chainId = (0, util_1.ensureIntNumber)(params[0].chainId);\n                const switched = this.stateManager.switchChain(chainId);\n                // \"return null if the request was successful\"\n                // https://eips.ethereum.org/EIPS/eip-3326#wallet_switchethereumchain\n                return switched ? null : undefined;\n            }\n            case 'wallet_getCapabilities': {\n                const walletCapabilities = this.stateManager.walletCapabilities;\n                if (!walletCapabilities) {\n                    // This should never be the case for scw connections as capabilities are set during handshake\n                    throw error_1.standardErrors.provider.unauthorized('No wallet capabilities found, please disconnect and reconnect');\n                }\n                return walletCapabilities;\n            }\n            default:\n                return undefined;\n        }\n    }\n    async sendEncryptedRequest(request) {\n        const sharedSecret = await this.keyManager.getSharedSecret();\n        if (!sharedSecret) {\n            throw error_1.standardErrors.provider.unauthorized('No valid session found, try requestAccounts before other methods');\n        }\n        const encrypted = await (0, cipher_1.encryptContent)({\n            action: request,\n            chainId: this.stateManager.activeChain.id,\n        }, sharedSecret);\n        const message = await this.createRequestMessage({ encrypted });\n        return this.communicator.postRequestAndWaitForResponse(message);\n    }\n    async createRequestMessage(content) {\n        const publicKey = await (0, cipher_1.exportKeyToHexString)('public', await this.keyManager.getOwnPublicKey());\n        return {\n            id: crypto.randomUUID(),\n            sender: publicKey,\n            content,\n            timestamp: new Date(),\n        };\n    }\n    async decryptResponseMessage(message) {\n        const content = message.content;\n        // throw protocol level error\n        if ('failure' in content) {\n            throw content.failure;\n        }\n        const sharedSecret = await this.keyManager.getSharedSecret();\n        if (!sharedSecret) {\n            throw error_1.standardErrors.provider.unauthorized('Invalid session');\n        }\n        return (0, cipher_1.decryptContent)(content.encrypted, sharedSecret);\n    }\n    updateInternalState(request, response) {\n        var _a, _b;\n        const availableChains = (_a = response.data) === null || _a === void 0 ? void 0 : _a.chains;\n        if (availableChains) {\n            this.stateManager.updateAvailableChains(availableChains);\n        }\n        const walletCapabilities = (_b = response.data) === null || _b === void 0 ? void 0 : _b.capabilities;\n        if (walletCapabilities) {\n            this.stateManager.updateWalletCapabilities(walletCapabilities);\n        }\n        const result = response.result;\n        if ('error' in result)\n            return;\n        switch (request.method) {\n            case 'eth_requestAccounts': {\n                const accounts = result.value;\n                this.stateManager.updateAccounts(accounts);\n                break;\n            }\n            case 'wallet_switchEthereumChain': {\n                // \"return null if the request was successful\"\n                // https://eips.ethereum.org/EIPS/eip-3326#wallet_switchethereumchain\n                if (result.value !== null)\n                    return;\n                const params = request.params;\n                const chainId = (0, util_1.ensureIntNumber)(params[0].chainId);\n                this.stateManager.switchChain(chainId);\n                break;\n            }\n            default:\n                break;\n        }\n    }\n}\nexports.SCWSigner = SCWSigner;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWStateManager.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWStateManager.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SCWStateManager = void 0;\nconst ScopedLocalStorage_1 = __webpack_require__(/*! ../../util/ScopedLocalStorage */ \"./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js\");\nconst ACCOUNTS_KEY = 'accounts';\nconst ACTIVE_CHAIN_STORAGE_KEY = 'activeChain';\nconst AVAILABLE_CHAINS_STORAGE_KEY = 'availableChains';\nconst WALLET_CAPABILITIES_STORAGE_KEY = 'walletCapabilities';\nclass SCWStateManager {\n    get accounts() {\n        return this._accounts;\n    }\n    get activeChain() {\n        return this._activeChain;\n    }\n    get walletCapabilities() {\n        return this._walletCapabilities;\n    }\n    constructor(params) {\n        var _a, _b;\n        this.storage = new ScopedLocalStorage_1.ScopedLocalStorage('CBWSDK', 'SCWStateManager');\n        this.updateListener = params.updateListener;\n        this.availableChains = this.loadItemFromStorage(AVAILABLE_CHAINS_STORAGE_KEY);\n        this._walletCapabilities = this.loadItemFromStorage(WALLET_CAPABILITIES_STORAGE_KEY);\n        const accounts = this.loadItemFromStorage(ACCOUNTS_KEY);\n        const chain = this.loadItemFromStorage(ACTIVE_CHAIN_STORAGE_KEY);\n        if (accounts) {\n            this.updateListener.onAccountsUpdate({\n                accounts,\n                source: 'storage',\n            });\n        }\n        if (chain) {\n            this.updateListener.onChainUpdate({\n                chain,\n                source: 'storage',\n            });\n        }\n        this._accounts = accounts || [];\n        this._activeChain = chain || { id: (_b = (_a = params.appChainIds) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : 1 };\n    }\n    updateAccounts(accounts) {\n        this._accounts = accounts;\n        this.storeItemToStorage(ACCOUNTS_KEY, accounts);\n        this.updateListener.onAccountsUpdate({\n            accounts,\n            source: 'wallet',\n        });\n    }\n    switchChain(chainId) {\n        var _a;\n        const chain = (_a = this.availableChains) === null || _a === void 0 ? void 0 : _a.find((chain) => chain.id === chainId);\n        if (!chain)\n            return false;\n        if (chain === this._activeChain)\n            return true;\n        this._activeChain = chain;\n        this.storeItemToStorage(ACTIVE_CHAIN_STORAGE_KEY, chain);\n        this.updateListener.onChainUpdate({\n            chain,\n            source: 'wallet',\n        });\n        return true;\n    }\n    updateAvailableChains(rawChains) {\n        if (!rawChains || Object.keys(rawChains).length === 0)\n            return;\n        const chains = Object.entries(rawChains).map(([id, rpcUrl]) => ({ id: Number(id), rpcUrl }));\n        this.availableChains = chains;\n        this.storeItemToStorage(AVAILABLE_CHAINS_STORAGE_KEY, chains);\n        this.switchChain(this._activeChain.id);\n    }\n    updateWalletCapabilities(capabilities) {\n        this._walletCapabilities = capabilities;\n        this.storeItemToStorage(WALLET_CAPABILITIES_STORAGE_KEY, capabilities);\n    }\n    storeItemToStorage(key, item) {\n        this.storage.setItem(key, JSON.stringify(item));\n    }\n    loadItemFromStorage(key) {\n        const item = this.storage.getItem(key);\n        return item ? JSON.parse(item) : undefined;\n    }\n    clear() {\n        this.storage.clear();\n    }\n}\nexports.SCWStateManager = SCWStateManager;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWStateManager.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/util.js":
/*!*************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/util.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createSigner = exports.fetchSignerType = exports.storeSignerType = exports.loadSignerType = void 0;\nconst SCWSigner_1 = __webpack_require__(/*! ./scw/SCWSigner */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js\");\nconst WalletLinkSigner_1 = __webpack_require__(/*! ./walletlink/WalletLinkSigner */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js\");\nconst error_1 = __webpack_require__(/*! ../core/error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nconst provider_1 = __webpack_require__(/*! ../util/provider */ \"./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\");\nconst ScopedLocalStorage_1 = __webpack_require__(/*! ../util/ScopedLocalStorage */ \"./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js\");\nconst SIGNER_TYPE_KEY = 'SignerType';\nconst storage = new ScopedLocalStorage_1.ScopedLocalStorage('CBWSDK', 'SignerConfigurator');\nfunction loadSignerType() {\n    return storage.getItem(SIGNER_TYPE_KEY);\n}\nexports.loadSignerType = loadSignerType;\nfunction storeSignerType(signerType) {\n    storage.setItem(SIGNER_TYPE_KEY, signerType);\n}\nexports.storeSignerType = storeSignerType;\nasync function fetchSignerType(params) {\n    const { communicator, metadata } = params;\n    listenForWalletLinkSessionRequest(communicator, metadata).catch(() => { });\n    const request = {\n        id: crypto.randomUUID(),\n        event: 'selectSignerType',\n        data: params.preference,\n    };\n    const { data } = await communicator.postRequestAndWaitForResponse(request);\n    return data;\n}\nexports.fetchSignerType = fetchSignerType;\nfunction createSigner(params) {\n    const { signerType, metadata, communicator, updateListener } = params;\n    switch (signerType) {\n        case 'scw':\n            return new SCWSigner_1.SCWSigner({\n                metadata,\n                updateListener,\n                communicator,\n            });\n        case 'walletlink':\n            return new WalletLinkSigner_1.WalletLinkSigner({\n                metadata,\n                updateListener,\n            });\n        case 'extension': {\n            const injectedSigner = (0, provider_1.getCoinbaseInjectedSigner)();\n            if (!injectedSigner) {\n                throw error_1.standardErrors.rpc.internal('injected signer not found');\n            }\n            return injectedSigner;\n        }\n    }\n}\nexports.createSigner = createSigner;\nasync function listenForWalletLinkSessionRequest(communicator, metadata) {\n    await communicator.onMessage(({ event }) => event === 'WalletLinkSessionRequest');\n    // temporary walletlink signer instance to handle WalletLinkSessionRequest\n    // will revisit this when refactoring the walletlink signer\n    const walletlink = new WalletLinkSigner_1.WalletLinkSigner({\n        metadata,\n    });\n    // send wallet link session to popup\n    communicator.postMessage({\n        event: 'WalletLinkUpdate',\n        data: { session: walletlink.getSession() },\n    });\n    // wait for handshake to complete\n    await walletlink.handshake();\n    // send connected status to popup\n    communicator.postMessage({\n        event: 'WalletLinkUpdate',\n        data: { connected: true },\n    });\n}\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/util.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n/* eslint-disable @typescript-eslint/no-explicit-any */\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkSigner = void 0;\n// Copyright (c) 2018-2024 Coinbase, Inc. <https://www.coinbase.com/>\nconst eth_eip712_util_1 = __importDefault(__webpack_require__(/*! ../../vendor-js/eth-eip712-util */ \"./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.js\"));\nconst constants_1 = __webpack_require__(/*! ./relay/constants */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js\");\nconst RelayEventManager_1 = __webpack_require__(/*! ./relay/RelayEventManager */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js\");\nconst Web3Response_1 = __webpack_require__(/*! ./relay/type/Web3Response */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js\");\nconst WalletLinkRelay_1 = __webpack_require__(/*! ./relay/WalletLinkRelay */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js\");\nconst constants_2 = __webpack_require__(/*! ../../core/constants */ \"./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\");\nconst error_1 = __webpack_require__(/*! ../../core/error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nconst util_1 = __webpack_require__(/*! ../../core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nconst ScopedLocalStorage_1 = __webpack_require__(/*! ../../util/ScopedLocalStorage */ \"./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js\");\nconst DEFAULT_CHAIN_ID_KEY = 'DefaultChainId';\nconst DEFAULT_JSON_RPC_URL = 'DefaultJsonRpcUrl';\n// original source: https://github.com/coinbase/coinbase-wallet-sdk/blob/v3.7.1/packages/wallet-sdk/src/provider/CoinbaseWalletProvider.ts\nclass WalletLinkSigner {\n    constructor(options) {\n        var _a, _b;\n        this._relay = null;\n        this._addresses = [];\n        this.hasMadeFirstChainChangedEmission = false;\n        const { appName, appLogoUrl } = options.metadata;\n        this._appName = appName;\n        this._appLogoUrl = appLogoUrl;\n        this._storage = new ScopedLocalStorage_1.ScopedLocalStorage('walletlink', constants_2.WALLETLINK_URL);\n        this.updateListener = options.updateListener;\n        this._relayEventManager = new RelayEventManager_1.RelayEventManager();\n        this._jsonRpcUrlFromOpts = '';\n        const cachedAddresses = this._storage.getItem(constants_1.LOCAL_STORAGE_ADDRESSES_KEY);\n        if (cachedAddresses) {\n            const addresses = cachedAddresses.split(' ');\n            if (addresses[0] !== '') {\n                this._addresses = addresses.map((address) => (0, util_1.ensureAddressString)(address));\n                (_a = this.updateListener) === null || _a === void 0 ? void 0 : _a.onAccountsUpdate({\n                    accounts: this._addresses,\n                    source: 'storage',\n                });\n            }\n        }\n        const cachedChainId = this._storage.getItem(DEFAULT_CHAIN_ID_KEY);\n        if (cachedChainId) {\n            (_b = this.updateListener) === null || _b === void 0 ? void 0 : _b.onChainUpdate({\n                chain: {\n                    id: this.getChainId(),\n                    rpcUrl: this.jsonRpcUrl,\n                },\n                source: 'storage',\n            });\n            this.hasMadeFirstChainChangedEmission = true;\n        }\n        this.initializeRelay();\n    }\n    getSession() {\n        const relay = this.initializeRelay();\n        const { id, secret } = relay.getWalletLinkSession();\n        return { id, secret };\n    }\n    async handshake() {\n        const ethAddresses = await this.request({ method: 'eth_requestAccounts' });\n        return ethAddresses;\n    }\n    get selectedAddress() {\n        return this._addresses[0] || undefined;\n    }\n    get jsonRpcUrl() {\n        var _a;\n        return (_a = this._storage.getItem(DEFAULT_JSON_RPC_URL)) !== null && _a !== void 0 ? _a : this._jsonRpcUrlFromOpts;\n    }\n    set jsonRpcUrl(value) {\n        this._storage.setItem(DEFAULT_JSON_RPC_URL, value);\n    }\n    updateProviderInfo(jsonRpcUrl, chainId) {\n        var _a;\n        this.jsonRpcUrl = jsonRpcUrl;\n        // emit chainChanged event if necessary\n        const originalChainId = this.getChainId();\n        this._storage.setItem(DEFAULT_CHAIN_ID_KEY, chainId.toString(10));\n        const chainChanged = (0, util_1.ensureIntNumber)(chainId) !== originalChainId;\n        if (chainChanged || !this.hasMadeFirstChainChangedEmission) {\n            (_a = this.updateListener) === null || _a === void 0 ? void 0 : _a.onChainUpdate({\n                chain: { id: chainId, rpcUrl: jsonRpcUrl },\n                source: 'wallet',\n            });\n            this.hasMadeFirstChainChangedEmission = true;\n        }\n    }\n    async watchAsset(type, address, symbol, decimals, image, chainId) {\n        const relay = this.initializeRelay();\n        const result = await relay.watchAsset(type, address, symbol, decimals, image, chainId === null || chainId === void 0 ? void 0 : chainId.toString());\n        if ((0, Web3Response_1.isErrorResponse)(result))\n            return false;\n        return !!result.result;\n    }\n    async addEthereumChain(chainId, rpcUrls, blockExplorerUrls, chainName, iconUrls, nativeCurrency) {\n        var _a, _b;\n        if ((0, util_1.ensureIntNumber)(chainId) === this.getChainId()) {\n            return false;\n        }\n        const relay = this.initializeRelay();\n        if (!this._isAuthorized()) {\n            await relay.requestEthereumAccounts();\n        }\n        const res = await relay.addEthereumChain(chainId.toString(), rpcUrls, iconUrls, blockExplorerUrls, chainName, nativeCurrency);\n        if ((0, Web3Response_1.isErrorResponse)(res))\n            return false;\n        if (((_a = res.result) === null || _a === void 0 ? void 0 : _a.isApproved) === true) {\n            this.updateProviderInfo(rpcUrls[0], chainId);\n        }\n        return ((_b = res.result) === null || _b === void 0 ? void 0 : _b.isApproved) === true;\n    }\n    async switchEthereumChain(chainId) {\n        const relay = this.initializeRelay();\n        const res = await relay.switchEthereumChain(chainId.toString(10), this.selectedAddress || undefined);\n        // backward compatibility\n        if ((0, Web3Response_1.isErrorResponse)(res)) {\n            if (!res.errorCode)\n                return;\n            if (res.errorCode === error_1.standardErrorCodes.provider.unsupportedChain) {\n                throw error_1.standardErrors.provider.unsupportedChain();\n            }\n            else {\n                throw error_1.standardErrors.provider.custom({\n                    message: res.errorMessage,\n                    code: res.errorCode,\n                });\n            }\n        }\n        const switchResponse = res.result;\n        if (switchResponse.isApproved && switchResponse.rpcUrl.length > 0) {\n            this.updateProviderInfo(switchResponse.rpcUrl, chainId);\n        }\n    }\n    async disconnect() {\n        if (this._relay) {\n            this._relay.resetAndReload();\n        }\n        this._storage.clear();\n    }\n    async request(args) {\n        try {\n            return this._request(args).catch((error) => {\n                throw error;\n            });\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async _request(args) {\n        if (!args || typeof args !== 'object' || Array.isArray(args)) {\n            throw error_1.standardErrors.rpc.invalidRequest({\n                message: 'Expected a single, non-array, object argument.',\n                data: args,\n            });\n        }\n        const { method, params } = args;\n        if (typeof method !== 'string' || method.length === 0) {\n            throw error_1.standardErrors.rpc.invalidRequest({\n                message: \"'args.method' must be a non-empty string.\",\n                data: args,\n            });\n        }\n        if (params !== undefined &&\n            !Array.isArray(params) &&\n            (typeof params !== 'object' || params === null)) {\n            throw error_1.standardErrors.rpc.invalidRequest({\n                message: \"'args.params' must be an object or array if provided.\",\n                data: args,\n            });\n        }\n        const newParams = params === undefined ? [] : params;\n        // Coinbase Wallet Requests\n        const id = this._relayEventManager.makeRequestId();\n        const result = await this._sendRequestAsync({\n            method,\n            params: newParams,\n            jsonrpc: '2.0',\n            id,\n        });\n        return result.result;\n    }\n    _setAddresses(addresses, _) {\n        var _a;\n        if (!Array.isArray(addresses)) {\n            throw new Error('addresses is not an array');\n        }\n        const newAddresses = addresses.map((address) => (0, util_1.ensureAddressString)(address));\n        if (JSON.stringify(newAddresses) === JSON.stringify(this._addresses)) {\n            return;\n        }\n        this._addresses = newAddresses;\n        (_a = this.updateListener) === null || _a === void 0 ? void 0 : _a.onAccountsUpdate({\n            accounts: newAddresses,\n            source: 'wallet',\n        });\n        this._storage.setItem(constants_1.LOCAL_STORAGE_ADDRESSES_KEY, newAddresses.join(' '));\n    }\n    _sendRequestAsync(request) {\n        return new Promise((resolve, reject) => {\n            try {\n                const syncResult = this._handleSynchronousMethods(request);\n                if (syncResult !== undefined) {\n                    return resolve({\n                        jsonrpc: '2.0',\n                        id: request.id,\n                        result: syncResult,\n                    });\n                }\n            }\n            catch (err) {\n                return reject(err);\n            }\n            this._handleAsynchronousMethods(request)\n                .then((res) => res && resolve(Object.assign(Object.assign({}, res), { id: request.id })))\n                .catch((err) => reject(err));\n        });\n    }\n    _handleSynchronousMethods(request) {\n        const { method } = request;\n        switch (method) {\n            case 'eth_accounts':\n                return this._eth_accounts();\n            case 'eth_coinbase':\n                return this._eth_coinbase();\n            case 'net_version':\n                return this._net_version();\n            case 'eth_chainId':\n                return this._eth_chainId();\n            default:\n                return undefined;\n        }\n    }\n    async _handleAsynchronousMethods(request) {\n        const { method } = request;\n        const params = request.params || [];\n        switch (method) {\n            case 'eth_requestAccounts':\n                return this._eth_requestAccounts();\n            case 'eth_sign':\n                return this._eth_sign(params);\n            case 'eth_ecRecover':\n                return this._eth_ecRecover(params);\n            case 'personal_sign':\n                return this._personal_sign(params);\n            case 'personal_ecRecover':\n                return this._personal_ecRecover(params);\n            case 'eth_signTransaction':\n                return this._eth_signTransaction(params);\n            case 'eth_sendRawTransaction':\n                return this._eth_sendRawTransaction(params);\n            case 'eth_sendTransaction':\n                return this._eth_sendTransaction(params);\n            case 'eth_signTypedData_v1':\n                return this._eth_signTypedData_v1(params);\n            case 'eth_signTypedData_v2':\n                return this._throwUnsupportedMethodError();\n            case 'eth_signTypedData_v3':\n                return this._eth_signTypedData_v3(params);\n            case 'eth_signTypedData_v4':\n            case 'eth_signTypedData':\n                return this._eth_signTypedData_v4(params);\n            case 'wallet_addEthereumChain':\n                return this._wallet_addEthereumChain(params);\n            case 'wallet_switchEthereumChain':\n                return this._wallet_switchEthereumChain(params);\n            case 'wallet_watchAsset':\n                return this._wallet_watchAsset(params);\n            default:\n                return this._throwUnsupportedMethodError();\n        }\n    }\n    _isKnownAddress(addressString) {\n        try {\n            const addressStr = (0, util_1.ensureAddressString)(addressString);\n            const lowercaseAddresses = this._addresses.map((address) => (0, util_1.ensureAddressString)(address));\n            return lowercaseAddresses.includes(addressStr);\n        }\n        catch (_a) {\n            // noop\n        }\n        return false;\n    }\n    _ensureKnownAddress(addressString) {\n        if (!this._isKnownAddress(addressString)) {\n            throw new Error('Unknown Ethereum address');\n        }\n    }\n    _prepareTransactionParams(tx) {\n        const fromAddress = tx.from ? (0, util_1.ensureAddressString)(tx.from) : this.selectedAddress;\n        if (!fromAddress) {\n            throw new Error('Ethereum address is unavailable');\n        }\n        this._ensureKnownAddress(fromAddress);\n        const toAddress = tx.to ? (0, util_1.ensureAddressString)(tx.to) : null;\n        const weiValue = tx.value != null ? (0, util_1.ensureBigInt)(tx.value) : BigInt(0);\n        const data = tx.data ? (0, util_1.ensureBuffer)(tx.data) : Buffer.alloc(0);\n        const nonce = tx.nonce != null ? (0, util_1.ensureIntNumber)(tx.nonce) : null;\n        const gasPriceInWei = tx.gasPrice != null ? (0, util_1.ensureBigInt)(tx.gasPrice) : null;\n        const maxFeePerGas = tx.maxFeePerGas != null ? (0, util_1.ensureBigInt)(tx.maxFeePerGas) : null;\n        const maxPriorityFeePerGas = tx.maxPriorityFeePerGas != null ? (0, util_1.ensureBigInt)(tx.maxPriorityFeePerGas) : null;\n        const gasLimit = tx.gas != null ? (0, util_1.ensureBigInt)(tx.gas) : null;\n        const chainId = tx.chainId ? (0, util_1.ensureIntNumber)(tx.chainId) : this.getChainId();\n        return {\n            fromAddress,\n            toAddress,\n            weiValue,\n            data,\n            nonce,\n            gasPriceInWei,\n            maxFeePerGas,\n            maxPriorityFeePerGas,\n            gasLimit,\n            chainId,\n        };\n    }\n    _isAuthorized() {\n        return this._addresses.length > 0;\n    }\n    _requireAuthorization() {\n        if (!this._isAuthorized()) {\n            throw error_1.standardErrors.provider.unauthorized({});\n        }\n    }\n    _throwUnsupportedMethodError() {\n        throw error_1.standardErrors.provider.unsupportedMethod({});\n    }\n    async _signEthereumMessage(message, address, addPrefix, typedDataJson) {\n        this._ensureKnownAddress(address);\n        try {\n            const relay = this.initializeRelay();\n            const res = await relay.signEthereumMessage(message, address, addPrefix, typedDataJson);\n            if ((0, Web3Response_1.isErrorResponse)(res)) {\n                throw new Error(res.errorMessage);\n            }\n            return { jsonrpc: '2.0', id: 0, result: res.result };\n        }\n        catch (err) {\n            if (typeof err.message === 'string' && err.message.match(/(denied|rejected)/i)) {\n                throw error_1.standardErrors.provider.userRejectedRequest('User denied message signature');\n            }\n            throw err;\n        }\n    }\n    async _ethereumAddressFromSignedMessage(message, signature, addPrefix) {\n        const relay = this.initializeRelay();\n        const res = await relay.ethereumAddressFromSignedMessage(message, signature, addPrefix);\n        if ((0, Web3Response_1.isErrorResponse)(res)) {\n            throw new Error(res.errorMessage);\n        }\n        return { jsonrpc: '2.0', id: 0, result: res.result };\n    }\n    _eth_accounts() {\n        return [...this._addresses];\n    }\n    _eth_coinbase() {\n        return this.selectedAddress || null;\n    }\n    _net_version() {\n        return this.getChainId().toString(10);\n    }\n    _eth_chainId() {\n        return (0, util_1.hexStringFromIntNumber)(this.getChainId());\n    }\n    getChainId() {\n        const chainIdStr = this._storage.getItem(DEFAULT_CHAIN_ID_KEY);\n        if (!chainIdStr) {\n            return (0, util_1.ensureIntNumber)(1); // default to mainnet\n        }\n        const chainId = parseInt(chainIdStr, 10);\n        return (0, util_1.ensureIntNumber)(chainId);\n    }\n    async _eth_requestAccounts() {\n        if (this._isAuthorized()) {\n            return Promise.resolve({\n                jsonrpc: '2.0',\n                id: 0,\n                result: this._addresses,\n            });\n        }\n        let res;\n        try {\n            const relay = this.initializeRelay();\n            res = await relay.requestEthereumAccounts();\n            if ((0, Web3Response_1.isErrorResponse)(res)) {\n                throw new Error(res.errorMessage);\n            }\n        }\n        catch (err) {\n            if (typeof err.message === 'string' && err.message.match(/(denied|rejected)/i)) {\n                throw error_1.standardErrors.provider.userRejectedRequest('User denied account authorization');\n            }\n            throw err;\n        }\n        if (!res.result) {\n            throw new Error('accounts received is empty');\n        }\n        this._setAddresses(res.result);\n        return { jsonrpc: '2.0', id: 0, result: this._addresses };\n    }\n    _eth_sign(params) {\n        this._requireAuthorization();\n        const address = (0, util_1.ensureAddressString)(params[0]);\n        const message = (0, util_1.ensureBuffer)(params[1]);\n        return this._signEthereumMessage(message, address, false);\n    }\n    _eth_ecRecover(params) {\n        const message = (0, util_1.ensureBuffer)(params[0]);\n        const signature = (0, util_1.ensureBuffer)(params[1]);\n        return this._ethereumAddressFromSignedMessage(message, signature, false);\n    }\n    _personal_sign(params) {\n        this._requireAuthorization();\n        const message = (0, util_1.ensureBuffer)(params[0]);\n        const address = (0, util_1.ensureAddressString)(params[1]);\n        return this._signEthereumMessage(message, address, true);\n    }\n    _personal_ecRecover(params) {\n        const message = (0, util_1.ensureBuffer)(params[0]);\n        const signature = (0, util_1.ensureBuffer)(params[1]);\n        return this._ethereumAddressFromSignedMessage(message, signature, true);\n    }\n    async _eth_signTransaction(params) {\n        this._requireAuthorization();\n        const tx = this._prepareTransactionParams(params[0] || {});\n        try {\n            const relay = this.initializeRelay();\n            const res = await relay.signEthereumTransaction(tx);\n            if ((0, Web3Response_1.isErrorResponse)(res)) {\n                throw new Error(res.errorMessage);\n            }\n            return { jsonrpc: '2.0', id: 0, result: res.result };\n        }\n        catch (err) {\n            if (typeof err.message === 'string' && err.message.match(/(denied|rejected)/i)) {\n                throw error_1.standardErrors.provider.userRejectedRequest('User denied transaction signature');\n            }\n            throw err;\n        }\n    }\n    async _eth_sendRawTransaction(params) {\n        const signedTransaction = (0, util_1.ensureBuffer)(params[0]);\n        const relay = this.initializeRelay();\n        const res = await relay.submitEthereumTransaction(signedTransaction, this.getChainId());\n        if ((0, Web3Response_1.isErrorResponse)(res)) {\n            throw new Error(res.errorMessage);\n        }\n        return { jsonrpc: '2.0', id: 0, result: res.result };\n    }\n    async _eth_sendTransaction(params) {\n        this._requireAuthorization();\n        const tx = this._prepareTransactionParams(params[0] || {});\n        try {\n            const relay = this.initializeRelay();\n            const res = await relay.signAndSubmitEthereumTransaction(tx);\n            if ((0, Web3Response_1.isErrorResponse)(res)) {\n                throw new Error(res.errorMessage);\n            }\n            return { jsonrpc: '2.0', id: 0, result: res.result };\n        }\n        catch (err) {\n            if (typeof err.message === 'string' && err.message.match(/(denied|rejected)/i)) {\n                throw error_1.standardErrors.provider.userRejectedRequest('User denied transaction signature');\n            }\n            throw err;\n        }\n    }\n    async _eth_signTypedData_v1(params) {\n        this._requireAuthorization();\n        const typedData = (0, util_1.ensureParsedJSONObject)(params[0]);\n        const address = (0, util_1.ensureAddressString)(params[1]);\n        this._ensureKnownAddress(address);\n        const message = eth_eip712_util_1.default.hashForSignTypedDataLegacy({ data: typedData });\n        const typedDataJSON = JSON.stringify(typedData, null, 2);\n        return this._signEthereumMessage(message, address, false, typedDataJSON);\n    }\n    async _eth_signTypedData_v3(params) {\n        this._requireAuthorization();\n        const address = (0, util_1.ensureAddressString)(params[0]);\n        const typedData = (0, util_1.ensureParsedJSONObject)(params[1]);\n        this._ensureKnownAddress(address);\n        const message = eth_eip712_util_1.default.hashForSignTypedData_v3({ data: typedData });\n        const typedDataJSON = JSON.stringify(typedData, null, 2);\n        return this._signEthereumMessage(message, address, false, typedDataJSON);\n    }\n    async _eth_signTypedData_v4(params) {\n        this._requireAuthorization();\n        const address = (0, util_1.ensureAddressString)(params[0]);\n        const typedData = (0, util_1.ensureParsedJSONObject)(params[1]);\n        this._ensureKnownAddress(address);\n        const message = eth_eip712_util_1.default.hashForSignTypedData_v4({ data: typedData });\n        const typedDataJSON = JSON.stringify(typedData, null, 2);\n        return this._signEthereumMessage(message, address, false, typedDataJSON);\n    }\n    async _wallet_addEthereumChain(params) {\n        var _a, _b, _c, _d;\n        const request = params[0];\n        if (((_a = request.rpcUrls) === null || _a === void 0 ? void 0 : _a.length) === 0) {\n            return {\n                jsonrpc: '2.0',\n                id: 0,\n                error: { code: 2, message: `please pass in at least 1 rpcUrl` },\n            };\n        }\n        if (!request.chainName || request.chainName.trim() === '') {\n            throw error_1.standardErrors.rpc.invalidParams('chainName is a required field');\n        }\n        if (!request.nativeCurrency) {\n            throw error_1.standardErrors.rpc.invalidParams('nativeCurrency is a required field');\n        }\n        const chainIdNumber = parseInt(request.chainId, 16);\n        const success = await this.addEthereumChain(chainIdNumber, (_b = request.rpcUrls) !== null && _b !== void 0 ? _b : [], (_c = request.blockExplorerUrls) !== null && _c !== void 0 ? _c : [], request.chainName, (_d = request.iconUrls) !== null && _d !== void 0 ? _d : [], request.nativeCurrency);\n        if (success) {\n            return { jsonrpc: '2.0', id: 0, result: null };\n        }\n        return {\n            jsonrpc: '2.0',\n            id: 0,\n            error: { code: 2, message: `unable to add ethereum chain` },\n        };\n    }\n    async _wallet_switchEthereumChain(params) {\n        const request = params[0];\n        await this.switchEthereumChain(parseInt(request.chainId, 16));\n        return { jsonrpc: '2.0', id: 0, result: null };\n    }\n    async _wallet_watchAsset(params) {\n        const request = (Array.isArray(params) ? params[0] : params);\n        if (!request.type) {\n            throw error_1.standardErrors.rpc.invalidParams('Type is required');\n        }\n        if ((request === null || request === void 0 ? void 0 : request.type) !== 'ERC20') {\n            throw error_1.standardErrors.rpc.invalidParams(`Asset of type '${request.type}' is not supported`);\n        }\n        if (!(request === null || request === void 0 ? void 0 : request.options)) {\n            throw error_1.standardErrors.rpc.invalidParams('Options are required');\n        }\n        if (!(request === null || request === void 0 ? void 0 : request.options.address)) {\n            throw error_1.standardErrors.rpc.invalidParams('Address is required');\n        }\n        const chainId = this.getChainId();\n        const { address, symbol, image, decimals } = request.options;\n        const res = await this.watchAsset(request.type, address, symbol, decimals, image, chainId);\n        return { jsonrpc: '2.0', id: 0, result: res };\n    }\n    initializeRelay() {\n        if (!this._relay) {\n            const relay = new WalletLinkRelay_1.WalletLinkRelay({\n                linkAPIUrl: constants_2.WALLETLINK_URL,\n                storage: this._storage,\n            });\n            relay.setAppInfo(this._appName, this._appLogoUrl);\n            relay.attachUI();\n            relay.setAccountsCallback((accounts, isDisconnect) => this._setAddresses(accounts, isDisconnect));\n            relay.setChainCallback((chainId, jsonRpcUrl) => {\n                this.updateProviderInfo(jsonRpcUrl, parseInt(chainId, 10));\n            });\n            this._relay = relay;\n        }\n        return this._relay;\n    }\n}\nexports.WalletLinkSigner = WalletLinkSigner;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RelayEventManager = void 0;\nconst util_1 = __webpack_require__(/*! ../../../core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nclass RelayEventManager {\n    constructor() {\n        this._nextRequestId = 0;\n        this.callbacks = new Map();\n    }\n    makeRequestId() {\n        // max nextId == max int32 for compatibility with mobile\n        this._nextRequestId = (this._nextRequestId + 1) % 0x7fffffff;\n        const id = this._nextRequestId;\n        const idStr = (0, util_1.prepend0x)(id.toString(16));\n        // unlikely that this will ever be an issue, but just to be safe\n        const callback = this.callbacks.get(idStr);\n        if (callback) {\n            this.callbacks.delete(idStr);\n        }\n        return id;\n    }\n}\nexports.RelayEventManager = RelayEventManager;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkRelay = void 0;\nconst WalletLinkConnection_1 = __webpack_require__(/*! ./connection/WalletLinkConnection */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js\");\nconst constants_1 = __webpack_require__(/*! ./constants */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js\");\nconst RelayEventManager_1 = __webpack_require__(/*! ./RelayEventManager */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js\");\nconst WalletLinkSession_1 = __webpack_require__(/*! ./type/WalletLinkSession */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js\");\nconst Web3Response_1 = __webpack_require__(/*! ./type/Web3Response */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js\");\nconst util_1 = __webpack_require__(/*! ./ui/components/util */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\nconst WalletLinkRelayUI_1 = __webpack_require__(/*! ./ui/WalletLinkRelayUI */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js\");\nconst WLMobileRelayUI_1 = __webpack_require__(/*! ./ui/WLMobileRelayUI */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js\");\nconst error_1 = __webpack_require__(/*! ../../../core/error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nconst util_2 = __webpack_require__(/*! ../../../core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nconst ScopedLocalStorage_1 = __webpack_require__(/*! ../../../util/ScopedLocalStorage */ \"./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js\");\nclass WalletLinkRelay {\n    constructor(options) {\n        this.accountsCallback = null;\n        this.chainCallbackParams = { chainId: '', jsonRpcUrl: '' }; // to implement distinctUntilChanged\n        this.chainCallback = null;\n        this.dappDefaultChain = 1;\n        this.isMobileWeb = (0, util_1.isMobileWeb)();\n        this.appName = '';\n        this.appLogoUrl = null;\n        this.linkedUpdated = (linked) => {\n            this.isLinked = linked;\n            const cachedAddresses = this.storage.getItem(constants_1.LOCAL_STORAGE_ADDRESSES_KEY);\n            if (linked) {\n                // Only set linked session variable one way\n                this._session.linked = linked;\n            }\n            this.isUnlinkedErrorState = false;\n            if (cachedAddresses) {\n                const addresses = cachedAddresses.split(' ');\n                const wasConnectedViaStandalone = this.storage.getItem('IsStandaloneSigning') === 'true';\n                if (addresses[0] !== '' && !linked && this._session.linked && !wasConnectedViaStandalone) {\n                    this.isUnlinkedErrorState = true;\n                }\n            }\n        };\n        this.metadataUpdated = (key, value) => {\n            this.storage.setItem(key, value);\n        };\n        this.chainUpdated = (chainId, jsonRpcUrl) => {\n            if (this.chainCallbackParams.chainId === chainId &&\n                this.chainCallbackParams.jsonRpcUrl === jsonRpcUrl) {\n                return;\n            }\n            this.chainCallbackParams = {\n                chainId,\n                jsonRpcUrl,\n            };\n            if (this.chainCallback) {\n                this.chainCallback(chainId, jsonRpcUrl);\n            }\n        };\n        this.accountUpdated = (selectedAddress) => {\n            if (this.accountsCallback) {\n                this.accountsCallback([selectedAddress]);\n            }\n            if (WalletLinkRelay.accountRequestCallbackIds.size > 0) {\n                // We get the ethereum address from the metadata.  If for whatever\n                // reason we don't get a response via an explicit web3 message\n                // we can still fulfill the eip1102 request.\n                Array.from(WalletLinkRelay.accountRequestCallbackIds.values()).forEach((id) => {\n                    const message = {\n                        type: 'WEB3_RESPONSE',\n                        id,\n                        response: {\n                            method: 'requestEthereumAccounts',\n                            result: [selectedAddress],\n                        },\n                    };\n                    this.invokeCallback(Object.assign(Object.assign({}, message), { id }));\n                });\n                WalletLinkRelay.accountRequestCallbackIds.clear();\n            }\n        };\n        this.resetAndReload = this.resetAndReload.bind(this);\n        this.linkAPIUrl = options.linkAPIUrl;\n        this.storage = options.storage;\n        const { session, ui, connection } = this.subscribe();\n        this._session = session;\n        this.connection = connection;\n        this.relayEventManager = new RelayEventManager_1.RelayEventManager();\n        this.ui = ui;\n    }\n    subscribe() {\n        const session = WalletLinkSession_1.WalletLinkSession.load(this.storage) || new WalletLinkSession_1.WalletLinkSession(this.storage).save();\n        const { linkAPIUrl } = this;\n        const connection = new WalletLinkConnection_1.WalletLinkConnection({\n            session,\n            linkAPIUrl,\n            listener: this,\n        });\n        const ui = this.isMobileWeb ? new WLMobileRelayUI_1.WLMobileRelayUI() : new WalletLinkRelayUI_1.WalletLinkRelayUI();\n        connection.connect();\n        return { session, ui, connection };\n    }\n    attachUI() {\n        this.ui.attach();\n    }\n    resetAndReload() {\n        Promise.race([\n            this.connection.setSessionMetadata('__destroyed', '1'),\n            new Promise((resolve) => setTimeout(() => resolve(null), 1000)),\n        ])\n            .then(() => {\n            this.connection.destroy();\n            /**\n             * Only clear storage if the session id we have in memory matches the one on disk\n             * Otherwise, in the case where we have 2 tabs, another tab might have cleared\n             * storage already.  In that case if we clear storage again, the user will be in\n             * a state where the first tab allows the user to connect but the session that\n             * was used isn't persisted.  This leaves the user in a state where they aren't\n             * connected to the mobile app.\n             */\n            const storedSession = WalletLinkSession_1.WalletLinkSession.load(this.storage);\n            if ((storedSession === null || storedSession === void 0 ? void 0 : storedSession.id) === this._session.id) {\n                ScopedLocalStorage_1.ScopedLocalStorage.clearAll();\n            }\n            document.location.reload();\n        })\n            .catch((_) => { });\n    }\n    setAppInfo(appName, appLogoUrl) {\n        this.appName = appName;\n        this.appLogoUrl = appLogoUrl;\n    }\n    getStorageItem(key) {\n        return this.storage.getItem(key);\n    }\n    setStorageItem(key, value) {\n        this.storage.setItem(key, value);\n    }\n    signEthereumMessage(message, address, addPrefix, typedDataJson) {\n        return this.sendRequest({\n            method: 'signEthereumMessage',\n            params: {\n                message: (0, util_2.hexStringFromBuffer)(message, true),\n                address,\n                addPrefix,\n                typedDataJson: typedDataJson || null,\n            },\n        });\n    }\n    ethereumAddressFromSignedMessage(message, signature, addPrefix) {\n        return this.sendRequest({\n            method: 'ethereumAddressFromSignedMessage',\n            params: {\n                message: (0, util_2.hexStringFromBuffer)(message, true),\n                signature: (0, util_2.hexStringFromBuffer)(signature, true),\n                addPrefix,\n            },\n        });\n    }\n    signEthereumTransaction(params) {\n        return this.sendRequest({\n            method: 'signEthereumTransaction',\n            params: {\n                fromAddress: params.fromAddress,\n                toAddress: params.toAddress,\n                weiValue: (0, util_2.bigIntStringFromBigInt)(params.weiValue),\n                data: (0, util_2.hexStringFromBuffer)(params.data, true),\n                nonce: params.nonce,\n                gasPriceInWei: params.gasPriceInWei ? (0, util_2.bigIntStringFromBigInt)(params.gasPriceInWei) : null,\n                maxFeePerGas: params.gasPriceInWei ? (0, util_2.bigIntStringFromBigInt)(params.gasPriceInWei) : null,\n                maxPriorityFeePerGas: params.gasPriceInWei\n                    ? (0, util_2.bigIntStringFromBigInt)(params.gasPriceInWei)\n                    : null,\n                gasLimit: params.gasLimit ? (0, util_2.bigIntStringFromBigInt)(params.gasLimit) : null,\n                chainId: params.chainId,\n                shouldSubmit: false,\n            },\n        });\n    }\n    signAndSubmitEthereumTransaction(params) {\n        return this.sendRequest({\n            method: 'signEthereumTransaction',\n            params: {\n                fromAddress: params.fromAddress,\n                toAddress: params.toAddress,\n                weiValue: (0, util_2.bigIntStringFromBigInt)(params.weiValue),\n                data: (0, util_2.hexStringFromBuffer)(params.data, true),\n                nonce: params.nonce,\n                gasPriceInWei: params.gasPriceInWei ? (0, util_2.bigIntStringFromBigInt)(params.gasPriceInWei) : null,\n                maxFeePerGas: params.maxFeePerGas ? (0, util_2.bigIntStringFromBigInt)(params.maxFeePerGas) : null,\n                maxPriorityFeePerGas: params.maxPriorityFeePerGas\n                    ? (0, util_2.bigIntStringFromBigInt)(params.maxPriorityFeePerGas)\n                    : null,\n                gasLimit: params.gasLimit ? (0, util_2.bigIntStringFromBigInt)(params.gasLimit) : null,\n                chainId: params.chainId,\n                shouldSubmit: true,\n            },\n        });\n    }\n    submitEthereumTransaction(signedTransaction, chainId) {\n        return this.sendRequest({\n            method: 'submitEthereumTransaction',\n            params: {\n                signedTransaction: (0, util_2.hexStringFromBuffer)(signedTransaction, true),\n                chainId,\n            },\n        });\n    }\n    scanQRCode(regExp) {\n        return this.sendRequest({\n            method: 'scanQRCode',\n            params: {\n                regExp,\n            },\n        });\n    }\n    getWalletLinkSession() {\n        return this._session;\n    }\n    genericRequest(data, action) {\n        return this.sendRequest({\n            method: 'generic',\n            params: {\n                action,\n                data,\n            },\n        });\n    }\n    sendGenericMessage(request) {\n        return this.sendRequest(request);\n    }\n    sendRequest(request) {\n        let hideSnackbarItem = null;\n        const id = (0, util_2.randomBytesHex)(8);\n        const cancel = (error) => {\n            this.publishWeb3RequestCanceledEvent(id);\n            this.handleErrorResponse(id, request.method, error);\n            hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n        };\n        return new Promise((resolve, reject) => {\n            {\n                hideSnackbarItem = this.ui.showConnecting({\n                    isUnlinkedErrorState: this.isUnlinkedErrorState,\n                    onCancel: cancel,\n                    onResetConnection: this.resetAndReload, // eslint-disable-line @typescript-eslint/unbound-method\n                });\n            }\n            this.relayEventManager.callbacks.set(id, (response) => {\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0, Web3Response_1.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    setAccountsCallback(accountsCallback) {\n        this.accountsCallback = accountsCallback;\n    }\n    setChainCallback(chainCallback) {\n        this.chainCallback = chainCallback;\n    }\n    setDappDefaultChainCallback(chainId) {\n        this.dappDefaultChain = chainId;\n    }\n    publishWeb3RequestEvent(id, request) {\n        const message = { type: 'WEB3_REQUEST', id, request };\n        this.publishEvent('Web3Request', message, true)\n            .then((_) => { })\n            .catch((err) => {\n            this.handleWeb3ResponseMessage({\n                type: 'WEB3_RESPONSE',\n                id: message.id,\n                response: {\n                    method: request.method,\n                    errorMessage: err.message,\n                },\n            });\n        });\n        if (this.isMobileWeb) {\n            this.openCoinbaseWalletDeeplink(request.method);\n        }\n    }\n    // copied from MobileRelay\n    openCoinbaseWalletDeeplink(method) {\n        if (!(this.ui instanceof WLMobileRelayUI_1.WLMobileRelayUI))\n            return;\n        // For mobile relay requests, open the Coinbase Wallet app\n        switch (method) {\n            case 'requestEthereumAccounts': // requestEthereumAccounts is handled via popup\n            case 'switchEthereumChain': // switchEthereumChain doesn't need to open the app\n                return;\n            default:\n                window.addEventListener('blur', () => {\n                    window.addEventListener('focus', () => {\n                        this.connection.checkUnseenEvents();\n                    }, { once: true });\n                }, { once: true });\n                this.ui.openCoinbaseWalletDeeplink();\n                break;\n        }\n    }\n    publishWeb3RequestCanceledEvent(id) {\n        const message = {\n            type: 'WEB3_REQUEST_CANCELED',\n            id,\n        };\n        this.publishEvent('Web3RequestCanceled', message, false).then();\n    }\n    publishEvent(event, message, callWebhook) {\n        return this.connection.publishEvent(event, message, callWebhook);\n    }\n    handleWeb3ResponseMessage(message) {\n        const { response } = message;\n        if (response.method === 'requestEthereumAccounts') {\n            WalletLinkRelay.accountRequestCallbackIds.forEach((id) => this.invokeCallback(Object.assign(Object.assign({}, message), { id })));\n            WalletLinkRelay.accountRequestCallbackIds.clear();\n            return;\n        }\n        this.invokeCallback(message);\n    }\n    handleErrorResponse(id, method, error) {\n        var _a;\n        const errorMessage = (_a = error === null || error === void 0 ? void 0 : error.message) !== null && _a !== void 0 ? _a : 'Unspecified error message.';\n        this.handleWeb3ResponseMessage({\n            type: 'WEB3_RESPONSE',\n            id,\n            response: {\n                method,\n                errorMessage,\n            },\n        });\n    }\n    invokeCallback(message) {\n        const callback = this.relayEventManager.callbacks.get(message.id);\n        if (callback) {\n            callback(message.response);\n            this.relayEventManager.callbacks.delete(message.id);\n        }\n    }\n    requestEthereumAccounts() {\n        const request = {\n            method: 'requestEthereumAccounts',\n            params: {\n                appName: this.appName,\n                appLogoUrl: this.appLogoUrl || null,\n            },\n        };\n        const hideSnackbarItem = null;\n        const id = (0, util_2.randomBytesHex)(8);\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                // @ts-ignore\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0, Web3Response_1.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            WalletLinkRelay.accountRequestCallbackIds.add(id);\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    watchAsset(type, address, symbol, decimals, image, chainId) {\n        const request = {\n            method: 'watchAsset',\n            params: {\n                type,\n                options: {\n                    address,\n                    symbol,\n                    decimals,\n                    image,\n                },\n                chainId,\n            },\n        };\n        let hideSnackbarItem = null;\n        const id = (0, util_2.randomBytesHex)(8);\n        const cancel = (error) => {\n            this.publishWeb3RequestCanceledEvent(id);\n            this.handleErrorResponse(id, request.method, error);\n            hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n        };\n        {\n            hideSnackbarItem = this.ui.showConnecting({\n                isUnlinkedErrorState: this.isUnlinkedErrorState,\n                onCancel: cancel,\n                onResetConnection: this.resetAndReload, // eslint-disable-line @typescript-eslint/unbound-method\n            });\n        }\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0, Web3Response_1.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    addEthereumChain(chainId, rpcUrls, iconUrls, blockExplorerUrls, chainName, nativeCurrency) {\n        const request = {\n            method: 'addEthereumChain',\n            params: {\n                chainId,\n                rpcUrls,\n                blockExplorerUrls,\n                chainName,\n                iconUrls,\n                nativeCurrency,\n            },\n        };\n        let hideSnackbarItem = null;\n        const id = (0, util_2.randomBytesHex)(8);\n        const cancel = (error) => {\n            this.publishWeb3RequestCanceledEvent(id);\n            this.handleErrorResponse(id, request.method, error);\n            hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n        };\n        {\n            hideSnackbarItem = this.ui.showConnecting({\n                isUnlinkedErrorState: this.isUnlinkedErrorState,\n                onCancel: cancel,\n                onResetConnection: this.resetAndReload, // eslint-disable-line @typescript-eslint/unbound-method\n            });\n        }\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0, Web3Response_1.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    switchEthereumChain(chainId, address) {\n        const request = {\n            method: 'switchEthereumChain',\n            params: Object.assign({ chainId }, { address }),\n        };\n        const id = (0, util_2.randomBytesHex)(8);\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                if ((0, Web3Response_1.isErrorResponse)(response) && response.errorCode) {\n                    return reject(error_1.standardErrors.provider.custom({\n                        code: response.errorCode,\n                        message: `Unrecognized chain ID. Try adding the chain using addEthereumChain first.`,\n                    }));\n                }\n                else if ((0, Web3Response_1.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n}\nexports.WalletLinkRelay = WalletLinkRelay;\nWalletLinkRelay.accountRequestCallbackIds = new Set();\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkCipher = void 0;\nconst util_1 = __webpack_require__(/*! ../../../../core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nclass WalletLinkCipher {\n    // @param secret hex representation of 32-byte secret\n    constructor(secret) {\n        this.secret = secret;\n    }\n    /**\n     *\n     * @param plainText string to be encrypted\n     * returns hex string representation of bytes in the order: initialization vector (iv),\n     * auth tag, encrypted plaintext. IV is 12 bytes. Auth tag is 16 bytes. Remaining bytes are the\n     * encrypted plainText.\n     */\n    async encrypt(plainText) {\n        const secret = this.secret;\n        if (secret.length !== 64)\n            throw Error(`secret must be 256 bits`);\n        const ivBytes = crypto.getRandomValues(new Uint8Array(12));\n        const secretKey = await crypto.subtle.importKey('raw', (0, util_1.hexStringToUint8Array)(secret), { name: 'aes-gcm' }, false, ['encrypt', 'decrypt']);\n        const enc = new TextEncoder();\n        // Will return encrypted plainText with auth tag (ie MAC or checksum) appended at the end\n        const encryptedResult = await window.crypto.subtle.encrypt({\n            name: 'AES-GCM',\n            iv: ivBytes,\n        }, secretKey, enc.encode(plainText));\n        const tagLength = 16;\n        const authTag = encryptedResult.slice(encryptedResult.byteLength - tagLength);\n        const encryptedPlaintext = encryptedResult.slice(0, encryptedResult.byteLength - tagLength);\n        const authTagBytes = new Uint8Array(authTag);\n        const encryptedPlaintextBytes = new Uint8Array(encryptedPlaintext);\n        const concatted = new Uint8Array([...ivBytes, ...authTagBytes, ...encryptedPlaintextBytes]);\n        return (0, util_1.uint8ArrayToHex)(concatted);\n    }\n    /**\n     *\n     * @param cipherText hex string representation of bytes in the order: initialization vector (iv),\n     * auth tag, encrypted plaintext. IV is 12 bytes. Auth tag is 16 bytes.\n     */\n    async decrypt(cipherText) {\n        const secret = this.secret;\n        if (secret.length !== 64)\n            throw Error(`secret must be 256 bits`);\n        return new Promise((resolve, reject) => {\n            void (async function () {\n                const secretKey = await crypto.subtle.importKey('raw', (0, util_1.hexStringToUint8Array)(secret), { name: 'aes-gcm' }, false, ['encrypt', 'decrypt']);\n                const encrypted = (0, util_1.hexStringToUint8Array)(cipherText);\n                const ivBytes = encrypted.slice(0, 12);\n                const authTagBytes = encrypted.slice(12, 28);\n                const encryptedPlaintextBytes = encrypted.slice(28);\n                const concattedBytes = new Uint8Array([...encryptedPlaintextBytes, ...authTagBytes]);\n                const algo = {\n                    name: 'AES-GCM',\n                    iv: new Uint8Array(ivBytes),\n                };\n                try {\n                    const decrypted = await window.crypto.subtle.decrypt(algo, secretKey, concattedBytes);\n                    const decoder = new TextDecoder();\n                    resolve(decoder.decode(decrypted));\n                }\n                catch (err) {\n                    reject(err);\n                }\n            })();\n        });\n    }\n}\nexports.WalletLinkCipher = WalletLinkCipher;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkConnection = void 0;\nconst constants_1 = __webpack_require__(/*! ../constants */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js\");\nconst WalletLinkCipher_1 = __webpack_require__(/*! ./WalletLinkCipher */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js\");\nconst WalletLinkHTTP_1 = __webpack_require__(/*! ./WalletLinkHTTP */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js\");\nconst WalletLinkWebSocket_1 = __webpack_require__(/*! ./WalletLinkWebSocket */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js\");\nconst type_1 = __webpack_require__(/*! ../../../../core/type */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js\");\nconst HEARTBEAT_INTERVAL = 10000;\nconst REQUEST_TIMEOUT = 60000;\n/**\n * Coinbase Wallet Connection\n */\nclass WalletLinkConnection {\n    /**\n     * Constructor\n     * @param session Session\n     * @param linkAPIUrl Coinbase Wallet link server URL\n     * @param listener WalletLinkConnectionUpdateListener\n     * @param [WebSocketClass] Custom WebSocket implementation\n     */\n    constructor({ session, linkAPIUrl, listener, WebSocketClass = WebSocket, }) {\n        this.destroyed = false;\n        this.lastHeartbeatResponse = 0;\n        this.nextReqId = (0, type_1.IntNumber)(1);\n        /**\n         * true if connected and authenticated, else false\n         * runs listener when connected status changes\n         */\n        this._connected = false;\n        /**\n         * true if linked (a guest has joined before)\n         * runs listener when linked status changes\n         */\n        this._linked = false;\n        this.shouldFetchUnseenEventsOnConnect = false;\n        this.requestResolutions = new Map();\n        this.handleSessionMetadataUpdated = (metadata) => {\n            if (!metadata)\n                return;\n            // Map of metadata key to handler function\n            const handlers = new Map([\n                ['__destroyed', this.handleDestroyed],\n                ['EthereumAddress', this.handleAccountUpdated],\n                ['WalletUsername', this.handleWalletUsernameUpdated],\n                ['AppVersion', this.handleAppVersionUpdated],\n                [\n                    'ChainId',\n                    (v) => metadata.JsonRpcUrl && this.handleChainUpdated(v, metadata.JsonRpcUrl),\n                ],\n            ]);\n            // call handler for each metadata key if value is defined\n            handlers.forEach((handler, key) => {\n                const value = metadata[key];\n                if (value === undefined)\n                    return;\n                handler(value);\n            });\n        };\n        this.handleDestroyed = (__destroyed) => {\n            var _a;\n            if (__destroyed !== '1')\n                return;\n            (_a = this.listener) === null || _a === void 0 ? void 0 : _a.resetAndReload();\n        };\n        this.handleAccountUpdated = async (encryptedEthereumAddress) => {\n            var _a;\n            {\n                const address = await this.cipher.decrypt(encryptedEthereumAddress);\n                (_a = this.listener) === null || _a === void 0 ? void 0 : _a.accountUpdated(address);\n            }\n        };\n        this.handleMetadataUpdated = async (key, encryptedMetadataValue) => {\n            var _a;\n            {\n                const decryptedValue = await this.cipher.decrypt(encryptedMetadataValue);\n                (_a = this.listener) === null || _a === void 0 ? void 0 : _a.metadataUpdated(key, decryptedValue);\n            }\n        };\n        this.handleWalletUsernameUpdated = async (walletUsername) => {\n            this.handleMetadataUpdated(constants_1.WALLET_USER_NAME_KEY, walletUsername);\n        };\n        this.handleAppVersionUpdated = async (appVersion) => {\n            this.handleMetadataUpdated(constants_1.APP_VERSION_KEY, appVersion);\n        };\n        this.handleChainUpdated = async (encryptedChainId, encryptedJsonRpcUrl) => {\n            var _a;\n            {\n                const chainId = await this.cipher.decrypt(encryptedChainId);\n                const jsonRpcUrl = await this.cipher.decrypt(encryptedJsonRpcUrl);\n                (_a = this.listener) === null || _a === void 0 ? void 0 : _a.chainUpdated(chainId, jsonRpcUrl);\n            }\n        };\n        this.session = session;\n        this.cipher = new WalletLinkCipher_1.WalletLinkCipher(session.secret);\n        this.listener = listener;\n        const ws = new WalletLinkWebSocket_1.WalletLinkWebSocket(`${linkAPIUrl}/rpc`, WebSocketClass);\n        ws.setConnectionStateListener(async (state) => {\n            // attempt to reconnect every 5 seconds when disconnected\n            let connected = false;\n            switch (state) {\n                case WalletLinkWebSocket_1.ConnectionState.DISCONNECTED:\n                    // if DISCONNECTED and not destroyed\n                    if (!this.destroyed) {\n                        const connect = async () => {\n                            // wait 5 seconds\n                            await new Promise((resolve) => setTimeout(resolve, 5000));\n                            // check whether it's destroyed again\n                            if (!this.destroyed) {\n                                // reconnect\n                                ws.connect().catch(() => {\n                                    connect();\n                                });\n                            }\n                        };\n                        connect();\n                    }\n                    break;\n                case WalletLinkWebSocket_1.ConnectionState.CONNECTED:\n                    // perform authentication upon connection\n                    try {\n                        // if CONNECTED, authenticate, and then check link status\n                        await this.authenticate();\n                        this.sendIsLinked();\n                        this.sendGetSessionConfig();\n                        connected = true;\n                    }\n                    catch (_a) {\n                        /* empty */\n                    }\n                    // send heartbeat every n seconds while connected\n                    // if CONNECTED, start the heartbeat timer\n                    // first timer event updates lastHeartbeat timestamp\n                    // subsequent calls send heartbeat message\n                    this.updateLastHeartbeat();\n                    setInterval(() => {\n                        this.heartbeat();\n                    }, HEARTBEAT_INTERVAL);\n                    // check for unseen events\n                    if (this.shouldFetchUnseenEventsOnConnect) {\n                        this.fetchUnseenEventsAPI();\n                    }\n                    break;\n                case WalletLinkWebSocket_1.ConnectionState.CONNECTING:\n                    break;\n            }\n            // distinctUntilChanged\n            if (this.connected !== connected) {\n                this.connected = connected;\n            }\n        });\n        ws.setIncomingDataListener((m) => {\n            var _a;\n            switch (m.type) {\n                // handle server's heartbeat responses\n                case 'Heartbeat':\n                    this.updateLastHeartbeat();\n                    return;\n                // handle link status updates\n                case 'IsLinkedOK':\n                case 'Linked': {\n                    const linked = m.type === 'IsLinkedOK' ? m.linked : undefined;\n                    this.linked = linked || m.onlineGuests > 0;\n                    break;\n                }\n                // handle session config updates\n                case 'GetSessionConfigOK':\n                case 'SessionConfigUpdated': {\n                    this.handleSessionMetadataUpdated(m.metadata);\n                    break;\n                }\n                case 'Event': {\n                    this.handleIncomingEvent(m);\n                    break;\n                }\n            }\n            // resolve request promises\n            if (m.id !== undefined) {\n                (_a = this.requestResolutions.get(m.id)) === null || _a === void 0 ? void 0 : _a(m);\n            }\n        });\n        this.ws = ws;\n        this.http = new WalletLinkHTTP_1.WalletLinkHTTP(linkAPIUrl, session.id, session.key);\n    }\n    /**\n     * Make a connection to the server\n     */\n    connect() {\n        if (this.destroyed) {\n            throw new Error('instance is destroyed');\n        }\n        this.ws.connect();\n    }\n    /**\n     * Terminate connection, and mark as destroyed. To reconnect, create a new\n     * instance of WalletSDKConnection\n     */\n    destroy() {\n        this.destroyed = true;\n        this.ws.disconnect();\n        this.listener = undefined;\n    }\n    get isDestroyed() {\n        return this.destroyed;\n    }\n    get connected() {\n        return this._connected;\n    }\n    set connected(connected) {\n        var _a;\n        this._connected = connected;\n        if (connected)\n            (_a = this.onceConnected) === null || _a === void 0 ? void 0 : _a.call(this);\n    }\n    setOnceConnected(callback) {\n        return new Promise((resolve) => {\n            if (this.connected) {\n                callback().then(resolve);\n            }\n            else {\n                this.onceConnected = () => {\n                    callback().then(resolve);\n                    this.onceConnected = undefined;\n                };\n            }\n        });\n    }\n    get linked() {\n        return this._linked;\n    }\n    set linked(linked) {\n        var _a, _b;\n        this._linked = linked;\n        if (linked)\n            (_a = this.onceLinked) === null || _a === void 0 ? void 0 : _a.call(this);\n        (_b = this.listener) === null || _b === void 0 ? void 0 : _b.linkedUpdated(linked);\n    }\n    setOnceLinked(callback) {\n        return new Promise((resolve) => {\n            if (this.linked) {\n                callback().then(resolve);\n            }\n            else {\n                this.onceLinked = () => {\n                    callback().then(resolve);\n                    this.onceLinked = undefined;\n                };\n            }\n        });\n    }\n    async handleIncomingEvent(m) {\n        var _a;\n        if (m.type !== 'Event' || m.event !== 'Web3Response') {\n            return;\n        }\n        {\n            const decryptedData = await this.cipher.decrypt(m.data);\n            const message = JSON.parse(decryptedData);\n            if (message.type !== 'WEB3_RESPONSE')\n                return;\n            (_a = this.listener) === null || _a === void 0 ? void 0 : _a.handleWeb3ResponseMessage(message);\n        }\n    }\n    async checkUnseenEvents() {\n        if (!this.connected) {\n            this.shouldFetchUnseenEventsOnConnect = true;\n            return;\n        }\n        await new Promise((resolve) => setTimeout(resolve, 250));\n        try {\n            await this.fetchUnseenEventsAPI();\n        }\n        catch (e) {\n            console.error('Unable to check for unseen events', e);\n        }\n    }\n    async fetchUnseenEventsAPI() {\n        this.shouldFetchUnseenEventsOnConnect = false;\n        const responseEvents = await this.http.fetchUnseenEvents();\n        responseEvents.forEach((e) => this.handleIncomingEvent(e));\n    }\n    /**\n     * Set session metadata in SessionConfig object\n     * @param key\n     * @param value\n     * @returns a Promise that completes when successful\n     */\n    async setSessionMetadata(key, value) {\n        const message = {\n            type: 'SetSessionConfig',\n            id: (0, type_1.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n            metadata: { [key]: value },\n        };\n        return this.setOnceConnected(async () => {\n            const res = await this.makeRequest(message);\n            if (res.type === 'Fail') {\n                throw new Error(res.error || 'failed to set session metadata');\n            }\n        });\n    }\n    /**\n     * Publish an event and emit event ID when successful\n     * @param event event name\n     * @param unencryptedData unencrypted event data\n     * @param callWebhook whether the webhook should be invoked\n     * @returns a Promise that emits event ID when successful\n     */\n    async publishEvent(event, unencryptedData, callWebhook = false) {\n        const data = await this.cipher.encrypt(JSON.stringify(Object.assign(Object.assign({}, unencryptedData), { origin: location.origin, relaySource: 'coinbaseWalletExtension' in window && window.coinbaseWalletExtension\n                ? 'injected_sdk'\n                : 'sdk' })));\n        const message = {\n            type: 'PublishEvent',\n            id: (0, type_1.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n            event,\n            data,\n            callWebhook,\n        };\n        return this.setOnceLinked(async () => {\n            const res = await this.makeRequest(message);\n            if (res.type === 'Fail') {\n                throw new Error(res.error || 'failed to publish event');\n            }\n            return res.eventId;\n        });\n    }\n    sendData(message) {\n        this.ws.sendData(JSON.stringify(message));\n    }\n    updateLastHeartbeat() {\n        this.lastHeartbeatResponse = Date.now();\n    }\n    heartbeat() {\n        if (Date.now() - this.lastHeartbeatResponse > HEARTBEAT_INTERVAL * 2) {\n            this.ws.disconnect();\n            return;\n        }\n        try {\n            this.ws.sendData('h');\n        }\n        catch (_a) {\n            // noop\n        }\n    }\n    async makeRequest(message, timeout = REQUEST_TIMEOUT) {\n        const reqId = message.id;\n        this.sendData(message);\n        // await server message with corresponding id\n        let timeoutId;\n        return Promise.race([\n            new Promise((_, reject) => {\n                timeoutId = window.setTimeout(() => {\n                    reject(new Error(`request ${reqId} timed out`));\n                }, timeout);\n            }),\n            new Promise((resolve) => {\n                this.requestResolutions.set(reqId, (m) => {\n                    clearTimeout(timeoutId); // clear the timeout\n                    resolve(m);\n                    this.requestResolutions.delete(reqId);\n                });\n            }),\n        ]);\n    }\n    async authenticate() {\n        const m = {\n            type: 'HostSession',\n            id: (0, type_1.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n            sessionKey: this.session.key,\n        };\n        const res = await this.makeRequest(m);\n        if (res.type === 'Fail') {\n            throw new Error(res.error || 'failed to authenticate');\n        }\n    }\n    sendIsLinked() {\n        const m = {\n            type: 'IsLinked',\n            id: (0, type_1.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n        };\n        this.sendData(m);\n    }\n    sendGetSessionConfig() {\n        const m = {\n            type: 'GetSessionConfig',\n            id: (0, type_1.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n        };\n        this.sendData(m);\n    }\n}\nexports.WalletLinkConnection = WalletLinkConnection;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkHTTP = void 0;\nclass WalletLinkHTTP {\n    constructor(linkAPIUrl, sessionId, sessionKey) {\n        this.linkAPIUrl = linkAPIUrl;\n        this.sessionId = sessionId;\n        const credentials = `${sessionId}:${sessionKey}`;\n        this.auth = `Basic ${btoa(credentials)}`;\n    }\n    // mark unseen events as seen\n    async markUnseenEventsAsSeen(events) {\n        return Promise.all(events.map((e) => fetch(`${this.linkAPIUrl}/events/${e.eventId}/seen`, {\n            method: 'POST',\n            headers: {\n                Authorization: this.auth,\n            },\n        }))).catch((error) => console.error('Unabled to mark event as failed:', error));\n    }\n    async fetchUnseenEvents() {\n        var _a;\n        const response = await fetch(`${this.linkAPIUrl}/events?unseen=true`, {\n            headers: {\n                Authorization: this.auth,\n            },\n        });\n        if (response.ok) {\n            const { events, error } = (await response.json());\n            if (error) {\n                throw new Error(`Check unseen events failed: ${error}`);\n            }\n            const responseEvents = (_a = events === null || events === void 0 ? void 0 : events.filter((e) => e.event === 'Web3Response').map((e) => ({\n                type: 'Event',\n                sessionId: this.sessionId,\n                eventId: e.id,\n                event: e.event,\n                data: e.data,\n            }))) !== null && _a !== void 0 ? _a : [];\n            this.markUnseenEventsAsSeen(responseEvents);\n            return responseEvents;\n        }\n        throw new Error(`Check unseen events failed: ${response.status}`);\n    }\n}\nexports.WalletLinkHTTP = WalletLinkHTTP;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkWebSocket = exports.ConnectionState = void 0;\nvar ConnectionState;\n(function (ConnectionState) {\n    ConnectionState[ConnectionState[\"DISCONNECTED\"] = 0] = \"DISCONNECTED\";\n    ConnectionState[ConnectionState[\"CONNECTING\"] = 1] = \"CONNECTING\";\n    ConnectionState[ConnectionState[\"CONNECTED\"] = 2] = \"CONNECTED\";\n})(ConnectionState || (exports.ConnectionState = ConnectionState = {}));\nclass WalletLinkWebSocket {\n    setConnectionStateListener(listener) {\n        this.connectionStateListener = listener;\n    }\n    setIncomingDataListener(listener) {\n        this.incomingDataListener = listener;\n    }\n    /**\n     * Constructor\n     * @param url WebSocket server URL\n     * @param [WebSocketClass] Custom WebSocket implementation\n     */\n    constructor(url, WebSocketClass = WebSocket) {\n        this.WebSocketClass = WebSocketClass;\n        this.webSocket = null;\n        this.pendingData = [];\n        this.url = url.replace(/^http/, 'ws');\n    }\n    /**\n     * Make a websocket connection\n     * @returns a Promise that resolves when connected\n     */\n    async connect() {\n        if (this.webSocket) {\n            throw new Error('webSocket object is not null');\n        }\n        return new Promise((resolve, reject) => {\n            var _a;\n            let webSocket;\n            try {\n                this.webSocket = webSocket = new this.WebSocketClass(this.url);\n            }\n            catch (err) {\n                reject(err);\n                return;\n            }\n            (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.CONNECTING);\n            webSocket.onclose = (evt) => {\n                var _a;\n                this.clearWebSocket();\n                reject(new Error(`websocket error ${evt.code}: ${evt.reason}`));\n                (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.DISCONNECTED);\n            };\n            webSocket.onopen = (_) => {\n                var _a;\n                resolve();\n                (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.CONNECTED);\n                if (this.pendingData.length > 0) {\n                    const pending = [...this.pendingData];\n                    pending.forEach((data) => this.sendData(data));\n                    this.pendingData = [];\n                }\n            };\n            webSocket.onmessage = (evt) => {\n                var _a, _b;\n                if (evt.data === 'h') {\n                    (_a = this.incomingDataListener) === null || _a === void 0 ? void 0 : _a.call(this, {\n                        type: 'Heartbeat',\n                    });\n                }\n                else {\n                    try {\n                        const message = JSON.parse(evt.data);\n                        (_b = this.incomingDataListener) === null || _b === void 0 ? void 0 : _b.call(this, message);\n                    }\n                    catch (_c) {\n                        /* empty */\n                    }\n                }\n            };\n        });\n    }\n    /**\n     * Disconnect from server\n     */\n    disconnect() {\n        var _a;\n        const { webSocket } = this;\n        if (!webSocket) {\n            return;\n        }\n        this.clearWebSocket();\n        (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.DISCONNECTED);\n        this.connectionStateListener = undefined;\n        this.incomingDataListener = undefined;\n        try {\n            webSocket.close();\n        }\n        catch (_b) {\n            // noop\n        }\n    }\n    /**\n     * Send data to server\n     * @param data text to send\n     */\n    sendData(data) {\n        const { webSocket } = this;\n        if (!webSocket) {\n            this.pendingData.push(data);\n            this.connect();\n            return;\n        }\n        webSocket.send(data);\n    }\n    clearWebSocket() {\n        const { webSocket } = this;\n        if (!webSocket) {\n            return;\n        }\n        this.webSocket = null;\n        webSocket.onclose = null;\n        webSocket.onerror = null;\n        webSocket.onmessage = null;\n        webSocket.onopen = null;\n    }\n}\nexports.WalletLinkWebSocket = WalletLinkWebSocket;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.APP_VERSION_KEY = exports.LOCAL_STORAGE_ADDRESSES_KEY = exports.WALLET_USER_NAME_KEY = void 0;\nexports.WALLET_USER_NAME_KEY = 'walletUsername';\nexports.LOCAL_STORAGE_ADDRESSES_KEY = 'Addresses';\nexports.APP_VERSION_KEY = 'AppVersion';\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkSession = void 0;\nconst sha_js_1 = __webpack_require__(/*! sha.js */ \"./node_modules/sha.js/index.js\");\nconst util_1 = __webpack_require__(/*! ../../../../core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nconst STORAGE_KEY_SESSION_ID = 'session:id';\nconst STORAGE_KEY_SESSION_SECRET = 'session:secret';\nconst STORAGE_KEY_SESSION_LINKED = 'session:linked';\nclass WalletLinkSession {\n    constructor(storage, id, secret, linked) {\n        this._storage = storage;\n        this._id = id || (0, util_1.randomBytesHex)(16);\n        this._secret = secret || (0, util_1.randomBytesHex)(32);\n        this._key = new sha_js_1.sha256()\n            .update(`${this._id}, ${this._secret} WalletLink`) // ensure old sessions stay connected\n            .digest('hex');\n        this._linked = !!linked;\n    }\n    static load(storage) {\n        const id = storage.getItem(STORAGE_KEY_SESSION_ID);\n        const linked = storage.getItem(STORAGE_KEY_SESSION_LINKED);\n        const secret = storage.getItem(STORAGE_KEY_SESSION_SECRET);\n        if (id && secret) {\n            return new WalletLinkSession(storage, id, secret, linked === '1');\n        }\n        return null;\n    }\n    get id() {\n        return this._id;\n    }\n    get secret() {\n        return this._secret;\n    }\n    get key() {\n        return this._key;\n    }\n    get linked() {\n        return this._linked;\n    }\n    set linked(val) {\n        this._linked = val;\n        this.persistLinked();\n    }\n    save() {\n        this._storage.setItem(STORAGE_KEY_SESSION_ID, this._id);\n        this._storage.setItem(STORAGE_KEY_SESSION_SECRET, this._secret);\n        this.persistLinked();\n        return this;\n    }\n    persistLinked() {\n        this._storage.setItem(STORAGE_KEY_SESSION_LINKED, this._linked ? '1' : '0');\n    }\n}\nexports.WalletLinkSession = WalletLinkSession;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isErrorResponse = void 0;\nfunction isErrorResponse(response) {\n    return response.errorMessage !== undefined;\n}\nexports.isErrorResponse = isErrorResponse;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WLMobileRelayUI = void 0;\nconst RedirectDialog_1 = __webpack_require__(/*! ./components/RedirectDialog/RedirectDialog */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js\");\nconst util_1 = __webpack_require__(/*! ./components/util */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\nconst constants_1 = __webpack_require__(/*! ../../../../core/constants */ \"./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\");\nclass WLMobileRelayUI {\n    constructor() {\n        this.attached = false;\n        this.redirectDialog = new RedirectDialog_1.RedirectDialog();\n    }\n    attach() {\n        if (this.attached) {\n            throw new Error('Coinbase Wallet SDK UI is already attached');\n        }\n        this.redirectDialog.attach();\n        this.attached = true;\n    }\n    redirectToCoinbaseWallet(walletLinkUrl) {\n        const url = new URL(constants_1.CBW_MOBILE_DEEPLINK_URL);\n        url.searchParams.append('redirect_url', (0, util_1.getLocation)().href);\n        if (walletLinkUrl) {\n            url.searchParams.append('wl_url', walletLinkUrl);\n        }\n        const anchorTag = document.createElement('a');\n        anchorTag.target = 'cbw-opener';\n        anchorTag.href = url.href;\n        anchorTag.rel = 'noreferrer noopener';\n        anchorTag.click();\n    }\n    openCoinbaseWalletDeeplink(walletLinkUrl) {\n        this.redirectDialog.present({\n            title: 'Redirecting to Coinbase Wallet...',\n            buttonText: 'Open',\n            onButtonClick: () => {\n                this.redirectToCoinbaseWallet(walletLinkUrl);\n            },\n        });\n        setTimeout(() => {\n            this.redirectToCoinbaseWallet(walletLinkUrl);\n        }, 99);\n    }\n    showConnecting(_options) {\n        // it uses the return callback to clear the dialog\n        return () => {\n            this.redirectDialog.clear();\n        };\n    }\n}\nexports.WLMobileRelayUI = WLMobileRelayUI;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WalletLinkRelayUI = void 0;\nconst cssReset_1 = __webpack_require__(/*! ./components/cssReset/cssReset */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js\");\nconst Snackbar_1 = __webpack_require__(/*! ./components/Snackbar/Snackbar */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js\");\nclass WalletLinkRelayUI {\n    constructor() {\n        this.attached = false;\n        this.snackbar = new Snackbar_1.Snackbar();\n    }\n    attach() {\n        if (this.attached) {\n            throw new Error('Coinbase Wallet SDK UI is already attached');\n        }\n        const el = document.documentElement;\n        const container = document.createElement('div');\n        container.className = '-cbwsdk-css-reset';\n        el.appendChild(container);\n        this.snackbar.attach(container);\n        this.attached = true;\n        (0, cssReset_1.injectCssReset)();\n    }\n    showConnecting(options) {\n        let snackbarProps;\n        if (options.isUnlinkedErrorState) {\n            snackbarProps = {\n                autoExpand: true,\n                message: 'Connection lost',\n                menuItems: [\n                    {\n                        isRed: false,\n                        info: 'Reset connection',\n                        svgWidth: '10',\n                        svgHeight: '11',\n                        path: 'M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z',\n                        defaultFillRule: 'evenodd',\n                        defaultClipRule: 'evenodd',\n                        onClick: options.onResetConnection,\n                    },\n                ],\n            };\n        }\n        else {\n            snackbarProps = {\n                message: 'Confirm on phone',\n                menuItems: [\n                    {\n                        isRed: true,\n                        info: 'Cancel transaction',\n                        svgWidth: '11',\n                        svgHeight: '11',\n                        path: 'M10.3711 1.52346L9.21775 0.370117L5.37109 4.21022L1.52444 0.370117L0.371094 1.52346L4.2112 5.37012L0.371094 9.21677L1.52444 10.3701L5.37109 6.53001L9.21775 10.3701L10.3711 9.21677L6.53099 5.37012L10.3711 1.52346Z',\n                        defaultFillRule: 'inherit',\n                        defaultClipRule: 'inherit',\n                        onClick: options.onCancel,\n                    },\n                    {\n                        isRed: false,\n                        info: 'Reset connection',\n                        svgWidth: '10',\n                        svgHeight: '11',\n                        path: 'M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z',\n                        defaultFillRule: 'evenodd',\n                        defaultClipRule: 'evenodd',\n                        onClick: options.onResetConnection,\n                    },\n                ],\n            };\n        }\n        return this.snackbar.presentItem(snackbarProps);\n    }\n}\nexports.WalletLinkRelayUI = WalletLinkRelayUI;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = (() => `.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop{position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s;background-color:rgba(10,11,13,.5)}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box{display:block;position:fixed;top:50%;left:50%;transform:translate(-50%, -50%);padding:20px;border-radius:8px;background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box p{display:block;font-weight:400;font-size:14px;line-height:20px;padding-bottom:12px;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box button{appearance:none;border:none;background:none;color:#0052ff;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark{background-color:#0a0b0d;color:#fff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark button{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light{background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light button{color:#0052ff}`)();\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js ***!
  \*********************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RedirectDialog = void 0;\nconst clsx_1 = __importDefault(__webpack_require__(/*! clsx */ \"./node_modules/clsx/dist/clsx.m.js\"));\nconst preact_1 = __webpack_require__(/*! preact */ \"./node_modules/preact/dist/preact.module.js\");\nconst cssReset_1 = __webpack_require__(/*! ../cssReset/cssReset */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js\");\nconst Snackbar_1 = __webpack_require__(/*! ../Snackbar/Snackbar */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js\");\nconst util_1 = __webpack_require__(/*! ../util */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\nconst RedirectDialog_css_1 = __importDefault(__webpack_require__(/*! ./RedirectDialog-css */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js\"));\nclass RedirectDialog {\n    constructor() {\n        this.root = null;\n        this.darkMode = (0, util_1.isDarkMode)();\n    }\n    attach() {\n        const el = document.documentElement;\n        this.root = document.createElement('div');\n        this.root.className = '-cbwsdk-css-reset';\n        el.appendChild(this.root);\n        (0, cssReset_1.injectCssReset)();\n    }\n    present(props) {\n        this.render(props);\n    }\n    clear() {\n        this.render(null);\n    }\n    render(props) {\n        if (!this.root)\n            return;\n        (0, preact_1.render)(null, this.root);\n        if (!props)\n            return;\n        (0, preact_1.render)((0, preact_1.h)(RedirectDialogContent, Object.assign({}, props, { onDismiss: () => {\n                this.clear();\n            }, darkMode: this.darkMode })), this.root);\n    }\n}\nexports.RedirectDialog = RedirectDialog;\nconst RedirectDialogContent = ({ title, buttonText, darkMode, onButtonClick, onDismiss }) => {\n    const theme = darkMode ? 'dark' : 'light';\n    return ((0, preact_1.h)(Snackbar_1.SnackbarContainer, { darkMode: darkMode },\n        (0, preact_1.h)(\"div\", { class: \"-cbwsdk-redirect-dialog\" },\n            (0, preact_1.h)(\"style\", null, RedirectDialog_css_1.default),\n            (0, preact_1.h)(\"div\", { class: \"-cbwsdk-redirect-dialog-backdrop\", onClick: onDismiss }),\n            (0, preact_1.h)(\"div\", { class: (0, clsx_1.default)('-cbwsdk-redirect-dialog-box', theme) },\n                (0, preact_1.h)(\"p\", null, title),\n                (0, preact_1.h)(\"button\", { onClick: onButtonClick }, buttonText)))));\n};\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = (() => `.-cbwsdk-css-reset .-gear-container{margin-left:16px !important;margin-right:9px !important;display:flex;align-items:center;justify-content:center;width:24px;height:24px;transition:opacity .25s}.-cbwsdk-css-reset .-gear-container *{user-select:none}.-cbwsdk-css-reset .-gear-container svg{opacity:0;position:absolute}.-cbwsdk-css-reset .-gear-icon{height:12px;width:12px;z-index:10000}.-cbwsdk-css-reset .-cbwsdk-snackbar{align-items:flex-end;display:flex;flex-direction:column;position:fixed;right:0;top:0;z-index:2147483647}.-cbwsdk-css-reset .-cbwsdk-snackbar *{user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance{display:flex;flex-direction:column;margin:8px 16px 0 16px;overflow:visible;text-align:left;transform:translateX(0);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header:hover .-gear-container svg{opacity:1}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header{display:flex;align-items:center;background:#fff;overflow:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-cblogo{margin:8px 8px 8px 8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-message{color:#000;font-size:13px;line-height:1.5;user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu{background:#fff;transition:opacity .25s ease-in-out,transform .25s linear,visibility 0s;visibility:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;opacity:0;flex-direction:column;padding-left:8px;padding-right:8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:last-child{margin-bottom:8px !important}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover{background:#f5f7f8;border-radius:6px;transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover span{color:#050f19;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover svg path{fill:#000;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item{visibility:inherit;height:35px;margin-top:8px;margin-bottom:0;display:flex;flex-direction:row;align-items:center;padding:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item *{visibility:inherit;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover{background:rgba(223,95,103,.2);transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover svg path{fill:#df5f67;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover span{color:#df5f67;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-info{color:#aaa;font-size:13px;margin:0 8px 0 32px;position:absolute}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-hidden{opacity:0;text-align:left;transform:translateX(25%);transition:opacity .5s linear}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-expanded .-cbwsdk-snackbar-instance-menu{opacity:1;display:flex;transform:translateY(8px);visibility:visible}`)();\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js ***!
  \*********************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SnackbarInstance = exports.SnackbarContainer = exports.Snackbar = void 0;\nconst clsx_1 = __importDefault(__webpack_require__(/*! clsx */ \"./node_modules/clsx/dist/clsx.m.js\"));\nconst preact_1 = __webpack_require__(/*! preact */ \"./node_modules/preact/dist/preact.module.js\");\nconst hooks_1 = __webpack_require__(/*! preact/hooks */ \"./node_modules/preact/hooks/dist/hooks.module.js\");\nconst util_1 = __webpack_require__(/*! ../util */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\nconst Snackbar_css_1 = __importDefault(__webpack_require__(/*! ./Snackbar-css */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js\"));\nconst cblogo = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDkyIDEwLjQxOWE4LjkzIDguOTMgMCAwMTguOTMtOC45M2gxMS4xNjNhOC45MyA4LjkzIDAgMDE4LjkzIDguOTN2MTEuMTYzYTguOTMgOC45MyAwIDAxLTguOTMgOC45M0gxMC40MjJhOC45MyA4LjkzIDAgMDEtOC45My04LjkzVjEwLjQxOXoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEwLjQxOSAwSDIxLjU4QzI3LjMzNSAwIDMyIDQuNjY1IDMyIDEwLjQxOVYyMS41OEMzMiAyNy4zMzUgMjcuMzM1IDMyIDIxLjU4MSAzMkgxMC40MkM0LjY2NSAzMiAwIDI3LjMzNSAwIDIxLjU4MVYxMC40MkMwIDQuNjY1IDQuNjY1IDAgMTAuNDE5IDB6bTAgMS40ODhhOC45MyA4LjkzIDAgMDAtOC45MyA4LjkzdjExLjE2M2E4LjkzIDguOTMgMCAwMDguOTMgOC45M0gyMS41OGE4LjkzIDguOTMgMCAwMDguOTMtOC45M1YxMC40MmE4LjkzIDguOTMgMCAwMC04LjkzLTguOTNIMTAuNDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS45OTggMjYuMDQ5Yy01LjU0OSAwLTEwLjA0Ny00LjQ5OC0xMC4wNDctMTAuMDQ3IDAtNS41NDggNC40OTgtMTAuMDQ2IDEwLjA0Ny0xMC4wNDYgNS41NDggMCAxMC4wNDYgNC40OTggMTAuMDQ2IDEwLjA0NiAwIDUuNTQ5LTQuNDk4IDEwLjA0Ny0xMC4wNDYgMTAuMDQ3eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xMi43NjIgMTQuMjU0YzAtLjgyMi42NjctMS40ODkgMS40ODktMS40ODloMy40OTdjLjgyMiAwIDEuNDg4LjY2NiAxLjQ4OCAxLjQ4OXYzLjQ5N2MwIC44MjItLjY2NiAxLjQ4OC0xLjQ4OCAxLjQ4OGgtMy40OTdhMS40ODggMS40ODggMCAwMS0xLjQ4OS0xLjQ4OHYtMy40OTh6IiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+`;\nconst gearIcon = `data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;\nclass Snackbar {\n    constructor() {\n        this.items = new Map();\n        this.nextItemKey = 0;\n        this.root = null;\n        this.darkMode = (0, util_1.isDarkMode)();\n    }\n    attach(el) {\n        this.root = document.createElement('div');\n        this.root.className = '-cbwsdk-snackbar-root';\n        el.appendChild(this.root);\n        this.render();\n    }\n    presentItem(itemProps) {\n        const key = this.nextItemKey++;\n        this.items.set(key, itemProps);\n        this.render();\n        return () => {\n            this.items.delete(key);\n            this.render();\n        };\n    }\n    clear() {\n        this.items.clear();\n        this.render();\n    }\n    render() {\n        if (!this.root) {\n            return;\n        }\n        (0, preact_1.render)((0, preact_1.h)(\"div\", null,\n            (0, preact_1.h)(exports.SnackbarContainer, { darkMode: this.darkMode }, Array.from(this.items.entries()).map(([key, itemProps]) => ((0, preact_1.h)(exports.SnackbarInstance, Object.assign({}, itemProps, { key: key })))))), this.root);\n    }\n}\nexports.Snackbar = Snackbar;\nconst SnackbarContainer = (props) => ((0, preact_1.h)(\"div\", { class: (0, clsx_1.default)('-cbwsdk-snackbar-container') },\n    (0, preact_1.h)(\"style\", null, Snackbar_css_1.default),\n    (0, preact_1.h)(\"div\", { class: \"-cbwsdk-snackbar\" }, props.children)));\nexports.SnackbarContainer = SnackbarContainer;\nconst SnackbarInstance = ({ autoExpand, message, menuItems, }) => {\n    const [hidden, setHidden] = (0, hooks_1.useState)(true);\n    const [expanded, setExpanded] = (0, hooks_1.useState)(autoExpand !== null && autoExpand !== void 0 ? autoExpand : false);\n    (0, hooks_1.useEffect)(() => {\n        const timers = [\n            window.setTimeout(() => {\n                setHidden(false);\n            }, 1),\n            window.setTimeout(() => {\n                setExpanded(true);\n            }, 10000),\n        ];\n        return () => {\n            timers.forEach(window.clearTimeout);\n        };\n    });\n    const toggleExpanded = () => {\n        setExpanded(!expanded);\n    };\n    return ((0, preact_1.h)(\"div\", { class: (0, clsx_1.default)('-cbwsdk-snackbar-instance', hidden && '-cbwsdk-snackbar-instance-hidden', expanded && '-cbwsdk-snackbar-instance-expanded') },\n        (0, preact_1.h)(\"div\", { class: \"-cbwsdk-snackbar-instance-header\", onClick: toggleExpanded },\n            (0, preact_1.h)(\"img\", { src: cblogo, class: \"-cbwsdk-snackbar-instance-header-cblogo\" }),\n            ' ',\n            (0, preact_1.h)(\"div\", { class: \"-cbwsdk-snackbar-instance-header-message\" }, message),\n            (0, preact_1.h)(\"div\", { class: \"-gear-container\" },\n                !expanded && ((0, preact_1.h)(\"svg\", { width: \"24\", height: \"24\", viewBox: \"0 0 24 24\", fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" },\n                    (0, preact_1.h)(\"circle\", { cx: \"12\", cy: \"12\", r: \"12\", fill: \"#F5F7F8\" }))),\n                (0, preact_1.h)(\"img\", { src: gearIcon, class: \"-gear-icon\", title: \"Expand\" }))),\n        menuItems && menuItems.length > 0 && ((0, preact_1.h)(\"div\", { class: \"-cbwsdk-snackbar-instance-menu\" }, menuItems.map((action, i) => ((0, preact_1.h)(\"div\", { class: (0, clsx_1.default)('-cbwsdk-snackbar-instance-menu-item', action.isRed && '-cbwsdk-snackbar-instance-menu-item-is-red'), onClick: action.onClick, key: i },\n            (0, preact_1.h)(\"svg\", { width: action.svgWidth, height: action.svgHeight, viewBox: \"0 0 10 11\", fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" },\n                (0, preact_1.h)(\"path\", { \"fill-rule\": action.defaultFillRule, \"clip-rule\": action.defaultClipRule, d: action.path, fill: \"#AAAAAA\" })),\n            (0, preact_1.h)(\"span\", { class: (0, clsx_1.default)('-cbwsdk-snackbar-instance-menu-item-info', action.isRed && '-cbwsdk-snackbar-instance-menu-item-info-is-red') }, action.info))))))));\n};\nexports.SnackbarInstance = SnackbarInstance;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = (() => `@namespace svg \"http://www.w3.org/2000/svg\";.-cbwsdk-css-reset,.-cbwsdk-css-reset *{animation:none;animation-delay:0;animation-direction:normal;animation-duration:0;animation-fill-mode:none;animation-iteration-count:1;animation-name:none;animation-play-state:running;animation-timing-function:ease;backface-visibility:visible;background:0;background-attachment:scroll;background-clip:border-box;background-color:rgba(0,0,0,0);background-image:none;background-origin:padding-box;background-position:0 0;background-position-x:0;background-position-y:0;background-repeat:repeat;background-size:auto auto;border:0;border-style:none;border-width:medium;border-color:inherit;border-bottom:0;border-bottom-color:inherit;border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-style:none;border-bottom-width:medium;border-collapse:separate;border-image:none;border-left:0;border-left-color:inherit;border-left-style:none;border-left-width:medium;border-radius:0;border-right:0;border-right-color:inherit;border-right-style:none;border-right-width:medium;border-spacing:0;border-top:0;border-top-color:inherit;border-top-left-radius:0;border-top-right-radius:0;border-top-style:none;border-top-width:medium;box-shadow:none;box-sizing:border-box;caption-side:top;clear:none;clip:auto;color:inherit;columns:auto;column-count:auto;column-fill:balance;column-gap:normal;column-rule:medium none currentColor;column-rule-color:currentColor;column-rule-style:none;column-rule-width:none;column-span:1;column-width:auto;counter-increment:none;counter-reset:none;direction:ltr;empty-cells:show;float:none;font:normal;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Helvetica Neue\",Arial,sans-serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;height:auto;hyphens:none;letter-spacing:normal;line-height:normal;list-style:none;list-style-image:none;list-style-position:outside;list-style-type:disc;margin:0;margin-bottom:0;margin-left:0;margin-right:0;margin-top:0;opacity:1;orphans:0;outline:0;outline-color:invert;outline-style:none;outline-width:medium;overflow:visible;overflow-x:visible;overflow-y:visible;padding:0;padding-bottom:0;padding-left:0;padding-right:0;padding-top:0;page-break-after:auto;page-break-before:auto;page-break-inside:auto;perspective:none;perspective-origin:50% 50%;pointer-events:auto;position:static;quotes:\"\\\\201C\" \"\\\\201D\" \"\\\\2018\" \"\\\\2019\";tab-size:8;table-layout:auto;text-align:inherit;text-align-last:auto;text-decoration:none;text-decoration-color:inherit;text-decoration-line:none;text-decoration-style:solid;text-indent:0;text-shadow:none;text-transform:none;transform:none;transform-style:flat;transition:none;transition-delay:0s;transition-duration:0s;transition-property:none;transition-timing-function:ease;unicode-bidi:normal;vertical-align:baseline;visibility:visible;white-space:normal;widows:0;word-spacing:normal;z-index:auto}.-cbwsdk-css-reset strong{font-weight:bold}.-cbwsdk-css-reset *{box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Helvetica Neue\",Arial,sans-serif;line-height:1}.-cbwsdk-css-reset [class*=container]{margin:0;padding:0}.-cbwsdk-css-reset style{display:none}`)();\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js ***!
  \*********************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.injectCssReset = void 0;\nconst cssReset_css_1 = __importDefault(__webpack_require__(/*! ./cssReset-css */ \"./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js\"));\nfunction injectCssReset() {\n    const styleEl = document.createElement('style');\n    styleEl.type = 'text/css';\n    styleEl.appendChild(document.createTextNode(cssReset_css_1.default));\n    document.documentElement.appendChild(styleEl);\n}\nexports.injectCssReset = injectCssReset;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isDarkMode = exports.isMobileWeb = exports.getLocation = exports.createQrUrl = void 0;\nfunction createQrUrl(sessionId, sessionSecret, serverUrl, isParentConnection, version, chainId) {\n    const sessionIdKey = isParentConnection ? 'parent-id' : 'id';\n    const query = new URLSearchParams({\n        [sessionIdKey]: sessionId,\n        secret: sessionSecret,\n        server: serverUrl,\n        v: version,\n        chainId: chainId.toString(),\n    }).toString();\n    const qrUrl = `${serverUrl}/#/link?${query}`;\n    return qrUrl;\n}\nexports.createQrUrl = createQrUrl;\nfunction isInIFrame() {\n    try {\n        return window.frameElement !== null;\n    }\n    catch (e) {\n        return false;\n    }\n}\nfunction getLocation() {\n    try {\n        if (isInIFrame() && window.top) {\n            return window.top.location;\n        }\n        return window.location;\n    }\n    catch (e) {\n        return window.location;\n    }\n}\nexports.getLocation = getLocation;\nfunction isMobileWeb() {\n    var _a;\n    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test((_a = window === null || window === void 0 ? void 0 : window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent);\n}\nexports.isMobileWeb = isMobileWeb;\nfunction isDarkMode() {\n    var _a, _b;\n    return (_b = (_a = window === null || window === void 0 ? void 0 : window.matchMedia) === null || _a === void 0 ? void 0 : _a.call(window, '(prefers-color-scheme: dark)').matches) !== null && _b !== void 0 ? _b : false;\n}\nexports.isDarkMode = isDarkMode;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n// Copyright (c) 2018-2024 Coinbase, Inc. <https://www.coinbase.com/>\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ScopedLocalStorage = void 0;\nclass ScopedLocalStorage {\n    constructor(scope, module) {\n        this.scope = scope;\n        this.module = module;\n    }\n    setItem(key, value) {\n        localStorage.setItem(this.scopedKey(key), value);\n    }\n    getItem(key) {\n        return localStorage.getItem(this.scopedKey(key));\n    }\n    removeItem(key) {\n        localStorage.removeItem(this.scopedKey(key));\n    }\n    clear() {\n        const prefix = this.scopedKey('');\n        const keysToRemove = [];\n        for (let i = 0; i < localStorage.length; i++) {\n            const key = localStorage.key(i);\n            if (typeof key === 'string' && key.startsWith(prefix)) {\n                keysToRemove.push(key);\n            }\n        }\n        keysToRemove.forEach((key) => localStorage.removeItem(key));\n    }\n    scopedKey(key) {\n        return `-${this.scope}${this.module ? `:${this.module}` : ''}:${key}`;\n    }\n    static clearAll() {\n        new ScopedLocalStorage('CBWSDK').clear();\n        new ScopedLocalStorage('walletlink').clear();\n    }\n}\nexports.ScopedLocalStorage = ScopedLocalStorage;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/util/ScopedLocalStorage.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js":
/*!***************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.decryptContent = exports.encryptContent = exports.importKeyFromHexString = exports.exportKeyToHexString = exports.decrypt = exports.encrypt = exports.deriveSharedSecret = exports.generateKeyPair = void 0;\nconst util_1 = __webpack_require__(/*! ../core/type/util */ \"./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\nasync function generateKeyPair() {\n    return crypto.subtle.generateKey({\n        name: 'ECDH',\n        namedCurve: 'P-256',\n    }, true, ['deriveKey']);\n}\nexports.generateKeyPair = generateKeyPair;\nasync function deriveSharedSecret(ownPrivateKey, peerPublicKey) {\n    return crypto.subtle.deriveKey({\n        name: 'ECDH',\n        public: peerPublicKey,\n    }, ownPrivateKey, {\n        name: 'AES-GCM',\n        length: 256,\n    }, false, ['encrypt', 'decrypt']);\n}\nexports.deriveSharedSecret = deriveSharedSecret;\nasync function encrypt(sharedSecret, plainText) {\n    const iv = crypto.getRandomValues(new Uint8Array(12));\n    const cipherText = await crypto.subtle.encrypt({\n        name: 'AES-GCM',\n        iv,\n    }, sharedSecret, new TextEncoder().encode(plainText));\n    return { iv, cipherText };\n}\nexports.encrypt = encrypt;\nasync function decrypt(sharedSecret, { iv, cipherText }) {\n    const plainText = await crypto.subtle.decrypt({\n        name: 'AES-GCM',\n        iv,\n    }, sharedSecret, cipherText);\n    return new TextDecoder().decode(plainText);\n}\nexports.decrypt = decrypt;\nfunction getFormat(keyType) {\n    switch (keyType) {\n        case 'public':\n            return 'spki';\n        case 'private':\n            return 'pkcs8';\n    }\n}\nasync function exportKeyToHexString(type, key) {\n    const format = getFormat(type);\n    const exported = await crypto.subtle.exportKey(format, key);\n    return (0, util_1.uint8ArrayToHex)(new Uint8Array(exported));\n}\nexports.exportKeyToHexString = exportKeyToHexString;\nasync function importKeyFromHexString(type, hexString) {\n    const format = getFormat(type);\n    const arrayBuffer = (0, util_1.hexStringToUint8Array)(hexString).buffer;\n    return await crypto.subtle.importKey(format, arrayBuffer, {\n        name: 'ECDH',\n        namedCurve: 'P-256',\n    }, true, type === 'private' ? ['deriveKey'] : []);\n}\nexports.importKeyFromHexString = importKeyFromHexString;\nasync function encryptContent(content, sharedSecret) {\n    const serialized = JSON.stringify(content, (_, value) => {\n        if (!(value instanceof Error))\n            return value;\n        const error = value;\n        return Object.assign(Object.assign({}, (error.code ? { code: error.code } : {})), { message: error.message });\n    });\n    return encrypt(sharedSecret, serialized);\n}\nexports.encryptContent = encryptContent;\nasync function decryptContent(encryptedData, sharedSecret) {\n    return JSON.parse(await decrypt(sharedSecret, encryptedData));\n}\nexports.decryptContent = decryptContent;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/util/provider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/provider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.checkErrorForInvalidRequestArgs = exports.getCoinbaseInjectedProvider = exports.getCoinbaseInjectedSigner = exports.fetchRPCRequest = void 0;\nconst version_1 = __webpack_require__(/*! ../version */ \"./node_modules/@coinbase/wallet-sdk/dist/version.js\");\nconst error_1 = __webpack_require__(/*! ../core/error */ \"./node_modules/@coinbase/wallet-sdk/dist/core/error/index.js\");\nasync function fetchRPCRequest(request, chain) {\n    if (!chain.rpcUrl)\n        throw error_1.standardErrors.rpc.internal('No RPC URL set for chain');\n    const requestBody = Object.assign(Object.assign({}, request), { jsonrpc: '2.0', id: crypto.randomUUID() });\n    const res = await window.fetch(chain.rpcUrl, {\n        method: 'POST',\n        body: JSON.stringify(requestBody),\n        mode: 'cors',\n        headers: { 'Content-Type': 'application/json', 'X-Cbw-Sdk-Version': version_1.LIB_VERSION },\n    });\n    const response = await res.json();\n    return response.result;\n}\nexports.fetchRPCRequest = fetchRPCRequest;\nfunction getCoinbaseInjectedSigner() {\n    const window = globalThis;\n    return window.coinbaseWalletSigner;\n}\nexports.getCoinbaseInjectedSigner = getCoinbaseInjectedSigner;\nfunction getCoinbaseInjectedLegacyProvider() {\n    const window = globalThis;\n    return window.coinbaseWalletExtension;\n}\nfunction getInjectedEthereum() {\n    var _a, _b;\n    try {\n        const window = globalThis;\n        return (_a = window.ethereum) !== null && _a !== void 0 ? _a : (_b = window.top) === null || _b === void 0 ? void 0 : _b.ethereum;\n    }\n    catch (_c) {\n        return undefined;\n    }\n}\nfunction getCoinbaseInjectedProvider({ metadata, preference, }) {\n    var _a;\n    if (preference.options !== 'smartWalletOnly') {\n        const signer = getCoinbaseInjectedSigner();\n        if (signer)\n            return undefined; // use signer instead\n        const extension = getCoinbaseInjectedLegacyProvider();\n        if (extension) {\n            const { appName, appLogoUrl, appChainIds } = metadata;\n            (_a = extension.setAppInfo) === null || _a === void 0 ? void 0 : _a.call(extension, appName, appLogoUrl, appChainIds);\n            return extension;\n        }\n    }\n    const ethereum = getInjectedEthereum();\n    if (ethereum === null || ethereum === void 0 ? void 0 : ethereum.isCoinbaseBrowser) {\n        return ethereum;\n    }\n    return undefined;\n}\nexports.getCoinbaseInjectedProvider = getCoinbaseInjectedProvider;\n/**\n * Validates the arguments for an invalid request and returns an error if any validation fails.\n * Valid request args are defined here: https://eips.ethereum.org/EIPS/eip-1193#request\n * @param args The request arguments to validate.\n * @returns An error object if the arguments are invalid, otherwise undefined.\n */\nfunction checkErrorForInvalidRequestArgs(args) {\n    if (!args || typeof args !== 'object' || Array.isArray(args)) {\n        return error_1.standardErrors.rpc.invalidParams({\n            message: 'Expected a single, non-array, object argument.',\n            data: args,\n        });\n    }\n    const { method, params } = args;\n    if (typeof method !== 'string' || method.length === 0) {\n        return error_1.standardErrors.rpc.invalidParams({\n            message: \"'args.method' must be a non-empty string.\",\n            data: args,\n        });\n    }\n    if (params !== undefined &&\n        !Array.isArray(params) &&\n        (typeof params !== 'object' || params === null)) {\n        return error_1.standardErrors.rpc.invalidParams({\n            message: \"'args.params' must be an object or array if provided.\",\n            data: args,\n        });\n    }\n    return undefined;\n}\nexports.checkErrorForInvalidRequestArgs = checkErrorForInvalidRequestArgs;\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/util/provider.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Extracted from https://github.com/ethereumjs/ethereumjs-abi and stripped out irrelevant code\n// Original code licensed under the MIT License - Copyright (c) 2015 Alex Beregszaszi\n\n/* eslint-disable */\n//prettier-ignore\nconst util = __webpack_require__(/*! ./util */ \"./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.js\")\n\n// Convert from short to canonical names\n// FIXME: optimise or make this nicer?\nfunction elementaryName (name) {\n  if (name.startsWith('int[')) {\n    return 'int256' + name.slice(3)\n  } else if (name === 'int') {\n    return 'int256'\n  } else if (name.startsWith('uint[')) {\n    return 'uint256' + name.slice(4)\n  } else if (name === 'uint') {\n    return 'uint256'\n  } else if (name.startsWith('fixed[')) {\n    return 'fixed128x128' + name.slice(5)\n  } else if (name === 'fixed') {\n    return 'fixed128x128'\n  } else if (name.startsWith('ufixed[')) {\n    return 'ufixed128x128' + name.slice(6)\n  } else if (name === 'ufixed') {\n    return 'ufixed128x128'\n  }\n  return name\n}\n\n// Parse N from type<N>\nfunction parseTypeN (type) {\n  return parseInt(/^\\D+(\\d+)$/.exec(type)[1], 10)\n}\n\n// Parse N,M from type<N>x<M>\nfunction parseTypeNxM (type) {\n  var tmp = /^\\D+(\\d+)x(\\d+)$/.exec(type)\n  return [ parseInt(tmp[1], 10), parseInt(tmp[2], 10) ]\n}\n\n// Parse N in type[<N>] where \"type\" can itself be an array type.\nfunction parseTypeArray (type) {\n  var tmp = type.match(/(.*)\\[(.*?)\\]$/)\n  if (tmp) {\n    return tmp[2] === '' ? 'dynamic' : parseInt(tmp[2], 10)\n  }\n  return null\n}\n\nfunction parseNumber (arg) {\n  var type = typeof arg\n  if (type === 'string' || type === 'number') {\n    return BigInt(arg)\n  } else if (type === 'bigint') {\n    return arg\n  } else {\n    throw new Error('Argument is not a number')\n  }\n}\n\n// Encodes a single item (can be dynamic array)\n// @returns: Buffer\nfunction encodeSingle (type, arg) {\n  var size, num, ret, i\n\n  if (type === 'address') {\n    return encodeSingle('uint160', parseNumber(arg))\n  } else if (type === 'bool') {\n    return encodeSingle('uint8', arg ? 1 : 0)\n  } else if (type === 'string') {\n    return encodeSingle('bytes', new Buffer(arg, 'utf8'))\n  } else if (isArray(type)) {\n    // this part handles fixed-length ([2]) and variable length ([]) arrays\n    // NOTE: we catch here all calls to arrays, that simplifies the rest\n    if (typeof arg.length === 'undefined') {\n      throw new Error('Not an array?')\n    }\n    size = parseTypeArray(type)\n    if (size !== 'dynamic' && size !== 0 && arg.length > size) {\n      throw new Error('Elements exceed array size: ' + size)\n    }\n    ret = []\n    type = type.slice(0, type.lastIndexOf('['))\n    if (typeof arg === 'string') {\n      arg = JSON.parse(arg)\n    }\n    for (i in arg) {\n      ret.push(encodeSingle(type, arg[i]))\n    }\n    if (size === 'dynamic') {\n      var length = encodeSingle('uint256', arg.length)\n      ret.unshift(length)\n    }\n    return Buffer.concat(ret)\n  } else if (type === 'bytes') {\n    arg = new Buffer(arg)\n\n    ret = Buffer.concat([ encodeSingle('uint256', arg.length), arg ])\n\n    if ((arg.length % 32) !== 0) {\n      ret = Buffer.concat([ ret, util.zeros(32 - (arg.length % 32)) ])\n    }\n\n    return ret\n  } else if (type.startsWith('bytes')) {\n    size = parseTypeN(type)\n    if (size < 1 || size > 32) {\n      throw new Error('Invalid bytes<N> width: ' + size)\n    }\n\n    return util.setLengthRight(arg, 32)\n  } else if (type.startsWith('uint')) {\n    size = parseTypeN(type)\n    if ((size % 8) || (size < 8) || (size > 256)) {\n      throw new Error('Invalid uint<N> width: ' + size)\n    }\n\n    num = parseNumber(arg)\n    const bitLength = util.bitLengthFromBigInt(num)\n    if (bitLength > size) {\n      throw new Error('Supplied uint exceeds width: ' + size + ' vs ' + bitLength)\n    }\n\n    if (num < 0) {\n      throw new Error('Supplied uint is negative')\n    }\n\n    return util.bufferBEFromBigInt(num, 32);\n  } else if (type.startsWith('int')) {\n    size = parseTypeN(type)\n    if ((size % 8) || (size < 8) || (size > 256)) {\n      throw new Error('Invalid int<N> width: ' + size)\n    }\n\n    num = parseNumber(arg)\n    const bitLength = util.bitLengthFromBigInt(num)\n    if (bitLength > size) {\n      throw new Error('Supplied int exceeds width: ' + size + ' vs ' + bitLength)\n    }\n\n    const twos = util.twosFromBigInt(num, 256);\n\n    return util.bufferBEFromBigInt(twos, 32);\n  } else if (type.startsWith('ufixed')) {\n    size = parseTypeNxM(type)\n\n    num = parseNumber(arg)\n\n    if (num < 0) {\n      throw new Error('Supplied ufixed is negative')\n    }\n\n    return encodeSingle('uint256', num * BigInt(2) ** BigInt(size[1]))\n  } else if (type.startsWith('fixed')) {\n    size = parseTypeNxM(type)\n\n    return encodeSingle('int256', parseNumber(arg) * BigInt(2) ** BigInt(size[1]))\n  }\n\n  throw new Error('Unsupported or invalid type: ' + type)\n}\n\n// Is a type dynamic?\nfunction isDynamic (type) {\n  // FIXME: handle all types? I don't think anything is missing now\n  return (type === 'string') || (type === 'bytes') || (parseTypeArray(type) === 'dynamic')\n}\n\n// Is a type an array?\nfunction isArray (type) {\n  return type.lastIndexOf(']') === type.length - 1\n}\n\n// Encode a method/event with arguments\n// @types an array of string type names\n// @args  an array of the appropriate values\nfunction rawEncode (types, values) {\n  var output = []\n  var data = []\n\n  var headLength = 32 * types.length\n\n  for (var i in types) {\n    var type = elementaryName(types[i])\n    var value = values[i]\n    var cur = encodeSingle(type, value)\n\n    // Use the head/tail method for storing dynamic data\n    if (isDynamic(type)) {\n      output.push(encodeSingle('uint256', headLength))\n      data.push(cur)\n      headLength += cur.length\n    } else {\n      output.push(cur)\n    }\n  }\n\n  return Buffer.concat(output.concat(data))\n}\n\nfunction solidityPack (types, values) {\n  if (types.length !== values.length) {\n    throw new Error('Number of types are not matching the values')\n  }\n\n  var size, num\n  var ret = []\n\n  for (var i = 0; i < types.length; i++) {\n    var type = elementaryName(types[i])\n    var value = values[i]\n\n    if (type === 'bytes') {\n      ret.push(value)\n    } else if (type === 'string') {\n      ret.push(new Buffer(value, 'utf8'))\n    } else if (type === 'bool') {\n      ret.push(new Buffer(value ? '01' : '00', 'hex'))\n    } else if (type === 'address') {\n      ret.push(util.setLength(value, 20))\n    } else if (type.startsWith('bytes')) {\n      size = parseTypeN(type)\n      if (size < 1 || size > 32) {\n        throw new Error('Invalid bytes<N> width: ' + size)\n      }\n\n      ret.push(util.setLengthRight(value, size))\n    } else if (type.startsWith('uint')) {\n      size = parseTypeN(type)\n      if ((size % 8) || (size < 8) || (size > 256)) {\n        throw new Error('Invalid uint<N> width: ' + size)\n      }\n\n      num = parseNumber(value)\n      const bitLength = util.bitLengthFromBigInt(num)\n      if (bitLength > size) {\n        throw new Error('Supplied uint exceeds width: ' + size + ' vs ' + bitLength)\n      }\n\n      ret.push(util.bufferBEFromBigInt(num, size / 8))\n    } else if (type.startsWith('int')) {\n      size = parseTypeN(type)\n      if ((size % 8) || (size < 8) || (size > 256)) {\n        throw new Error('Invalid int<N> width: ' + size)\n      }\n\n      num = parseNumber(value)\n      const bitLength = util.bitLengthFromBigInt(num)\n      if (bitLength > size) {\n        throw new Error('Supplied int exceeds width: ' + size + ' vs ' + bitLength)\n      }\n\n      const twos = util.twosFromBigInt(num, size);\n      ret.push(util.bufferBEFromBigInt(twos, size / 8))\n    } else {\n      // FIXME: support all other types\n      throw new Error('Unsupported or invalid type: ' + type)\n    }\n  }\n\n  return Buffer.concat(ret)\n}\n\nfunction soliditySHA3 (types, values) {\n  return util.keccak(solidityPack(types, values))\n}\n\nmodule.exports = {\n  rawEncode,\n  solidityPack,\n  soliditySHA3\n}\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! ./util */ \"./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.js\")\nconst abi = __webpack_require__(/*! ./abi */ \"./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.js\")\n\nconst TYPED_MESSAGE_SCHEMA = {\n  type: 'object',\n  properties: {\n    types: {\n      type: 'object',\n      additionalProperties: {\n        type: 'array',\n        items: {\n          type: 'object',\n          properties: {\n            name: {type: 'string'},\n            type: {type: 'string'},\n          },\n          required: ['name', 'type'],\n        },\n      },\n    },\n    primaryType: {type: 'string'},\n    domain: {type: 'object'},\n    message: {type: 'object'},\n  },\n  required: ['types', 'primaryType', 'domain', 'message'],\n}\n\n/**\n * A collection of utility functions used for signing typed data\n */\nconst TypedDataUtils = {\n  /**\n   * Encodes an object by encoding and concatenating each of its members\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} data - Object to encode\n   * @param {Object} types - Type definitions\n   * @returns {string} - Encoded representation of an object\n   */\n  encodeData (primaryType, data, types, useV4 = true) {\n    const encodedTypes = ['bytes32']\n    const encodedValues = [this.hashType(primaryType, types)]\n\n    if(useV4) {\n      const encodeField = (name, type, value) => {\n        if (types[type] !== undefined) {\n          return ['bytes32', value == null ?\n            '0x0000000000000000000000000000000000000000000000000000000000000000' :\n            util.keccak(this.encodeData(type, value, types, useV4))]\n        }\n\n        if(value === undefined)\n          throw new Error(`missing value for field ${name} of type ${type}`)\n\n        if (type === 'bytes') {\n          return ['bytes32', util.keccak(value)]\n        }\n\n        if (type === 'string') {\n          // convert string to buffer - prevents ethUtil from interpreting strings like '0xabcd' as hex\n          if (typeof value === 'string') {\n            value = Buffer.from(value, 'utf8')\n          }\n          return ['bytes32', util.keccak(value)]\n        }\n\n        if (type.lastIndexOf(']') === type.length - 1) {\n          const parsedType = type.slice(0, type.lastIndexOf('['))\n          const typeValuePairs = value.map(item =>\n            encodeField(name, parsedType, item))\n          return ['bytes32', util.keccak(abi.rawEncode(\n            typeValuePairs.map(([type]) => type),\n            typeValuePairs.map(([, value]) => value),\n          ))]\n        }\n\n        return [type, value]\n      }\n\n      for (const field of types[primaryType]) {\n        const [type, value] = encodeField(field.name, field.type, data[field.name])\n        encodedTypes.push(type)\n        encodedValues.push(value)\n      }\n    } else {\n      for (const field of types[primaryType]) {\n        let value = data[field.name]\n        if (value !== undefined) {\n          if (field.type === 'bytes') {\n            encodedTypes.push('bytes32')\n            value = util.keccak(value)\n            encodedValues.push(value)\n          } else if (field.type === 'string') {\n            encodedTypes.push('bytes32')\n            // convert string to buffer - prevents ethUtil from interpreting strings like '0xabcd' as hex\n            if (typeof value === 'string') {\n              value = Buffer.from(value, 'utf8')\n            }\n            value = util.keccak(value)\n            encodedValues.push(value)\n          } else if (types[field.type] !== undefined) {\n            encodedTypes.push('bytes32')\n            value = util.keccak(this.encodeData(field.type, value, types, useV4))\n            encodedValues.push(value)\n          } else if (field.type.lastIndexOf(']') === field.type.length - 1) {\n            throw new Error('Arrays currently unimplemented in encodeData')\n          } else {\n            encodedTypes.push(field.type)\n            encodedValues.push(value)\n          }\n        }\n      }\n    }\n\n    return abi.rawEncode(encodedTypes, encodedValues)\n  },\n\n  /**\n   * Encodes the type of an object by encoding a comma delimited list of its members\n   *\n   * @param {string} primaryType - Root type to encode\n   * @param {Object} types - Type definitions\n   * @returns {string} - Encoded representation of the type of an object\n   */\n  encodeType (primaryType, types) {\n    let result = ''\n    let deps = this.findTypeDependencies(primaryType, types).filter(dep => dep !== primaryType)\n    deps = [primaryType].concat(deps.sort())\n    for (const type of deps) {\n      const children = types[type]\n      if (!children) {\n        throw new Error('No type definition specified: ' + type)\n      }\n      result += type + '(' + types[type].map(({ name, type }) => type + ' ' + name).join(',') + ')'\n    }\n    return result\n  },\n\n  /**\n   * Finds all types within a type definition object\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} types - Type definitions\n   * @param {Array} results - current set of accumulated types\n   * @returns {Array} - Set of all types found in the type definition\n   */\n  findTypeDependencies (primaryType, types, results = []) {\n    primaryType = primaryType.match(/^\\w*/)[0]\n    if (results.includes(primaryType) || types[primaryType] === undefined) { return results }\n    results.push(primaryType)\n    for (const field of types[primaryType]) {\n      for (const dep of this.findTypeDependencies(field.type, types, results)) {\n        !results.includes(dep) && results.push(dep)\n      }\n    }\n    return results\n  },\n\n  /**\n   * Hashes an object\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} data - Object to hash\n   * @param {Object} types - Type definitions\n   * @returns {Buffer} - Hash of an object\n   */\n  hashStruct (primaryType, data, types, useV4 = true) {\n    return util.keccak(this.encodeData(primaryType, data, types, useV4))\n  },\n\n  /**\n   * Hashes the type of an object\n   *\n   * @param {string} primaryType - Root type to hash\n   * @param {Object} types - Type definitions\n   * @returns {string} - Hash of an object\n   */\n  hashType (primaryType, types) {\n    return util.keccak(this.encodeType(primaryType, types))\n  },\n\n  /**\n   * Removes properties from a message object that are not defined per EIP-712\n   *\n   * @param {Object} data - typed message object\n   * @returns {Object} - typed message object with only allowed fields\n   */\n  sanitizeData (data) {\n    const sanitizedData = {}\n    for (const key in TYPED_MESSAGE_SCHEMA.properties) {\n      data[key] && (sanitizedData[key] = data[key])\n    }\n    if (sanitizedData.types) {\n      sanitizedData.types = Object.assign({ EIP712Domain: [] }, sanitizedData.types)\n    }\n    return sanitizedData\n  },\n\n  /**\n   * Returns the hash of a typed message as per EIP-712 for signing\n   *\n   * @param {Object} typedData - Types message data to sign\n   * @returns {string} - sha3 hash for signing\n   */\n  hash (typedData, useV4 = true) {\n    const sanitizedData = this.sanitizeData(typedData)\n    const parts = [Buffer.from('1901', 'hex')]\n    parts.push(this.hashStruct('EIP712Domain', sanitizedData.domain, sanitizedData.types, useV4))\n    if (sanitizedData.primaryType !== 'EIP712Domain') {\n      parts.push(this.hashStruct(sanitizedData.primaryType, sanitizedData.message, sanitizedData.types, useV4))\n    }\n    return util.keccak(Buffer.concat(parts))\n  },\n}\n\nmodule.exports = {\n  TYPED_MESSAGE_SCHEMA,\n  TypedDataUtils,\n\n  hashForSignTypedDataLegacy: function (msgParams) {\n    return typedSignatureHashLegacy(msgParams.data)\n  },\n\n  hashForSignTypedData_v3: function (msgParams) {\n    return TypedDataUtils.hash(msgParams.data, false)\n  },\n\n  hashForSignTypedData_v4: function (msgParams) {\n    return TypedDataUtils.hash(msgParams.data)\n  },\n}\n\n/**\n * @param typedData - Array of data along with types, as per EIP712.\n * @returns Buffer\n */\nfunction typedSignatureHashLegacy(typedData) {\n  const error = new Error('Expect argument to be non-empty array')\n  if (typeof typedData !== 'object' || !typedData.length) throw error\n\n  const data = typedData.map(function (e) {\n    return e.type === 'bytes' ? util.toBuffer(e.value) : e.value\n  })\n  const types = typedData.map(function (e) { return e.type })\n  const schema = typedData.map(function (e) {\n    if (!e.name) throw error\n    return e.type + ' ' + e.name\n  })\n\n  return abi.soliditySHA3(\n    ['bytes32', 'bytes32'],\n    [\n      abi.soliditySHA3(new Array(typedData.length).fill('string'), schema),\n      abi.soliditySHA3(types, data)\n    ]\n  )\n}\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Extracted from https://github.com/ethereumjs/ethereumjs-util and stripped out irrelevant code\n// Original code licensed under the Mozilla Public License Version 2.0\n\n/* eslint-disable */\n//prettier-ignore\nconst createKeccakHash = __webpack_require__(/*! keccak/js */ \"./node_modules/keccak/js.js\")\n\n/**\n * Returns a buffer filled with 0s\n * @method zeros\n * @param {Number} bytes  the number of bytes the buffer should be\n * @return {Buffer}\n */\nfunction zeros (bytes) {\n  return Buffer.allocUnsafe(bytes).fill(0)\n}\n\nfunction bitLengthFromBigInt (num) {\n  return num.toString(2).length\n}\n\nfunction bufferBEFromBigInt(num, length) {\n  let hex = num.toString(16);\n  // Ensure the hex string length is even\n  if (hex.length % 2 !== 0) hex = '0' + hex;\n  // Convert hex string to a byte array\n  const byteArray = hex.match(/.{1,2}/g).map(byte => parseInt(byte, 16));\n  // Ensure the byte array is of the specified length\n  while (byteArray.length < length) {\n    byteArray.unshift(0); // Prepend with zeroes if shorter than required length\n  }\n\n  return Buffer.from(byteArray);\n}\n\nfunction twosFromBigInt(value, width) {\n  const isNegative = value < 0n;\n  let result;\n  if (isNegative) {\n    // Prepare a mask for the specified width to perform NOT operation\n    const mask = (1n << BigInt(width)) - 1n;\n    // Invert bits (using NOT) and add one\n    result = (~value & mask) + 1n;\n  } else {\n    result = value;\n  }\n  // Ensure the result fits in the specified width\n  result &= (1n << BigInt(width)) - 1n;\n\n  return result;\n}\n\n/**\n * Left Pads an `Array` or `Buffer` with leading zeros till it has `length` bytes.\n * Or it truncates the beginning if it exceeds.\n * @method setLength\n * @param {Buffer|Array} msg the value to pad\n * @param {Number} length the number of bytes the output should be\n * @param {Boolean} [right=false] whether to start padding form the left or right\n * @return {Buffer|Array}\n */\nfunction setLength (msg, length, right) {\n  const buf = zeros(length)\n  msg = toBuffer(msg)\n  if (right) {\n    if (msg.length < length) {\n      msg.copy(buf)\n      return buf\n    }\n    return msg.slice(0, length)\n  } else {\n    if (msg.length < length) {\n      msg.copy(buf, length - msg.length)\n      return buf\n    }\n    return msg.slice(-length)\n  }\n}\n\n/**\n * Right Pads an `Array` or `Buffer` with leading zeros till it has `length` bytes.\n * Or it truncates the beginning if it exceeds.\n * @param {Buffer|Array} msg the value to pad\n * @param {Number} length the number of bytes the output should be\n * @return {Buffer|Array}\n */\nfunction setLengthRight (msg, length) {\n  return setLength(msg, length, true)\n}\n\n/**\n * Attempts to turn a value into a `Buffer`. As input it supports `Buffer`, `String`, `Number`, null/undefined, `BIgInt` and other objects with a `toArray()` method.\n * @param {*} v the value\n */\nfunction toBuffer (v) {\n  if (!Buffer.isBuffer(v)) {\n    if (Array.isArray(v)) {\n      v = Buffer.from(v)\n    } else if (typeof v === 'string') {\n      if (isHexString(v)) {\n        v = Buffer.from(padToEven(stripHexPrefix(v)), 'hex')\n      } else {\n        v = Buffer.from(v)\n      }\n    } else if (typeof v === 'number') {\n      v = intToBuffer(v)\n    } else if (v === null || v === undefined) {\n      v = Buffer.allocUnsafe(0)\n    } else if (typeof v === 'bigint') {\n      v = bufferBEFromBigInt(v)\n    } else if (v.toArray) {\n      // TODO: bigint should be handled above, may remove this duplicate\n      // converts a BigInt to a Buffer\n      v = Buffer.from(v.toArray())\n    } else {\n      throw new Error('invalid type')\n    }\n  }\n  return v\n}\n\n/**\n * Converts a `Buffer` into a hex `String`\n * @param {Buffer} buf\n * @return {String}\n */\nfunction bufferToHex (buf) {\n  buf = toBuffer(buf)\n  return '0x' + buf.toString('hex')\n}\n\n/**\n * Creates Keccak hash of the input\n * @param {Buffer|Array|String|Number} a the input data\n * @param {Number} [bits=256] the Keccak width\n * @return {Buffer}\n */\nfunction keccak (a, bits) {\n  a = toBuffer(a)\n  if (!bits) bits = 256\n\n  return createKeccakHash('keccak' + bits).update(a).digest()\n}\n\nfunction padToEven (str) {\n  return str.length % 2 ? '0' + str : str\n}\n\nfunction isHexString (str) {\n  return typeof str === 'string' && str.match(/^0x[0-9A-Fa-f]*$/)\n}\n\nfunction stripHexPrefix (str) {\n  if (typeof str === 'string' && str.startsWith('0x')) {\n    return str.slice(2)\n  }\n  return str\n}\n\nmodule.exports = {\n  zeros,\n  setLength,\n  setLengthRight,\n  isHexString,\n  stripHexPrefix,\n  toBuffer,\n  bufferToHex,\n  keccak,\n  bitLengthFromBigInt,\n  bufferBEFromBigInt,\n  twosFromBigInt\n}\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.js?");

/***/ }),

/***/ "./node_modules/@coinbase/wallet-sdk/dist/version.js":
/*!***********************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/version.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LIB_VERSION = void 0;\nexports.LIB_VERSION = '4.0.4';\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@coinbase/wallet-sdk/dist/version.js?");

/***/ })

}]);