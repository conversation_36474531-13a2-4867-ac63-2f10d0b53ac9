{"API": {"HTTPHeaders": {}}, "Addresses": {"API": "/ip4/0.0.0.0/tcp/5001", "Announce": [], "AppendAnnounce": [], "Gateway": "/ip4/0.0.0.0/tcp/8080", "NoAnnounce": [], "Swarm": ["/ip4/0.0.0.0/tcp/4001", "/ip6/::/tcp/4001", "/ip4/0.0.0.0/udp/4001/quic-v1", "/ip4/0.0.0.0/udp/4001/quic-v1/webtransport", "/ip6/::/udp/4001/quic-v1", "/ip6/::/udp/4001/quic-v1/webtransport"]}, "AutoNAT": {}, "Bootstrap": ["/dnsaddr/bootstrap.libp2p.io/p2p/QmNnooDu7bfjPFoTZYxMNLWUQJyrVwtbZg5gBMjTezGAJN", "/dnsaddr/bootstrap.libp2p.io/p2p/QmQCU2EcMqAqQPR2i9bChDtGNJchTbq5TbXJJ16u19uLTa", "/dnsaddr/bootstrap.libp2p.io/p2p/QmbLHAnMoJPWSCR5Zhtx6BHJX9KiKNN6tpvbUcqanj75Nb", "/dnsaddr/bootstrap.libp2p.io/p2p/QmcZf59bWwK5XFi76CZX8cbJ4BhTzzA3gU1ZjYZcYW3dwt", "/ip4/**************/tcp/4001/p2p/QmaCpDMGvV2BGHeYERUEnRQAwe3N8SzbUtfsmvsqQLuvuJ", "/ip4/**************/udp/4001/quic-v1/p2p/QmaCpDMGvV2BGHeYERUEnRQAwe3N8SzbUtfsmvsqQLuvuJ"], "DNS": {"Resolvers": {}}, "Datastore": {"BloomFilterSize": 1048576, "GCPeriod": "24h", "HashOnRead": false, "Spec": {"mounts": [{"child": {"path": "/uniondrive/ipfs_datastore/blocks", "shardFunc": "/repo/flatfs/shard/v1/next-to-last/2", "sync": true, "type": "flatfs"}, "mountpoint": "/blocks", "prefix": "flatfs.datastore", "type": "measure"}, {"child": {"compression": "none", "path": "/uniondrive/ipfs_datastore/datastore", "type": "pebbleds"}, "mountpoint": "/", "prefix": "pebble.datastore", "type": "measure"}], "type": "mount"}, "StorageGCWatermark": 90, "StorageMax": "20GB"}, "Discovery": {"MDNS": {"Enabled": true}}, "Experimental": {"FilestoreEnabled": false, "Libp2pStreamMounting": false, "OptimisticProvide": true, "OptimisticProvideJobsPoolSize": 60, "P2pHttpProxy": false, "StrategicProviding": false, "UrlstoreEnabled": false}, "Gateway": {"DeserializedResponses": null, "DisableHTMLErrors": null, "ExposeRoutingAPI": null, "HTTPHeaders": {}, "NoDNSLink": false, "NoFetch": false, "PublicGateways": null, "RootRedirect": ""}, "Identity": {"PeerID": "", "PrivKey": ""}, "Internal": {}, "Ipns": {"RecordLifetime": "", "RepublishPeriod": "", "ResolveCacheSize": 128}, "Migration": {"DownloadSources": [], "Keep": ""}, "Mounts": {"FuseAllowOther": false, "IPFS": "/ipfs", "IPNS": "/ipns"}, "Peering": {"Peers": null}, "Pinning": {"RemoteServices": {}}, "Plugins": {"Plugins": null}, "Provider": {"Strategy": ""}, "Pubsub": {"DisableSigning": false, "Router": ""}, "Reprovider": {}, "Routing": {"AcceleratedDHTClient": false, "Methods": null, "Routers": null}, "Swarm": {"AddrFilters": null, "ConnMgr": {"HighWater": 200, "LowWater": 50, "GracePeriod": "20s"}, "DisableBandwidthMetrics": false, "DisableNatPortMap": false, "RelayClient": {}, "RelayService": {}, "ResourceMgr": {}, "Transports": {"Multiplexers": {}, "Network": {}, "Security": {}}}}