/*! For license information please see 860.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunkfula_webui=self.webpackChunkfula_webui||[]).push([[860],{28207:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0});var i=r(15571);function n(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>8,e[r+1]=t>>>0,e}function s(t,e,r){return void 0===e&&(e=new Uint8Array(2)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e}function o(t,e){return void 0===e&&(e=0),t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}function h(t,e){return void 0===e&&(e=0),(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}function f(t,e){return void 0===e&&(e=0),t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e]}function u(t,e){return void 0===e&&(e=0),(t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e])>>>0}function a(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>24,e[r+1]=t>>>16,e[r+2]=t>>>8,e[r+3]=t>>>0,e}function l(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),e[r+0]=t>>>0,e[r+1]=t>>>8,e[r+2]=t>>>16,e[r+3]=t>>>24,e}function d(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),a(t/4294967296>>>0,e,r),a(t>>>0,e,r+4),e}function c(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),l(t>>>0,e,r),l(t/4294967296>>>0,e,r+4),e}e.readInt16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])<<16>>16},e.readUint16BE=function(t,e){return void 0===e&&(e=0),(t[e+0]<<8|t[e+1])>>>0},e.readInt16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])<<16>>16},e.readUint16LE=function(t,e){return void 0===e&&(e=0),(t[e+1]<<8|t[e])>>>0},e.writeUint16BE=n,e.writeInt16BE=n,e.writeUint16LE=s,e.writeInt16LE=s,e.readInt32BE=o,e.readUint32BE=h,e.readInt32LE=f,e.readUint32LE=u,e.writeUint32BE=a,e.writeInt32BE=a,e.writeUint32LE=l,e.writeInt32LE=l,e.readInt64BE=function(t,e){void 0===e&&(e=0);var r=o(t,e),i=o(t,e+4);return 4294967296*r+i-4294967296*(i>>31)},e.readUint64BE=function(t,e){return void 0===e&&(e=0),4294967296*h(t,e)+h(t,e+4)},e.readInt64LE=function(t,e){void 0===e&&(e=0);var r=f(t,e);return 4294967296*f(t,e+4)+r-4294967296*(r>>31)},e.readUint64LE=function(t,e){void 0===e&&(e=0);var r=u(t,e);return 4294967296*u(t,e+4)+r},e.writeUint64BE=d,e.writeInt64BE=d,e.writeUint64LE=c,e.writeInt64LE=c,e.readUintBE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintBE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintBE: array is too short for the given bitLength");for(var i=0,n=1,s=t/8+r-1;s>=r;s--)i+=e[s]*n,n*=256;return i},e.readUintLE=function(t,e,r){if(void 0===r&&(r=0),t%8!=0)throw new Error("readUintLE supports only bitLengths divisible by 8");if(t/8>e.length-r)throw new Error("readUintLE: array is too short for the given bitLength");for(var i=0,n=1,s=r;s<r+t/8;s++)i+=e[s]*n,n*=256;return i},e.writeUintBE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintBE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintBE value must be an integer");for(var s=1,o=t/8+n-1;o>=n;o--)r[o]=e/s&255,s*=256;return r},e.writeUintLE=function(t,e,r,n){if(void 0===r&&(r=new Uint8Array(t/8)),void 0===n&&(n=0),t%8!=0)throw new Error("writeUintLE supports only bitLengths divisible by 8");if(!i.isSafeInteger(e))throw new Error("writeUintLE value must be an integer");for(var s=1,o=n;o<n+t/8;o++)r[o]=e/s&255,s*=256;return r},e.readFloat32BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e)},e.readFloat32LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat32(e,!0)},e.readFloat64BE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e)},e.readFloat64LE=function(t,e){return void 0===e&&(e=0),new DataView(t.buffer,t.byteOffset,t.byteLength).getFloat64(e,!0)},e.writeFloat32BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t),e},e.writeFloat32LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(4)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat32(r,t,!0),e},e.writeFloat64BE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t),e},e.writeFloat64LE=function(t,e,r){return void 0===e&&(e=new Uint8Array(8)),void 0===r&&(r=0),new DataView(e.buffer,e.byteOffset,e.byteLength).setFloat64(r,t,!0),e}},15571:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.mul=Math.imul||function(t,e){var r=65535&t,i=65535&e;return r*i+((t>>>16&65535)*i+r*(e>>>16&65535)<<16>>>0)|0},e.add=function(t,e){return t+e|0},e.sub=function(t,e){return t-e|0},e.rotl=function(t,e){return t<<e|t>>>32-e},e.rotr=function(t,e){return t<<32-e|t>>>e},e.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},e.MAX_SAFE_INTEGER=9007199254740991,e.isSafeInteger=function(t){return e.isInteger(t)&&t>=-e.MAX_SAFE_INTEGER&&t<=e.MAX_SAFE_INTEGER}},86783:(t,e,r)=>{e.yE=void 0;const i=r(69369);r(28207),r(97159);e.yE=new i.SystemRandomSource},68462:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BrowserRandomSource=void 0,e.BrowserRandomSource=class{constructor(){this.isAvailable=!1,this.isInstantiated=!1;const t="undefined"!=typeof self?self.crypto||self.msCrypto:null;t&&void 0!==t.getRandomValues&&(this._crypto=t,this.isAvailable=!0,this.isInstantiated=!0)}randomBytes(t){if(!this.isAvailable||!this._crypto)throw new Error("Browser random byte generator is not available.");const e=new Uint8Array(t);for(let t=0;t<e.length;t+=65536)this._crypto.getRandomValues(e.subarray(t,t+Math.min(e.length-t,65536)));return e}}},93012:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NodeRandomSource=void 0;const i=r(97159);e.NodeRandomSource=class{constructor(){this.isAvailable=!1,this.isInstantiated=!1;{const t=r(69683);t&&t.randomBytes&&(this._crypto=t,this.isAvailable=!0,this.isInstantiated=!0)}}randomBytes(t){if(!this.isAvailable||!this._crypto)throw new Error("Node.js random byte generator is not available.");let e=this._crypto.randomBytes(t);if(e.length!==t)throw new Error("NodeRandomSource: got fewer bytes than requested");const r=new Uint8Array(t);for(let t=0;t<r.length;t++)r[t]=e[t];return(0,i.wipe)(e),r}}},69369:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SystemRandomSource=void 0;const i=r(68462),n=r(93012);e.SystemRandomSource=class{constructor(){return this.isAvailable=!1,this.name="",this._source=new i.BrowserRandomSource,this._source.isAvailable?(this.isAvailable=!0,void(this.name="Browser")):(this._source=new n.NodeRandomSource,this._source.isAvailable?(this.isAvailable=!0,void(this.name="Node")):void 0)}randomBytes(t){if(!this.isAvailable)throw new Error("System random byte generator is not available.");return this._source.randomBytes(t)}}},97159:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.wipe=function(t){for(var e=0;e<t.length;e++)t[e]=0;return t}},64860:(t,e,r)=>{r.d(e,{SIWEController:()=>si,getDidAddress:()=>ei,getDidChainId:()=>ti});var i=r(11525);r(28917),r(88900),r(38196),r(42063),r(86663),r(51612),r(16804),r(86783),r(50204),r(774),r(44117),r(27302),r(43228),Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var n=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof r.g<"u"?r.g:typeof self<"u"?self:{};!function(t){!function(){var e="input is invalid type",r="object"==typeof window,i=r?window:{};i.JS_SHA3_NO_WINDOW&&(r=!1);var s=!r&&"object"==typeof self;!i.JS_SHA3_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node?i=n:s&&(i=self);var o=!i.JS_SHA3_NO_COMMON_JS&&t.exports,h=!i.JS_SHA3_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",f="0123456789abcdef".split(""),u=[4,1024,262144,67108864],a=[0,8,16,24],l=[1,0,32898,0,32906,2147483648,2147516416,2147483648,32907,0,2147483649,0,2147516545,2147483648,32777,2147483648,138,0,136,0,2147516425,0,2147483658,0,2147516555,0,139,2147483648,32905,2147483648,32771,2147483648,32770,2147483648,128,2147483648,32778,0,2147483658,2147483648,2147516545,2147483648,32896,2147483648,2147483649,0,2147516424,2147483648],d=[224,256,384,512],c=[128,256],p=["hex","buffer","arrayBuffer","array","digest"],m={128:168,256:136};(i.JS_SHA3_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),h&&(i.JS_SHA3_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});for(var g=function(t,e,r){return function(i){return new R(t,e,t).update(i)[r]()}},A=function(t,e,r){return function(i,n){return new R(t,e,n).update(i)[r]()}},v=function(t,e,r){return function(e,i,n,s){return E["cshake"+t].update(e,i,n,s)[r]()}},y=function(t,e,r){return function(e,i,n,s){return E["kmac"+t].update(e,i,n,s)[r]()}},w=function(t,e,r,i){for(var n=0;n<p.length;++n){var s=p[n];t[s]=e(r,i,s)}return t},b=function(t,e){var r=g(t,e,"hex");return r.create=function(){return new R(t,e,t)},r.update=function(t){return r.create().update(t)},w(r,g,t,e)},M=[{name:"keccak",padding:[1,256,65536,16777216],bits:d,createMethod:b},{name:"sha3",padding:[6,1536,393216,100663296],bits:d,createMethod:b},{name:"shake",padding:[31,7936,2031616,520093696],bits:c,createMethod:function(t,e){var r=A(t,e,"hex");return r.create=function(r){return new R(t,e,r)},r.update=function(t,e){return r.create(e).update(t)},w(r,A,t,e)}},{name:"cshake",padding:u,bits:c,createMethod:function(t,e){var r=m[t],i=v(t,0,"hex");return i.create=function(i,n,s){return n||s?new R(t,e,i).bytepad([n,s],r):E["shake"+t].create(i)},i.update=function(t,e,r,n){return i.create(e,r,n).update(t)},w(i,v,t,e)}},{name:"kmac",padding:u,bits:c,createMethod:function(t,e){var r=m[t],i=y(t,0,"hex");return i.create=function(i,n,s){return new U(t,e,n).bytepad(["KMAC",s],r).bytepad([i],r)},i.update=function(t,e,r,n){return i.create(t,r,n).update(e)},w(i,y,t,e)}}],E={},S=[],_=0;_<M.length;++_)for(var B=M[_],I=B.bits,C=0;C<I.length;++C){var N=B.name+"_"+I[C];if(S.push(N),E[N]=B.createMethod(I[C],B.padding),"sha3"!==B.name){var x=B.name+I[C];S.push(x),E[x]=E[N]}}function R(t,e,r){this.blocks=[],this.s=[],this.padding=e,this.outputBits=r,this.reset=!0,this.finalized=!1,this.block=0,this.start=0,this.blockCount=1600-(t<<1)>>5,this.byteCount=this.blockCount<<2,this.outputBlocks=r>>5,this.extraBytes=(31&r)>>3;for(var i=0;i<50;++i)this.s[i]=0}function U(t,e,r){R.call(this,t,e,r)}R.prototype.update=function(t){if(this.finalized)throw new Error("finalize already called");var r,i=typeof t;if("string"!==i){if("object"!==i)throw new Error(e);if(null===t)throw new Error(e);if(h&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||h&&ArrayBuffer.isView(t)))throw new Error(e);r=!0}for(var n,s,o=this.blocks,f=this.byteCount,u=t.length,l=this.blockCount,d=0,c=this.s;d<u;){if(this.reset)for(this.reset=!1,o[0]=this.block,n=1;n<l+1;++n)o[n]=0;if(r)for(n=this.start;d<u&&n<f;++d)o[n>>2]|=t[d]<<a[3&n++];else for(n=this.start;d<u&&n<f;++d)(s=t.charCodeAt(d))<128?o[n>>2]|=s<<a[3&n++]:s<2048?(o[n>>2]|=(192|s>>6)<<a[3&n++],o[n>>2]|=(128|63&s)<<a[3&n++]):s<55296||s>=57344?(o[n>>2]|=(224|s>>12)<<a[3&n++],o[n>>2]|=(128|s>>6&63)<<a[3&n++],o[n>>2]|=(128|63&s)<<a[3&n++]):(s=65536+((1023&s)<<10|1023&t.charCodeAt(++d)),o[n>>2]|=(240|s>>18)<<a[3&n++],o[n>>2]|=(128|s>>12&63)<<a[3&n++],o[n>>2]|=(128|s>>6&63)<<a[3&n++],o[n>>2]|=(128|63&s)<<a[3&n++]);if(this.lastByteIndex=n,n>=f){for(this.start=n-f,this.block=o[l],n=0;n<l;++n)c[n]^=o[n];O(c),this.reset=!0}else this.start=n}return this},R.prototype.encode=function(t,e){var r=255&t,i=1,n=[r];for(r=255&(t>>=8);r>0;)n.unshift(r),r=255&(t>>=8),++i;return e?n.push(i):n.unshift(i),this.update(n),n.length},R.prototype.encodeString=function(t){var r,i=typeof t;if("string"!==i){if("object"!==i)throw new Error(e);if(null===t)throw new Error(e);if(h&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||h&&ArrayBuffer.isView(t)))throw new Error(e);r=!0}var n=0,s=t.length;if(r)n=s;else for(var o=0;o<t.length;++o){var f=t.charCodeAt(o);f<128?n+=1:f<2048?n+=2:f<55296||f>=57344?n+=3:(f=65536+((1023&f)<<10|1023&t.charCodeAt(++o)),n+=4)}return n+=this.encode(8*n),this.update(t),n},R.prototype.bytepad=function(t,e){for(var r=this.encode(e),i=0;i<t.length;++i)r+=this.encodeString(t[i]);var n=e-r%e,s=[];return s.length=n,this.update(s),this},R.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex,r=this.blockCount,i=this.s;if(t[e>>2]|=this.padding[3&e],this.lastByteIndex===this.byteCount)for(t[0]=t[r],e=1;e<r+1;++e)t[e]=0;for(t[r-1]|=2147483648,e=0;e<r;++e)i[e]^=t[e];O(i)}},R.prototype.toString=R.prototype.hex=function(){this.finalize();for(var t,e=this.blockCount,r=this.s,i=this.outputBlocks,n=this.extraBytes,s=0,o=0,h="";o<i;){for(s=0;s<e&&o<i;++s,++o)t=r[s],h+=f[t>>4&15]+f[15&t]+f[t>>12&15]+f[t>>8&15]+f[t>>20&15]+f[t>>16&15]+f[t>>28&15]+f[t>>24&15];o%e==0&&(O(r),s=0)}return n&&(t=r[s],h+=f[t>>4&15]+f[15&t],n>1&&(h+=f[t>>12&15]+f[t>>8&15]),n>2&&(h+=f[t>>20&15]+f[t>>16&15])),h},R.prototype.arrayBuffer=function(){this.finalize();var t,e=this.blockCount,r=this.s,i=this.outputBlocks,n=this.extraBytes,s=0,o=0,h=this.outputBits>>3;t=n?new ArrayBuffer(i+1<<2):new ArrayBuffer(h);for(var f=new Uint32Array(t);o<i;){for(s=0;s<e&&o<i;++s,++o)f[o]=r[s];o%e==0&&O(r)}return n&&(f[s]=r[s],t=t.slice(0,h)),t},R.prototype.buffer=R.prototype.arrayBuffer,R.prototype.digest=R.prototype.array=function(){this.finalize();for(var t,e,r=this.blockCount,i=this.s,n=this.outputBlocks,s=this.extraBytes,o=0,h=0,f=[];h<n;){for(o=0;o<r&&h<n;++o,++h)t=h<<2,e=i[o],f[t]=255&e,f[t+1]=e>>8&255,f[t+2]=e>>16&255,f[t+3]=e>>24&255;h%r==0&&O(i)}return s&&(t=h<<2,e=i[o],f[t]=255&e,s>1&&(f[t+1]=e>>8&255),s>2&&(f[t+2]=e>>16&255)),f},U.prototype=new R,U.prototype.finalize=function(){return this.encode(this.outputBits,!0),R.prototype.finalize.call(this)};var O=function(t){var e,r,i,n,s,o,h,f,u,a,d,c,p,m,g,A,v,y,w,b,M,E,S,_,B,I,C,N,x,R,U,O,T,P,k,F,D,L,z,q,H,Q,j,Y,J,G,K,W,V,X,Z,$,tt,et,rt,it,nt,st,ot,ht,ft,ut,at;for(i=0;i<48;i+=2)n=t[0]^t[10]^t[20]^t[30]^t[40],s=t[1]^t[11]^t[21]^t[31]^t[41],o=t[2]^t[12]^t[22]^t[32]^t[42],h=t[3]^t[13]^t[23]^t[33]^t[43],f=t[4]^t[14]^t[24]^t[34]^t[44],u=t[5]^t[15]^t[25]^t[35]^t[45],a=t[6]^t[16]^t[26]^t[36]^t[46],d=t[7]^t[17]^t[27]^t[37]^t[47],e=(c=t[8]^t[18]^t[28]^t[38]^t[48])^(o<<1|h>>>31),r=(p=t[9]^t[19]^t[29]^t[39]^t[49])^(h<<1|o>>>31),t[0]^=e,t[1]^=r,t[10]^=e,t[11]^=r,t[20]^=e,t[21]^=r,t[30]^=e,t[31]^=r,t[40]^=e,t[41]^=r,e=n^(f<<1|u>>>31),r=s^(u<<1|f>>>31),t[2]^=e,t[3]^=r,t[12]^=e,t[13]^=r,t[22]^=e,t[23]^=r,t[32]^=e,t[33]^=r,t[42]^=e,t[43]^=r,e=o^(a<<1|d>>>31),r=h^(d<<1|a>>>31),t[4]^=e,t[5]^=r,t[14]^=e,t[15]^=r,t[24]^=e,t[25]^=r,t[34]^=e,t[35]^=r,t[44]^=e,t[45]^=r,e=f^(c<<1|p>>>31),r=u^(p<<1|c>>>31),t[6]^=e,t[7]^=r,t[16]^=e,t[17]^=r,t[26]^=e,t[27]^=r,t[36]^=e,t[37]^=r,t[46]^=e,t[47]^=r,e=a^(n<<1|s>>>31),r=d^(s<<1|n>>>31),t[8]^=e,t[9]^=r,t[18]^=e,t[19]^=r,t[28]^=e,t[29]^=r,t[38]^=e,t[39]^=r,t[48]^=e,t[49]^=r,m=t[0],g=t[1],G=t[11]<<4|t[10]>>>28,K=t[10]<<4|t[11]>>>28,N=t[20]<<3|t[21]>>>29,x=t[21]<<3|t[20]>>>29,ht=t[31]<<9|t[30]>>>23,ft=t[30]<<9|t[31]>>>23,Q=t[40]<<18|t[41]>>>14,j=t[41]<<18|t[40]>>>14,P=t[2]<<1|t[3]>>>31,k=t[3]<<1|t[2]>>>31,A=t[13]<<12|t[12]>>>20,v=t[12]<<12|t[13]>>>20,W=t[22]<<10|t[23]>>>22,V=t[23]<<10|t[22]>>>22,R=t[33]<<13|t[32]>>>19,U=t[32]<<13|t[33]>>>19,ut=t[42]<<2|t[43]>>>30,at=t[43]<<2|t[42]>>>30,et=t[5]<<30|t[4]>>>2,rt=t[4]<<30|t[5]>>>2,F=t[14]<<6|t[15]>>>26,D=t[15]<<6|t[14]>>>26,y=t[25]<<11|t[24]>>>21,w=t[24]<<11|t[25]>>>21,X=t[34]<<15|t[35]>>>17,Z=t[35]<<15|t[34]>>>17,O=t[45]<<29|t[44]>>>3,T=t[44]<<29|t[45]>>>3,_=t[6]<<28|t[7]>>>4,B=t[7]<<28|t[6]>>>4,it=t[17]<<23|t[16]>>>9,nt=t[16]<<23|t[17]>>>9,L=t[26]<<25|t[27]>>>7,z=t[27]<<25|t[26]>>>7,b=t[36]<<21|t[37]>>>11,M=t[37]<<21|t[36]>>>11,$=t[47]<<24|t[46]>>>8,tt=t[46]<<24|t[47]>>>8,Y=t[8]<<27|t[9]>>>5,J=t[9]<<27|t[8]>>>5,I=t[18]<<20|t[19]>>>12,C=t[19]<<20|t[18]>>>12,st=t[29]<<7|t[28]>>>25,ot=t[28]<<7|t[29]>>>25,q=t[38]<<8|t[39]>>>24,H=t[39]<<8|t[38]>>>24,E=t[48]<<14|t[49]>>>18,S=t[49]<<14|t[48]>>>18,t[0]=m^~A&y,t[1]=g^~v&w,t[10]=_^~I&N,t[11]=B^~C&x,t[20]=P^~F&L,t[21]=k^~D&z,t[30]=Y^~G&W,t[31]=J^~K&V,t[40]=et^~it&st,t[41]=rt^~nt&ot,t[2]=A^~y&b,t[3]=v^~w&M,t[12]=I^~N&R,t[13]=C^~x&U,t[22]=F^~L&q,t[23]=D^~z&H,t[32]=G^~W&X,t[33]=K^~V&Z,t[42]=it^~st&ht,t[43]=nt^~ot&ft,t[4]=y^~b&E,t[5]=w^~M&S,t[14]=N^~R&O,t[15]=x^~U&T,t[24]=L^~q&Q,t[25]=z^~H&j,t[34]=W^~X&$,t[35]=V^~Z&tt,t[44]=st^~ht&ut,t[45]=ot^~ft&at,t[6]=b^~E&m,t[7]=M^~S&g,t[16]=R^~O&_,t[17]=U^~T&B,t[26]=q^~Q&P,t[27]=H^~j&k,t[36]=X^~$&Y,t[37]=Z^~tt&J,t[46]=ht^~ut&et,t[47]=ft^~at&rt,t[8]=E^~m&A,t[9]=S^~g&v,t[18]=O^~_&I,t[19]=T^~B&C,t[28]=Q^~P&F,t[29]=j^~k&D,t[38]=$^~Y&G,t[39]=tt^~J&K,t[48]=ut^~et&it,t[49]=at^~rt&nt,t[0]^=l[i],t[1]^=l[i+1]};if(o)t.exports=E;else for(_=0;_<S.length;++_)i[S[_]]=E[S[_]]}()}({exports:{}});let s=!1,o=!1;const h={debug:1,default:2,info:2,warning:3,error:4,off:5};let f=h.default,u=null;const a=function(){try{const t=[];if(["NFD","NFC","NFKD","NFKC"].forEach((e=>{try{if("test"!=="test".normalize(e))throw new Error("bad normalize")}catch{t.push(e)}})),t.length)throw new Error("missing "+t.join(", "));if(String.fromCharCode(233).normalize("NFD")!==String.fromCharCode(101,769))throw new Error("broken implementation")}catch(t){return t.message}return null}();var l,d;!function(t){t.DEBUG="DEBUG",t.INFO="INFO",t.WARNING="WARNING",t.ERROR="ERROR",t.OFF="OFF"}(l||(l={})),function(t){t.UNKNOWN_ERROR="UNKNOWN_ERROR",t.NOT_IMPLEMENTED="NOT_IMPLEMENTED",t.UNSUPPORTED_OPERATION="UNSUPPORTED_OPERATION",t.NETWORK_ERROR="NETWORK_ERROR",t.SERVER_ERROR="SERVER_ERROR",t.TIMEOUT="TIMEOUT",t.BUFFER_OVERRUN="BUFFER_OVERRUN",t.NUMERIC_FAULT="NUMERIC_FAULT",t.MISSING_NEW="MISSING_NEW",t.INVALID_ARGUMENT="INVALID_ARGUMENT",t.MISSING_ARGUMENT="MISSING_ARGUMENT",t.UNEXPECTED_ARGUMENT="UNEXPECTED_ARGUMENT",t.CALL_EXCEPTION="CALL_EXCEPTION",t.INSUFFICIENT_FUNDS="INSUFFICIENT_FUNDS",t.NONCE_EXPIRED="NONCE_EXPIRED",t.REPLACEMENT_UNDERPRICED="REPLACEMENT_UNDERPRICED",t.UNPREDICTABLE_GAS_LIMIT="UNPREDICTABLE_GAS_LIMIT",t.TRANSACTION_REPLACED="TRANSACTION_REPLACED",t.ACTION_REJECTED="ACTION_REJECTED"}(d||(d={}));const c="0123456789abcdef";class p{constructor(t){Object.defineProperty(this,"version",{enumerable:!0,value:t,writable:!1})}_log(t,e){const r=t.toLowerCase();null==h[r]&&this.throwArgumentError("invalid log level name","logLevel",t),!(f>h[r])&&console.log.apply(console,e)}debug(...t){this._log(p.levels.DEBUG,t)}info(...t){this._log(p.levels.INFO,t)}warn(...t){this._log(p.levels.WARNING,t)}makeError(t,e,r){if(o)return this.makeError("censored error",e,{});e||(e=p.errors.UNKNOWN_ERROR),r||(r={});const i=[];Object.keys(r).forEach((t=>{const e=r[t];try{if(e instanceof Uint8Array){let r="";for(let t=0;t<e.length;t++)r+=c[e[t]>>4],r+=c[15&e[t]];i.push(t+"=Uint8Array(0x"+r+")")}else i.push(t+"="+JSON.stringify(e))}catch{i.push(t+"="+JSON.stringify(r[t].toString()))}})),i.push(`code=${e}`),i.push(`version=${this.version}`);const n=t;let s="";switch(e){case d.NUMERIC_FAULT:{s="NUMERIC_FAULT";const e=t;switch(e){case"overflow":case"underflow":case"division-by-zero":s+="-"+e;break;case"negative-power":case"negative-width":s+="-unsupported";break;case"unbound-bitwise-result":s+="-unbound-result"}break}case d.CALL_EXCEPTION:case d.INSUFFICIENT_FUNDS:case d.MISSING_NEW:case d.NONCE_EXPIRED:case d.REPLACEMENT_UNDERPRICED:case d.TRANSACTION_REPLACED:case d.UNPREDICTABLE_GAS_LIMIT:s=e}s&&(t+=" [ See: https://links.ethers.org/v5-errors-"+s+" ]"),i.length&&(t+=" ("+i.join(", ")+")");const h=new Error(t);return h.reason=n,h.code=e,Object.keys(r).forEach((function(t){h[t]=r[t]})),h}throwError(t,e,r){throw this.makeError(t,e,r)}throwArgumentError(t,e,r){return this.throwError(t,p.errors.INVALID_ARGUMENT,{argument:e,value:r})}assert(t,e,r,i){t||this.throwError(e,r,i)}assertArgument(t,e,r,i){t||this.throwArgumentError(e,r,i)}checkNormalize(t){a&&this.throwError("platform missing String.prototype.normalize",p.errors.UNSUPPORTED_OPERATION,{operation:"String.prototype.normalize",form:a})}checkSafeUint53(t,e){"number"==typeof t&&(null==e&&(e="value not safe"),(t<0||t>=9007199254740991)&&this.throwError(e,p.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"out-of-safe-range",value:t}),t%1&&this.throwError(e,p.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"non-integer",value:t}))}checkArgumentCount(t,e,r){r=r?": "+r:"",t<e&&this.throwError("missing argument"+r,p.errors.MISSING_ARGUMENT,{count:t,expectedCount:e}),t>e&&this.throwError("too many arguments"+r,p.errors.UNEXPECTED_ARGUMENT,{count:t,expectedCount:e})}checkNew(t,e){(t===Object||null==t)&&this.throwError("missing new",p.errors.MISSING_NEW,{name:e.name})}checkAbstract(t,e){t===e?this.throwError("cannot instantiate abstract class "+JSON.stringify(e.name)+" directly; use a sub-class",p.errors.UNSUPPORTED_OPERATION,{name:t.name,operation:"new"}):(t===Object||null==t)&&this.throwError("missing new",p.errors.MISSING_NEW,{name:e.name})}static globalLogger(){return u||(u=new p("logger/5.7.0")),u}static setCensorship(t,e){if(!t&&e&&this.globalLogger().throwError("cannot permanently disable censorship",p.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"}),s){if(!t)return;this.globalLogger().throwError("error censorship permanent",p.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"})}o=!!t,s=!!e}static setLogLevel(t){const e=h[t.toLowerCase()];null!=e?f=e:p.globalLogger().warn("invalid log level - "+t)}static from(t){return new p(t)}}p.errors=d,p.levels=l;const m=new p("bytes/5.7.0");function g(t){return!!t.toHexString}function A(t){return t.slice||(t.slice=function(){const e=Array.prototype.slice.call(arguments);return A(new Uint8Array(Array.prototype.slice.apply(t,e)))}),t}function v(t){return"number"==typeof t&&t==t&&t%1==0}function y(t){if(null==t)return!1;if(t.constructor===Uint8Array)return!0;if("string"==typeof t||!v(t.length)||t.length<0)return!1;for(let e=0;e<t.length;e++){const r=t[e];if(!v(r)||r<0||r>=256)return!1}return!0}function w(t,e){if(e||(e={}),"number"==typeof t){m.checkSafeUint53(t,"invalid arrayify value");const e=[];for(;t;)e.unshift(255&t),t=parseInt(String(t/256));return 0===e.length&&e.push(0),A(new Uint8Array(e))}if(e.allowMissingPrefix&&"string"==typeof t&&"0x"!==t.substring(0,2)&&(t="0x"+t),g(t)&&(t=t.toHexString()),b(t)){let r=t.substring(2);r.length%2&&("left"===e.hexPad?r="0"+r:"right"===e.hexPad?r+="0":m.throwArgumentError("hex data is odd-length","value",t));const i=[];for(let t=0;t<r.length;t+=2)i.push(parseInt(r.substring(t,t+2),16));return A(new Uint8Array(i))}return y(t)?A(new Uint8Array(t)):m.throwArgumentError("invalid arrayify value","value",t)}function b(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/)||e&&t.length!==2+2*e)}const M="0123456789abcdef";function E(t,e){if(e||(e={}),"number"==typeof t){m.checkSafeUint53(t,"invalid hexlify value");let e="";for(;t;)e=M[15&t]+e,t=Math.floor(t/16);return e.length?(e.length%2&&(e="0"+e),"0x"+e):"0x00"}if("bigint"==typeof t)return(t=t.toString(16)).length%2?"0x0"+t:"0x"+t;if(e.allowMissingPrefix&&"string"==typeof t&&"0x"!==t.substring(0,2)&&(t="0x"+t),g(t))return t.toHexString();if(b(t))return t.length%2&&("left"===e.hexPad?t="0x0"+t.substring(2):"right"===e.hexPad?t+="0":m.throwArgumentError("hex data is odd-length","value",t)),t.toLowerCase();if(y(t)){let e="0x";for(let r=0;r<t.length;r++){let i=t[r];e+=M[(240&i)>>4]+M[15&i]}return e}return m.throwArgumentError("invalid hexlify value","value",t)}function S(t,e){for("string"!=typeof t?t=E(t):b(t)||m.throwArgumentError("invalid hex string","value",t),t.length>2*e+2&&m.throwArgumentError("value out of range","value",arguments[1]);t.length<2*e+2;)t="0x0"+t.substring(2);return t}var _={exports:{}},B=function(t){var e=t.default;if("function"==typeof e){var r=function(){return e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var i=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(r,e,i.get?i:{enumerable:!0,get:function(){return t[e]}})})),r}(Object.freeze({__proto__:null,default:{}}));!function(t){!function(t,e){function r(t,e){if(!t)throw new Error(e||"Assertion failed")}function i(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}function n(t,e,r){if(n.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&(("le"===e||"be"===e)&&(r=e,e=10),this._init(t||0,e||10,r||"be"))}var s;"object"==typeof t?t.exports=n:e.BN=n,n.BN=n,n.wordSize=26;try{s=typeof window<"u"&&typeof window.Buffer<"u"?window.Buffer:B.Buffer}catch{}function o(t,e){var i=t.charCodeAt(e);return i>=48&&i<=57?i-48:i>=65&&i<=70?i-55:i>=97&&i<=102?i-87:void r(!1,"Invalid character in "+t)}function h(t,e,r){var i=o(t,r);return r-1>=e&&(i|=o(t,r-1)<<4),i}function f(t,e,i,n){for(var s=0,o=0,h=Math.min(t.length,i),f=e;f<h;f++){var u=t.charCodeAt(f)-48;s*=n,o=u>=49?u-49+10:u>=17?u-17+10:u,r(u>=0&&o<n,"Invalid character"),s+=o}return s}function u(t,e){t.words=e.words,t.length=e.length,t.negative=e.negative,t.red=e.red}if(n.isBN=function(t){return t instanceof n||null!==t&&"object"==typeof t&&t.constructor.wordSize===n.wordSize&&Array.isArray(t.words)},n.max=function(t,e){return t.cmp(e)>0?t:e},n.min=function(t,e){return t.cmp(e)<0?t:e},n.prototype._init=function(t,e,i){if("number"==typeof t)return this._initNumber(t,e,i);if("object"==typeof t)return this._initArray(t,e,i);"hex"===e&&(e=16),r(e===(0|e)&&e>=2&&e<=36);var n=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&(n++,this.negative=1),n<t.length&&(16===e?this._parseHex(t,n,i):(this._parseBase(t,e,n),"le"===i&&this._initArray(this.toArray(),e,i)))},n.prototype._initNumber=function(t,e,i){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(r(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===i&&this._initArray(this.toArray(),e,i)},n.prototype._initArray=function(t,e,i){if(r("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var s,o,h=0;if("be"===i)for(n=t.length-1,s=0;n>=0;n-=3)o=t[n]|t[n-1]<<8|t[n-2]<<16,this.words[s]|=o<<h&67108863,this.words[s+1]=o>>>26-h&67108863,(h+=24)>=26&&(h-=26,s++);else if("le"===i)for(n=0,s=0;n<t.length;n+=3)o=t[n]|t[n+1]<<8|t[n+2]<<16,this.words[s]|=o<<h&67108863,this.words[s+1]=o>>>26-h&67108863,(h+=24)>=26&&(h-=26,s++);return this._strip()},n.prototype._parseHex=function(t,e,r){this.length=Math.ceil((t.length-e)/6),this.words=new Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var n,s=0,o=0;if("be"===r)for(i=t.length-1;i>=e;i-=2)n=h(t,e,i)<<s,this.words[o]|=67108863&n,s>=18?(s-=18,o+=1,this.words[o]|=n>>>26):s+=8;else for(i=(t.length-e)%2==0?e+1:e;i<t.length;i+=2)n=h(t,e,i)<<s,this.words[o]|=67108863&n,s>=18?(s-=18,o+=1,this.words[o]|=n>>>26):s+=8;this._strip()},n.prototype._parseBase=function(t,e,r){this.words=[0],this.length=1;for(var i=0,n=1;n<=67108863;n*=e)i++;i--,n=n/e|0;for(var s=t.length-r,o=s%i,h=Math.min(s,s-o)+r,u=0,a=r;a<h;a+=i)u=f(t,a,a+i,e),this.imuln(n),this.words[0]+u<67108864?this.words[0]+=u:this._iaddn(u);if(0!==o){var l=1;for(u=f(t,a,t.length,e),a=0;a<o;a++)l*=e;this.imuln(l),this.words[0]+u<67108864?this.words[0]+=u:this._iaddn(u)}this._strip()},n.prototype.copy=function(t){t.words=new Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},n.prototype._move=function(t){u(t,this)},n.prototype.clone=function(){var t=new n(null);return this.copy(t),t},n.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},n.prototype._strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},n.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},typeof Symbol<"u"&&"function"==typeof Symbol.for)try{n.prototype[Symbol.for("nodejs.util.inspect.custom")]=a}catch{n.prototype.inspect=a}else n.prototype.inspect=a;function a(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var l=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],d=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],c=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function p(t,e,r){r.negative=e.negative^t.negative;var i=t.length+e.length|0;r.length=i,i=i-1|0;var n=0|t.words[0],s=0|e.words[0],o=n*s,h=67108863&o,f=o/67108864|0;r.words[0]=h;for(var u=1;u<i;u++){for(var a=f>>>26,l=67108863&f,d=Math.min(u,e.length-1),c=Math.max(0,u-t.length+1);c<=d;c++){var p=u-c|0;a+=(o=(n=0|t.words[p])*(s=0|e.words[c])+l)/67108864|0,l=67108863&o}r.words[u]=0|l,f=0|a}return 0!==f?r.words[u]=0|f:r.length--,r._strip()}n.prototype.toString=function(t,e){var i;if(e=0|e||1,16===(t=t||10)||"hex"===t){i="";for(var n=0,s=0,o=0;o<this.length;o++){var h=this.words[o],f=(16777215&(h<<n|s)).toString(16);s=h>>>24-n&16777215,(n+=2)>=26&&(n-=26,o--),i=0!==s||o!==this.length-1?l[6-f.length]+f+i:f+i}for(0!==s&&(i=s.toString(16)+i);i.length%e!=0;)i="0"+i;return 0!==this.negative&&(i="-"+i),i}if(t===(0|t)&&t>=2&&t<=36){var u=d[t],a=c[t];i="";var p=this.clone();for(p.negative=0;!p.isZero();){var m=p.modrn(a).toString(t);i=(p=p.idivn(a)).isZero()?m+i:l[u-m.length]+m+i}for(this.isZero()&&(i="0"+i);i.length%e!=0;)i="0"+i;return 0!==this.negative&&(i="-"+i),i}r(!1,"Base should be between 2 and 36")},n.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&r(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},n.prototype.toJSON=function(){return this.toString(16,2)},s&&(n.prototype.toBuffer=function(t,e){return this.toArrayLike(s,t,e)}),n.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},n.prototype.toArrayLike=function(t,e,i){this._strip();var n=this.byteLength(),s=i||Math.max(1,n);r(n<=s,"byte array longer than desired length"),r(s>0,"Requested array length <= 0");var o=function(t,e){return t.allocUnsafe?t.allocUnsafe(e):new t(e)}(t,s);return this["_toArrayLike"+("le"===e?"LE":"BE")](o,n),o},n.prototype._toArrayLikeLE=function(t,e){for(var r=0,i=0,n=0,s=0;n<this.length;n++){var o=this.words[n]<<s|i;t[r++]=255&o,r<t.length&&(t[r++]=o>>8&255),r<t.length&&(t[r++]=o>>16&255),6===s?(r<t.length&&(t[r++]=o>>24&255),i=0,s=0):(i=o>>>24,s+=2)}if(r<t.length)for(t[r++]=i;r<t.length;)t[r++]=0},n.prototype._toArrayLikeBE=function(t,e){for(var r=t.length-1,i=0,n=0,s=0;n<this.length;n++){var o=this.words[n]<<s|i;t[r--]=255&o,r>=0&&(t[r--]=o>>8&255),r>=0&&(t[r--]=o>>16&255),6===s?(r>=0&&(t[r--]=o>>24&255),i=0,s=0):(i=o>>>24,s+=2)}if(r>=0)for(t[r--]=i;r>=0;)t[r--]=0},Math.clz32?n.prototype._countBits=function(t){return 32-Math.clz32(t)}:n.prototype._countBits=function(t){var e=t,r=0;return e>=4096&&(r+=13,e>>>=13),e>=64&&(r+=7,e>>>=7),e>=8&&(r+=4,e>>>=4),e>=2&&(r+=2,e>>>=2),r+e},n.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,r=0;return 8191&e||(r+=13,e>>>=13),127&e||(r+=7,e>>>=7),15&e||(r+=4,e>>>=4),3&e||(r+=2,e>>>=2),1&e||r++,r},n.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return 26*(this.length-1)+e},n.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var r=this._zeroBits(this.words[e]);if(t+=r,26!==r)break}return t},n.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},n.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},n.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},n.prototype.isNeg=function(){return 0!==this.negative},n.prototype.neg=function(){return this.clone().ineg()},n.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},n.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this._strip()},n.prototype.ior=function(t){return r(!(this.negative|t.negative)),this.iuor(t)},n.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},n.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},n.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var r=0;r<e.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=e.length,this._strip()},n.prototype.iand=function(t){return r(!(this.negative|t.negative)),this.iuand(t)},n.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},n.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},n.prototype.iuxor=function(t){var e,r;this.length>t.length?(e=this,r=t):(e=t,r=this);for(var i=0;i<r.length;i++)this.words[i]=e.words[i]^r.words[i];if(this!==e)for(;i<e.length;i++)this.words[i]=e.words[i];return this.length=e.length,this._strip()},n.prototype.ixor=function(t){return r(!(this.negative|t.negative)),this.iuxor(t)},n.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},n.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},n.prototype.inotn=function(t){r("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),i=t%26;this._expand(e),i>0&&e--;for(var n=0;n<e;n++)this.words[n]=67108863&~this.words[n];return i>0&&(this.words[n]=~this.words[n]&67108863>>26-i),this._strip()},n.prototype.notn=function(t){return this.clone().inotn(t)},n.prototype.setn=function(t,e){r("number"==typeof t&&t>=0);var i=t/26|0,n=t%26;return this._expand(i+1),this.words[i]=e?this.words[i]|1<<n:this.words[i]&~(1<<n),this._strip()},n.prototype.iadd=function(t){var e,r,i;if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(r=this,i=t):(r=t,i=this);for(var n=0,s=0;s<i.length;s++)e=(0|r.words[s])+(0|i.words[s])+n,this.words[s]=67108863&e,n=e>>>26;for(;0!==n&&s<r.length;s++)e=(0|r.words[s])+n,this.words[s]=67108863&e,n=e>>>26;if(this.length=r.length,0!==n)this.words[this.length]=n,this.length++;else if(r!==this)for(;s<r.length;s++)this.words[s]=r.words[s];return this},n.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},n.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var r,i,n=this.cmp(t);if(0===n)return this.negative=0,this.length=1,this.words[0]=0,this;n>0?(r=this,i=t):(r=t,i=this);for(var s=0,o=0;o<i.length;o++)s=(e=(0|r.words[o])-(0|i.words[o])+s)>>26,this.words[o]=67108863&e;for(;0!==s&&o<r.length;o++)s=(e=(0|r.words[o])+s)>>26,this.words[o]=67108863&e;if(0===s&&o<r.length&&r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this.length=Math.max(this.length,o),r!==this&&(this.negative=1),this._strip()},n.prototype.sub=function(t){return this.clone().isub(t)};var m=function(t,e,r){var i,n,s,o=t.words,h=e.words,f=r.words,u=0,a=0|o[0],l=8191&a,d=a>>>13,c=0|o[1],p=8191&c,m=c>>>13,g=0|o[2],A=8191&g,v=g>>>13,y=0|o[3],w=8191&y,b=y>>>13,M=0|o[4],E=8191&M,S=M>>>13,_=0|o[5],B=8191&_,I=_>>>13,C=0|o[6],N=8191&C,x=C>>>13,R=0|o[7],U=8191&R,O=R>>>13,T=0|o[8],P=8191&T,k=T>>>13,F=0|o[9],D=8191&F,L=F>>>13,z=0|h[0],q=8191&z,H=z>>>13,Q=0|h[1],j=8191&Q,Y=Q>>>13,J=0|h[2],G=8191&J,K=J>>>13,W=0|h[3],V=8191&W,X=W>>>13,Z=0|h[4],$=8191&Z,tt=Z>>>13,et=0|h[5],rt=8191&et,it=et>>>13,nt=0|h[6],st=8191&nt,ot=nt>>>13,ht=0|h[7],ft=8191&ht,ut=ht>>>13,at=0|h[8],lt=8191&at,dt=at>>>13,ct=0|h[9],pt=8191&ct,mt=ct>>>13;r.negative=t.negative^e.negative,r.length=19;var gt=(u+(i=Math.imul(l,q))|0)+((8191&(n=(n=Math.imul(l,H))+Math.imul(d,q)|0))<<13)|0;u=((s=Math.imul(d,H))+(n>>>13)|0)+(gt>>>26)|0,gt&=67108863,i=Math.imul(p,q),n=(n=Math.imul(p,H))+Math.imul(m,q)|0,s=Math.imul(m,H);var At=(u+(i=i+Math.imul(l,j)|0)|0)+((8191&(n=(n=n+Math.imul(l,Y)|0)+Math.imul(d,j)|0))<<13)|0;u=((s=s+Math.imul(d,Y)|0)+(n>>>13)|0)+(At>>>26)|0,At&=67108863,i=Math.imul(A,q),n=(n=Math.imul(A,H))+Math.imul(v,q)|0,s=Math.imul(v,H),i=i+Math.imul(p,j)|0,n=(n=n+Math.imul(p,Y)|0)+Math.imul(m,j)|0,s=s+Math.imul(m,Y)|0;var vt=(u+(i=i+Math.imul(l,G)|0)|0)+((8191&(n=(n=n+Math.imul(l,K)|0)+Math.imul(d,G)|0))<<13)|0;u=((s=s+Math.imul(d,K)|0)+(n>>>13)|0)+(vt>>>26)|0,vt&=67108863,i=Math.imul(w,q),n=(n=Math.imul(w,H))+Math.imul(b,q)|0,s=Math.imul(b,H),i=i+Math.imul(A,j)|0,n=(n=n+Math.imul(A,Y)|0)+Math.imul(v,j)|0,s=s+Math.imul(v,Y)|0,i=i+Math.imul(p,G)|0,n=(n=n+Math.imul(p,K)|0)+Math.imul(m,G)|0,s=s+Math.imul(m,K)|0;var yt=(u+(i=i+Math.imul(l,V)|0)|0)+((8191&(n=(n=n+Math.imul(l,X)|0)+Math.imul(d,V)|0))<<13)|0;u=((s=s+Math.imul(d,X)|0)+(n>>>13)|0)+(yt>>>26)|0,yt&=67108863,i=Math.imul(E,q),n=(n=Math.imul(E,H))+Math.imul(S,q)|0,s=Math.imul(S,H),i=i+Math.imul(w,j)|0,n=(n=n+Math.imul(w,Y)|0)+Math.imul(b,j)|0,s=s+Math.imul(b,Y)|0,i=i+Math.imul(A,G)|0,n=(n=n+Math.imul(A,K)|0)+Math.imul(v,G)|0,s=s+Math.imul(v,K)|0,i=i+Math.imul(p,V)|0,n=(n=n+Math.imul(p,X)|0)+Math.imul(m,V)|0,s=s+Math.imul(m,X)|0;var wt=(u+(i=i+Math.imul(l,$)|0)|0)+((8191&(n=(n=n+Math.imul(l,tt)|0)+Math.imul(d,$)|0))<<13)|0;u=((s=s+Math.imul(d,tt)|0)+(n>>>13)|0)+(wt>>>26)|0,wt&=67108863,i=Math.imul(B,q),n=(n=Math.imul(B,H))+Math.imul(I,q)|0,s=Math.imul(I,H),i=i+Math.imul(E,j)|0,n=(n=n+Math.imul(E,Y)|0)+Math.imul(S,j)|0,s=s+Math.imul(S,Y)|0,i=i+Math.imul(w,G)|0,n=(n=n+Math.imul(w,K)|0)+Math.imul(b,G)|0,s=s+Math.imul(b,K)|0,i=i+Math.imul(A,V)|0,n=(n=n+Math.imul(A,X)|0)+Math.imul(v,V)|0,s=s+Math.imul(v,X)|0,i=i+Math.imul(p,$)|0,n=(n=n+Math.imul(p,tt)|0)+Math.imul(m,$)|0,s=s+Math.imul(m,tt)|0;var bt=(u+(i=i+Math.imul(l,rt)|0)|0)+((8191&(n=(n=n+Math.imul(l,it)|0)+Math.imul(d,rt)|0))<<13)|0;u=((s=s+Math.imul(d,it)|0)+(n>>>13)|0)+(bt>>>26)|0,bt&=67108863,i=Math.imul(N,q),n=(n=Math.imul(N,H))+Math.imul(x,q)|0,s=Math.imul(x,H),i=i+Math.imul(B,j)|0,n=(n=n+Math.imul(B,Y)|0)+Math.imul(I,j)|0,s=s+Math.imul(I,Y)|0,i=i+Math.imul(E,G)|0,n=(n=n+Math.imul(E,K)|0)+Math.imul(S,G)|0,s=s+Math.imul(S,K)|0,i=i+Math.imul(w,V)|0,n=(n=n+Math.imul(w,X)|0)+Math.imul(b,V)|0,s=s+Math.imul(b,X)|0,i=i+Math.imul(A,$)|0,n=(n=n+Math.imul(A,tt)|0)+Math.imul(v,$)|0,s=s+Math.imul(v,tt)|0,i=i+Math.imul(p,rt)|0,n=(n=n+Math.imul(p,it)|0)+Math.imul(m,rt)|0,s=s+Math.imul(m,it)|0;var Mt=(u+(i=i+Math.imul(l,st)|0)|0)+((8191&(n=(n=n+Math.imul(l,ot)|0)+Math.imul(d,st)|0))<<13)|0;u=((s=s+Math.imul(d,ot)|0)+(n>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,i=Math.imul(U,q),n=(n=Math.imul(U,H))+Math.imul(O,q)|0,s=Math.imul(O,H),i=i+Math.imul(N,j)|0,n=(n=n+Math.imul(N,Y)|0)+Math.imul(x,j)|0,s=s+Math.imul(x,Y)|0,i=i+Math.imul(B,G)|0,n=(n=n+Math.imul(B,K)|0)+Math.imul(I,G)|0,s=s+Math.imul(I,K)|0,i=i+Math.imul(E,V)|0,n=(n=n+Math.imul(E,X)|0)+Math.imul(S,V)|0,s=s+Math.imul(S,X)|0,i=i+Math.imul(w,$)|0,n=(n=n+Math.imul(w,tt)|0)+Math.imul(b,$)|0,s=s+Math.imul(b,tt)|0,i=i+Math.imul(A,rt)|0,n=(n=n+Math.imul(A,it)|0)+Math.imul(v,rt)|0,s=s+Math.imul(v,it)|0,i=i+Math.imul(p,st)|0,n=(n=n+Math.imul(p,ot)|0)+Math.imul(m,st)|0,s=s+Math.imul(m,ot)|0;var Et=(u+(i=i+Math.imul(l,ft)|0)|0)+((8191&(n=(n=n+Math.imul(l,ut)|0)+Math.imul(d,ft)|0))<<13)|0;u=((s=s+Math.imul(d,ut)|0)+(n>>>13)|0)+(Et>>>26)|0,Et&=67108863,i=Math.imul(P,q),n=(n=Math.imul(P,H))+Math.imul(k,q)|0,s=Math.imul(k,H),i=i+Math.imul(U,j)|0,n=(n=n+Math.imul(U,Y)|0)+Math.imul(O,j)|0,s=s+Math.imul(O,Y)|0,i=i+Math.imul(N,G)|0,n=(n=n+Math.imul(N,K)|0)+Math.imul(x,G)|0,s=s+Math.imul(x,K)|0,i=i+Math.imul(B,V)|0,n=(n=n+Math.imul(B,X)|0)+Math.imul(I,V)|0,s=s+Math.imul(I,X)|0,i=i+Math.imul(E,$)|0,n=(n=n+Math.imul(E,tt)|0)+Math.imul(S,$)|0,s=s+Math.imul(S,tt)|0,i=i+Math.imul(w,rt)|0,n=(n=n+Math.imul(w,it)|0)+Math.imul(b,rt)|0,s=s+Math.imul(b,it)|0,i=i+Math.imul(A,st)|0,n=(n=n+Math.imul(A,ot)|0)+Math.imul(v,st)|0,s=s+Math.imul(v,ot)|0,i=i+Math.imul(p,ft)|0,n=(n=n+Math.imul(p,ut)|0)+Math.imul(m,ft)|0,s=s+Math.imul(m,ut)|0;var St=(u+(i=i+Math.imul(l,lt)|0)|0)+((8191&(n=(n=n+Math.imul(l,dt)|0)+Math.imul(d,lt)|0))<<13)|0;u=((s=s+Math.imul(d,dt)|0)+(n>>>13)|0)+(St>>>26)|0,St&=67108863,i=Math.imul(D,q),n=(n=Math.imul(D,H))+Math.imul(L,q)|0,s=Math.imul(L,H),i=i+Math.imul(P,j)|0,n=(n=n+Math.imul(P,Y)|0)+Math.imul(k,j)|0,s=s+Math.imul(k,Y)|0,i=i+Math.imul(U,G)|0,n=(n=n+Math.imul(U,K)|0)+Math.imul(O,G)|0,s=s+Math.imul(O,K)|0,i=i+Math.imul(N,V)|0,n=(n=n+Math.imul(N,X)|0)+Math.imul(x,V)|0,s=s+Math.imul(x,X)|0,i=i+Math.imul(B,$)|0,n=(n=n+Math.imul(B,tt)|0)+Math.imul(I,$)|0,s=s+Math.imul(I,tt)|0,i=i+Math.imul(E,rt)|0,n=(n=n+Math.imul(E,it)|0)+Math.imul(S,rt)|0,s=s+Math.imul(S,it)|0,i=i+Math.imul(w,st)|0,n=(n=n+Math.imul(w,ot)|0)+Math.imul(b,st)|0,s=s+Math.imul(b,ot)|0,i=i+Math.imul(A,ft)|0,n=(n=n+Math.imul(A,ut)|0)+Math.imul(v,ft)|0,s=s+Math.imul(v,ut)|0,i=i+Math.imul(p,lt)|0,n=(n=n+Math.imul(p,dt)|0)+Math.imul(m,lt)|0,s=s+Math.imul(m,dt)|0;var _t=(u+(i=i+Math.imul(l,pt)|0)|0)+((8191&(n=(n=n+Math.imul(l,mt)|0)+Math.imul(d,pt)|0))<<13)|0;u=((s=s+Math.imul(d,mt)|0)+(n>>>13)|0)+(_t>>>26)|0,_t&=67108863,i=Math.imul(D,j),n=(n=Math.imul(D,Y))+Math.imul(L,j)|0,s=Math.imul(L,Y),i=i+Math.imul(P,G)|0,n=(n=n+Math.imul(P,K)|0)+Math.imul(k,G)|0,s=s+Math.imul(k,K)|0,i=i+Math.imul(U,V)|0,n=(n=n+Math.imul(U,X)|0)+Math.imul(O,V)|0,s=s+Math.imul(O,X)|0,i=i+Math.imul(N,$)|0,n=(n=n+Math.imul(N,tt)|0)+Math.imul(x,$)|0,s=s+Math.imul(x,tt)|0,i=i+Math.imul(B,rt)|0,n=(n=n+Math.imul(B,it)|0)+Math.imul(I,rt)|0,s=s+Math.imul(I,it)|0,i=i+Math.imul(E,st)|0,n=(n=n+Math.imul(E,ot)|0)+Math.imul(S,st)|0,s=s+Math.imul(S,ot)|0,i=i+Math.imul(w,ft)|0,n=(n=n+Math.imul(w,ut)|0)+Math.imul(b,ft)|0,s=s+Math.imul(b,ut)|0,i=i+Math.imul(A,lt)|0,n=(n=n+Math.imul(A,dt)|0)+Math.imul(v,lt)|0,s=s+Math.imul(v,dt)|0;var Bt=(u+(i=i+Math.imul(p,pt)|0)|0)+((8191&(n=(n=n+Math.imul(p,mt)|0)+Math.imul(m,pt)|0))<<13)|0;u=((s=s+Math.imul(m,mt)|0)+(n>>>13)|0)+(Bt>>>26)|0,Bt&=67108863,i=Math.imul(D,G),n=(n=Math.imul(D,K))+Math.imul(L,G)|0,s=Math.imul(L,K),i=i+Math.imul(P,V)|0,n=(n=n+Math.imul(P,X)|0)+Math.imul(k,V)|0,s=s+Math.imul(k,X)|0,i=i+Math.imul(U,$)|0,n=(n=n+Math.imul(U,tt)|0)+Math.imul(O,$)|0,s=s+Math.imul(O,tt)|0,i=i+Math.imul(N,rt)|0,n=(n=n+Math.imul(N,it)|0)+Math.imul(x,rt)|0,s=s+Math.imul(x,it)|0,i=i+Math.imul(B,st)|0,n=(n=n+Math.imul(B,ot)|0)+Math.imul(I,st)|0,s=s+Math.imul(I,ot)|0,i=i+Math.imul(E,ft)|0,n=(n=n+Math.imul(E,ut)|0)+Math.imul(S,ft)|0,s=s+Math.imul(S,ut)|0,i=i+Math.imul(w,lt)|0,n=(n=n+Math.imul(w,dt)|0)+Math.imul(b,lt)|0,s=s+Math.imul(b,dt)|0;var It=(u+(i=i+Math.imul(A,pt)|0)|0)+((8191&(n=(n=n+Math.imul(A,mt)|0)+Math.imul(v,pt)|0))<<13)|0;u=((s=s+Math.imul(v,mt)|0)+(n>>>13)|0)+(It>>>26)|0,It&=67108863,i=Math.imul(D,V),n=(n=Math.imul(D,X))+Math.imul(L,V)|0,s=Math.imul(L,X),i=i+Math.imul(P,$)|0,n=(n=n+Math.imul(P,tt)|0)+Math.imul(k,$)|0,s=s+Math.imul(k,tt)|0,i=i+Math.imul(U,rt)|0,n=(n=n+Math.imul(U,it)|0)+Math.imul(O,rt)|0,s=s+Math.imul(O,it)|0,i=i+Math.imul(N,st)|0,n=(n=n+Math.imul(N,ot)|0)+Math.imul(x,st)|0,s=s+Math.imul(x,ot)|0,i=i+Math.imul(B,ft)|0,n=(n=n+Math.imul(B,ut)|0)+Math.imul(I,ft)|0,s=s+Math.imul(I,ut)|0,i=i+Math.imul(E,lt)|0,n=(n=n+Math.imul(E,dt)|0)+Math.imul(S,lt)|0,s=s+Math.imul(S,dt)|0;var Ct=(u+(i=i+Math.imul(w,pt)|0)|0)+((8191&(n=(n=n+Math.imul(w,mt)|0)+Math.imul(b,pt)|0))<<13)|0;u=((s=s+Math.imul(b,mt)|0)+(n>>>13)|0)+(Ct>>>26)|0,Ct&=67108863,i=Math.imul(D,$),n=(n=Math.imul(D,tt))+Math.imul(L,$)|0,s=Math.imul(L,tt),i=i+Math.imul(P,rt)|0,n=(n=n+Math.imul(P,it)|0)+Math.imul(k,rt)|0,s=s+Math.imul(k,it)|0,i=i+Math.imul(U,st)|0,n=(n=n+Math.imul(U,ot)|0)+Math.imul(O,st)|0,s=s+Math.imul(O,ot)|0,i=i+Math.imul(N,ft)|0,n=(n=n+Math.imul(N,ut)|0)+Math.imul(x,ft)|0,s=s+Math.imul(x,ut)|0,i=i+Math.imul(B,lt)|0,n=(n=n+Math.imul(B,dt)|0)+Math.imul(I,lt)|0,s=s+Math.imul(I,dt)|0;var Nt=(u+(i=i+Math.imul(E,pt)|0)|0)+((8191&(n=(n=n+Math.imul(E,mt)|0)+Math.imul(S,pt)|0))<<13)|0;u=((s=s+Math.imul(S,mt)|0)+(n>>>13)|0)+(Nt>>>26)|0,Nt&=67108863,i=Math.imul(D,rt),n=(n=Math.imul(D,it))+Math.imul(L,rt)|0,s=Math.imul(L,it),i=i+Math.imul(P,st)|0,n=(n=n+Math.imul(P,ot)|0)+Math.imul(k,st)|0,s=s+Math.imul(k,ot)|0,i=i+Math.imul(U,ft)|0,n=(n=n+Math.imul(U,ut)|0)+Math.imul(O,ft)|0,s=s+Math.imul(O,ut)|0,i=i+Math.imul(N,lt)|0,n=(n=n+Math.imul(N,dt)|0)+Math.imul(x,lt)|0,s=s+Math.imul(x,dt)|0;var xt=(u+(i=i+Math.imul(B,pt)|0)|0)+((8191&(n=(n=n+Math.imul(B,mt)|0)+Math.imul(I,pt)|0))<<13)|0;u=((s=s+Math.imul(I,mt)|0)+(n>>>13)|0)+(xt>>>26)|0,xt&=67108863,i=Math.imul(D,st),n=(n=Math.imul(D,ot))+Math.imul(L,st)|0,s=Math.imul(L,ot),i=i+Math.imul(P,ft)|0,n=(n=n+Math.imul(P,ut)|0)+Math.imul(k,ft)|0,s=s+Math.imul(k,ut)|0,i=i+Math.imul(U,lt)|0,n=(n=n+Math.imul(U,dt)|0)+Math.imul(O,lt)|0,s=s+Math.imul(O,dt)|0;var Rt=(u+(i=i+Math.imul(N,pt)|0)|0)+((8191&(n=(n=n+Math.imul(N,mt)|0)+Math.imul(x,pt)|0))<<13)|0;u=((s=s+Math.imul(x,mt)|0)+(n>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,i=Math.imul(D,ft),n=(n=Math.imul(D,ut))+Math.imul(L,ft)|0,s=Math.imul(L,ut),i=i+Math.imul(P,lt)|0,n=(n=n+Math.imul(P,dt)|0)+Math.imul(k,lt)|0,s=s+Math.imul(k,dt)|0;var Ut=(u+(i=i+Math.imul(U,pt)|0)|0)+((8191&(n=(n=n+Math.imul(U,mt)|0)+Math.imul(O,pt)|0))<<13)|0;u=((s=s+Math.imul(O,mt)|0)+(n>>>13)|0)+(Ut>>>26)|0,Ut&=67108863,i=Math.imul(D,lt),n=(n=Math.imul(D,dt))+Math.imul(L,lt)|0,s=Math.imul(L,dt);var Ot=(u+(i=i+Math.imul(P,pt)|0)|0)+((8191&(n=(n=n+Math.imul(P,mt)|0)+Math.imul(k,pt)|0))<<13)|0;u=((s=s+Math.imul(k,mt)|0)+(n>>>13)|0)+(Ot>>>26)|0,Ot&=67108863;var Tt=(u+(i=Math.imul(D,pt))|0)+((8191&(n=(n=Math.imul(D,mt))+Math.imul(L,pt)|0))<<13)|0;return u=((s=Math.imul(L,mt))+(n>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,f[0]=gt,f[1]=At,f[2]=vt,f[3]=yt,f[4]=wt,f[5]=bt,f[6]=Mt,f[7]=Et,f[8]=St,f[9]=_t,f[10]=Bt,f[11]=It,f[12]=Ct,f[13]=Nt,f[14]=xt,f[15]=Rt,f[16]=Ut,f[17]=Ot,f[18]=Tt,0!==u&&(f[19]=u,r.length++),r};function g(t,e,r){r.negative=e.negative^t.negative,r.length=t.length+e.length;for(var i=0,n=0,s=0;s<r.length-1;s++){var o=n;n=0;for(var h=67108863&i,f=Math.min(s,e.length-1),u=Math.max(0,s-t.length+1);u<=f;u++){var a=s-u,l=(0|t.words[a])*(0|e.words[u]),d=67108863&l;h=67108863&(d=d+h|0),n+=(o=(o=o+(l/67108864|0)|0)+(d>>>26)|0)>>>26,o&=67108863}r.words[s]=h,i=o,o=n}return 0!==i?r.words[s]=i:r.length--,r._strip()}function A(t,e,r){return g(t,e,r)}Math.imul||(m=p),n.prototype.mulTo=function(t,e){var r=this.length+t.length;return 10===this.length&&10===t.length?m(this,t,e):r<63?p(this,t,e):r<1024?g(this,t,e):A(this,t,e)},n.prototype.mul=function(t){var e=new n(null);return e.words=new Array(this.length+t.length),this.mulTo(t,e)},n.prototype.mulf=function(t){var e=new n(null);return e.words=new Array(this.length+t.length),A(this,t,e)},n.prototype.imul=function(t){return this.clone().mulTo(t,this)},n.prototype.imuln=function(t){var e=t<0;e&&(t=-t),r("number"==typeof t),r(t<67108864);for(var i=0,n=0;n<this.length;n++){var s=(0|this.words[n])*t,o=(67108863&s)+(67108863&i);i>>=26,i+=s/67108864|0,i+=o>>>26,this.words[n]=67108863&o}return 0!==i&&(this.words[n]=i,this.length++),e?this.ineg():this},n.prototype.muln=function(t){return this.clone().imuln(t)},n.prototype.sqr=function(){return this.mul(this)},n.prototype.isqr=function(){return this.imul(this.clone())},n.prototype.pow=function(t){var e=function(t){for(var e=new Array(t.bitLength()),r=0;r<e.length;r++){var i=r/26|0,n=r%26;e[r]=t.words[i]>>>n&1}return e}(t);if(0===e.length)return new n(1);for(var r=this,i=0;i<e.length&&0===e[i];i++,r=r.sqr());if(++i<e.length)for(var s=r.sqr();i<e.length;i++,s=s.sqr())0!==e[i]&&(r=r.mul(s));return r},n.prototype.iushln=function(t){r("number"==typeof t&&t>=0);var e,i=t%26,n=(t-i)/26,s=67108863>>>26-i<<26-i;if(0!==i){var o=0;for(e=0;e<this.length;e++){var h=this.words[e]&s,f=(0|this.words[e])-h<<i;this.words[e]=f|o,o=h>>>26-i}o&&(this.words[e]=o,this.length++)}if(0!==n){for(e=this.length-1;e>=0;e--)this.words[e+n]=this.words[e];for(e=0;e<n;e++)this.words[e]=0;this.length+=n}return this._strip()},n.prototype.ishln=function(t){return r(0===this.negative),this.iushln(t)},n.prototype.iushrn=function(t,e,i){var n;r("number"==typeof t&&t>=0),n=e?(e-e%26)/26:0;var s=t%26,o=Math.min((t-s)/26,this.length),h=67108863^67108863>>>s<<s,f=i;if(n-=o,n=Math.max(0,n),f){for(var u=0;u<o;u++)f.words[u]=this.words[u];f.length=o}if(0!==o)if(this.length>o)for(this.length-=o,u=0;u<this.length;u++)this.words[u]=this.words[u+o];else this.words[0]=0,this.length=1;var a=0;for(u=this.length-1;u>=0&&(0!==a||u>=n);u--){var l=0|this.words[u];this.words[u]=a<<26-s|l>>>s,a=l&h}return f&&0!==a&&(f.words[f.length++]=a),0===this.length&&(this.words[0]=0,this.length=1),this._strip()},n.prototype.ishrn=function(t,e,i){return r(0===this.negative),this.iushrn(t,e,i)},n.prototype.shln=function(t){return this.clone().ishln(t)},n.prototype.ushln=function(t){return this.clone().iushln(t)},n.prototype.shrn=function(t){return this.clone().ishrn(t)},n.prototype.ushrn=function(t){return this.clone().iushrn(t)},n.prototype.testn=function(t){r("number"==typeof t&&t>=0);var e=t%26,i=(t-e)/26,n=1<<e;return!(this.length<=i||!(this.words[i]&n))},n.prototype.imaskn=function(t){r("number"==typeof t&&t>=0);var e=t%26,i=(t-e)/26;if(r(0===this.negative,"imaskn works only with positive numbers"),this.length<=i)return this;if(0!==e&&i++,this.length=Math.min(i,this.length),0!==e){var n=67108863^67108863>>>e<<e;this.words[this.length-1]&=n}return this._strip()},n.prototype.maskn=function(t){return this.clone().imaskn(t)},n.prototype.iaddn=function(t){return r("number"==typeof t),r(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<=t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},n.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=67108864;e++)this.words[e]-=67108864,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},n.prototype.isubn=function(t){if(r("number"==typeof t),r(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=67108864,this.words[e+1]-=1;return this._strip()},n.prototype.addn=function(t){return this.clone().iaddn(t)},n.prototype.subn=function(t){return this.clone().isubn(t)},n.prototype.iabs=function(){return this.negative=0,this},n.prototype.abs=function(){return this.clone().iabs()},n.prototype._ishlnsubmul=function(t,e,i){var n,s=t.length+i;this._expand(s);var o,h=0;for(n=0;n<t.length;n++){o=(0|this.words[n+i])+h;var f=(0|t.words[n])*e;h=((o-=67108863&f)>>26)-(f/67108864|0),this.words[n+i]=67108863&o}for(;n<this.length-i;n++)h=(o=(0|this.words[n+i])+h)>>26,this.words[n+i]=67108863&o;if(0===h)return this._strip();for(r(-1===h),h=0,n=0;n<this.length;n++)h=(o=-(0|this.words[n])+h)>>26,this.words[n]=67108863&o;return this.negative=1,this._strip()},n.prototype._wordDiv=function(t,e){var r=(this.length,t.length),i=this.clone(),s=t,o=0|s.words[s.length-1];0!=(r=26-this._countBits(o))&&(s=s.ushln(r),i.iushln(r),o=0|s.words[s.length-1]);var h,f=i.length-s.length;if("mod"!==e){(h=new n(null)).length=f+1,h.words=new Array(h.length);for(var u=0;u<h.length;u++)h.words[u]=0}var a=i.clone()._ishlnsubmul(s,1,f);0===a.negative&&(i=a,h&&(h.words[f]=1));for(var l=f-1;l>=0;l--){var d=67108864*(0|i.words[s.length+l])+(0|i.words[s.length+l-1]);for(d=Math.min(d/o|0,67108863),i._ishlnsubmul(s,d,l);0!==i.negative;)d--,i.negative=0,i._ishlnsubmul(s,1,l),i.isZero()||(i.negative^=1);h&&(h.words[l]=d)}return h&&h._strip(),i._strip(),"div"!==e&&0!==r&&i.iushrn(r),{div:h||null,mod:i}},n.prototype.divmod=function(t,e,i){return r(!t.isZero()),this.isZero()?{div:new n(0),mod:new n(0)}:0!==this.negative&&0===t.negative?(h=this.neg().divmod(t,e),"mod"!==e&&(s=h.div.neg()),"div"!==e&&(o=h.mod.neg(),i&&0!==o.negative&&o.iadd(t)),{div:s,mod:o}):0===this.negative&&0!==t.negative?(h=this.divmod(t.neg(),e),"mod"!==e&&(s=h.div.neg()),{div:s,mod:h.mod}):this.negative&t.negative?(h=this.neg().divmod(t.neg(),e),"div"!==e&&(o=h.mod.neg(),i&&0!==o.negative&&o.isub(t)),{div:h.div,mod:o}):t.length>this.length||this.cmp(t)<0?{div:new n(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new n(this.modrn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new n(this.modrn(t.words[0]))}:this._wordDiv(t,e);var s,o,h},n.prototype.div=function(t){return this.divmod(t,"div",!1).div},n.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},n.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},n.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var r=0!==e.div.negative?e.mod.isub(t):e.mod,i=t.ushrn(1),n=t.andln(1),s=r.cmp(i);return s<0||1===n&&0===s?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},n.prototype.modrn=function(t){var e=t<0;e&&(t=-t),r(t<=67108863);for(var i=(1<<26)%t,n=0,s=this.length-1;s>=0;s--)n=(i*n+(0|this.words[s]))%t;return e?-n:n},n.prototype.modn=function(t){return this.modrn(t)},n.prototype.idivn=function(t){var e=t<0;e&&(t=-t),r(t<=67108863);for(var i=0,n=this.length-1;n>=0;n--){var s=(0|this.words[n])+67108864*i;this.words[n]=s/t|0,i=s%t}return this._strip(),e?this.ineg():this},n.prototype.divn=function(t){return this.clone().idivn(t)},n.prototype.egcd=function(t){r(0===t.negative),r(!t.isZero());var e=this,i=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var s=new n(1),o=new n(0),h=new n(0),f=new n(1),u=0;e.isEven()&&i.isEven();)e.iushrn(1),i.iushrn(1),++u;for(var a=i.clone(),l=e.clone();!e.isZero();){for(var d=0,c=1;!(e.words[0]&c)&&d<26;++d,c<<=1);if(d>0)for(e.iushrn(d);d-- >0;)(s.isOdd()||o.isOdd())&&(s.iadd(a),o.isub(l)),s.iushrn(1),o.iushrn(1);for(var p=0,m=1;!(i.words[0]&m)&&p<26;++p,m<<=1);if(p>0)for(i.iushrn(p);p-- >0;)(h.isOdd()||f.isOdd())&&(h.iadd(a),f.isub(l)),h.iushrn(1),f.iushrn(1);e.cmp(i)>=0?(e.isub(i),s.isub(h),o.isub(f)):(i.isub(e),h.isub(s),f.isub(o))}return{a:h,b:f,gcd:i.iushln(u)}},n.prototype._invmp=function(t){r(0===t.negative),r(!t.isZero());var e,i=this,s=t.clone();i=0!==i.negative?i.umod(t):i.clone();for(var o=new n(1),h=new n(0),f=s.clone();i.cmpn(1)>0&&s.cmpn(1)>0;){for(var u=0,a=1;!(i.words[0]&a)&&u<26;++u,a<<=1);if(u>0)for(i.iushrn(u);u-- >0;)o.isOdd()&&o.iadd(f),o.iushrn(1);for(var l=0,d=1;!(s.words[0]&d)&&l<26;++l,d<<=1);if(l>0)for(s.iushrn(l);l-- >0;)h.isOdd()&&h.iadd(f),h.iushrn(1);i.cmp(s)>=0?(i.isub(s),o.isub(h)):(s.isub(i),h.isub(o))}return(e=0===i.cmpn(1)?o:h).cmpn(0)<0&&e.iadd(t),e},n.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),r=t.clone();e.negative=0,r.negative=0;for(var i=0;e.isEven()&&r.isEven();i++)e.iushrn(1),r.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;r.isEven();)r.iushrn(1);var n=e.cmp(r);if(n<0){var s=e;e=r,r=s}else if(0===n||0===r.cmpn(1))break;e.isub(r)}return r.iushln(i)},n.prototype.invm=function(t){return this.egcd(t).a.umod(t)},n.prototype.isEven=function(){return!(1&this.words[0])},n.prototype.isOdd=function(){return!(1&~this.words[0])},n.prototype.andln=function(t){return this.words[0]&t},n.prototype.bincn=function(t){r("number"==typeof t);var e=t%26,i=(t-e)/26,n=1<<e;if(this.length<=i)return this._expand(i+1),this.words[i]|=n,this;for(var s=n,o=i;0!==s&&o<this.length;o++){var h=0|this.words[o];s=(h+=s)>>>26,h&=67108863,this.words[o]=h}return 0!==s&&(this.words[o]=s,this.length++),this},n.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},n.prototype.cmpn=function(t){var e,i=t<0;if(0!==this.negative&&!i)return-1;if(0===this.negative&&i)return 1;if(this._strip(),this.length>1)e=1;else{i&&(t=-t),r(t<=67108863,"Number is too big");var n=0|this.words[0];e=n===t?0:n<t?-1:1}return 0!==this.negative?0|-e:e},n.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},n.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var e=0,r=this.length-1;r>=0;r--){var i=0|this.words[r],n=0|t.words[r];if(i!==n){i<n?e=-1:i>n&&(e=1);break}}return e},n.prototype.gtn=function(t){return 1===this.cmpn(t)},n.prototype.gt=function(t){return 1===this.cmp(t)},n.prototype.gten=function(t){return this.cmpn(t)>=0},n.prototype.gte=function(t){return this.cmp(t)>=0},n.prototype.ltn=function(t){return-1===this.cmpn(t)},n.prototype.lt=function(t){return-1===this.cmp(t)},n.prototype.lten=function(t){return this.cmpn(t)<=0},n.prototype.lte=function(t){return this.cmp(t)<=0},n.prototype.eqn=function(t){return 0===this.cmpn(t)},n.prototype.eq=function(t){return 0===this.cmp(t)},n.red=function(t){return new S(t)},n.prototype.toRed=function(t){return r(!this.red,"Already a number in reduction context"),r(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},n.prototype.fromRed=function(){return r(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},n.prototype._forceRed=function(t){return this.red=t,this},n.prototype.forceRed=function(t){return r(!this.red,"Already a number in reduction context"),this._forceRed(t)},n.prototype.redAdd=function(t){return r(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},n.prototype.redIAdd=function(t){return r(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},n.prototype.redSub=function(t){return r(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},n.prototype.redISub=function(t){return r(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},n.prototype.redShl=function(t){return r(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},n.prototype.redMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},n.prototype.redIMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},n.prototype.redSqr=function(){return r(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},n.prototype.redISqr=function(){return r(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},n.prototype.redSqrt=function(){return r(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},n.prototype.redInvm=function(){return r(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},n.prototype.redNeg=function(){return r(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},n.prototype.redPow=function(t){return r(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var v={k256:null,p224:null,p192:null,p25519:null};function y(t,e){this.name=t,this.p=new n(e,16),this.n=this.p.bitLength(),this.k=new n(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function w(){y.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function b(){y.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function M(){y.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function E(){y.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function S(t){if("string"==typeof t){var e=n._prime(t);this.m=e.p,this.prime=e}else r(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function _(t){S.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new n(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}y.prototype._tmp=function(){var t=new n(null);return t.words=new Array(Math.ceil(this.n/13)),t},y.prototype.ireduce=function(t){var e,r=t;do{this.split(r,this.tmp),e=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(e>this.n);var i=e<this.n?-1:r.ucmp(this.p);return 0===i?(r.words[0]=0,r.length=1):i>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},y.prototype.split=function(t,e){t.iushrn(this.n,0,e)},y.prototype.imulK=function(t){return t.imul(this.k)},i(w,y),w.prototype.split=function(t,e){for(var r=4194303,i=Math.min(t.length,9),n=0;n<i;n++)e.words[n]=t.words[n];if(e.length=i,t.length<=9)return t.words[0]=0,void(t.length=1);var s=t.words[9];for(e.words[e.length++]=s&r,n=10;n<t.length;n++){var o=0|t.words[n];t.words[n-10]=(o&r)<<4|s>>>22,s=o}s>>>=22,t.words[n-10]=s,0===s&&t.length>10?t.length-=10:t.length-=9},w.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,r=0;r<t.length;r++){var i=0|t.words[r];e+=977*i,t.words[r]=67108863&e,e=64*i+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(b,y),i(M,y),i(E,y),E.prototype.imulK=function(t){for(var e=0,r=0;r<t.length;r++){var i=19*(0|t.words[r])+e,n=67108863&i;i>>>=26,t.words[r]=n,e=i}return 0!==e&&(t.words[t.length++]=e),t},n._prime=function(t){if(v[t])return v[t];var e;if("k256"===t)e=new w;else if("p224"===t)e=new b;else if("p192"===t)e=new M;else{if("p25519"!==t)throw new Error("Unknown prime "+t);e=new E}return v[t]=e,e},S.prototype._verify1=function(t){r(0===t.negative,"red works only with positives"),r(t.red,"red works only with red numbers")},S.prototype._verify2=function(t,e){r(!(t.negative|e.negative),"red works only with positives"),r(t.red&&t.red===e.red,"red works only with red numbers")},S.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):(u(t,t.umod(this.m)._forceRed(this)),t)},S.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},S.prototype.add=function(t,e){this._verify2(t,e);var r=t.add(e);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},S.prototype.iadd=function(t,e){this._verify2(t,e);var r=t.iadd(e);return r.cmp(this.m)>=0&&r.isub(this.m),r},S.prototype.sub=function(t,e){this._verify2(t,e);var r=t.sub(e);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},S.prototype.isub=function(t,e){this._verify2(t,e);var r=t.isub(e);return r.cmpn(0)<0&&r.iadd(this.m),r},S.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},S.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},S.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},S.prototype.isqr=function(t){return this.imul(t,t.clone())},S.prototype.sqr=function(t){return this.mul(t,t)},S.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(r(e%2==1),3===e){var i=this.m.add(new n(1)).iushrn(2);return this.pow(t,i)}for(var s=this.m.subn(1),o=0;!s.isZero()&&0===s.andln(1);)o++,s.iushrn(1);r(!s.isZero());var h=new n(1).toRed(this),f=h.redNeg(),u=this.m.subn(1).iushrn(1),a=this.m.bitLength();for(a=new n(2*a*a).toRed(this);0!==this.pow(a,u).cmp(f);)a.redIAdd(f);for(var l=this.pow(a,s),d=this.pow(t,s.addn(1).iushrn(1)),c=this.pow(t,s),p=o;0!==c.cmp(h);){for(var m=c,g=0;0!==m.cmp(h);g++)m=m.redSqr();r(g<p);var A=this.pow(l,new n(1).iushln(p-g-1));d=d.redMul(A),l=A.redSqr(),c=c.redMul(l),p=g}return d},S.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},S.prototype.pow=function(t,e){if(e.isZero())return new n(1).toRed(this);if(0===e.cmpn(1))return t.clone();var r=new Array(16);r[0]=new n(1).toRed(this),r[1]=t;for(var i=2;i<r.length;i++)r[i]=this.mul(r[i-1],t);var s=r[0],o=0,h=0,f=e.bitLength()%26;for(0===f&&(f=26),i=e.length-1;i>=0;i--){for(var u=e.words[i],a=f-1;a>=0;a--){var l=u>>a&1;s!==r[0]&&(s=this.sqr(s)),0!==l||0!==o?(o<<=1,o|=l,(4==++h||0===i&&0===a)&&(s=this.mul(s,r[o]),h=0,o=0)):h=0}f=26}return s},S.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},S.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},n.mont=function(t){return new _(t)},i(_,S),_.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},_.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},_.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),s=n;return n.cmp(this.m)>=0?s=n.isub(this.m):n.cmpn(0)<0&&(s=n.iadd(this.m)),s._forceRed(this)},_.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new n(0)._forceRed(this);var r=t.mul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),s=r.isub(i).iushrn(this.shift),o=s;return s.cmp(this.m)>=0?o=s.isub(this.m):s.cmpn(0)<0&&(o=s.iadd(this.m)),o._forceRed(this)},_.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t,n)}(_);var I=_.exports;const C="bignumber/5.7.0";var N=I.BN;const x=new p(C),R={},U=9007199254740991;let O=!1;class T{constructor(t,e){t!==R&&x.throwError("cannot call constructor directly; use BigNumber.from",p.errors.UNSUPPORTED_OPERATION,{operation:"new (BigNumber)"}),this._hex=e,this._isBigNumber=!0,Object.freeze(this)}fromTwos(t){return k(F(this).fromTwos(t))}toTwos(t){return k(F(this).toTwos(t))}abs(){return"-"===this._hex[0]?T.from(this._hex.substring(1)):this}add(t){return k(F(this).add(F(t)))}sub(t){return k(F(this).sub(F(t)))}div(t){return T.from(t).isZero()&&D("division-by-zero","div"),k(F(this).div(F(t)))}mul(t){return k(F(this).mul(F(t)))}mod(t){const e=F(t);return e.isNeg()&&D("division-by-zero","mod"),k(F(this).umod(e))}pow(t){const e=F(t);return e.isNeg()&&D("negative-power","pow"),k(F(this).pow(e))}and(t){const e=F(t);return(this.isNegative()||e.isNeg())&&D("unbound-bitwise-result","and"),k(F(this).and(e))}or(t){const e=F(t);return(this.isNegative()||e.isNeg())&&D("unbound-bitwise-result","or"),k(F(this).or(e))}xor(t){const e=F(t);return(this.isNegative()||e.isNeg())&&D("unbound-bitwise-result","xor"),k(F(this).xor(e))}mask(t){return(this.isNegative()||t<0)&&D("negative-width","mask"),k(F(this).maskn(t))}shl(t){return(this.isNegative()||t<0)&&D("negative-width","shl"),k(F(this).shln(t))}shr(t){return(this.isNegative()||t<0)&&D("negative-width","shr"),k(F(this).shrn(t))}eq(t){return F(this).eq(F(t))}lt(t){return F(this).lt(F(t))}lte(t){return F(this).lte(F(t))}gt(t){return F(this).gt(F(t))}gte(t){return F(this).gte(F(t))}isNegative(){return"-"===this._hex[0]}isZero(){return F(this).isZero()}toNumber(){try{return F(this).toNumber()}catch{D("overflow","toNumber",this.toString())}return null}toBigInt(){try{return BigInt(this.toString())}catch{}return x.throwError("this platform does not support BigInt",p.errors.UNSUPPORTED_OPERATION,{value:this.toString()})}toString(){return arguments.length>0&&(10===arguments[0]?O||(O=!0,x.warn("BigNumber.toString does not accept any parameters; base-10 is assumed")):16===arguments[0]?x.throwError("BigNumber.toString does not accept any parameters; use bigNumber.toHexString()",p.errors.UNEXPECTED_ARGUMENT,{}):x.throwError("BigNumber.toString does not accept parameters",p.errors.UNEXPECTED_ARGUMENT,{})),F(this).toString(10)}toHexString(){return this._hex}toJSON(t){return{type:"BigNumber",hex:this.toHexString()}}static from(t){if(t instanceof T)return t;if("string"==typeof t)return t.match(/^-?0x[0-9a-f]+$/i)?new T(R,P(t)):t.match(/^-?[0-9]+$/)?new T(R,P(new N(t))):x.throwArgumentError("invalid BigNumber string","value",t);if("number"==typeof t)return t%1&&D("underflow","BigNumber.from",t),(t>=U||t<=-U)&&D("overflow","BigNumber.from",t),T.from(String(t));const e=t;if("bigint"==typeof e)return T.from(e.toString());if(y(e))return T.from(E(e));if(e)if(e.toHexString){const t=e.toHexString();if("string"==typeof t)return T.from(t)}else{let t=e._hex;if(null==t&&"BigNumber"===e.type&&(t=e.hex),"string"==typeof t&&(b(t)||"-"===t[0]&&b(t.substring(1))))return T.from(t)}return x.throwArgumentError("invalid BigNumber value","value",t)}static isBigNumber(t){return!(!t||!t._isBigNumber)}}function P(t){if("string"!=typeof t)return P(t.toString(16));if("-"===t[0])return"-"===(t=t.substring(1))[0]&&x.throwArgumentError("invalid hex","value",t),"0x00"===(t=P(t))?t:"-"+t;if("0x"!==t.substring(0,2)&&(t="0x"+t),"0x"===t)return"0x00";for(t.length%2&&(t="0x0"+t.substring(2));t.length>4&&"0x00"===t.substring(0,4);)t="0x"+t.substring(4);return t}function k(t){return T.from(P(t))}function F(t){const e=T.from(t).toHexString();return"-"===e[0]?new N("-"+e.substring(3),16):new N(e.substring(2),16)}function D(t,e,r){const i={fault:t,operation:e};return null!=r&&(i.value=r),x.throwError(t,p.errors.NUMERIC_FAULT,i)}const L=new p(C),z={},q=T.from(0),H=T.from(-1);function Q(t,e,r,i){const n={fault:e,operation:r};return void 0!==i&&(n.value=i),L.throwError(t,p.errors.NUMERIC_FAULT,n)}let j="0";for(;j.length<256;)j+=j;function Y(t){if("number"!=typeof t)try{t=T.from(t).toNumber()}catch{}return"number"==typeof t&&t>=0&&t<=256&&!(t%1)?"1"+j.substring(0,t):L.throwArgumentError("invalid decimal size","decimals",t)}function J(t,e){null==e&&(e=0);const r=Y(e),i=(t=T.from(t)).lt(q);i&&(t=t.mul(H));let n=t.mod(r).toString();for(;n.length<r.length-1;)n="0"+n;n=n.match(/^([0-9]*[1-9]|0)(0*)/)[1];const s=t.div(r).toString();return t=1===r.length?s:s+"."+n,i&&(t="-"+t),t}function G(t,e){null==e&&(e=0);const r=Y(e);("string"!=typeof t||!t.match(/^-?[0-9.]+$/))&&L.throwArgumentError("invalid decimal value","value",t);const i="-"===t.substring(0,1);i&&(t=t.substring(1)),"."===t&&L.throwArgumentError("missing value","value",t);const n=t.split(".");n.length>2&&L.throwArgumentError("too many decimal points","value",t);let s=n[0],o=n[1];for(s||(s="0"),o||(o="0");"0"===o[o.length-1];)o=o.substring(0,o.length-1);for(o.length>r.length-1&&Q("fractional component exceeds decimals","underflow","parseFixed"),""===o&&(o="0");o.length<r.length-1;)o+="0";const h=T.from(s),f=T.from(o);let u=h.mul(r).add(f);return i&&(u=u.mul(H)),u}class K{constructor(t,e,r,i){t!==z&&L.throwError("cannot use FixedFormat constructor; use FixedFormat.from",p.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.signed=e,this.width=r,this.decimals=i,this.name=(e?"":"u")+"fixed"+String(r)+"x"+String(i),this._multiplier=Y(i),Object.freeze(this)}static from(t){if(t instanceof K)return t;"number"==typeof t&&(t=`fixed128x${t}`);let e=!0,r=128,i=18;if("string"==typeof t){if("fixed"!==t)if("ufixed"===t)e=!1;else{const n=t.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);n||L.throwArgumentError("invalid fixed format","format",t),e="u"!==n[1],r=parseInt(n[2]),i=parseInt(n[3])}}else if(t){const n=(e,r,i)=>null==t[e]?i:(typeof t[e]!==r&&L.throwArgumentError("invalid fixed format ("+e+" not "+r+")","format."+e,t[e]),t[e]);e=n("signed","boolean",e),r=n("width","number",r),i=n("decimals","number",i)}return r%8&&L.throwArgumentError("invalid fixed format width (not byte aligned)","format.width",r),i>80&&L.throwArgumentError("invalid fixed format (decimals too large)","format.decimals",i),new K(z,e,r,i)}}class W{constructor(t,e,r,i){t!==z&&L.throwError("cannot use FixedNumber constructor; use FixedNumber.from",p.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.format=i,this._hex=e,this._value=r,this._isFixedNumber=!0,Object.freeze(this)}_checkFormat(t){this.format.name!==t.format.name&&L.throwArgumentError("incompatible format; use fixedNumber.toFormat","other",t)}addUnsafe(t){this._checkFormat(t);const e=G(this._value,this.format.decimals),r=G(t._value,t.format.decimals);return W.fromValue(e.add(r),this.format.decimals,this.format)}subUnsafe(t){this._checkFormat(t);const e=G(this._value,this.format.decimals),r=G(t._value,t.format.decimals);return W.fromValue(e.sub(r),this.format.decimals,this.format)}mulUnsafe(t){this._checkFormat(t);const e=G(this._value,this.format.decimals),r=G(t._value,t.format.decimals);return W.fromValue(e.mul(r).div(this.format._multiplier),this.format.decimals,this.format)}divUnsafe(t){this._checkFormat(t);const e=G(this._value,this.format.decimals),r=G(t._value,t.format.decimals);return W.fromValue(e.mul(this.format._multiplier).div(r),this.format.decimals,this.format)}floor(){const t=this.toString().split(".");1===t.length&&t.push("0");let e=W.from(t[0],this.format);const r=!t[1].match(/^(0*)$/);return this.isNegative()&&r&&(e=e.subUnsafe(V.toFormat(e.format))),e}ceiling(){const t=this.toString().split(".");1===t.length&&t.push("0");let e=W.from(t[0],this.format);const r=!t[1].match(/^(0*)$/);return!this.isNegative()&&r&&(e=e.addUnsafe(V.toFormat(e.format))),e}round(t){null==t&&(t=0);const e=this.toString().split(".");if(1===e.length&&e.push("0"),(t<0||t>80||t%1)&&L.throwArgumentError("invalid decimal count","decimals",t),e[1].length<=t)return this;const r=W.from("1"+j.substring(0,t),this.format),i=X.toFormat(this.format);return this.mulUnsafe(r).addUnsafe(i).floor().divUnsafe(r)}isZero(){return"0.0"===this._value||"0"===this._value}isNegative(){return"-"===this._value[0]}toString(){return this._value}toHexString(t){return null==t?this._hex:(t%8&&L.throwArgumentError("invalid byte width","width",t),S(T.from(this._hex).fromTwos(this.format.width).toTwos(t).toHexString(),t/8))}toUnsafeFloat(){return parseFloat(this.toString())}toFormat(t){return W.fromString(this._value,t)}static fromValue(t,e,r){return null==r&&null!=e&&!function(t){return null!=t&&(T.isBigNumber(t)||"number"==typeof t&&t%1==0||"string"==typeof t&&!!t.match(/^-?[0-9]+$/)||b(t)||"bigint"==typeof t||y(t))}(e)&&(r=e,e=null),null==e&&(e=0),null==r&&(r="fixed"),W.fromString(J(t,e),K.from(r))}static fromString(t,e){null==e&&(e="fixed");const r=K.from(e),i=G(t,r.decimals);!r.signed&&i.lt(q)&&Q("unsigned value cannot be negative","overflow","value",t);let n=null;r.signed?n=i.toTwos(r.width).toHexString():(n=i.toHexString(),n=S(n,r.width/8));const s=J(i,r.decimals);return new W(z,n,s,r)}static fromBytes(t,e){null==e&&(e="fixed");const r=K.from(e);if(w(t).length>r.width/8)throw new Error("overflow");let i=T.from(t);r.signed&&(i=i.fromTwos(r.width));const n=i.toTwos((r.signed?0:1)+r.width).toHexString(),s=J(i,r.decimals);return new W(z,n,s,r)}static from(t,e){if("string"==typeof t)return W.fromString(t,e);if(y(t))return W.fromBytes(t,e);try{return W.fromValue(t,0,e)}catch(t){if(t.code!==p.errors.INVALID_ARGUMENT)throw t}return L.throwArgumentError("invalid FixedNumber value","value",t)}static isFixedNumber(t){return!(!t||!t._isFixedNumber)}}const V=W.from(1),X=W.from("0.5"),Z=new p("strings/5.7.0");var $,tt;function et(t,e,r,i,n){if(t===tt.BAD_PREFIX||t===tt.UNEXPECTED_CONTINUE){let t=0;for(let i=e+1;i<r.length&&r[i]>>6==2;i++)t++;return t}return t===tt.OVERRUN?r.length-e-1:0}function rt(t,e){e||(e=function(t){return[parseInt(t,16)]});let r=0,i={};return t.split(",").forEach((t=>{let n=t.split(":");r+=parseInt(n[0],16),i[r]=e(n[1])})),i}function it(t){let e=0;return t.split(",").map((t=>{let r=t.split("-");1===r.length?r[1]="0":""===r[1]&&(r[1]="1");let i=e+parseInt(r[0],16);return e=parseInt(r[1],16),{l:i,h:e}}))}!function(t){t.current="",t.NFC="NFC",t.NFD="NFD",t.NFKC="NFKC",t.NFKD="NFKD"}($||($={})),function(t){t.UNEXPECTED_CONTINUE="unexpected continuation byte",t.BAD_PREFIX="bad codepoint prefix",t.OVERRUN="string overrun",t.MISSING_CONTINUE="missing continuation byte",t.OUT_OF_RANGE="out of UTF-8 range",t.UTF16_SURROGATE="UTF-16 surrogate",t.OVERLONG="overlong representation"}(tt||(tt={})),Object.freeze({error:function(t,e,r,i,n){return Z.throwArgumentError(`invalid codepoint at offset ${e}; ${t}`,"bytes",r)},ignore:et,replace:function(t,e,r,i,n){return t===tt.OVERLONG?(i.push(n),0):(i.push(65533),et(t,e,r))}}),it("221,13-1b,5f-,40-10,51-f,11-3,3-3,2-2,2-4,8,2,15,2d,28-8,88,48,27-,3-5,11-20,27-,8,28,3-5,12,18,b-a,1c-4,6-16,2-d,2-2,2,1b-4,17-9,8f-,10,f,1f-2,1c-34,33-14e,4,36-,13-,6-2,1a-f,4,9-,3-,17,8,2-2,5-,2,8-,3-,4-8,2-3,3,6-,16-6,2-,7-3,3-,17,8,3,3,3-,2,6-3,3-,4-a,5,2-6,10-b,4,8,2,4,17,8,3,6-,b,4,4-,2-e,2-4,b-10,4,9-,3-,17,8,3-,5-,9-2,3-,4-7,3-3,3,4-3,c-10,3,7-2,4,5-2,3,2,3-2,3-2,4-2,9,4-3,6-2,4,5-8,2-e,d-d,4,9,4,18,b,6-3,8,4,5-6,3-8,3-3,b-11,3,9,4,18,b,6-3,8,4,5-6,3-6,2,3-3,b-11,3,9,4,18,11-3,7-,4,5-8,2-7,3-3,b-11,3,13-2,19,a,2-,8-2,2-3,7,2,9-11,4-b,3b-3,1e-24,3,2-,3,2-,2-5,5,8,4,2,2-,3,e,4-,6,2,7-,b-,3-21,49,23-5,1c-3,9,25,10-,2-2f,23,6,3,8-2,5-5,1b-45,27-9,2a-,2-3,5b-4,45-4,53-5,8,40,2,5-,8,2,5-,28,2,5-,20,2,5-,8,2,5-,8,8,18,20,2,5-,8,28,14-5,1d-22,56-b,277-8,1e-2,52-e,e,8-a,18-8,15-b,e,4,3-b,5e-2,b-15,10,b-5,59-7,2b-555,9d-3,5b-5,17-,7-,27-,7-,9,2,2,2,20-,36,10,f-,7,14-,4,a,54-3,2-6,6-5,9-,1c-10,13-1d,1c-14,3c-,10-6,32-b,240-30,28-18,c-14,a0,115-,3,66-,b-76,5,5-,1d,24,2,5-2,2,8-,35-2,19,f-10,1d-3,311-37f,1b,5a-b,d7-19,d-3,41,57-,68-4,29-3,5f,29-37,2e-2,25-c,2c-2,4e-3,30,78-3,64-,20,19b7-49,51a7-59,48e-2,38-738,2ba5-5b,222f-,3c-94,8-b,6-4,1b,6,2,3,3,6d-20,16e-f,41-,37-7,2e-2,11-f,5-b,18-,b,14,5-3,6,88-,2,bf-2,7-,7-,7-,4-2,8,8-9,8-2ff,20,5-b,1c-b4,27-,27-cbb1,f7-9,28-2,b5-221,56,48,3-,2-,3-,5,d,2,5,3,42,5-,9,8,1d,5,6,2-2,8,153-3,123-3,33-27fd,a6da-5128,21f-5df,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3-fffd,3,2-1d,61-ff7d"),"ad,34f,1806,180b,180c,180d,200b,200c,200d,2060,feff".split(",").map((t=>parseInt(t,16))),rt("b5:3bc,c3:ff,7:73,2:253,5:254,3:256,1:257,5:259,1:25b,3:260,1:263,2:269,1:268,5:26f,1:272,2:275,7:280,3:283,5:288,3:28a,1:28b,5:292,3f:195,1:1bf,29:19e,125:3b9,8b:3b2,1:3b8,1:3c5,3:3c6,1:3c0,1a:3ba,1:3c1,1:3c3,2:3b8,1:3b5,1bc9:3b9,1c:1f76,1:1f77,f:1f7a,1:1f7b,d:1f78,1:1f79,1:1f7c,1:1f7d,107:63,5:25b,4:68,1:68,1:68,3:69,1:69,1:6c,3:6e,4:70,1:71,1:72,1:72,1:72,7:7a,2:3c9,2:7a,2:6b,1:e5,1:62,1:63,3:65,1:66,2:6d,b:3b3,1:3c0,6:64,1b574:3b8,1a:3c3,20:3b8,1a:3c3,20:3b8,1a:3c3,20:3b8,1a:3c3,20:3b8,1a:3c3"),rt("179:1,2:1,2:1,5:1,2:1,a:4f,a:1,8:1,2:1,2:1,3:1,5:1,3:1,4:1,2:1,3:1,4:1,8:2,1:1,2:2,1:1,2:2,27:2,195:26,2:25,1:25,1:25,2:40,2:3f,1:3f,33:1,11:-6,1:-9,1ac7:-3a,6d:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,b:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,c:-8,2:-8,2:-8,2:-8,9:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,1:-8,49:-8,1:-8,1:-4a,1:-4a,d:-56,1:-56,1:-56,1:-56,d:-8,1:-8,f:-8,1:-8,3:-7"),rt("df:00730073,51:00690307,19:02BC006E,a7:006A030C,18a:002003B9,16:03B903080301,20:03C503080301,1d7:05650582,190f:00680331,1:00740308,1:0077030A,1:0079030A,1:006102BE,b6:03C50313,2:03C503130300,2:03C503130301,2:03C503130342,2a:1F0003B9,1:1F0103B9,1:1F0203B9,1:1F0303B9,1:1F0403B9,1:1F0503B9,1:1F0603B9,1:1F0703B9,1:1F0003B9,1:1F0103B9,1:1F0203B9,1:1F0303B9,1:1F0403B9,1:1F0503B9,1:1F0603B9,1:1F0703B9,1:1F2003B9,1:1F2103B9,1:1F2203B9,1:1F2303B9,1:1F2403B9,1:1F2503B9,1:1F2603B9,1:1F2703B9,1:1F2003B9,1:1F2103B9,1:1F2203B9,1:1F2303B9,1:1F2403B9,1:1F2503B9,1:1F2603B9,1:1F2703B9,1:1F6003B9,1:1F6103B9,1:1F6203B9,1:1F6303B9,1:1F6403B9,1:1F6503B9,1:1F6603B9,1:1F6703B9,1:1F6003B9,1:1F6103B9,1:1F6203B9,1:1F6303B9,1:1F6403B9,1:1F6503B9,1:1F6603B9,1:1F6703B9,3:1F7003B9,1:03B103B9,1:03AC03B9,2:03B10342,1:03B1034203B9,5:03B103B9,6:1F7403B9,1:03B703B9,1:03AE03B9,2:03B70342,1:03B7034203B9,5:03B703B9,6:03B903080300,1:03B903080301,3:03B90342,1:03B903080342,b:03C503080300,1:03C503080301,1:03C10313,2:03C50342,1:03C503080342,b:1F7C03B9,1:03C903B9,1:03CE03B9,2:03C90342,1:03C9034203B9,5:03C903B9,ac:00720073,5b:00B00063,6:00B00066,d:006E006F,a:0073006D,1:00740065006C,1:0074006D,124f:006800700061,2:00610075,2:006F0076,b:00700061,1:006E0061,1:03BC0061,1:006D0061,1:006B0061,1:006B0062,1:006D0062,1:00670062,3:00700066,1:006E0066,1:03BC0066,4:0068007A,1:006B0068007A,1:006D0068007A,1:00670068007A,1:00740068007A,15:00700061,1:006B00700061,1:006D00700061,1:006700700061,8:00700076,1:006E0076,1:03BC0076,1:006D0076,1:006B0076,1:006D0076,1:00700077,1:006E0077,1:03BC0077,1:006D0077,1:006B0077,1:006D0077,1:006B03C9,1:006D03C9,2:00620071,3:00632215006B0067,1:0063006F002E,1:00640062,1:00670079,2:00680070,2:006B006B,1:006B006D,9:00700068,2:00700070006D,1:00700072,2:00730076,1:00770062,c723:00660066,1:00660069,1:0066006C,1:006600660069,1:00660066006C,1:00730074,1:00730074,d:05740576,1:05740565,1:0574056B,1:057E0576,1:0574056D",(function(t){if(t.length%4!=0)throw new Error("bad data");let e=[];for(let r=0;r<t.length;r+=4)e.push(parseInt(t.substring(r,r+4),16));return e})),it("80-20,2a0-,39c,32,f71,18e,7f2-f,19-7,30-4,7-5,f81-b,5,a800-20ff,4d1-1f,110,fa-6,d174-7,2e84-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,ffff-,2,1f-5f,ff7f-20001");const nt="hash/5.7.0";function st(t,e){null==e&&(e=1);const r=[],i=r.forEach,n=function(t,e){i.call(t,(function(t){e>0&&Array.isArray(t)?n(t,e-1):r.push(t)}))};return n(t,e),r}function ot(t){return 1&t?~t>>1:t>>1}function ht(t,e){let r=Array(t);for(let i=0,n=-1;i<t;i++)r[i]=n+=1+e();return r}function ft(t,e){let r=Array(t);for(let i=0,n=0;i<t;i++)r[i]=n+=ot(e());return r}function ut(t,e){let r=ht(t(),t),i=t(),n=ht(i,t),s=function(t,e){let r=Array(t);for(let i=0;i<t;i++)r[i]=1+e();return r}(i,t);for(let t=0;t<i;t++)for(let e=0;e<s[t];e++)r.push(n[t]+e);return e?r.map((t=>e[t])):r}function at(t,e,r){let i=Array(t).fill(void 0).map((()=>[]));for(let n=0;n<e;n++)ft(t,r).forEach(((t,e)=>i[e].push(t)));return i}function lt(t,e){let r=1+e(),i=e(),n=function(t){let e=[];for(;;){let r=t();if(0==r)break;e.push(r)}return e}(e);return st(at(n.length,1+t,e).map(((t,e)=>{const s=t[0],o=t.slice(1);return Array(n[e]).fill(void 0).map(((t,e)=>{let n=e*i;return[s+e*r,o.map((t=>t+n))]}))})))}function dt(t,e){return at(1+e(),1+t,e).map((t=>[t[0],t.slice(1)]))}const ct=function(t){return function(t){let e=0;return()=>t[e++]}(function(t){let e=0;function r(){return t[e++]<<8|t[e++]}let i=r(),n=1,s=[0,1];for(let t=1;t<i;t++)s.push(n+=r());let o=r(),h=e;e+=o;let f=0,u=0;function a(){return 0==f&&(u=u<<8|t[e++],f=8),u>>--f&1}const l=Math.pow(2,31),d=l>>>1,c=d>>1,p=l-1;let m=0;for(let t=0;t<31;t++)m=m<<1|a();let g=[],A=0,v=l;for(;;){let t=Math.floor(((m-A+1)*n-1)/v),e=0,r=i;for(;r-e>1;){let i=e+r>>>1;t<s[i]?r=i:e=i}if(0==e)break;g.push(e);let o=A+Math.floor(v*s[e]/n),h=A+Math.floor(v*s[e+1]/n)-1;for(;!((o^h)&d);)m=m<<1&p|a(),o=o<<1&p,h=h<<1&p|1;for(;o&~h&c;)m=m&d|m<<1&p>>>1|a(),o=o<<1^d,h=(h^d)<<1|d|1;A=o,v=1+h-o}let y=i-4;return g.map((e=>{switch(e-y){case 3:return y+65792+(t[h++]<<16|t[h++]<<8|t[h++]);case 2:return y+256+(t[h++]<<8|t[h++]);case 1:return y+t[h++];default:return e-1}}))}(t))}(function(t){t=atob(t);const e=[];for(let r=0;r<t.length;r++)e.push(t.charCodeAt(r));return w(e)}("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"));new Set(ut(ct)),new Set(ut(ct)),function(t){let e=[];for(;;){let r=t();if(0==r)break;e.push(lt(r,t))}for(;;){let r=t()-1;if(r<0)break;e.push(dt(r,t))}!function(t){const e={};for(let r=0;r<t.length;r++){const i=t[r];e[i[0]]=i[1]}}(st(e))}(ct),function(t){let e=ut(t).sort(((t,e)=>t-e));!function r(){let i=[];for(;;){let n=ut(t,e);if(0==n.length)break;i.push({set:new Set(n),node:r()})}i.sort(((t,e)=>e.set.size-t.set.size));let n=t(),s=n%3;n=n/3|0;let o=!!(1&n);return n>>=1,{branches:i,valid:s,fe0f:o,save:1==n,check:2==n}}()}(ct),new p(nt),new Uint8Array(32).fill(0),new p("rlp/5.7.0"),new p("address/5.7.0");const pt={};for(let t=0;t<10;t++)pt[String(t)]=String(t);for(let t=0;t<26;t++)pt[String.fromCharCode(65+t)]=String(10+t);Math.floor(function(t){return Math.log10?Math.log10(t):Math.log(t)/Math.LN10}(9007199254740991)),new p("properties/5.7.0"),new p(nt),new Uint8Array(32).fill(0),T.from(-1);const mt=T.from(0),gt=T.from(1);T.from("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),S(gt.toHexString(),32),S(mt.toHexString(),32);var At={},vt={},yt=wt;function wt(t,e){if(!t)throw new Error(e||"Assertion failed")}wt.equal=function(t,e,r){if(t!=e)throw new Error(r||"Assertion failed: "+t+" != "+e)};var bt={exports:{}};"function"==typeof Object.create?bt.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:bt.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}};var Mt=yt,Et=bt.exports;function St(t,e){return!(55296!=(64512&t.charCodeAt(e))||e<0||e+1>=t.length)&&56320==(64512&t.charCodeAt(e+1))}function _t(t){return(t>>>24|t>>>8&65280|t<<8&16711680|(255&t)<<24)>>>0}function Bt(t){return 1===t.length?"0"+t:t}function It(t){return 7===t.length?"0"+t:6===t.length?"00"+t:5===t.length?"000"+t:4===t.length?"0000"+t:3===t.length?"00000"+t:2===t.length?"000000"+t:1===t.length?"0000000"+t:t}vt.inherits=Et,vt.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var r=[];if("string"==typeof t)if(e){if("hex"===e)for((t=t.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(t="0"+t),n=0;n<t.length;n+=2)r.push(parseInt(t[n]+t[n+1],16))}else for(var i=0,n=0;n<t.length;n++){var s=t.charCodeAt(n);s<128?r[i++]=s:s<2048?(r[i++]=s>>6|192,r[i++]=63&s|128):St(t,n)?(s=65536+((1023&s)<<10)+(1023&t.charCodeAt(++n)),r[i++]=s>>18|240,r[i++]=s>>12&63|128,r[i++]=s>>6&63|128,r[i++]=63&s|128):(r[i++]=s>>12|224,r[i++]=s>>6&63|128,r[i++]=63&s|128)}else for(n=0;n<t.length;n++)r[n]=0|t[n];return r},vt.toHex=function(t){for(var e="",r=0;r<t.length;r++)e+=Bt(t[r].toString(16));return e},vt.htonl=_t,vt.toHex32=function(t,e){for(var r="",i=0;i<t.length;i++){var n=t[i];"little"===e&&(n=_t(n)),r+=It(n.toString(16))}return r},vt.zero2=Bt,vt.zero8=It,vt.join32=function(t,e,r,i){var n=r-e;Mt(n%4==0);for(var s=new Array(n/4),o=0,h=e;o<s.length;o++,h+=4){var f;f="big"===i?t[h]<<24|t[h+1]<<16|t[h+2]<<8|t[h+3]:t[h+3]<<24|t[h+2]<<16|t[h+1]<<8|t[h],s[o]=f>>>0}return s},vt.split32=function(t,e){for(var r=new Array(4*t.length),i=0,n=0;i<t.length;i++,n+=4){var s=t[i];"big"===e?(r[n]=s>>>24,r[n+1]=s>>>16&255,r[n+2]=s>>>8&255,r[n+3]=255&s):(r[n+3]=s>>>24,r[n+2]=s>>>16&255,r[n+1]=s>>>8&255,r[n]=255&s)}return r},vt.rotr32=function(t,e){return t>>>e|t<<32-e},vt.rotl32=function(t,e){return t<<e|t>>>32-e},vt.sum32=function(t,e){return t+e>>>0},vt.sum32_3=function(t,e,r){return t+e+r>>>0},vt.sum32_4=function(t,e,r,i){return t+e+r+i>>>0},vt.sum32_5=function(t,e,r,i,n){return t+e+r+i+n>>>0},vt.sum64=function(t,e,r,i){var n=t[e],s=i+t[e+1]>>>0,o=(s<i?1:0)+r+n;t[e]=o>>>0,t[e+1]=s},vt.sum64_hi=function(t,e,r,i){return(e+i>>>0<e?1:0)+t+r>>>0},vt.sum64_lo=function(t,e,r,i){return e+i>>>0},vt.sum64_4_hi=function(t,e,r,i,n,s,o,h){var f=0,u=e;return f+=(u=u+i>>>0)<e?1:0,f+=(u=u+s>>>0)<s?1:0,t+r+n+o+(f+=(u=u+h>>>0)<h?1:0)>>>0},vt.sum64_4_lo=function(t,e,r,i,n,s,o,h){return e+i+s+h>>>0},vt.sum64_5_hi=function(t,e,r,i,n,s,o,h,f,u){var a=0,l=e;return a+=(l=l+i>>>0)<e?1:0,a+=(l=l+s>>>0)<s?1:0,a+=(l=l+h>>>0)<h?1:0,t+r+n+o+f+(a+=(l=l+u>>>0)<u?1:0)>>>0},vt.sum64_5_lo=function(t,e,r,i,n,s,o,h,f,u){return e+i+s+h+u>>>0},vt.rotr64_hi=function(t,e,r){return(e<<32-r|t>>>r)>>>0},vt.rotr64_lo=function(t,e,r){return(t<<32-r|e>>>r)>>>0},vt.shr64_hi=function(t,e,r){return t>>>r},vt.shr64_lo=function(t,e,r){return(t<<32-r|e>>>r)>>>0};var Ct={},Nt=vt,xt=yt;function Rt(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}Ct.BlockHash=Rt,Rt.prototype.update=function(t,e){if(t=Nt.toArray(t,e),this.pending?this.pending=this.pending.concat(t):this.pending=t,this.pendingTotal+=t.length,this.pending.length>=this._delta8){var r=(t=this.pending).length%this._delta8;this.pending=t.slice(t.length-r,t.length),0===this.pending.length&&(this.pending=null),t=Nt.join32(t,0,t.length-r,this.endian);for(var i=0;i<t.length;i+=this._delta32)this._update(t,i,i+this._delta32)}return this},Rt.prototype.digest=function(t){return this.update(this._pad()),xt(null===this.pending),this._digest(t)},Rt.prototype._pad=function(){var t=this.pendingTotal,e=this._delta8,r=e-(t+this.padLength)%e,i=new Array(r+this.padLength);i[0]=128;for(var n=1;n<r;n++)i[n]=0;if(t<<=3,"big"===this.endian){for(var s=8;s<this.padLength;s++)i[n++]=0;i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=t>>>24&255,i[n++]=t>>>16&255,i[n++]=t>>>8&255,i[n++]=255&t}else for(i[n++]=255&t,i[n++]=t>>>8&255,i[n++]=t>>>16&255,i[n++]=t>>>24&255,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,s=8;s<this.padLength;s++)i[n++]=0;return i};var Ut={},Ot={},Tt=vt.rotr32;function Pt(t,e,r){return t&e^~t&r}function kt(t,e,r){return t&e^t&r^e&r}function Ft(t,e,r){return t^e^r}Ot.ft_1=function(t,e,r,i){return 0===t?Pt(e,r,i):1===t||3===t?Ft(e,r,i):2===t?kt(e,r,i):void 0},Ot.ch32=Pt,Ot.maj32=kt,Ot.p32=Ft,Ot.s0_256=function(t){return Tt(t,2)^Tt(t,13)^Tt(t,22)},Ot.s1_256=function(t){return Tt(t,6)^Tt(t,11)^Tt(t,25)},Ot.g0_256=function(t){return Tt(t,7)^Tt(t,18)^t>>>3},Ot.g1_256=function(t){return Tt(t,17)^Tt(t,19)^t>>>10};var Dt=vt,Lt=Ct,zt=Ot,qt=Dt.rotl32,Ht=Dt.sum32,Qt=Dt.sum32_5,jt=zt.ft_1,Yt=Lt.BlockHash,Jt=[1518500249,1859775393,2400959708,3395469782];function Gt(){if(!(this instanceof Gt))return new Gt;Yt.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.W=new Array(80)}Dt.inherits(Gt,Yt);var Kt=Gt;Gt.blockSize=512,Gt.outSize=160,Gt.hmacStrength=80,Gt.padLength=64,Gt.prototype._update=function(t,e){for(var r=this.W,i=0;i<16;i++)r[i]=t[e+i];for(;i<r.length;i++)r[i]=qt(r[i-3]^r[i-8]^r[i-14]^r[i-16],1);var n=this.h[0],s=this.h[1],o=this.h[2],h=this.h[3],f=this.h[4];for(i=0;i<r.length;i++){var u=~~(i/20),a=Qt(qt(n,5),jt(u,s,o,h),f,r[i],Jt[u]);f=h,h=o,o=qt(s,30),s=n,n=a}this.h[0]=Ht(this.h[0],n),this.h[1]=Ht(this.h[1],s),this.h[2]=Ht(this.h[2],o),this.h[3]=Ht(this.h[3],h),this.h[4]=Ht(this.h[4],f)},Gt.prototype._digest=function(t){return"hex"===t?Dt.toHex32(this.h,"big"):Dt.split32(this.h,"big")};var Wt=vt,Vt=Ct,Xt=Ot,Zt=yt,$t=Wt.sum32,te=Wt.sum32_4,ee=Wt.sum32_5,re=Xt.ch32,ie=Xt.maj32,ne=Xt.s0_256,se=Xt.s1_256,oe=Xt.g0_256,he=Xt.g1_256,fe=Vt.BlockHash,ue=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function ae(){if(!(this instanceof ae))return new ae;fe.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=ue,this.W=new Array(64)}Wt.inherits(ae,fe);var le=ae;ae.blockSize=512,ae.outSize=256,ae.hmacStrength=192,ae.padLength=64,ae.prototype._update=function(t,e){for(var r=this.W,i=0;i<16;i++)r[i]=t[e+i];for(;i<r.length;i++)r[i]=te(he(r[i-2]),r[i-7],oe(r[i-15]),r[i-16]);var n=this.h[0],s=this.h[1],o=this.h[2],h=this.h[3],f=this.h[4],u=this.h[5],a=this.h[6],l=this.h[7];for(Zt(this.k.length===r.length),i=0;i<r.length;i++){var d=ee(l,se(f),re(f,u,a),this.k[i],r[i]),c=$t(ne(n),ie(n,s,o));l=a,a=u,u=f,f=$t(h,d),h=o,o=s,s=n,n=$t(d,c)}this.h[0]=$t(this.h[0],n),this.h[1]=$t(this.h[1],s),this.h[2]=$t(this.h[2],o),this.h[3]=$t(this.h[3],h),this.h[4]=$t(this.h[4],f),this.h[5]=$t(this.h[5],u),this.h[6]=$t(this.h[6],a),this.h[7]=$t(this.h[7],l)},ae.prototype._digest=function(t){return"hex"===t?Wt.toHex32(this.h,"big"):Wt.split32(this.h,"big")};var de=vt,ce=le;function pe(){if(!(this instanceof pe))return new pe;ce.call(this),this.h=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]}de.inherits(pe,ce);var me=pe;pe.blockSize=512,pe.outSize=224,pe.hmacStrength=192,pe.padLength=64,pe.prototype._digest=function(t){return"hex"===t?de.toHex32(this.h.slice(0,7),"big"):de.split32(this.h.slice(0,7),"big")};var ge=vt,Ae=Ct,ve=yt,ye=ge.rotr64_hi,we=ge.rotr64_lo,be=ge.shr64_hi,Me=ge.shr64_lo,Ee=ge.sum64,Se=ge.sum64_hi,_e=ge.sum64_lo,Be=ge.sum64_4_hi,Ie=ge.sum64_4_lo,Ce=ge.sum64_5_hi,Ne=ge.sum64_5_lo,xe=Ae.BlockHash,Re=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function Ue(){if(!(this instanceof Ue))return new Ue;xe.call(this),this.h=[1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209],this.k=Re,this.W=new Array(160)}ge.inherits(Ue,xe);var Oe=Ue;function Te(t,e,r,i,n){var s=t&r^~t&n;return s<0&&(s+=4294967296),s}function Pe(t,e,r,i,n,s){var o=e&i^~e&s;return o<0&&(o+=4294967296),o}function ke(t,e,r,i,n){var s=t&r^t&n^r&n;return s<0&&(s+=4294967296),s}function Fe(t,e,r,i,n,s){var o=e&i^e&s^i&s;return o<0&&(o+=4294967296),o}function De(t,e){var r=ye(t,e,28)^ye(e,t,2)^ye(e,t,7);return r<0&&(r+=4294967296),r}function Le(t,e){var r=we(t,e,28)^we(e,t,2)^we(e,t,7);return r<0&&(r+=4294967296),r}function ze(t,e){var r=ye(t,e,14)^ye(t,e,18)^ye(e,t,9);return r<0&&(r+=4294967296),r}function qe(t,e){var r=we(t,e,14)^we(t,e,18)^we(e,t,9);return r<0&&(r+=4294967296),r}function He(t,e){var r=ye(t,e,1)^ye(t,e,8)^be(t,e,7);return r<0&&(r+=4294967296),r}function Qe(t,e){var r=we(t,e,1)^we(t,e,8)^Me(t,e,7);return r<0&&(r+=4294967296),r}function je(t,e){var r=ye(t,e,19)^ye(e,t,29)^be(t,e,6);return r<0&&(r+=4294967296),r}function Ye(t,e){var r=we(t,e,19)^we(e,t,29)^Me(t,e,6);return r<0&&(r+=4294967296),r}Ue.blockSize=1024,Ue.outSize=512,Ue.hmacStrength=192,Ue.padLength=128,Ue.prototype._prepareBlock=function(t,e){for(var r=this.W,i=0;i<32;i++)r[i]=t[e+i];for(;i<r.length;i+=2){var n=je(r[i-4],r[i-3]),s=Ye(r[i-4],r[i-3]),o=r[i-14],h=r[i-13],f=He(r[i-30],r[i-29]),u=Qe(r[i-30],r[i-29]),a=r[i-32],l=r[i-31];r[i]=Be(n,s,o,h,f,u,a,l),r[i+1]=Ie(n,s,o,h,f,u,a,l)}},Ue.prototype._update=function(t,e){this._prepareBlock(t,e);var r=this.W,i=this.h[0],n=this.h[1],s=this.h[2],o=this.h[3],h=this.h[4],f=this.h[5],u=this.h[6],a=this.h[7],l=this.h[8],d=this.h[9],c=this.h[10],p=this.h[11],m=this.h[12],g=this.h[13],A=this.h[14],v=this.h[15];ve(this.k.length===r.length);for(var y=0;y<r.length;y+=2){var w=A,b=v,M=ze(l,d),E=qe(l,d),S=Te(l,0,c,0,m),_=Pe(0,d,0,p,0,g),B=this.k[y],I=this.k[y+1],C=r[y],N=r[y+1],x=Ce(w,b,M,E,S,_,B,I,C,N),R=Ne(w,b,M,E,S,_,B,I,C,N);w=De(i,n),b=Le(i,n),M=ke(i,0,s,0,h),E=Fe(0,n,0,o,0,f);var U=Se(w,b,M,E),O=_e(w,b,M,E);A=m,v=g,m=c,g=p,c=l,p=d,l=Se(u,a,x,R),d=_e(a,a,x,R),u=h,a=f,h=s,f=o,s=i,o=n,i=Se(x,R,U,O),n=_e(x,R,U,O)}Ee(this.h,0,i,n),Ee(this.h,2,s,o),Ee(this.h,4,h,f),Ee(this.h,6,u,a),Ee(this.h,8,l,d),Ee(this.h,10,c,p),Ee(this.h,12,m,g),Ee(this.h,14,A,v)},Ue.prototype._digest=function(t){return"hex"===t?ge.toHex32(this.h,"big"):ge.split32(this.h,"big")};var Je=vt,Ge=Oe;function Ke(){if(!(this instanceof Ke))return new Ke;Ge.call(this),this.h=[3418070365,3238371032,1654270250,914150663,2438529370,812702999,355462360,4144912697,1731405415,4290775857,2394180231,1750603025,3675008525,1694076839,1203062813,3204075428]}Je.inherits(Ke,Ge);var We=Ke;Ke.blockSize=1024,Ke.outSize=384,Ke.hmacStrength=192,Ke.padLength=128,Ke.prototype._digest=function(t){return"hex"===t?Je.toHex32(this.h.slice(0,12),"big"):Je.split32(this.h.slice(0,12),"big")},Ut.sha1=Kt,Ut.sha224=me,Ut.sha256=le,Ut.sha384=We,Ut.sha512=Oe;var Ve={},Xe=vt,Ze=Ct,$e=Xe.rotl32,tr=Xe.sum32,er=Xe.sum32_3,rr=Xe.sum32_4,ir=Ze.BlockHash;function nr(){if(!(this instanceof nr))return new nr;ir.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.endian="little"}function sr(t,e,r,i){return t<=15?e^r^i:t<=31?e&r|~e&i:t<=47?(e|~r)^i:t<=63?e&i|r&~i:e^(r|~i)}function or(t){return t<=15?0:t<=31?1518500249:t<=47?1859775393:t<=63?2400959708:2840853838}function hr(t){return t<=15?1352829926:t<=31?1548603684:t<=47?1836072691:t<=63?2053994217:0}Xe.inherits(nr,ir),Ve.ripemd160=nr,nr.blockSize=512,nr.outSize=160,nr.hmacStrength=192,nr.padLength=64,nr.prototype._update=function(t,e){for(var r=this.h[0],i=this.h[1],n=this.h[2],s=this.h[3],o=this.h[4],h=r,f=i,u=n,a=s,l=o,d=0;d<80;d++){var c=tr($e(rr(r,sr(d,i,n,s),t[fr[d]+e],or(d)),ar[d]),o);r=o,o=s,s=$e(n,10),n=i,i=c,c=tr($e(rr(h,sr(79-d,f,u,a),t[ur[d]+e],hr(d)),lr[d]),l),h=l,l=a,a=$e(u,10),u=f,f=c}c=er(this.h[1],n,a),this.h[1]=er(this.h[2],s,l),this.h[2]=er(this.h[3],o,h),this.h[3]=er(this.h[4],r,f),this.h[4]=er(this.h[0],i,u),this.h[0]=c},nr.prototype._digest=function(t){return"hex"===t?Xe.toHex32(this.h,"little"):Xe.split32(this.h,"little")};var fr=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],ur=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],ar=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],lr=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11],dr=vt,cr=yt;function pr(t,e,r){if(!(this instanceof pr))return new pr(t,e,r);this.Hash=t,this.blockSize=t.blockSize/8,this.outSize=t.outSize/8,this.inner=null,this.outer=null,this._init(dr.toArray(e,r))}var mr=pr;function gr(t,e,r){return r={path:e,exports:{},require:function(t,e){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(e??r.path)}},t(r,r.exports),r.exports}pr.prototype._init=function(t){t.length>this.blockSize&&(t=(new this.Hash).update(t).digest()),cr(t.length<=this.blockSize);for(var e=t.length;e<this.blockSize;e++)t.push(0);for(e=0;e<t.length;e++)t[e]^=54;for(this.inner=(new this.Hash).update(t),e=0;e<t.length;e++)t[e]^=106;this.outer=(new this.Hash).update(t)},pr.prototype.update=function(t,e){return this.inner.update(t,e),this},pr.prototype.digest=function(t){return this.outer.update(this.inner.digest()),this.outer.digest(t)},function(t){var e=t;e.utils=vt,e.common=Ct,e.sha=Ut,e.ripemd=Ve,e.hmac=mr,e.sha1=e.sha.sha1,e.sha256=e.sha.sha256,e.sha224=e.sha.sha224,e.sha384=e.sha.sha384,e.sha512=e.sha.sha512,e.ripemd160=e.ripemd.ripemd160}(At);var Ar=vr;function vr(t,e){if(!t)throw new Error(e||"Assertion failed")}vr.equal=function(t,e,r){if(t!=e)throw new Error(r||"Assertion failed: "+t+" != "+e)};var yr=gr((function(t,e){var r=e;function i(t){return 1===t.length?"0"+t:t}function n(t){for(var e="",r=0;r<t.length;r++)e+=i(t[r].toString(16));return e}r.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var r=[];if("string"!=typeof t){for(var i=0;i<t.length;i++)r[i]=0|t[i];return r}if("hex"===e)for((t=t.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(t="0"+t),i=0;i<t.length;i+=2)r.push(parseInt(t[i]+t[i+1],16));else for(i=0;i<t.length;i++){var n=t.charCodeAt(i),s=n>>8,o=255&n;s?r.push(s,o):r.push(o)}return r},r.zero2=i,r.toHex=n,r.encode=function(t,e){return"hex"===e?n(t):t}})),wr=gr((function(t,e){var r=e;r.assert=Ar,r.toArray=yr.toArray,r.zero2=yr.zero2,r.toHex=yr.toHex,r.encode=yr.encode,r.getNAF=function(t,e,r){var i=new Array(Math.max(t.bitLength(),r)+1);i.fill(0);for(var n=1<<e+1,s=t.clone(),o=0;o<i.length;o++){var h,f=s.andln(n-1);s.isOdd()?(h=f>(n>>1)-1?(n>>1)-f:f,s.isubn(h)):h=0,i[o]=h,s.iushrn(1)}return i},r.getJSF=function(t,e){var r=[[],[]];t=t.clone(),e=e.clone();for(var i,n=0,s=0;t.cmpn(-n)>0||e.cmpn(-s)>0;){var o,h,f=t.andln(3)+n&3,u=e.andln(3)+s&3;3===f&&(f=-1),3===u&&(u=-1),o=1&f?3!=(i=t.andln(7)+n&7)&&5!==i||2!==u?f:-f:0,r[0].push(o),h=1&u?3!=(i=e.andln(7)+s&7)&&5!==i||2!==f?u:-u:0,r[1].push(h),2*n===o+1&&(n=1-n),2*s===h+1&&(s=1-s),t.iushrn(1),e.iushrn(1)}return r},r.cachedProperty=function(t,e,r){var i="_"+e;t.prototype[e]=function(){return void 0!==this[i]?this[i]:this[i]=r.call(this)}},r.parseBytes=function(t){return"string"==typeof t?r.toArray(t,"hex"):t},r.intFromLE=function(t){return new I(t,"hex","le")}})),br=wr.getNAF,Mr=wr.getJSF,Er=wr.assert;function Sr(t,e){this.type=t,this.p=new I(e.p,16),this.red=e.prime?I.red(e.prime):I.mont(this.p),this.zero=new I(0).toRed(this.red),this.one=new I(1).toRed(this.red),this.two=new I(2).toRed(this.red),this.n=e.n&&new I(e.n,16),this.g=e.g&&this.pointFromJSON(e.g,e.gRed),this._wnafT1=new Array(4),this._wnafT2=new Array(4),this._wnafT3=new Array(4),this._wnafT4=new Array(4),this._bitLength=this.n?this.n.bitLength():0;var r=this.n&&this.p.div(this.n);!r||r.cmpn(100)>0?this.redN=null:(this._maxwellTrick=!0,this.redN=this.n.toRed(this.red))}var _r=Sr;function Br(t,e){this.curve=t,this.type=e,this.precomputed=null}Sr.prototype.point=function(){throw new Error("Not implemented")},Sr.prototype.validate=function(){throw new Error("Not implemented")},Sr.prototype._fixedNafMul=function(t,e){Er(t.precomputed);var r=t._getDoubles(),i=br(e,1,this._bitLength),n=(1<<r.step+1)-(r.step%2==0?2:1);n/=3;var s,o,h=[];for(s=0;s<i.length;s+=r.step){o=0;for(var f=s+r.step-1;f>=s;f--)o=(o<<1)+i[f];h.push(o)}for(var u=this.jpoint(null,null,null),a=this.jpoint(null,null,null),l=n;l>0;l--){for(s=0;s<h.length;s++)(o=h[s])===l?a=a.mixedAdd(r.points[s]):o===-l&&(a=a.mixedAdd(r.points[s].neg()));u=u.add(a)}return u.toP()},Sr.prototype._wnafMul=function(t,e){var r=4,i=t._getNAFPoints(r);r=i.wnd;for(var n=i.points,s=br(e,r,this._bitLength),o=this.jpoint(null,null,null),h=s.length-1;h>=0;h--){for(var f=0;h>=0&&0===s[h];h--)f++;if(h>=0&&f++,o=o.dblp(f),h<0)break;var u=s[h];Er(0!==u),o="affine"===t.type?u>0?o.mixedAdd(n[u-1>>1]):o.mixedAdd(n[-u-1>>1].neg()):u>0?o.add(n[u-1>>1]):o.add(n[-u-1>>1].neg())}return"affine"===t.type?o.toP():o},Sr.prototype._wnafMulAdd=function(t,e,r,i,n){var s,o,h,f=this._wnafT1,u=this._wnafT2,a=this._wnafT3,l=0;for(s=0;s<i;s++){var d=(h=e[s])._getNAFPoints(t);f[s]=d.wnd,u[s]=d.points}for(s=i-1;s>=1;s-=2){var c=s-1,p=s;if(1===f[c]&&1===f[p]){var m=[e[c],null,null,e[p]];0===e[c].y.cmp(e[p].y)?(m[1]=e[c].add(e[p]),m[2]=e[c].toJ().mixedAdd(e[p].neg())):0===e[c].y.cmp(e[p].y.redNeg())?(m[1]=e[c].toJ().mixedAdd(e[p]),m[2]=e[c].add(e[p].neg())):(m[1]=e[c].toJ().mixedAdd(e[p]),m[2]=e[c].toJ().mixedAdd(e[p].neg()));var g=[-3,-1,-5,-7,0,7,5,1,3],A=Mr(r[c],r[p]);for(l=Math.max(A[0].length,l),a[c]=new Array(l),a[p]=new Array(l),o=0;o<l;o++){var v=0|A[0][o],y=0|A[1][o];a[c][o]=g[3*(v+1)+(y+1)],a[p][o]=0,u[c]=m}}else a[c]=br(r[c],f[c],this._bitLength),a[p]=br(r[p],f[p],this._bitLength),l=Math.max(a[c].length,l),l=Math.max(a[p].length,l)}var w=this.jpoint(null,null,null),b=this._wnafT4;for(s=l;s>=0;s--){for(var M=0;s>=0;){var E=!0;for(o=0;o<i;o++)b[o]=0|a[o][s],0!==b[o]&&(E=!1);if(!E)break;M++,s--}if(s>=0&&M++,w=w.dblp(M),s<0)break;for(o=0;o<i;o++){var S=b[o];0!==S&&(S>0?h=u[o][S-1>>1]:S<0&&(h=u[o][-S-1>>1].neg()),w="affine"===h.type?w.mixedAdd(h):w.add(h))}}for(s=0;s<i;s++)u[s]=null;return n?w:w.toP()},Sr.BasePoint=Br,Br.prototype.eq=function(){throw new Error("Not implemented")},Br.prototype.validate=function(){return this.curve.validate(this)},Sr.prototype.decodePoint=function(t,e){t=wr.toArray(t,e);var r=this.p.byteLength();if((4===t[0]||6===t[0]||7===t[0])&&t.length-1==2*r)return 6===t[0]?Er(t[t.length-1]%2==0):7===t[0]&&Er(t[t.length-1]%2==1),this.point(t.slice(1,1+r),t.slice(1+r,1+2*r));if((2===t[0]||3===t[0])&&t.length-1===r)return this.pointFromX(t.slice(1,1+r),3===t[0]);throw new Error("Unknown point format")},Br.prototype.encodeCompressed=function(t){return this.encode(t,!0)},Br.prototype._encode=function(t){var e=this.curve.p.byteLength(),r=this.getX().toArray("be",e);return t?[this.getY().isEven()?2:3].concat(r):[4].concat(r,this.getY().toArray("be",e))},Br.prototype.encode=function(t,e){return wr.encode(this._encode(e),t)},Br.prototype.precompute=function(t){if(this.precomputed)return this;var e={doubles:null,naf:null,beta:null};return e.naf=this._getNAFPoints(8),e.doubles=this._getDoubles(4,t),e.beta=this._getBeta(),this.precomputed=e,this},Br.prototype._hasDoubles=function(t){if(!this.precomputed)return!1;var e=this.precomputed.doubles;return!!e&&e.points.length>=Math.ceil((t.bitLength()+1)/e.step)},Br.prototype._getDoubles=function(t,e){if(this.precomputed&&this.precomputed.doubles)return this.precomputed.doubles;for(var r=[this],i=this,n=0;n<e;n+=t){for(var s=0;s<t;s++)i=i.dbl();r.push(i)}return{step:t,points:r}},Br.prototype._getNAFPoints=function(t){if(this.precomputed&&this.precomputed.naf)return this.precomputed.naf;for(var e=[this],r=(1<<t)-1,i=1===r?null:this.dbl(),n=1;n<r;n++)e[n]=e[n-1].add(i);return{wnd:t,points:e}},Br.prototype._getBeta=function(){return null},Br.prototype.dblp=function(t){for(var e=this,r=0;r<t;r++)e=e.dbl();return e};var Ir=gr((function(t){"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}})),Cr=wr.assert;function Nr(t){_r.call(this,"short",t),this.a=new I(t.a,16).toRed(this.red),this.b=new I(t.b,16).toRed(this.red),this.tinv=this.two.redInvm(),this.zeroA=0===this.a.fromRed().cmpn(0),this.threeA=0===this.a.fromRed().sub(this.p).cmpn(-3),this.endo=this._getEndomorphism(t),this._endoWnafT1=new Array(4),this._endoWnafT2=new Array(4)}Ir(Nr,_r);var xr=Nr;function Rr(t,e,r,i){_r.BasePoint.call(this,t,"affine"),null===e&&null===r?(this.x=null,this.y=null,this.inf=!0):(this.x=new I(e,16),this.y=new I(r,16),i&&(this.x.forceRed(this.curve.red),this.y.forceRed(this.curve.red)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.inf=!1)}function Ur(t,e,r,i){_r.BasePoint.call(this,t,"jacobian"),null===e&&null===r&&null===i?(this.x=this.curve.one,this.y=this.curve.one,this.z=new I(0)):(this.x=new I(e,16),this.y=new I(r,16),this.z=new I(i,16)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.zOne=this.z===this.curve.one}Nr.prototype._getEndomorphism=function(t){if(this.zeroA&&this.g&&this.n&&1===this.p.modn(3)){var e,r,i;if(t.beta)e=new I(t.beta,16).toRed(this.red);else{var n=this._getEndoRoots(this.p);e=(e=n[0].cmp(n[1])<0?n[0]:n[1]).toRed(this.red)}if(t.lambda)r=new I(t.lambda,16);else{var s=this._getEndoRoots(this.n);0===this.g.mul(s[0]).x.cmp(this.g.x.redMul(e))?r=s[0]:(r=s[1],Cr(0===this.g.mul(r).x.cmp(this.g.x.redMul(e))))}return i=t.basis?t.basis.map((function(t){return{a:new I(t.a,16),b:new I(t.b,16)}})):this._getEndoBasis(r),{beta:e,lambda:r,basis:i}}},Nr.prototype._getEndoRoots=function(t){var e=t===this.p?this.red:I.mont(t),r=new I(2).toRed(e).redInvm(),i=r.redNeg(),n=new I(3).toRed(e).redNeg().redSqrt().redMul(r);return[i.redAdd(n).fromRed(),i.redSub(n).fromRed()]},Nr.prototype._getEndoBasis=function(t){for(var e,r,i,n,s,o,h,f,u,a=this.n.ushrn(Math.floor(this.n.bitLength()/2)),l=t,d=this.n.clone(),c=new I(1),p=new I(0),m=new I(0),g=new I(1),A=0;0!==l.cmpn(0);){var v=d.div(l);f=d.sub(v.mul(l)),u=m.sub(v.mul(c));var y=g.sub(v.mul(p));if(!i&&f.cmp(a)<0)e=h.neg(),r=c,i=f.neg(),n=u;else if(i&&2==++A)break;h=f,d=l,l=f,m=c,c=u,g=p,p=y}s=f.neg(),o=u;var w=i.sqr().add(n.sqr());return s.sqr().add(o.sqr()).cmp(w)>=0&&(s=e,o=r),i.negative&&(i=i.neg(),n=n.neg()),s.negative&&(s=s.neg(),o=o.neg()),[{a:i,b:n},{a:s,b:o}]},Nr.prototype._endoSplit=function(t){var e=this.endo.basis,r=e[0],i=e[1],n=i.b.mul(t).divRound(this.n),s=r.b.neg().mul(t).divRound(this.n),o=n.mul(r.a),h=s.mul(i.a),f=n.mul(r.b),u=s.mul(i.b);return{k1:t.sub(o).sub(h),k2:f.add(u).neg()}},Nr.prototype.pointFromX=function(t,e){(t=new I(t,16)).red||(t=t.toRed(this.red));var r=t.redSqr().redMul(t).redIAdd(t.redMul(this.a)).redIAdd(this.b),i=r.redSqrt();if(0!==i.redSqr().redSub(r).cmp(this.zero))throw new Error("invalid point");var n=i.fromRed().isOdd();return(e&&!n||!e&&n)&&(i=i.redNeg()),this.point(t,i)},Nr.prototype.validate=function(t){if(t.inf)return!0;var e=t.x,r=t.y,i=this.a.redMul(e),n=e.redSqr().redMul(e).redIAdd(i).redIAdd(this.b);return 0===r.redSqr().redISub(n).cmpn(0)},Nr.prototype._endoWnafMulAdd=function(t,e,r){for(var i=this._endoWnafT1,n=this._endoWnafT2,s=0;s<t.length;s++){var o=this._endoSplit(e[s]),h=t[s],f=h._getBeta();o.k1.negative&&(o.k1.ineg(),h=h.neg(!0)),o.k2.negative&&(o.k2.ineg(),f=f.neg(!0)),i[2*s]=h,i[2*s+1]=f,n[2*s]=o.k1,n[2*s+1]=o.k2}for(var u=this._wnafMulAdd(1,i,n,2*s,r),a=0;a<2*s;a++)i[a]=null,n[a]=null;return u},Ir(Rr,_r.BasePoint),Nr.prototype.point=function(t,e,r){return new Rr(this,t,e,r)},Nr.prototype.pointFromJSON=function(t,e){return Rr.fromJSON(this,t,e)},Rr.prototype._getBeta=function(){if(this.curve.endo){var t=this.precomputed;if(t&&t.beta)return t.beta;var e=this.curve.point(this.x.redMul(this.curve.endo.beta),this.y);if(t){var r=this.curve,i=function(t){return r.point(t.x.redMul(r.endo.beta),t.y)};t.beta=e,e.precomputed={beta:null,naf:t.naf&&{wnd:t.naf.wnd,points:t.naf.points.map(i)},doubles:t.doubles&&{step:t.doubles.step,points:t.doubles.points.map(i)}}}return e}},Rr.prototype.toJSON=function(){return this.precomputed?[this.x,this.y,this.precomputed&&{doubles:this.precomputed.doubles&&{step:this.precomputed.doubles.step,points:this.precomputed.doubles.points.slice(1)},naf:this.precomputed.naf&&{wnd:this.precomputed.naf.wnd,points:this.precomputed.naf.points.slice(1)}}]:[this.x,this.y]},Rr.fromJSON=function(t,e,r){"string"==typeof e&&(e=JSON.parse(e));var i=t.point(e[0],e[1],r);if(!e[2])return i;function n(e){return t.point(e[0],e[1],r)}var s=e[2];return i.precomputed={beta:null,doubles:s.doubles&&{step:s.doubles.step,points:[i].concat(s.doubles.points.map(n))},naf:s.naf&&{wnd:s.naf.wnd,points:[i].concat(s.naf.points.map(n))}},i},Rr.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+">"},Rr.prototype.isInfinity=function(){return this.inf},Rr.prototype.add=function(t){if(this.inf)return t;if(t.inf)return this;if(this.eq(t))return this.dbl();if(this.neg().eq(t))return this.curve.point(null,null);if(0===this.x.cmp(t.x))return this.curve.point(null,null);var e=this.y.redSub(t.y);0!==e.cmpn(0)&&(e=e.redMul(this.x.redSub(t.x).redInvm()));var r=e.redSqr().redISub(this.x).redISub(t.x),i=e.redMul(this.x.redSub(r)).redISub(this.y);return this.curve.point(r,i)},Rr.prototype.dbl=function(){if(this.inf)return this;var t=this.y.redAdd(this.y);if(0===t.cmpn(0))return this.curve.point(null,null);var e=this.curve.a,r=this.x.redSqr(),i=t.redInvm(),n=r.redAdd(r).redIAdd(r).redIAdd(e).redMul(i),s=n.redSqr().redISub(this.x.redAdd(this.x)),o=n.redMul(this.x.redSub(s)).redISub(this.y);return this.curve.point(s,o)},Rr.prototype.getX=function(){return this.x.fromRed()},Rr.prototype.getY=function(){return this.y.fromRed()},Rr.prototype.mul=function(t){return t=new I(t,16),this.isInfinity()?this:this._hasDoubles(t)?this.curve._fixedNafMul(this,t):this.curve.endo?this.curve._endoWnafMulAdd([this],[t]):this.curve._wnafMul(this,t)},Rr.prototype.mulAdd=function(t,e,r){var i=[this,e],n=[t,r];return this.curve.endo?this.curve._endoWnafMulAdd(i,n):this.curve._wnafMulAdd(1,i,n,2)},Rr.prototype.jmulAdd=function(t,e,r){var i=[this,e],n=[t,r];return this.curve.endo?this.curve._endoWnafMulAdd(i,n,!0):this.curve._wnafMulAdd(1,i,n,2,!0)},Rr.prototype.eq=function(t){return this===t||this.inf===t.inf&&(this.inf||0===this.x.cmp(t.x)&&0===this.y.cmp(t.y))},Rr.prototype.neg=function(t){if(this.inf)return this;var e=this.curve.point(this.x,this.y.redNeg());if(t&&this.precomputed){var r=this.precomputed,i=function(t){return t.neg()};e.precomputed={naf:r.naf&&{wnd:r.naf.wnd,points:r.naf.points.map(i)},doubles:r.doubles&&{step:r.doubles.step,points:r.doubles.points.map(i)}}}return e},Rr.prototype.toJ=function(){return this.inf?this.curve.jpoint(null,null,null):this.curve.jpoint(this.x,this.y,this.curve.one)},Ir(Ur,_r.BasePoint),Nr.prototype.jpoint=function(t,e,r){return new Ur(this,t,e,r)},Ur.prototype.toP=function(){if(this.isInfinity())return this.curve.point(null,null);var t=this.z.redInvm(),e=t.redSqr(),r=this.x.redMul(e),i=this.y.redMul(e).redMul(t);return this.curve.point(r,i)},Ur.prototype.neg=function(){return this.curve.jpoint(this.x,this.y.redNeg(),this.z)},Ur.prototype.add=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;var e=t.z.redSqr(),r=this.z.redSqr(),i=this.x.redMul(e),n=t.x.redMul(r),s=this.y.redMul(e.redMul(t.z)),o=t.y.redMul(r.redMul(this.z)),h=i.redSub(n),f=s.redSub(o);if(0===h.cmpn(0))return 0!==f.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var u=h.redSqr(),a=u.redMul(h),l=i.redMul(u),d=f.redSqr().redIAdd(a).redISub(l).redISub(l),c=f.redMul(l.redISub(d)).redISub(s.redMul(a)),p=this.z.redMul(t.z).redMul(h);return this.curve.jpoint(d,c,p)},Ur.prototype.mixedAdd=function(t){if(this.isInfinity())return t.toJ();if(t.isInfinity())return this;var e=this.z.redSqr(),r=this.x,i=t.x.redMul(e),n=this.y,s=t.y.redMul(e).redMul(this.z),o=r.redSub(i),h=n.redSub(s);if(0===o.cmpn(0))return 0!==h.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var f=o.redSqr(),u=f.redMul(o),a=r.redMul(f),l=h.redSqr().redIAdd(u).redISub(a).redISub(a),d=h.redMul(a.redISub(l)).redISub(n.redMul(u)),c=this.z.redMul(o);return this.curve.jpoint(l,d,c)},Ur.prototype.dblp=function(t){if(0===t)return this;if(this.isInfinity())return this;if(!t)return this.dbl();var e;if(this.curve.zeroA||this.curve.threeA){var r=this;for(e=0;e<t;e++)r=r.dbl();return r}var i=this.curve.a,n=this.curve.tinv,s=this.x,o=this.y,h=this.z,f=h.redSqr().redSqr(),u=o.redAdd(o);for(e=0;e<t;e++){var a=s.redSqr(),l=u.redSqr(),d=l.redSqr(),c=a.redAdd(a).redIAdd(a).redIAdd(i.redMul(f)),p=s.redMul(l),m=c.redSqr().redISub(p.redAdd(p)),g=p.redISub(m),A=c.redMul(g);A=A.redIAdd(A).redISub(d);var v=u.redMul(h);e+1<t&&(f=f.redMul(d)),s=m,h=v,u=A}return this.curve.jpoint(s,u.redMul(n),h)},Ur.prototype.dbl=function(){return this.isInfinity()?this:this.curve.zeroA?this._zeroDbl():this.curve.threeA?this._threeDbl():this._dbl()},Ur.prototype._zeroDbl=function(){var t,e,r;if(this.zOne){var i=this.x.redSqr(),n=this.y.redSqr(),s=n.redSqr(),o=this.x.redAdd(n).redSqr().redISub(i).redISub(s);o=o.redIAdd(o);var h=i.redAdd(i).redIAdd(i),f=h.redSqr().redISub(o).redISub(o),u=s.redIAdd(s);u=(u=u.redIAdd(u)).redIAdd(u),t=f,e=h.redMul(o.redISub(f)).redISub(u),r=this.y.redAdd(this.y)}else{var a=this.x.redSqr(),l=this.y.redSqr(),d=l.redSqr(),c=this.x.redAdd(l).redSqr().redISub(a).redISub(d);c=c.redIAdd(c);var p=a.redAdd(a).redIAdd(a),m=p.redSqr(),g=d.redIAdd(d);g=(g=g.redIAdd(g)).redIAdd(g),t=m.redISub(c).redISub(c),e=p.redMul(c.redISub(t)).redISub(g),r=(r=this.y.redMul(this.z)).redIAdd(r)}return this.curve.jpoint(t,e,r)},Ur.prototype._threeDbl=function(){var t,e,r;if(this.zOne){var i=this.x.redSqr(),n=this.y.redSqr(),s=n.redSqr(),o=this.x.redAdd(n).redSqr().redISub(i).redISub(s);o=o.redIAdd(o);var h=i.redAdd(i).redIAdd(i).redIAdd(this.curve.a),f=h.redSqr().redISub(o).redISub(o);t=f;var u=s.redIAdd(s);u=(u=u.redIAdd(u)).redIAdd(u),e=h.redMul(o.redISub(f)).redISub(u),r=this.y.redAdd(this.y)}else{var a=this.z.redSqr(),l=this.y.redSqr(),d=this.x.redMul(l),c=this.x.redSub(a).redMul(this.x.redAdd(a));c=c.redAdd(c).redIAdd(c);var p=d.redIAdd(d),m=(p=p.redIAdd(p)).redAdd(p);t=c.redSqr().redISub(m),r=this.y.redAdd(this.z).redSqr().redISub(l).redISub(a);var g=l.redSqr();g=(g=(g=g.redIAdd(g)).redIAdd(g)).redIAdd(g),e=c.redMul(p.redISub(t)).redISub(g)}return this.curve.jpoint(t,e,r)},Ur.prototype._dbl=function(){var t=this.curve.a,e=this.x,r=this.y,i=this.z,n=i.redSqr().redSqr(),s=e.redSqr(),o=r.redSqr(),h=s.redAdd(s).redIAdd(s).redIAdd(t.redMul(n)),f=e.redAdd(e),u=(f=f.redIAdd(f)).redMul(o),a=h.redSqr().redISub(u.redAdd(u)),l=u.redISub(a),d=o.redSqr();d=(d=(d=d.redIAdd(d)).redIAdd(d)).redIAdd(d);var c=h.redMul(l).redISub(d),p=r.redAdd(r).redMul(i);return this.curve.jpoint(a,c,p)},Ur.prototype.trpl=function(){if(!this.curve.zeroA)return this.dbl().add(this);var t=this.x.redSqr(),e=this.y.redSqr(),r=this.z.redSqr(),i=e.redSqr(),n=t.redAdd(t).redIAdd(t),s=n.redSqr(),o=this.x.redAdd(e).redSqr().redISub(t).redISub(i),h=(o=(o=(o=o.redIAdd(o)).redAdd(o).redIAdd(o)).redISub(s)).redSqr(),f=i.redIAdd(i);f=(f=(f=f.redIAdd(f)).redIAdd(f)).redIAdd(f);var u=n.redIAdd(o).redSqr().redISub(s).redISub(h).redISub(f),a=e.redMul(u);a=(a=a.redIAdd(a)).redIAdd(a);var l=this.x.redMul(h).redISub(a);l=(l=l.redIAdd(l)).redIAdd(l);var d=this.y.redMul(u.redMul(f.redISub(u)).redISub(o.redMul(h)));d=(d=(d=d.redIAdd(d)).redIAdd(d)).redIAdd(d);var c=this.z.redAdd(o).redSqr().redISub(r).redISub(h);return this.curve.jpoint(l,d,c)},Ur.prototype.mul=function(t,e){return t=new I(t,e),this.curve._wnafMul(this,t)},Ur.prototype.eq=function(t){if("affine"===t.type)return this.eq(t.toJ());if(this===t)return!0;var e=this.z.redSqr(),r=t.z.redSqr();if(0!==this.x.redMul(r).redISub(t.x.redMul(e)).cmpn(0))return!1;var i=e.redMul(this.z),n=r.redMul(t.z);return 0===this.y.redMul(n).redISub(t.y.redMul(i)).cmpn(0)},Ur.prototype.eqXToP=function(t){var e=this.z.redSqr(),r=t.toRed(this.curve.red).redMul(e);if(0===this.x.cmp(r))return!0;for(var i=t.clone(),n=this.curve.redN.redMul(e);;){if(i.iadd(this.curve.n),i.cmp(this.curve.p)>=0)return!1;if(r.redIAdd(n),0===this.x.cmp(r))return!0}},Ur.prototype.inspect=function(){return this.isInfinity()?"<EC JPoint Infinity>":"<EC JPoint x: "+this.x.toString(16,2)+" y: "+this.y.toString(16,2)+" z: "+this.z.toString(16,2)+">"},Ur.prototype.isInfinity=function(){return 0===this.z.cmpn(0)};var Or=gr((function(t,e){var r=e;r.base=_r,r.short=xr,r.mont=null,r.edwards=null})),Tr=gr((function(t,e){var r,i=e,n=wr.assert;function s(t){"short"===t.type?this.curve=new Or.short(t):"edwards"===t.type?this.curve=new Or.edwards(t):this.curve=new Or.mont(t),this.g=this.curve.g,this.n=this.curve.n,this.hash=t.hash,n(this.g.validate(),"Invalid curve"),n(this.g.mul(this.n).isInfinity(),"Invalid curve, G*N != O")}function o(t,e){Object.defineProperty(i,t,{configurable:!0,enumerable:!0,get:function(){var r=new s(e);return Object.defineProperty(i,t,{configurable:!0,enumerable:!0,value:r}),r}})}i.PresetCurve=s,o("p192",{type:"short",prime:"p192",p:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff",a:"ffffffff ffffffff ffffffff fffffffe ffffffff fffffffc",b:"64210519 e59c80e7 0fa7e9ab 72243049 feb8deec c146b9b1",n:"ffffffff ffffffff ffffffff 99def836 146bc9b1 b4d22831",hash:At.sha256,gRed:!1,g:["188da80e b03090f6 7cbf20eb 43a18800 f4ff0afd 82ff1012","07192b95 ffc8da78 631011ed 6b24cdd5 73f977a1 1e794811"]}),o("p224",{type:"short",prime:"p224",p:"ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001",a:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff fffffffe",b:"b4050a85 0c04b3ab f5413256 5044b0b7 d7bfd8ba 270b3943 2355ffb4",n:"ffffffff ffffffff ffffffff ffff16a2 e0b8f03e 13dd2945 5c5c2a3d",hash:At.sha256,gRed:!1,g:["b70e0cbd 6bb4bf7f 321390b9 4a03c1d3 56c21122 343280d6 115c1d21","bd376388 b5f723fb 4c22dfe6 cd4375a0 5a074764 44d58199 85007e34"]}),o("p256",{type:"short",prime:null,p:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff ffffffff",a:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff fffffffc",b:"5ac635d8 aa3a93e7 b3ebbd55 769886bc 651d06b0 cc53b0f6 3bce3c3e 27d2604b",n:"ffffffff 00000000 ffffffff ffffffff bce6faad a7179e84 f3b9cac2 fc632551",hash:At.sha256,gRed:!1,g:["6b17d1f2 e12c4247 f8bce6e5 63a440f2 77037d81 2deb33a0 f4a13945 d898c296","4fe342e2 fe1a7f9b 8ee7eb4a 7c0f9e16 2bce3357 6b315ece cbb64068 37bf51f5"]}),o("p384",{type:"short",prime:null,p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 ffffffff",a:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 fffffffc",b:"b3312fa7 e23ee7e4 988e056b e3f82d19 181d9c6e fe814112 0314088f 5013875a c656398d 8a2ed19d 2a85c8ed d3ec2aef",n:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff c7634d81 f4372ddf 581a0db2 48b0a77a ecec196a ccc52973",hash:At.sha384,gRed:!1,g:["aa87ca22 be8b0537 8eb1c71e f320ad74 6e1d3b62 8ba79b98 59f741e0 82542a38 5502f25d bf55296c 3a545e38 72760ab7","3617de4a 96262c6f 5d9e98bf 9292dc29 f8f41dbd 289a147c e9da3113 b5f0b8c0 0a60b1ce 1d7e819d 7a431d7c 90ea0e5f"]}),o("p521",{type:"short",prime:null,p:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff",a:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffc",b:"00000051 953eb961 8e1c9a1f 929a21a0 b68540ee a2da725b 99b315f3 b8b48991 8ef109e1 56193951 ec7e937b 1652c0bd 3bb1bf07 3573df88 3d2c34f1 ef451fd4 6b503f00",n:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffa 51868783 bf2f966b 7fcc0148 f709a5d0 3bb5c9b8 899c47ae bb6fb71e 91386409",hash:At.sha512,gRed:!1,g:["000000c6 858e06b7 0404e9cd 9e3ecb66 2395b442 9c648139 053fb521 f828af60 6b4d3dba a14b5e77 efe75928 fe1dc127 a2ffa8de 3348b3c1 856a429b f97e7e31 c2e5bd66","00000118 39296a78 9a3bc004 5c8a5fb4 2c7d1bd9 98f54449 579b4468 17afbd17 273e662c 97ee7299 5ef42640 c550b901 3fad0761 353c7086 a272c240 88be9476 9fd16650"]}),o("curve25519",{type:"mont",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"76d06",b:"1",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:At.sha256,gRed:!1,g:["9"]}),o("ed25519",{type:"edwards",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"-1",c:"1",d:"52036cee2b6ffe73 8cc740797779e898 00700a4d4141d8ab 75eb4dca135978a3",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:At.sha256,gRed:!1,g:["216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a","6666666666666666666666666666666666666666666666666666666666666658"]});try{r=null.crash()}catch{r=void 0}o("secp256k1",{type:"short",prime:"k256",p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f",a:"0",b:"7",n:"ffffffff ffffffff ffffffff fffffffe baaedce6 af48a03b bfd25e8c d0364141",h:"1",hash:At.sha256,beta:"7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee",lambda:"5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72",basis:[{a:"3086d221a7d46bcde86c90e49284eb15",b:"-e4437ed6010e88286f547fa90abfe4c3"},{a:"114ca50f7a8e2f3f657c1108d9d44cfd8",b:"3086d221a7d46bcde86c90e49284eb15"}],gRed:!1,g:["79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798","483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8",r]})}));function Pr(t){if(!(this instanceof Pr))return new Pr(t);this.hash=t.hash,this.predResist=!!t.predResist,this.outLen=this.hash.outSize,this.minEntropy=t.minEntropy||this.hash.hmacStrength,this._reseed=null,this.reseedInterval=null,this.K=null,this.V=null;var e=yr.toArray(t.entropy,t.entropyEnc||"hex"),r=yr.toArray(t.nonce,t.nonceEnc||"hex"),i=yr.toArray(t.pers,t.persEnc||"hex");Ar(e.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._init(e,r,i)}var kr=Pr;Pr.prototype._init=function(t,e,r){var i=t.concat(e).concat(r);this.K=new Array(this.outLen/8),this.V=new Array(this.outLen/8);for(var n=0;n<this.V.length;n++)this.K[n]=0,this.V[n]=1;this._update(i),this._reseed=1,this.reseedInterval=281474976710656},Pr.prototype._hmac=function(){return new At.hmac(this.hash,this.K)},Pr.prototype._update=function(t){var e=this._hmac().update(this.V).update([0]);t&&(e=e.update(t)),this.K=e.digest(),this.V=this._hmac().update(this.V).digest(),t&&(this.K=this._hmac().update(this.V).update([1]).update(t).digest(),this.V=this._hmac().update(this.V).digest())},Pr.prototype.reseed=function(t,e,r,i){"string"!=typeof e&&(i=r,r=e,e=null),t=yr.toArray(t,e),r=yr.toArray(r,i),Ar(t.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._update(t.concat(r||[])),this._reseed=1},Pr.prototype.generate=function(t,e,r,i){if(this._reseed>this.reseedInterval)throw new Error("Reseed is required");"string"!=typeof e&&(i=r,r=e,e=null),r&&(r=yr.toArray(r,i||"hex"),this._update(r));for(var n=[];n.length<t;)this.V=this._hmac().update(this.V).digest(),n=n.concat(this.V);var s=n.slice(0,t);return this._update(r),this._reseed++,yr.encode(s,e)};var Fr=wr.assert;function Dr(t,e){this.ec=t,this.priv=null,this.pub=null,e.priv&&this._importPrivate(e.priv,e.privEnc),e.pub&&this._importPublic(e.pub,e.pubEnc)}var Lr=Dr;Dr.fromPublic=function(t,e,r){return e instanceof Dr?e:new Dr(t,{pub:e,pubEnc:r})},Dr.fromPrivate=function(t,e,r){return e instanceof Dr?e:new Dr(t,{priv:e,privEnc:r})},Dr.prototype.validate=function(){var t=this.getPublic();return t.isInfinity()?{result:!1,reason:"Invalid public key"}:t.validate()?t.mul(this.ec.curve.n).isInfinity()?{result:!0,reason:null}:{result:!1,reason:"Public key * N != O"}:{result:!1,reason:"Public key is not a point"}},Dr.prototype.getPublic=function(t,e){return"string"==typeof t&&(e=t,t=null),this.pub||(this.pub=this.ec.g.mul(this.priv)),e?this.pub.encode(e,t):this.pub},Dr.prototype.getPrivate=function(t){return"hex"===t?this.priv.toString(16,2):this.priv},Dr.prototype._importPrivate=function(t,e){this.priv=new I(t,e||16),this.priv=this.priv.umod(this.ec.curve.n)},Dr.prototype._importPublic=function(t,e){if(t.x||t.y)return"mont"===this.ec.curve.type?Fr(t.x,"Need x coordinate"):("short"===this.ec.curve.type||"edwards"===this.ec.curve.type)&&Fr(t.x&&t.y,"Need both x and y coordinate"),void(this.pub=this.ec.curve.point(t.x,t.y));this.pub=this.ec.curve.decodePoint(t,e)},Dr.prototype.derive=function(t){return t.validate()||Fr(t.validate(),"public point not validated"),t.mul(this.priv).getX()},Dr.prototype.sign=function(t,e,r){return this.ec.sign(t,this,e,r)},Dr.prototype.verify=function(t,e){return this.ec.verify(t,e,this)},Dr.prototype.inspect=function(){return"<Key priv: "+(this.priv&&this.priv.toString(16,2))+" pub: "+(this.pub&&this.pub.inspect())+" >"};var zr=wr.assert;function qr(t,e){if(t instanceof qr)return t;this._importDER(t,e)||(zr(t.r&&t.s,"Signature without r or s"),this.r=new I(t.r,16),this.s=new I(t.s,16),void 0===t.recoveryParam?this.recoveryParam=null:this.recoveryParam=t.recoveryParam)}var Hr=qr;function Qr(){this.place=0}function jr(t,e){var r=t[e.place++];if(!(128&r))return r;var i=15&r;if(0===i||i>4)return!1;for(var n=0,s=0,o=e.place;s<i;s++,o++)n<<=8,n|=t[o],n>>>=0;return!(n<=127)&&(e.place=o,n)}function Yr(t){for(var e=0,r=t.length-1;!t[e]&&!(128&t[e+1])&&e<r;)e++;return 0===e?t:t.slice(e)}function Jr(t,e){if(e<128)t.push(e);else{var r=1+(Math.log(e)/Math.LN2>>>3);for(t.push(128|r);--r;)t.push(e>>>(r<<3)&255);t.push(e)}}qr.prototype._importDER=function(t,e){t=wr.toArray(t,e);var r=new Qr;if(48!==t[r.place++])return!1;var i=jr(t,r);if(!1===i||i+r.place!==t.length||2!==t[r.place++])return!1;var n=jr(t,r);if(!1===n)return!1;var s=t.slice(r.place,n+r.place);if(r.place+=n,2!==t[r.place++])return!1;var o=jr(t,r);if(!1===o||t.length!==o+r.place)return!1;var h=t.slice(r.place,o+r.place);if(0===s[0]){if(!(128&s[1]))return!1;s=s.slice(1)}if(0===h[0]){if(!(128&h[1]))return!1;h=h.slice(1)}return this.r=new I(s),this.s=new I(h),this.recoveryParam=null,!0},qr.prototype.toDER=function(t){var e=this.r.toArray(),r=this.s.toArray();for(128&e[0]&&(e=[0].concat(e)),128&r[0]&&(r=[0].concat(r)),e=Yr(e),r=Yr(r);!(r[0]||128&r[1]);)r=r.slice(1);var i=[2];Jr(i,e.length),(i=i.concat(e)).push(2),Jr(i,r.length);var n=i.concat(r),s=[48];return Jr(s,n.length),s=s.concat(n),wr.encode(s,t)};var Gr=function(){throw new Error("unsupported")},Kr=wr.assert;function Wr(t){if(!(this instanceof Wr))return new Wr(t);"string"==typeof t&&(Kr(Object.prototype.hasOwnProperty.call(Tr,t),"Unknown curve "+t),t=Tr[t]),t instanceof Tr.PresetCurve&&(t={curve:t}),this.curve=t.curve.curve,this.n=this.curve.n,this.nh=this.n.ushrn(1),this.g=this.curve.g,this.g=t.curve.g,this.g.precompute(t.curve.n.bitLength()+1),this.hash=t.hash||t.curve.hash}var Vr=Wr;Wr.prototype.keyPair=function(t){return new Lr(this,t)},Wr.prototype.keyFromPrivate=function(t,e){return Lr.fromPrivate(this,t,e)},Wr.prototype.keyFromPublic=function(t,e){return Lr.fromPublic(this,t,e)},Wr.prototype.genKeyPair=function(t){t||(t={});for(var e=new kr({hash:this.hash,pers:t.pers,persEnc:t.persEnc||"utf8",entropy:t.entropy||Gr(this.hash.hmacStrength),entropyEnc:t.entropy&&t.entropyEnc||"utf8",nonce:this.n.toArray()}),r=this.n.byteLength(),i=this.n.sub(new I(2));;){var n=new I(e.generate(r));if(!(n.cmp(i)>0))return n.iaddn(1),this.keyFromPrivate(n)}},Wr.prototype._truncateToN=function(t,e){var r=8*t.byteLength()-this.n.bitLength();return r>0&&(t=t.ushrn(r)),!e&&t.cmp(this.n)>=0?t.sub(this.n):t},Wr.prototype.sign=function(t,e,r,i){"object"==typeof r&&(i=r,r=null),i||(i={}),e=this.keyFromPrivate(e,r),t=this._truncateToN(new I(t,16));for(var n=this.n.byteLength(),s=e.getPrivate().toArray("be",n),o=t.toArray("be",n),h=new kr({hash:this.hash,entropy:s,nonce:o,pers:i.pers,persEnc:i.persEnc||"utf8"}),f=this.n.sub(new I(1)),u=0;;u++){var a=i.k?i.k(u):new I(h.generate(this.n.byteLength()));if(!((a=this._truncateToN(a,!0)).cmpn(1)<=0||a.cmp(f)>=0)){var l=this.g.mul(a);if(!l.isInfinity()){var d=l.getX(),c=d.umod(this.n);if(0!==c.cmpn(0)){var p=a.invm(this.n).mul(c.mul(e.getPrivate()).iadd(t));if(0!==(p=p.umod(this.n)).cmpn(0)){var m=(l.getY().isOdd()?1:0)|(0!==d.cmp(c)?2:0);return i.canonical&&p.cmp(this.nh)>0&&(p=this.n.sub(p),m^=1),new Hr({r:c,s:p,recoveryParam:m})}}}}}},Wr.prototype.verify=function(t,e,r,i){t=this._truncateToN(new I(t,16)),r=this.keyFromPublic(r,i);var n=(e=new Hr(e,"hex")).r,s=e.s;if(n.cmpn(1)<0||n.cmp(this.n)>=0||s.cmpn(1)<0||s.cmp(this.n)>=0)return!1;var o,h=s.invm(this.n),f=h.mul(t).umod(this.n),u=h.mul(n).umod(this.n);return this.curve._maxwellTrick?!(o=this.g.jmulAdd(f,r.getPublic(),u)).isInfinity()&&o.eqXToP(n):!(o=this.g.mulAdd(f,r.getPublic(),u)).isInfinity()&&0===o.getX().umod(this.n).cmp(n)},Wr.prototype.recoverPubKey=function(t,e,r,i){Kr((3&r)===r,"The recovery param is more than two bits"),e=new Hr(e,i);var n=this.n,s=new I(t),o=e.r,h=e.s,f=1&r,u=r>>1;if(o.cmp(this.curve.p.umod(this.curve.n))>=0&&u)throw new Error("Unable to find sencond key candinate");o=u?this.curve.pointFromX(o.add(this.curve.n),f):this.curve.pointFromX(o,f);var a=e.r.invm(n),l=n.sub(s).mul(a).umod(n),d=h.mul(a).umod(n);return this.g.mulAdd(l,o,d)},Wr.prototype.getKeyRecoveryParam=function(t,e,r,i){if(null!==(e=new Hr(e,i)).recoveryParam)return e.recoveryParam;for(var n=0;n<4;n++){var s;try{s=this.recoverPubKey(t,e,n)}catch{continue}if(s.eq(r))return n}throw new Error("Unable to find valid recovery factor")};var Xr,Zr=gr((function(t,e){var r=e;r.version="6.5.4",r.utils=wr,r.rand=function(){throw new Error("unsupported")},r.curve=Or,r.curves=Tr,r.ec=Vr,r.eddsa=null}));Zr.ec,new p("signing-key/5.7.0"),new p("transactions/5.7.0"),function(t){t[t.legacy=0]="legacy",t[t.eip2930=1]="eip2930",t[t.eip1559=2]="eip1559"}(Xr||(Xr={})),Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;const $r=t=>t?.split(":"),ti=t=>{const e=t&&$r(t);if(e)return t.includes("did:pkh:")?e[3]:e[1]},ei=t=>{const e=t&&$r(t);if(e)return e.pop()};Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable,Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var ri=r(4707),ii=r(29073);const ni=(0,ii.BX)({status:"uninitialized"}),si={state:ni,subscribeKey:(t,e)=>(0,ri.u$)(ni,t,e),subscribe:t=>(0,ii.B1)(ni,(()=>t(ni))),_getClient(){if(!ni._client)throw new Error("SIWEController client not set");return ni._client},async getNonce(t){const e=this._getClient(),r=await e.getNonce(t);return this.setNonce(r),r},async getSession(){try{const t=this._getClient(),e=await t.getSession();return e&&(this.setSession(e),this.setStatus("success")),e}catch{return}},createMessage(t){const e=this._getClient().createMessage(t);return this.setMessage(e),e},async verifyMessage(t){const e=this._getClient();return await e.verifyMessage(t)},async signIn(){const t=this._getClient();return await t.signIn()},async signOut(){const t=this._getClient();await t.signOut(),this.setStatus("ready"),this.setSession(void 0),t.onSignOut?.()},onSignIn(t){const e=this._getClient();e.onSignIn?.(t)},onSignOut(){const t=this._getClient();t.onSignOut?.()},setSIWEClient(t){ni._client=(0,ii.KR)(t),ni.status="ready",i.Hd.setIsSiweEnabled(t.options.enabled)},setNonce(t){ni.nonce=t},setStatus(t){ni.status=t},setMessage(t){ni.message=t},setSession(t){ni.session=t,ni.status=t?"success":"ready"}};var oi=r(76526);const hi=globalThis,fi=hi.ShadowRoot&&(void 0===hi.ShadyCSS||hi.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,ui=Symbol(),ai=new WeakMap;class li{constructor(t,e,r){if(this._$cssResult$=!0,r!==ui)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t,this.t=e}get styleSheet(){let t=this.o;const e=this.t;if(fi&&void 0===t){const r=void 0!==e&&1===e.length;r&&(t=ai.get(e)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),r&&ai.set(e,t))}return t}toString(){return this.cssText}}const di=(t,e)=>{if(fi)t.adoptedStyleSheets=e.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet));else for(const r of e){const e=document.createElement("style"),i=hi.litNonce;void 0!==i&&e.setAttribute("nonce",i),e.textContent=r.cssText,t.appendChild(e)}},ci=fi?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e="";for(const r of t.cssRules)e+=r.cssText;return(t=>new li("string"==typeof t?t:t+"",void 0,ui))(e)})(t):t,{is:pi,defineProperty:mi,getOwnPropertyDescriptor:gi,getOwnPropertyNames:Ai,getOwnPropertySymbols:vi,getPrototypeOf:yi}=Object,wi=globalThis,bi=wi.trustedTypes,Mi=bi?bi.emptyScript:"",Ei=wi.reactiveElementPolyfillSupport,Si=(t,e)=>t,_i={toAttribute(t,e){switch(e){case Boolean:t=t?Mi:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let r=t;switch(e){case Boolean:r=null!==t;break;case Number:r=null===t?null:Number(t);break;case Object:case Array:try{r=JSON.parse(t)}catch(t){r=null}}return r}},Bi=(t,e)=>!pi(t,e),Ii={attribute:!0,type:String,converter:_i,reflect:!1,hasChanged:Bi};Symbol.metadata??=Symbol("metadata"),wi.litPropertyMetadata??=new WeakMap;class Ci extends HTMLElement{static addInitializer(t){this._$Ei(),(this.l??=[]).push(t)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(t,e=Ii){if(e.state&&(e.attribute=!1),this._$Ei(),this.elementProperties.set(t,e),!e.noAccessor){const r=Symbol(),i=this.getPropertyDescriptor(t,r,e);void 0!==i&&mi(this.prototype,t,i)}}static getPropertyDescriptor(t,e,r){const{get:i,set:n}=gi(this.prototype,t)??{get(){return this[e]},set(t){this[e]=t}};return{get(){return i?.call(this)},set(e){const s=i?.call(this);n.call(this,e),this.requestUpdate(t,s,r)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)??Ii}static _$Ei(){if(this.hasOwnProperty(Si("elementProperties")))return;const t=yi(this);t.finalize(),void 0!==t.l&&(this.l=[...t.l]),this.elementProperties=new Map(t.elementProperties)}static finalize(){if(this.hasOwnProperty(Si("finalized")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(Si("properties"))){const t=this.properties,e=[...Ai(t),...vi(t)];for(const r of e)this.createProperty(r,t[r])}const t=this[Symbol.metadata];if(null!==t){const e=litPropertyMetadata.get(t);if(void 0!==e)for(const[t,r]of e)this.elementProperties.set(t,r)}this._$Eh=new Map;for(const[t,e]of this.elementProperties){const r=this._$Eu(t,e);void 0!==r&&this._$Eh.set(r,t)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const r=new Set(t.flat(1/0).reverse());for(const t of r)e.unshift(ci(t))}else void 0!==t&&e.push(ci(t));return e}static _$Eu(t,e){const r=e.attribute;return!1===r?void 0:"string"==typeof r?r:"string"==typeof t?t.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){this._$ES=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$E_(),this.requestUpdate(),this.constructor.l?.forEach((t=>t(this)))}addController(t){(this._$EO??=new Set).add(t),void 0!==this.renderRoot&&this.isConnected&&t.hostConnected?.()}removeController(t){this._$EO?.delete(t)}_$E_(){const t=new Map,e=this.constructor.elementProperties;for(const r of e.keys())this.hasOwnProperty(r)&&(t.set(r,this[r]),delete this[r]);t.size>0&&(this._$Ep=t)}createRenderRoot(){const t=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return di(t,this.constructor.elementStyles),t}connectedCallback(){this.renderRoot??=this.createRenderRoot(),this.enableUpdating(!0),this._$EO?.forEach((t=>t.hostConnected?.()))}enableUpdating(t){}disconnectedCallback(){this._$EO?.forEach((t=>t.hostDisconnected?.()))}attributeChangedCallback(t,e,r){this._$AK(t,r)}_$EC(t,e){const r=this.constructor.elementProperties.get(t),i=this.constructor._$Eu(t,r);if(void 0!==i&&!0===r.reflect){const n=(void 0!==r.converter?.toAttribute?r.converter:_i).toAttribute(e,r.type);this._$Em=t,null==n?this.removeAttribute(i):this.setAttribute(i,n),this._$Em=null}}_$AK(t,e){const r=this.constructor,i=r._$Eh.get(t);if(void 0!==i&&this._$Em!==i){const t=r.getPropertyOptions(i),n="function"==typeof t.converter?{fromAttribute:t.converter}:void 0!==t.converter?.fromAttribute?t.converter:_i;this._$Em=i,this[i]=n.fromAttribute(e,t.type),this._$Em=null}}requestUpdate(t,e,r){if(void 0!==t){if(r??=this.constructor.getPropertyOptions(t),!(r.hasChanged??Bi)(this[t],e))return;this.P(t,e,r)}!1===this.isUpdatePending&&(this._$ES=this._$ET())}P(t,e,r){this._$AL.has(t)||this._$AL.set(t,e),!0===r.reflect&&this._$Em!==t&&(this._$Ej??=new Set).add(t)}async _$ET(){this.isUpdatePending=!0;try{await this._$ES}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??=this.createRenderRoot(),this._$Ep){for(const[t,e]of this._$Ep)this[t]=e;this._$Ep=void 0}const t=this.constructor.elementProperties;if(t.size>0)for(const[e,r]of t)!0!==r.wrapped||this._$AL.has(e)||void 0===this[e]||this.P(e,this[e],r)}let t=!1;const e=this._$AL;try{t=this.shouldUpdate(e),t?(this.willUpdate(e),this._$EO?.forEach((t=>t.hostUpdate?.())),this.update(e)):this._$EU()}catch(e){throw t=!1,this._$EU(),e}t&&this._$AE(e)}willUpdate(t){}_$AE(t){this._$EO?.forEach((t=>t.hostUpdated?.())),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(t){return!0}update(t){this._$Ej&&=this._$Ej.forEach((t=>this._$EC(t,this[t]))),this._$EU()}updated(t){}firstUpdated(t){}}Ci.elementStyles=[],Ci.shadowRootOptions={mode:"open"},Ci[Si("elementProperties")]=new Map,Ci[Si("finalized")]=new Map,Ei?.({ReactiveElement:Ci}),(wi.reactiveElementVersions??=[]).push("2.0.4");const Ni=globalThis,xi=Ni.trustedTypes,Ri=xi?xi.createPolicy("lit-html",{createHTML:t=>t}):void 0,Ui="$lit$",Oi=`lit$${Math.random().toFixed(9).slice(2)}$`,Ti="?"+Oi,Pi=`<${Ti}>`,ki=document,Fi=()=>ki.createComment(""),Di=t=>null===t||"object"!=typeof t&&"function"!=typeof t,Li=Array.isArray,zi="[ \t\n\f\r]",qi=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,Hi=/-->/g,Qi=/>/g,ji=RegExp(`>|${zi}(?:([^\\s"'>=/]+)(${zi}*=${zi}*(?:[^ \t\n\f\r"'\`<>=]|("|')|))|$)`,"g"),Yi=/'/g,Ji=/"/g,Gi=/^(?:script|style|textarea|title)$/i,Ki=t=>(e,...r)=>({_$litType$:t,strings:e,values:r}),Wi=Ki(1),Vi=(Ki(2),Symbol.for("lit-noChange")),Xi=Symbol.for("lit-nothing"),Zi=new WeakMap,$i=ki.createTreeWalker(ki,129);function tn(t,e){if(!Array.isArray(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return void 0!==Ri?Ri.createHTML(e):e}const en=(t,e)=>{const r=t.length-1,i=[];let n,s=2===e?"<svg>":"",o=qi;for(let e=0;e<r;e++){const r=t[e];let h,f,u=-1,a=0;for(;a<r.length&&(o.lastIndex=a,f=o.exec(r),null!==f);)a=o.lastIndex,o===qi?"!--"===f[1]?o=Hi:void 0!==f[1]?o=Qi:void 0!==f[2]?(Gi.test(f[2])&&(n=RegExp("</"+f[2],"g")),o=ji):void 0!==f[3]&&(o=ji):o===ji?">"===f[0]?(o=n??qi,u=-1):void 0===f[1]?u=-2:(u=o.lastIndex-f[2].length,h=f[1],o=void 0===f[3]?ji:'"'===f[3]?Ji:Yi):o===Ji||o===Yi?o=ji:o===Hi||o===Qi?o=qi:(o=ji,n=void 0);const l=o===ji&&t[e+1].startsWith("/>")?" ":"";s+=o===qi?r+Pi:u>=0?(i.push(h),r.slice(0,u)+Ui+r.slice(u)+Oi+l):r+Oi+(-2===u?e:l)}return[tn(t,s+(t[r]||"<?>")+(2===e?"</svg>":"")),i]};class rn{constructor({strings:t,_$litType$:e},r){let i;this.parts=[];let n=0,s=0;const o=t.length-1,h=this.parts,[f,u]=en(t,e);if(this.el=rn.createElement(f,r),$i.currentNode=this.el.content,2===e){const t=this.el.content.firstChild;t.replaceWith(...t.childNodes)}for(;null!==(i=$i.nextNode())&&h.length<o;){if(1===i.nodeType){if(i.hasAttributes())for(const t of i.getAttributeNames())if(t.endsWith(Ui)){const e=u[s++],r=i.getAttribute(t).split(Oi),o=/([.?@])?(.*)/.exec(e);h.push({type:1,index:n,name:o[2],strings:r,ctor:"."===o[1]?fn:"?"===o[1]?un:"@"===o[1]?an:hn}),i.removeAttribute(t)}else t.startsWith(Oi)&&(h.push({type:6,index:n}),i.removeAttribute(t));if(Gi.test(i.tagName)){const t=i.textContent.split(Oi),e=t.length-1;if(e>0){i.textContent=xi?xi.emptyScript:"";for(let r=0;r<e;r++)i.append(t[r],Fi()),$i.nextNode(),h.push({type:2,index:++n});i.append(t[e],Fi())}}}else if(8===i.nodeType)if(i.data===Ti)h.push({type:2,index:n});else{let t=-1;for(;-1!==(t=i.data.indexOf(Oi,t+1));)h.push({type:7,index:n}),t+=Oi.length-1}n++}}static createElement(t,e){const r=ki.createElement("template");return r.innerHTML=t,r}}function nn(t,e,r=t,i){if(e===Vi)return e;let n=void 0!==i?r._$Co?.[i]:r._$Cl;const s=Di(e)?void 0:e._$litDirective$;return n?.constructor!==s&&(n?._$AO?.(!1),void 0===s?n=void 0:(n=new s(t),n._$AT(t,r,i)),void 0!==i?(r._$Co??=[])[i]=n:r._$Cl=n),void 0!==n&&(e=nn(t,n._$AS(t,e.values),n,i)),e}class sn{constructor(t,e){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){const{el:{content:e},parts:r}=this._$AD,i=(t?.creationScope??ki).importNode(e,!0);$i.currentNode=i;let n=$i.nextNode(),s=0,o=0,h=r[0];for(;void 0!==h;){if(s===h.index){let e;2===h.type?e=new on(n,n.nextSibling,this,t):1===h.type?e=new h.ctor(n,h.name,h.strings,this,t):6===h.type&&(e=new ln(n,this,t)),this._$AV.push(e),h=r[++o]}s!==h?.index&&(n=$i.nextNode(),s++)}return $i.currentNode=ki,i}p(t){let e=0;for(const r of this._$AV)void 0!==r&&(void 0!==r.strings?(r._$AI(t,r,e),e+=r.strings.length-2):r._$AI(t[e])),e++}}class on{get _$AU(){return this._$AM?._$AU??this._$Cv}constructor(t,e,r,i){this.type=2,this._$AH=Xi,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=r,this.options=i,this._$Cv=i?.isConnected??!0}get parentNode(){let t=this._$AA.parentNode;const e=this._$AM;return void 0!==e&&11===t?.nodeType&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,e=this){t=nn(this,t,e),Di(t)?t===Xi||null==t||""===t?(this._$AH!==Xi&&this._$AR(),this._$AH=Xi):t!==this._$AH&&t!==Vi&&this._(t):void 0!==t._$litType$?this.$(t):void 0!==t.nodeType?this.T(t):(t=>Li(t)||"function"==typeof t?.[Symbol.iterator])(t)?this.k(t):this._(t)}S(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.S(t))}_(t){this._$AH!==Xi&&Di(this._$AH)?this._$AA.nextSibling.data=t:this.T(ki.createTextNode(t)),this._$AH=t}$(t){const{values:e,_$litType$:r}=t,i="number"==typeof r?this._$AC(t):(void 0===r.el&&(r.el=rn.createElement(tn(r.h,r.h[0]),this.options)),r);if(this._$AH?._$AD===i)this._$AH.p(e);else{const t=new sn(i,this),r=t.u(this.options);t.p(e),this.T(r),this._$AH=t}}_$AC(t){let e=Zi.get(t.strings);return void 0===e&&Zi.set(t.strings,e=new rn(t)),e}k(t){Li(this._$AH)||(this._$AH=[],this._$AR());const e=this._$AH;let r,i=0;for(const n of t)i===e.length?e.push(r=new on(this.S(Fi()),this.S(Fi()),this,this.options)):r=e[i],r._$AI(n),i++;i<e.length&&(this._$AR(r&&r._$AB.nextSibling,i),e.length=i)}_$AR(t=this._$AA.nextSibling,e){for(this._$AP?.(!1,!0,e);t&&t!==this._$AB;){const e=t.nextSibling;t.remove(),t=e}}setConnected(t){void 0===this._$AM&&(this._$Cv=t,this._$AP?.(t))}}class hn{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,e,r,i,n){this.type=1,this._$AH=Xi,this._$AN=void 0,this.element=t,this.name=e,this._$AM=i,this.options=n,r.length>2||""!==r[0]||""!==r[1]?(this._$AH=Array(r.length-1).fill(new String),this.strings=r):this._$AH=Xi}_$AI(t,e=this,r,i){const n=this.strings;let s=!1;if(void 0===n)t=nn(this,t,e,0),s=!Di(t)||t!==this._$AH&&t!==Vi,s&&(this._$AH=t);else{const i=t;let o,h;for(t=n[0],o=0;o<n.length-1;o++)h=nn(this,i[r+o],e,o),h===Vi&&(h=this._$AH[o]),s||=!Di(h)||h!==this._$AH[o],h===Xi?t=Xi:t!==Xi&&(t+=(h??"")+n[o+1]),this._$AH[o]=h}s&&!i&&this.j(t)}j(t){t===Xi?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??"")}}class fn extends hn{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===Xi?void 0:t}}class un extends hn{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==Xi)}}class an extends hn{constructor(t,e,r,i,n){super(t,e,r,i,n),this.type=5}_$AI(t,e=this){if((t=nn(this,t,e,0)??Xi)===Vi)return;const r=this._$AH,i=t===Xi&&r!==Xi||t.capture!==r.capture||t.once!==r.once||t.passive!==r.passive,n=t!==Xi&&(r===Xi||i);i&&this.element.removeEventListener(this.name,this,r),n&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){"function"==typeof this._$AH?this._$AH.call(this.options?.host??this.element,t):this._$AH.handleEvent(t)}}class ln{constructor(t,e,r){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=r}get _$AU(){return this._$AM._$AU}_$AI(t){nn(this,t)}}const dn=Ni.litHtmlPolyfillSupport;dn?.(rn,on),(Ni.litHtmlVersions??=[]).push("3.1.4");class cn extends Ci{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){const t=super.createRenderRoot();return this.renderOptions.renderBefore??=t.firstChild,t}update(t){const e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=((t,e,r)=>{const i=r?.renderBefore??e;let n=i._$litPart$;if(void 0===n){const t=r?.renderBefore??null;i._$litPart$=n=new on(e.insertBefore(Fi(),t),t,void 0,r??{})}return n._$AI(t),n})(e,this.renderRoot,this.renderOptions)}connectedCallback(){super.connectedCallback(),this._$Do?.setConnected(!0)}disconnectedCallback(){super.disconnectedCallback(),this._$Do?.setConnected(!1)}render(){return Vi}}cn._$litElement$=!0,cn.finalized=!0,globalThis.litElementHydrateSupport?.({LitElement:cn});const pn=globalThis.litElementPolyfillSupport;pn?.({LitElement:cn}),(globalThis.litElementVersions??=[]).push("4.0.6");const mn=((t,...e)=>{const r=1===t.length?t[0]:e.reduce(((e,r,i)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if("number"==typeof t)return t;throw Error("Value passed to 'css' function must be a 'css' function result: "+t+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(r)+t[i+1]),t[0]);return new li(r,t,ui)})`
  :host {
    display: flex;
    justify-content: center;
    gap: var(--wui-spacing-2xl);
  }

  wui-visual-thumbnail:nth-child(1) {
    z-index: 1;
  }
`;let gn=class extends cn{constructor(){super(...arguments),this.dappImageUrl=i.Hd.state.metadata?.icons,this.walletImageUrl=i.iT.getConnectedWalletImageUrl()}firstUpdated(){const t=this.shadowRoot?.querySelectorAll("wui-visual-thumbnail");t?.[0]&&this.createAnimation(t[0],"translate(18px)"),t?.[1]&&this.createAnimation(t[1],"translate(-18px)")}render(){return Wi`
      <wui-visual-thumbnail
        ?borderRadiusFull=${!0}
        .imageSrc=${this.dappImageUrl?.[0]}
      ></wui-visual-thumbnail>
      <wui-visual-thumbnail .imageSrc=${this.walletImageUrl}></wui-visual-thumbnail>
    `}createAnimation(t,e){t.animate([{transform:"translateX(0px)"},{transform:e}],{duration:1600,easing:"cubic-bezier(0.56, 0, 0.48, 1)",direction:"alternate",iterations:1/0})}};gn.styles=mn,gn=function(t,e,r,i){var n,s=arguments.length,o=s<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,r,i);else for(var h=t.length-1;h>=0;h--)(n=t[h])&&(o=(s<3?n(o):s>3?n(e,r,o):n(e,r))||o);return s>3&&o&&Object.defineProperty(e,r,o),o}([(0,oi.customElement)("w3m-connecting-siwe")],gn);const An={attribute:!0,type:String,converter:_i,reflect:!1,hasChanged:Bi},vn=(t=An,e,r)=>{const{kind:i,metadata:n}=r;let s=globalThis.litPropertyMetadata.get(n);if(void 0===s&&globalThis.litPropertyMetadata.set(n,s=new Map),s.set(r.name,t),"accessor"===i){const{name:i}=r;return{set(r){const n=e.get.call(this);e.set.call(this,r),this.requestUpdate(i,n,t)},init(e){return void 0!==e&&this.P(i,void 0,t),e}}}if("setter"===i){const{name:i}=r;return function(r){const n=this[i];e.call(this,r),this.requestUpdate(i,n,t)}}throw Error("Unsupported decorator location: "+i)};var yn=r(47714),wn=function(t,e,r,i){var n,s=arguments.length,o=s<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,r,i);else for(var h=t.length-1;h>=0;h--)(n=t[h])&&(o=(s<3?n(o):s>3?n(e,r,o):n(e,r))||o);return s>3&&o&&Object.defineProperty(e,r,o),o};let bn=class extends cn{constructor(){super(...arguments),this.dappName=i.Hd.state.metadata?.name,this.isSigning=!1}render(){return this.onRender(),Wi`
      <wui-flex justifyContent="center" .padding=${["2xl","0","xxl","0"]}>
        <w3m-connecting-siwe></w3m-connecting-siwe>
      </wui-flex>
      <wui-flex
        .padding=${["0","4xl","l","4xl"]}
        gap="s"
        justifyContent="space-between"
      >
        <wui-text variant="paragraph-500" align="center" color="fg-100"
          >${this.dappName??"Dapp"} needs to connect to your wallet</wui-text
        >
      </wui-flex>
      <wui-flex
        .padding=${["0","3xl","l","3xl"]}
        gap="s"
        justifyContent="space-between"
      >
        <wui-text variant="small-400" align="center" color="fg-200"
          >Sign this message to prove you own this wallet and proceed. Canceling will disconnect
          you.</wui-text
        >
      </wui-flex>
      <wui-flex .padding=${["l","xl","xl","xl"]} gap="s" justifyContent="space-between">
        <wui-button
          size="lg"
          borderRadius="xs"
          fullWidth
          variant="neutral"
          @click=${this.onCancel.bind(this)}
          data-testid="w3m-connecting-siwe-cancel"
        >
          Cancel
        </wui-button>
        <wui-button
          size="lg"
          borderRadius="xs"
          fullWidth
          variant="main"
          @click=${this.onSign.bind(this)}
          ?loading=${this.isSigning}
          data-testid="w3m-connecting-siwe-sign"
        >
          ${this.isSigning?"Signing...":"Sign"}
        </wui-button>
      </wui-flex>
    `}onRender(){si.state.session&&i.W3.close()}async onSign(){this.isSigning=!0,i.En.sendEvent({event:"CLICK_SIGN_SIWE_MESSAGE",type:"track",properties:{network:i.p_.state.caipNetwork?.id||"",isSmartAccount:i.Uj.state.preferredAccountType===yn.Vl.ACCOUNT_TYPES.SMART_ACCOUNT}});try{si.setStatus("loading");const t=await si.signIn();return si.setStatus("success"),i.En.sendEvent({event:"SIWE_AUTH_SUCCESS",type:"track",properties:{network:i.p_.state.caipNetwork?.id||"",isSmartAccount:i.Uj.state.preferredAccountType===yn.Vl.ACCOUNT_TYPES.SMART_ACCOUNT}}),t}catch(t){const e=i.Uj.state.preferredAccountType===yn.Vl.ACCOUNT_TYPES.SMART_ACCOUNT;return e?i.Pt.showError("This application might not support Smart Accounts"):i.Pt.showError("Signature declined"),si.setStatus("error"),i.En.sendEvent({event:"SIWE_AUTH_ERROR",type:"track",properties:{network:i.p_.state.caipNetwork?.id||"",isSmartAccount:e}})}finally{this.isSigning=!1}}async onCancel(){i.Uj.state.isConnected?(await i.x4.disconnect(),i.W3.close()):i.IN.push("Connect"),i.En.sendEvent({event:"CLICK_CANCEL_SIWE",type:"track",properties:{network:i.p_.state.caipNetwork?.id||"",isSmartAccount:i.Uj.state.preferredAccountType===yn.Vl.ACCOUNT_TYPES.SMART_ACCOUNT}})}};wn([function(t){return(e,r)=>"object"==typeof r?vn(t,e,r):((t,e,r)=>{const i=e.hasOwnProperty(r);return e.constructor.createProperty(r,i?{...t,wrapped:!0}:t),i?Object.getOwnPropertyDescriptor(e,r):void 0})(t,e,r)}({state:!0,attribute:!1})],bn.prototype,"isSigning",void 0),bn=wn([(0,oi.customElement)("w3m-connecting-siwe-view")],bn)}}]);