name: Docker Image Go-Fula Test

on:
  workflow_dispatch:

jobs:

  build_go_fula:

    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ORG_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build the Docker images
        run: docker buildx create --name multiarch --driver docker-container --use --node multiarch0
      - name: Build go-fula
        run: cd ./docker && source env_test.sh && cd ./go-fula && bash ./build.sh
      - name: Download go-fula
        run: cd ./docker && source env_test.sh && cd ./go-fula && bash ./download_image.sh
      - name: Get Release Info
        id: get-release-info
        uses: actions/github-script@v5
        with:
          script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
               owner: context.repo.owner,
               repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;
      - name: Print release upload URL
        run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      - name: Upload go-fula Image to Release
        uses: actions/github-script@v5
        env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
            script: |
              const fs = require('fs');
              // Upload the release asset
              await github.rest.repos.uploadReleaseAsset({
                owner: context.repo.owner,
                repo: context.repo.repo,
                release_id: ${{ steps.get-release-info.outputs.result }},
                name: "go-fula.tar",
                data: await fs.readFileSync("./docker/go-fula/dockers/go-fula.tar")
              });
