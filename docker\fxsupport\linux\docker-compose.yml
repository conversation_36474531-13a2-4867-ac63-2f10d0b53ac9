version: "3.9"
services:

  watchtower:
    image: containrrr/watchtower
    restart: always
    container_name: fula_updater
    dns:
      - *******
      - *******
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /etc/timezone:/etc/timezone:ro
    environment:
      - WATCHTOWER_DEBUG=true
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_LABEL_ENABLE=true
      - WATCHTOWER_INCLUDE_RESTARTING=true
      - WATCHTOWER_INCLUDE_STOPPED=true
      - WATCHTOWER_NO_PULL=false
      - WATCHTOWER_MONITOR_ONLY=false
      - WATCHTOWER_POLL_INTERVAL=3600
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  kubo:
    image: ipfs/kubo:release
    container_name: ipfs_host
    restart: always
    depends_on:
      - fxsupport
    privileged: true
    volumes:
      - shared-volume-external:/uniondrive:rw
      - /media/pi:/storage:rw,shared
      - /home/<USER>/.internal:/internal:rw,shared
      - /usr/bin/fula/kubo:/container-init.d:rw,shared,uid=1000,gid=1000
      - /uniondrive/ipfs_staging:/export:rw,shared,uid=1000,gid=1000
    ports:
      - "4001:4001"
      - "4001:4001/udp"
      - "127.0.0.1:8081:8081"
      - "127.0.0.1:5001:5001"
    environment:
      - IPFS_PROFILE=flatfs
      - IPFS_PATH=/internal/ipfs_data
    dns:
      - *******
      - *******
    cap_add:
      - ALL
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"
  
  ipfs-cluster:
    image: functionland/ipfs-cluster:release
    container_name: ipfs_cluster
    restart: always
    #extra_hosts:
    #- "host.docker.internal:host-gateway"
    depends_on:
      - kubo
    volumes:
      - shared-volume-external:/uniondrive:rw
      - /home/<USER>/.internal:/internal:rw,shared
      - /usr/bin/fula/ipfs-cluster:/container-init.d:rw,shared,uid=1000,gid=1000
      - /usr/bin/fula/.env.cluster:/.env.cluster
    entrypoint: /container-init.d/ipfs-cluster-container-init.d.sh
    network_mode: "host"
    ports:
      - "9094:9094" # API port
      - "9095:9095" # Proxy API port
      - "9096:9096" # Cluster swarm port
    dns:
      - *******
      - *******
    privileged: true
    env_file:
      - .env.cluster
    cap_add:
      - ALL
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  go-fula:
    image: $GO_FULA
    restart: always
    container_name: fula_go
    volumes:
      - /media/pi/:/storage:rw,rshared
      - /var/run/dbus:/var/run/dbus
      - $WPA_SUPLICANT_PATH/wpa_supplicant.conf:$WPA_SUPLICANT_PATH/wpa_supplicant.conf
      - /home/<USER>/.internal/:/internal:rw,rshared
      - shared-volume-external:/uniondrive:rw
      - /var/run/docker.sock:/var/run/docker.sock
      - /etc/NetworkManager/system-connections:/etc/NetworkManager/system-connections
      - /home/<USER>/:/home:rw,rshared
      - /usr/bin/fula/.env.cluster:/.env.cluster
      - /usr/bin/fula/.env.gofula:/.env
    network_mode: "host"
    ports:
      - "40001:40001" #libp2p port
      - "3500:3500" #Wap http server
    devices:
       - /dev/fuse:/dev/fuse:rwm 
    cap_add:
      - ALL
    privileged: true
    dns:
      - *******
      - *******
    depends_on:
      - fxsupport
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"
  
  node:
    image: $SUGARFUNGE_NODE
    restart: always
    container_name: fula_node
    depends_on:
      - fxsupport
      - kubo
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    volumes:
      - /home/<USER>/.internal/:/internal:rw,rshared
      - shared-volume-external:/uniondrive:rw
      - /home/<USER>/:/home:rw,rshared
      - /media/pi/:/storage:rw,rshared
      - /var/run/dbus:/var/run/dbus
    dns:
      - *******
      - *******
    privileged: true
    # TODO: Correct ipfs inside node to accept localhost as parameter instead of using 127.0.0.1 to remove host mode and use host.docker.internal:5001
    network_mode: "host"
    # extra_hosts:
    # - "host.docker.internal:host-gateway"
    ports:
      - "127.0.0.1:4000:4000" # API port
      - "9945:9945" # Node Rpc Port
      - "30335:30335" #Node Port
    devices:
       - /dev/fuse:/dev/fuse:rwm 
    cap_add:
      - ALL
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  fxsupport:
    image: $FX_SUPPROT
    restart: always
    container_name: fula_fxsupport
    command: tail -F /dev/null
    dns:
      - *******
      - *******
    volumes:
      - /media/pi/:/storage:rw,rshared
      - /home/<USER>/.internal/:/internal:rw,rshared
      - /home/<USER>/:/home:rw,rshared
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    depends_on:
      - watchtower
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"
volumes:
 shared-volume:
 shared-volume-external:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /uniondrive