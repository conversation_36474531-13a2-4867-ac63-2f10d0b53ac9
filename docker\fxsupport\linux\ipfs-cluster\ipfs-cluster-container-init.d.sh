#!/bin/sh

# This scripts runs before ipfs container
set -ex

fula_file_path="/internal/config.yaml"
temp_file="/internal/.env.cluster.tmp"

log() {
  echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

check_files_and_folders() {
  if [ -d "/internal" ] && [ -d "/uniondrive" ] && [ -f "/internal/config.yaml" ] && [ -f "/internal/.ipfscluster_setup" ] && [ -f "/uniondrive/ipfs-cluster/identity.json" ]; then
    return 0 # Explicitly return success
  else
    return 1 # Explicitly return failure
  fi
}

check_writable() {
  # Try to create a temporary file
  touch "/uniondrive/.tmp3_write_check"
  
  # Check if the file exists after attempting to create it
  if [ -f "/uniondrive/.tmp3_write_check" ]; then
    # Attempt to remove the file regardless of the outcome
    rm -f "/uniondrive/.tmp3_write_check"
    # Return success even if 'rm' fails
    return 0
  else
    # Return failure if the file could not be created
    return 1
  fi
}

append_or_replace() {
    return 0
}

while ! check_files_and_folders || ! check_writable; do
  log "Waiting for /internal and /uniondrive to become available and writable..."
  sleep 5
done

poolName=""
# URL to check
cluster_url="https://api.node3.functionyard.fula.network" #This can be changed to blockchain api if we add metadata to each pool with 'creator_clusterpeerid' for example


# Wait for CLUSTER_SECRET to be set
poolName=$(grep 'poolName:' "${fula_file_path}" | cut -d':' -f2 | tr -d ' "')
while [ -z "$poolName" ] || [ "$poolName" = "0" ]; do
    echo "Waiting for CLUSTER_CLUSTERNAME to be set..."
    if [ -f "$fula_file_path" ];then
        poolName=$(grep 'poolName:' "${fula_file_path}" | cut -d':' -f2 | tr -d ' "')
    fi
    sleep 60
done

get_poolcreator_peerid() {
  # Step 5: Set CLUSTER_CRDT_TRUSTEDPEERS
  export CLUSTER_CRDT_TRUSTEDPEERS="12D3KooWS79EhkPU7ESUwgG4vyHHzW9FDNZLoWVth9b5N5NSrvaj"
}


export CLUSTER_CLUSTERNAME="${poolName}"
append_or_replace "/.env.cluster" "CLUSTER_CLUSTERNAME" "${CLUSTER_CLUSTERNAME}"


echo "CLUSTER_CLUSTERNAME is set."
secret=$(printf "%s" "${CLUSTER_CLUSTERNAME}" | sha256sum | cut -d' ' -f1)
export CLUSTER_SECRET="${secret}"
append_or_replace "/.env.cluster" "CLUSTER_SECRET" "${CLUSTER_SECRET}"

node_account=$(cat "/internal/.secrets/account.txt")
export CLUSTER_PEERNAME="${node_account}" #This is the node Aura account id
append_or_replace "/.env.cluster" "CLUSTER_PEERNAME" "${CLUSTER_PEERNAME}"
    
    mkdir -p /uniondrive/.tmp
    get_poolcreator_peerid
    append_or_replace "/.env.cluster" "CLUSTER_CRDT_TRUSTEDPEERS" "${CLUSTER_CRDT_TRUSTEDPEERS}"
    # Initialize ipfs-cluster-service if the configuration does not exist
    if [ ! -f "${IPFS_CLUSTER_PATH}/service.json" ]; then
        echo "Initializing ipfs-cluster-service..."
        /usr/local/bin/ipfs-cluster-service init
    fi

    if [ -f "${IPFS_CLUSTER_PATH}/service.json" ]; then
        echo "Modifying service.json to replace allocator and informer sections..."

        # Use jq to update the JSON file
        jq '
            .allocator = {
                "balanced": {
                    "allocate_by": [
                        "tag:group",
                        "pinqueue",
                        "reposize"
                    ]
                }
            } |
            .informer = {
                "disk": {
                    "metric_ttl": "30s",
                    "metric_type": "reposize"
                },
                "pinqueue": {
                    "metric_ttl": "30s",
                    "weight_bucket_size": 100000
                },
                "tags": {
                    "metric_ttl": "30s",
                    "tags": {
                        "group": "default"
                    }
                }
            }
        ' "${IPFS_CLUSTER_PATH}/service.json" > "${IPFS_CLUSTER_PATH}/service_temp.json"

        mv "${IPFS_CLUSTER_PATH}/service_temp.json" "${IPFS_CLUSTER_PATH}/service.json"

        echo "Modification completed."
    fi

    # Check if CLUSTER_CRDT_TRUSTEDPEERS is not empty
    if [ -n "${CLUSTER_CRDT_TRUSTEDPEERS}" ]; then
        echo "CLUSTER_CRDT_TRUSTEDPEERS is set to ${CLUSTER_CRDT_TRUSTEDPEERS}. Bootstrapping..."
        export CLUSTER_FOLLOWERMODE=true
        append_or_replace "/.env.cluster" "CLUSTER_FOLLOWERMODE" "${CLUSTER_FOLLOWERMODE}"
        
        echo "/dns4/${poolName}.pools.functionyard.fula.network/tcp/9096/p2p/${CLUSTER_CRDT_TRUSTEDPEERS}" > "${IPFS_CLUSTER_PATH}/peerstore"
        /usr/local/bin/ipfs-cluster-service daemon --upgrade --bootstrap "/dns4/${poolName}.pools.functionyard.fula.network/tcp/9096/p2p/${CLUSTER_CRDT_TRUSTEDPEERS}" --leave
    else
        /usr/local/bin/ipfs-cluster-service daemon --upgrade
    fi
rm /uniondrive/.tmp/pool_creator.json || true
rm /uniondrive/.tmp/pool_users.json || true
rm /uniondrive/.tmp/pool_data.json || true