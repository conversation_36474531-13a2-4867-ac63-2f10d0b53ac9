name: Docker Image CI

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  upload_Watchtowerrr:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ORG_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Pull Watchtower Docker Image
        run: docker pull --platform=linux/arm64 containrrr/watchtower:latest
      - name: Save Watchtower Image as Tar File
        run: docker save containrrr/watchtower:latest -o ./watchtower.tar
      - name: Get Release Info
        id: get-release-info
        uses: actions/github-script@v5
        with:
          script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
               owner: context.repo.owner,
               repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;

      - name: Print release upload URL
        run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      - name: Upload Watchtower Image to Release
        uses: actions/github-script@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const fs = require('fs');
            // Upload the release asset
            await github.rest.repos.uploadReleaseAsset({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ steps.get-release-info.outputs.result }},
              name: "watchtower.tar",
              data: await fs.readFileSync("./watchtower.tar")
            });

  upload_ipfs_cluster:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ORG_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build ipfs-cluster
        run: cd ./docker && source env_release.sh && cd ./ipfs-cluster && bash ./build.sh
      - name: Download ipfs-cluster
        run: cd ./docker && source env_release.sh && cd ./ipfs-cluster && bash ./download_image.sh
      - name: Get Release Info
        id: get-release-info
        uses: actions/github-script@v5
        with:
          script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
               owner: context.repo.owner,
               repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;
      - name: Print release upload URL
        run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      - name: Upload ipfs-cluster Image to Release
        uses: actions/github-script@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const fs = require('fs');
            // Upload the release asset
            await github.rest.repos.uploadReleaseAsset({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ steps.get-release-info.outputs.result }},
              name: "ipfs-cluster.tar",
              data: await fs.readFileSync("./docker/ipfs-cluster/dockers/ipfs-cluster.tar")
            });

  upload_Kubo:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ORG_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Pull Kubo Docker Image
        run: docker pull --platform=linux/arm64 ipfs/kubo:release
      - name: Save Kubo Image as Tar File
        run: docker save ipfs/kubo:release -o ./kubo.tar
      - name: Get Release Info
        id: get-release-info
        uses: actions/github-script@v5
        with:
          script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
               owner: context.repo.owner,
               repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;

      - name: Print release upload URL
        run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      - name: Upload Kubo Image to Release
        uses: actions/github-script@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const fs = require('fs');
            // Upload the release asset
            await github.rest.repos.uploadReleaseAsset({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ steps.get-release-info.outputs.result }},
              name: "kubo.tar",
              data: await fs.readFileSync("./kubo.tar")
            });

  build_fxsupport:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ORG_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build fxsupport
        run: cd ./docker && source env_release.sh && cd ./fxsupport && bash ./build.sh
      - name: Download fxsupport
        run: cd ./docker && source env_release.sh && cd ./fxsupport && bash ./download_image.sh
      - name: Get Release Info
        id: get-release-info
        uses: actions/github-script@v5
        with:
          script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
               owner: context.repo.owner,
               repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;
      - name: Print release upload URL
        run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      - name: Upload fxsupport Image to Release
        uses: actions/github-script@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const fs = require('fs');
            // Upload the release asset
            await github.rest.repos.uploadReleaseAsset({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ steps.get-release-info.outputs.result }},
              name: "fxsupport.tar",
              data: await fs.readFileSync("./docker/fxsupport/dockers/fxsupport.tar")
            });

  build_go_fula:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ORG_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build go-fula
        run: cd ./docker && source env_release.sh && cd ./go-fula && bash ./build.sh
      - name: Download go-fula
        run: cd ./docker && source env_release.sh && cd ./go-fula && bash ./download_image.sh
      - name: Get Release Info
        id: get-release-info
        uses: actions/github-script@v5
        with:
          script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
               owner: context.repo.owner,
               repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;
      - name: Print release upload URL
        run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      - name: Upload go-fula Image to Release
        uses: actions/github-script@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const fs = require('fs');
            // Upload the release asset
            await github.rest.repos.uploadReleaseAsset({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ steps.get-release-info.outputs.result }},
              name: "go-fula.tar",
              data: await fs.readFileSync("./docker/go-fula/dockers/go-fula.tar")
            });

  build_node:
    runs-on: self-hosted

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ORG_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build node
        run: cd ./docker && source env_release.sh && cd ./sugarfunge-node && bash ./build.sh
      - name: Download node
        run: cd ./docker && source env_release.sh && cd ./sugarfunge-node && bash ./download_image.sh
      - name: Get Release Info
        id: get-release-info
        uses: actions/github-script@v5
        with:
          script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
               owner: context.repo.owner,
               repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;
      - name: Print release upload URL
        run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      - name: Upload node Image to Release
        uses: actions/github-script@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const fs = require('fs');
            // Upload the release asset
            await github.rest.repos.uploadReleaseAsset({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ steps.get-release-info.outputs.result }},
              name: "node.tar",
              data: await fs.readFileSync("./docker/sugarfunge-node/dockers/node.tar")
            });
