<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join a Pool</title>
    <link rel="stylesheet" href="/webui/public/css/styles.css">
</head>
<body>
    <div class="container">
        <h1>Join a Pool</h1>
        <div id="sync-status-section">
            <p id="sync-status-text">Chain is Syncing: <span id="sync-progress">0</span>%</p>
            <progress id="sync-progress-bar" value="0" max="100"></progress>
        </div>
        <div id="info-box" class="info-box" style="display: none;">
            <p>In order to join pools, you need to be invited by a blox owner or hold an #FEC NFT.</p>
            <p>
                1. If you are an NFT holder, you can go to <a href="https://fund.functionyard.fula.network" target="_blank">https://fund.functionyard.fula.network</a> and join the testnet.<br>
                2. If you know a blox owner, provide your account id <span id="blox-account-id" class="clickable"></span> to them and they can add you to the testnet.
            </p>
        </div>
        <div id="search-section">
            <input type="text" id="search-input" placeholder="Search pools...">
            <button id="search-button">Search</button>
        </div>
        <div id="pools-list"></div>
        <div class="button-container">
            <button id="refresh-status-button" class="button">Refresh Status</button>
            <button id="go-home-button" class="button disabled" disabled>Go to Home</button>
        </div>
    </div>
    <footer class="footer">
        <p>Version: <span id="version-number"></span>
            <button id="reset-button" class="reset-button">&#8635;</button>
        </p>
    </footer>    
    <script src="/webui/public/js/bundle/pools.bundle.js"></script>
    <script src="/webui/public/js/bundle/common.bundle.js"></script>
</body>
</html>
