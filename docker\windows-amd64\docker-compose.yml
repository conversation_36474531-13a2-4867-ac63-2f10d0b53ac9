version: '3.9'
services:
  watchtower:
    image: containrrr/watchtower
    restart: always
    container_name: fula_updater
    dns:
      - *******
      - *******
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${env:InstallationPath}/timezone:/etc/timezone:ro
    environment:
      - WATCHTOWER_DEBUG=true
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_LABEL_ENABLE=true
      - WATCHTOWER_INCLUDE_RESTARTING=true
      - WATCHTOWER_INCLUDE_STOPPED=true
      - WATCHTOWER_NO_PULL=false
      - WATCHTOWER_MONITOR_ONLY=false
      - WATCHTOWER_POLL_INTERVAL=3600
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  kubo:
    image: ipfs/kubo:release
    container_name: ipfs_host
    restart: always
    depends_on:
      - fxsupport
    privileged: true
    volumes:
      - ${env:ExternalDrive}/uniondrive:/uniondrive:rw,shared
      - ${env:InstallationPath}/media:/storage:rw,shared
      - ${env:InstallationPath}/.internal:/internal:rw,shared
      - ${env:InstallationPath}/kubo:/container-init.d:rw,shared
      - ${env:ExternalDrive}/uniondrive/ipfs_staging:/export:rw,shared
    ports:
      - "4001:4001"
      - "4001:4001/udp"
      - "127.0.0.1:8181:8181"
      - "127.0.0.1:5001:5001"
    environment:
      - IPFS_PROFILE=flatfs
      - IPFS_PATH=/internal/ipfs_data
    dns:
      - *******
      - *******
    cap_add:
      - ALL
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  ipfs-cluster:
    image: functionland/ipfs-cluster:release_amd64
    container_name: ipfs_cluster
    restart: always
    depends_on:
      - kubo
    volumes:
      - ${env:ExternalDrive}/uniondrive:/uniondrive:rw,shared
      - ${env:InstallationPath}/.internal:/internal:rw,shared
      - ${env:InstallationPath}/ipfs-cluster:/container-init.d:rw,shared
      - ${env:envDir}/.env.cluster:/.env.cluster
    entrypoint: /container-init.d/ipfs-cluster-container-init.d.sh
    network_mode: "host"
    ports:
      - "9094:9094" # API port
      - "9095:9095" # Proxy API port
      - "9096:9096" # Cluster swarm port
    dns:
      - *******
      - *******
    privileged: true
    env_file:
      - ${env:envDir}/.env.cluster
    cap_add:
      - ALL
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  go-fula:
    image: ${GO_FULA}
    restart: always
    container_name: fula_go
    volumes:
      - ${env:InstallationPath}/media:/storage:rw,shared
      - ${env:InstallationPath}/.internal:/internal:rw,shared
      - ${env:ExternalDrive}/uniondrive:/uniondrive:rw,shared
      - /var/run/docker.sock:/var/run/docker.sock
      - ${env:envDir}/.env.cluster:/.env.cluster
      - ${env:envDir}/.env.gofula:/.env
    network_mode: "host"
    ports:
      - "40001:40001" #libp2p port
      - "3500:3500" #Wap http server
    devices:
       - /dev/fuse:/dev/fuse:rwm 
    cap_add:
      - ALL
    privileged: true
    dns:
      - *******
      - *******
    depends_on:
      - fxsupport
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  node:
    image: ${SUGARFUNGE_NODE}
    restart: always
    container_name: fula_node
    depends_on:
      - fxsupport
      - kubo
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    volumes:
      - ${env:InstallationPath}/.internal:/internal:rw,shared
      - ${env:ExternalDrive}/uniondrive:/uniondrive:rw,shared
      - ${env:InstallationPath}:/home:rw,shared
      - ${env:InstallationPath}/media:/storage:rw,shared
    dns:
      - *******
      - *******
    privileged: true
    network_mode: "host"
    ports:
      - "127.0.0.1:4000:4000" # API port
      - "9945:9945" # Node Rpc Port
      - "30335:30335" #Node Port
    devices:
       - /dev/fuse:/dev/fuse:rwm 
    cap_add:
      - ALL
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"

  fxsupport:
    image: ${FX_SUPPROT}
    restart: always
    container_name: fula_fxsupport
    command: tail -F /dev/null
    dns:
      - *******
      - *******
    volumes:
      - ${env:InstallationPath}/media:/storage:rw,shared
      - ${env:InstallationPath}/.internal:/internal:rw,shared
      - ${env:InstallationPath}:/home:rw,shared
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    depends_on:
      - watchtower
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "5"
