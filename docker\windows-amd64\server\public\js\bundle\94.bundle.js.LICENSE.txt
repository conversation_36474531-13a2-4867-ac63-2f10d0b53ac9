/*! *****************************************************************************
            Copyright (c) Microsoft Corporation.

            Permission to use, copy, modify, and/or distribute this software for any
            purpose with or without fee is hereby granted.

            THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
            REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
            AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
            INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
            LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
            OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
            PERFORMANCE OF THIS SOFTWARE.
            ***************************************************************************** */

/**
            	 * Checks if an event is supported in the current execution environment.
            	 *
            	 * NOTE: This will not work correctly for non-generic events such as `change`,
            	 * `reset`, `load`, `error`, and `select`.
            	 *
            	 * Borrows from Modernizr.
            	 *
            	 * @param {string} eventNameSuffix Event name, e.g. "click".
            	 * @return {boolean} True if the event is supported.
            	 * @internal
            	 * @license Modernizr 3.0.0pre (Custom Build) | MIT
            	 */

/**
             * @license React
             * react-dom.production.min.js
             *
             * Copyright (c) Facebook, Inc. and its affiliates.
             *
             * This source code is licensed under the MIT license found in the
             * LICENSE file in the root directory of this source tree.
             */
