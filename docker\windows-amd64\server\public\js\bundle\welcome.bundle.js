document.addEventListener("DOMContentLoaded",(function(){fetch("/api/properties").then((function(e){return e.json()})).then((function(e){var t=document.querySelector("#properties-table tbody");[{name:"Fula Image Date",value:e.containerInfo_fula.created},{name:"FxSupport Image Date",value:e.containerInfo_fxsupport.created},{name:"Node Image Date",value:e.containerInfo_node.created},{name:"Hardware ID",value:e.hardwareID},{name:"OTA Version",value:e.ota_version}].forEach((function(e){var n=document.createElement("tr");n.innerHTML="\n                    <td>".concat(e.name,"</td>\n                    <td>").concat(e.value,"</td>\n                "),t.appendChild(n)}))})).catch((function(e){return console.error("Error fetching properties:",e)})),document.getElementById("view-terms").addEventListener("click",(function(){window.open("https://fx.land/terms","_blank")})),document.getElementById("accept-terms").addEventListener("click",(function(){localStorage.setItem("setup_started","true"),window.location.href="/webui/connect-to-wallet"}))}));