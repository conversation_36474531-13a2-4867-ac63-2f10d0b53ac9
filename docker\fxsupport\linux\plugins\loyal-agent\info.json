{"name": "loyal-agent", "description": "This is a local version of AI agent that runs locally on your blox using NPU", "version": "101", "usage": {"storage": "medium", "compute": "low", "bandwidth": "low", "ram": "high", "gpu": "high"}, "rewards": [{"type": "none", "currency": "", "link": ""}], "socials": [{"telegram": "", "twitter": "", "email": "", "website": "", "discord": ""}], "instructions": [{"order": 1, "description": "Ensure you have 32GB of RAM and 10GB of storage", "url": ""}, {"order": 2, "description": "<PERSON><PERSON> Install"}, {"order": 3, "description": "After installation is finished, please give it some minutes to finalize downloading hte model before using it"}], "requiredInputs": [{"name": "ai-model", "instructions": "Currently only deepseek model is available", "type": "string", "default": "deepseek-llm-7b-chat-rk3588-w8a8_g256-opt-1-hybrid-ratio-0.5.rkllm"}], "outputs": [], "approved": true}