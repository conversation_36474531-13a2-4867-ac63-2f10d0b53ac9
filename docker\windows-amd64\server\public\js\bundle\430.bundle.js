/*! For license information please see 430.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunkfula_webui=self.webpackChunkfula_webui||[]).push([[430],{3430:(t,e,o)=>{o.r(e),o.d(e,{WcmModal:()=>gr,WcmQrCode:()=>Co});const r=window,i=r.ShadowRoot&&(void 0===r.ShadyCSS||r.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,a=Symbol(),n=new WeakMap;class l{constructor(t,e,o){if(this._$cssResult$=!0,o!==a)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t,this.t=e}get styleSheet(){let t=this.o;const e=this.t;if(i&&void 0===t){const o=void 0!==e&&1===e.length;o&&(t=n.get(e)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),o&&n.set(e,t))}return t}toString(){return this.cssText}}const s=(t,...e)=>{const o=1===t.length?t[0]:e.reduce(((e,o,r)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if("number"==typeof t)return t;throw Error("Value passed to 'css' function must be a 'css' function result: "+t+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(o)+t[r+1]),t[0]);return new l(o,t,a)},c=i?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e="";for(const o of t.cssRules)e+=o.cssText;return(t=>new l("string"==typeof t?t:t+"",void 0,a))(e)})(t):t;var d;const h=window,m=h.trustedTypes,p=m?m.emptyScript:"",u=h.reactiveElementPolyfillSupport,w={toAttribute(t,e){switch(e){case Boolean:t=t?p:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let o=t;switch(e){case Boolean:o=null!==t;break;case Number:o=null===t?null:Number(t);break;case Object:case Array:try{o=JSON.parse(t)}catch(t){o=null}}return o}},g=(t,e)=>e!==t&&(e==e||t==t),v={attribute:!0,type:String,converter:w,reflect:!1,hasChanged:g},f="finalized";class b extends HTMLElement{constructor(){super(),this._$Ei=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this._$El=null,this._$Eu()}static addInitializer(t){var e;this.finalize(),(null!==(e=this.h)&&void 0!==e?e:this.h=[]).push(t)}static get observedAttributes(){this.finalize();const t=[];return this.elementProperties.forEach(((e,o)=>{const r=this._$Ep(o,e);void 0!==r&&(this._$Ev.set(r,o),t.push(r))})),t}static createProperty(t,e=v){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){const o="symbol"==typeof t?Symbol():"__"+t,r=this.getPropertyDescriptor(t,o,e);void 0!==r&&Object.defineProperty(this.prototype,t,r)}}static getPropertyDescriptor(t,e,o){return{get(){return this[e]},set(r){const i=this[t];this[e]=r,this.requestUpdate(t,i,o)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||v}static finalize(){if(this.hasOwnProperty(f))return!1;this[f]=!0;const t=Object.getPrototypeOf(this);if(t.finalize(),void 0!==t.h&&(this.h=[...t.h]),this.elementProperties=new Map(t.elementProperties),this._$Ev=new Map,this.hasOwnProperty("properties")){const t=this.properties,e=[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)];for(const o of e)this.createProperty(o,t[o])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const o=new Set(t.flat(1/0).reverse());for(const t of o)e.unshift(c(t))}else void 0!==t&&e.push(c(t));return e}static _$Ep(t,e){const o=e.attribute;return!1===o?void 0:"string"==typeof o?o:"string"==typeof t?t.toLowerCase():void 0}_$Eu(){var t;this._$E_=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$Eg(),this.requestUpdate(),null===(t=this.constructor.h)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,o;(null!==(e=this._$ES)&&void 0!==e?e:this._$ES=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(o=t.hostConnected)||void 0===o||o.call(t))}removeController(t){var e;null===(e=this._$ES)||void 0===e||e.splice(this._$ES.indexOf(t)>>>0,1)}_$Eg(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this._$Ei.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t;const e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return((t,e)=>{i?t.adoptedStyleSheets=e.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):e.forEach((e=>{const o=document.createElement("style"),i=r.litNonce;void 0!==i&&o.setAttribute("nonce",i),o.textContent=e.cssText,t.appendChild(o)}))})(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this._$ES)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)}))}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this._$ES)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)}))}attributeChangedCallback(t,e,o){this._$AK(t,o)}_$EO(t,e,o=v){var r;const i=this.constructor._$Ep(t,o);if(void 0!==i&&!0===o.reflect){const a=(void 0!==(null===(r=o.converter)||void 0===r?void 0:r.toAttribute)?o.converter:w).toAttribute(e,o.type);this._$El=t,null==a?this.removeAttribute(i):this.setAttribute(i,a),this._$El=null}}_$AK(t,e){var o;const r=this.constructor,i=r._$Ev.get(t);if(void 0!==i&&this._$El!==i){const t=r.getPropertyOptions(i),a="function"==typeof t.converter?{fromAttribute:t.converter}:void 0!==(null===(o=t.converter)||void 0===o?void 0:o.fromAttribute)?t.converter:w;this._$El=i,this[i]=a.fromAttribute(e,t.type),this._$El=null}}requestUpdate(t,e,o){let r=!0;void 0!==t&&(((o=o||this.constructor.getPropertyOptions(t)).hasChanged||g)(this[t],e)?(this._$AL.has(t)||this._$AL.set(t,e),!0===o.reflect&&this._$El!==t&&(void 0===this._$EC&&(this._$EC=new Map),this._$EC.set(t,o))):r=!1),!this.isUpdatePending&&r&&(this._$E_=this._$Ej())}async _$Ej(){this.isUpdatePending=!0;try{await this._$E_}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var t;if(!this.isUpdatePending)return;this.hasUpdated,this._$Ei&&(this._$Ei.forEach(((t,e)=>this[e]=t)),this._$Ei=void 0);let e=!1;const o=this._$AL;try{e=this.shouldUpdate(o),e?(this.willUpdate(o),null===(t=this._$ES)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(o)):this._$Ek()}catch(t){throw e=!1,this._$Ek(),t}e&&this._$AE(o)}willUpdate(t){}_$AE(t){var e;null===(e=this._$ES)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$Ek(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$E_}shouldUpdate(t){return!0}update(t){void 0!==this._$EC&&(this._$EC.forEach(((t,e)=>this._$EO(e,this[e],t))),this._$EC=void 0),this._$Ek()}updated(t){}firstUpdated(t){}}var y;b[f]=!0,b.elementProperties=new Map,b.elementStyles=[],b.shadowRootOptions={mode:"open"},null==u||u({ReactiveElement:b}),(null!==(d=h.reactiveElementVersions)&&void 0!==d?d:h.reactiveElementVersions=[]).push("1.6.3");const x=window,$=x.trustedTypes,C=$?$.createPolicy("lit-html",{createHTML:t=>t}):void 0,A="$lit$",_=`lit$${(Math.random()+"").slice(9)}$`,k="?"+_,O=`<${k}>`,E=document,I=()=>E.createComment(""),P=t=>null===t||"object"!=typeof t&&"function"!=typeof t,M=Array.isArray,S="[ \t\n\f\r]",L=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,R=/-->/g,T=/>/g,j=RegExp(`>|${S}(?:([^\\s"'>=/]+)(${S}*=${S}*(?:[^ \t\n\f\r"'\`<>=]|("|')|))|$)`,"g"),W=/'/g,D=/"/g,N=/^(?:script|style|textarea|title)$/i,U=t=>(e,...o)=>({_$litType$:t,strings:e,values:o}),H=U(1),B=U(2),Z=Symbol.for("lit-noChange"),z=Symbol.for("lit-nothing"),V=new WeakMap,F=E.createTreeWalker(E,129,null,!1);function q(t,e){if(!Array.isArray(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return void 0!==C?C.createHTML(e):e}const K=(t,e)=>{const o=t.length-1,r=[];let i,a=2===e?"<svg>":"",n=L;for(let e=0;e<o;e++){const o=t[e];let l,s,c=-1,d=0;for(;d<o.length&&(n.lastIndex=d,s=n.exec(o),null!==s);)d=n.lastIndex,n===L?"!--"===s[1]?n=R:void 0!==s[1]?n=T:void 0!==s[2]?(N.test(s[2])&&(i=RegExp("</"+s[2],"g")),n=j):void 0!==s[3]&&(n=j):n===j?">"===s[0]?(n=null!=i?i:L,c=-1):void 0===s[1]?c=-2:(c=n.lastIndex-s[2].length,l=s[1],n=void 0===s[3]?j:'"'===s[3]?D:W):n===D||n===W?n=j:n===R||n===T?n=L:(n=j,i=void 0);const h=n===j&&t[e+1].startsWith("/>")?" ":"";a+=n===L?o+O:c>=0?(r.push(l),o.slice(0,c)+A+o.slice(c)+_+h):o+_+(-2===c?(r.push(void 0),e):h)}return[q(t,a+(t[o]||"<?>")+(2===e?"</svg>":"")),r]};class Q{constructor({strings:t,_$litType$:e},o){let r;this.parts=[];let i=0,a=0;const n=t.length-1,l=this.parts,[s,c]=K(t,e);if(this.el=Q.createElement(s,o),F.currentNode=this.el.content,2===e){const t=this.el.content,e=t.firstChild;e.remove(),t.append(...e.childNodes)}for(;null!==(r=F.nextNode())&&l.length<n;){if(1===r.nodeType){if(r.hasAttributes()){const t=[];for(const e of r.getAttributeNames())if(e.endsWith(A)||e.startsWith(_)){const o=c[a++];if(t.push(e),void 0!==o){const t=r.getAttribute(o.toLowerCase()+A).split(_),e=/([.?@])?(.*)/.exec(o);l.push({type:1,index:i,name:e[2],strings:t,ctor:"."===e[1]?tt:"?"===e[1]?ot:"@"===e[1]?rt:J})}else l.push({type:6,index:i})}for(const e of t)r.removeAttribute(e)}if(N.test(r.tagName)){const t=r.textContent.split(_),e=t.length-1;if(e>0){r.textContent=$?$.emptyScript:"";for(let o=0;o<e;o++)r.append(t[o],I()),F.nextNode(),l.push({type:2,index:++i});r.append(t[e],I())}}}else if(8===r.nodeType)if(r.data===k)l.push({type:2,index:i});else{let t=-1;for(;-1!==(t=r.data.indexOf(_,t+1));)l.push({type:7,index:i}),t+=_.length-1}i++}}static createElement(t,e){const o=E.createElement("template");return o.innerHTML=t,o}}function Y(t,e,o=t,r){var i,a,n,l;if(e===Z)return e;let s=void 0!==r?null===(i=o._$Co)||void 0===i?void 0:i[r]:o._$Cl;const c=P(e)?void 0:e._$litDirective$;return(null==s?void 0:s.constructor)!==c&&(null===(a=null==s?void 0:s._$AO)||void 0===a||a.call(s,!1),void 0===c?s=void 0:(s=new c(t),s._$AT(t,o,r)),void 0!==r?(null!==(n=(l=o)._$Co)&&void 0!==n?n:l._$Co=[])[r]=s:o._$Cl=s),void 0!==s&&(e=Y(t,s._$AS(t,e.values),s,r)),e}class G{constructor(t,e){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){var e;const{el:{content:o},parts:r}=this._$AD,i=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:E).importNode(o,!0);F.currentNode=i;let a=F.nextNode(),n=0,l=0,s=r[0];for(;void 0!==s;){if(n===s.index){let e;2===s.type?e=new X(a,a.nextSibling,this,t):1===s.type?e=new s.ctor(a,s.name,s.strings,this,t):6===s.type&&(e=new it(a,this,t)),this._$AV.push(e),s=r[++l]}n!==(null==s?void 0:s.index)&&(a=F.nextNode(),n++)}return F.currentNode=E,i}v(t){let e=0;for(const o of this._$AV)void 0!==o&&(void 0!==o.strings?(o._$AI(t,o,e),e+=o.strings.length-2):o._$AI(t[e])),e++}}class X{constructor(t,e,o,r){var i;this.type=2,this._$AH=z,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=o,this.options=r,this._$Cp=null===(i=null==r?void 0:r.isConnected)||void 0===i||i}get _$AU(){var t,e;return null!==(e=null===(t=this._$AM)||void 0===t?void 0:t._$AU)&&void 0!==e?e:this._$Cp}get parentNode(){let t=this._$AA.parentNode;const e=this._$AM;return void 0!==e&&11===(null==t?void 0:t.nodeType)&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,e=this){t=Y(this,t,e),P(t)?t===z||null==t||""===t?(this._$AH!==z&&this._$AR(),this._$AH=z):t!==this._$AH&&t!==Z&&this._(t):void 0!==t._$litType$?this.g(t):void 0!==t.nodeType?this.$(t):(t=>M(t)||"function"==typeof(null==t?void 0:t[Symbol.iterator]))(t)?this.T(t):this._(t)}k(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}$(t){this._$AH!==t&&(this._$AR(),this._$AH=this.k(t))}_(t){this._$AH!==z&&P(this._$AH)?this._$AA.nextSibling.data=t:this.$(E.createTextNode(t)),this._$AH=t}g(t){var e;const{values:o,_$litType$:r}=t,i="number"==typeof r?this._$AC(t):(void 0===r.el&&(r.el=Q.createElement(q(r.h,r.h[0]),this.options)),r);if((null===(e=this._$AH)||void 0===e?void 0:e._$AD)===i)this._$AH.v(o);else{const t=new G(i,this),e=t.u(this.options);t.v(o),this.$(e),this._$AH=t}}_$AC(t){let e=V.get(t.strings);return void 0===e&&V.set(t.strings,e=new Q(t)),e}T(t){M(this._$AH)||(this._$AH=[],this._$AR());const e=this._$AH;let o,r=0;for(const i of t)r===e.length?e.push(o=new X(this.k(I()),this.k(I()),this,this.options)):o=e[r],o._$AI(i),r++;r<e.length&&(this._$AR(o&&o._$AB.nextSibling,r),e.length=r)}_$AR(t=this._$AA.nextSibling,e){var o;for(null===(o=this._$AP)||void 0===o||o.call(this,!1,!0,e);t&&t!==this._$AB;){const e=t.nextSibling;t.remove(),t=e}}setConnected(t){var e;void 0===this._$AM&&(this._$Cp=t,null===(e=this._$AP)||void 0===e||e.call(this,t))}}class J{constructor(t,e,o,r,i){this.type=1,this._$AH=z,this._$AN=void 0,this.element=t,this.name=e,this._$AM=r,this.options=i,o.length>2||""!==o[0]||""!==o[1]?(this._$AH=Array(o.length-1).fill(new String),this.strings=o):this._$AH=z}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(t,e=this,o,r){const i=this.strings;let a=!1;if(void 0===i)t=Y(this,t,e,0),a=!P(t)||t!==this._$AH&&t!==Z,a&&(this._$AH=t);else{const r=t;let n,l;for(t=i[0],n=0;n<i.length-1;n++)l=Y(this,r[o+n],e,n),l===Z&&(l=this._$AH[n]),a||(a=!P(l)||l!==this._$AH[n]),l===z?t=z:t!==z&&(t+=(null!=l?l:"")+i[n+1]),this._$AH[n]=l}a&&!r&&this.j(t)}j(t){t===z?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:"")}}class tt extends J{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===z?void 0:t}}const et=$?$.emptyScript:"";class ot extends J{constructor(){super(...arguments),this.type=4}j(t){t&&t!==z?this.element.setAttribute(this.name,et):this.element.removeAttribute(this.name)}}class rt extends J{constructor(t,e,o,r,i){super(t,e,o,r,i),this.type=5}_$AI(t,e=this){var o;if((t=null!==(o=Y(this,t,e,0))&&void 0!==o?o:z)===Z)return;const r=this._$AH,i=t===z&&r!==z||t.capture!==r.capture||t.once!==r.once||t.passive!==r.passive,a=t!==z&&(r===z||i);i&&this.element.removeEventListener(this.name,this,r),a&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){var e,o;"function"==typeof this._$AH?this._$AH.call(null!==(o=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==o?o:this.element,t):this._$AH.handleEvent(t)}}class it{constructor(t,e,o){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=o}get _$AU(){return this._$AM._$AU}_$AI(t){Y(this,t)}}const at=x.litHtmlPolyfillSupport;var nt,lt;null==at||at(Q,X),(null!==(y=x.litHtmlVersions)&&void 0!==y?y:x.litHtmlVersions=[]).push("2.8.0");class st extends b{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var t,e;const o=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=o.firstChild),o}update(t){const e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=((t,e,o)=>{var r,i;const a=null!==(r=null==o?void 0:o.renderBefore)&&void 0!==r?r:e;let n=a._$litPart$;if(void 0===n){const t=null!==(i=null==o?void 0:o.renderBefore)&&void 0!==i?i:null;a._$litPart$=n=new X(e.insertBefore(I(),t),t,void 0,null!=o?o:{})}return n._$AI(t),n})(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this._$Do)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this._$Do)||void 0===t||t.setConnected(!1)}render(){return Z}}st.finalized=!0,st._$litElement$=!0,null===(nt=globalThis.litElementHydrateSupport)||void 0===nt||nt.call(globalThis,{LitElement:st});const ct=globalThis.litElementPolyfillSupport;null==ct||ct({LitElement:st}),(null!==(lt=globalThis.litElementVersions)&&void 0!==lt?lt:globalThis.litElementVersions=[]).push("3.3.3");const dt=t=>e=>"function"==typeof e?((t,e)=>(customElements.define(t,e),e))(t,e):((t,e)=>{const{kind:o,elements:r}=e;return{kind:o,elements:r,finisher(e){customElements.define(t,e)}}})(t,e),ht=(t,e)=>"method"===e.kind&&e.descriptor&&!("value"in e.descriptor)?{...e,finisher(o){o.createProperty(e.key,t)}}:{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){"function"==typeof e.initializer&&(this[e.key]=e.initializer.call(this))},finisher(o){o.createProperty(e.key,t)}},mt=(t,e,o)=>{e.constructor.createProperty(o,t)};function pt(t){return(e,o)=>void 0!==o?mt(t,e,o):ht(t,e)}function ut(t){return pt({...t,state:!0})}var wt;null===(wt=window.HTMLSlotElement)||void 0===wt||wt.prototype.assignedElements;class gt{constructor(t){}get _$AU(){return this._$AM._$AU}_$AT(t,e,o){this._$Ct=t,this._$AM=e,this._$Ci=o}_$AS(t,e){return this.update(t,e)}update(t,e){return this.render(...e)}}const vt=(ft=class extends gt{constructor(t){var e;if(super(t),1!==t.type||"class"!==t.name||(null===(e=t.strings)||void 0===e?void 0:e.length)>2)throw Error("`classMap()` can only be used in the `class` attribute and must be the only part in the attribute.")}render(t){return" "+Object.keys(t).filter((e=>t[e])).join(" ")+" "}update(t,[e]){var o,r;if(void 0===this.it){this.it=new Set,void 0!==t.strings&&(this.nt=new Set(t.strings.join(" ").split(/\s/).filter((t=>""!==t))));for(const t in e)e[t]&&!(null===(o=this.nt)||void 0===o?void 0:o.has(t))&&this.it.add(t);return this.render(e)}const i=t.element.classList;this.it.forEach((t=>{t in e||(i.remove(t),this.it.delete(t))}));for(const t in e){const o=!!e[t];o===this.it.has(t)||(null===(r=this.nt)||void 0===r?void 0:r.has(t))||(o?(i.add(t),this.it.add(t)):(i.remove(t),this.it.delete(t)))}return Z}},(...t)=>({_$litDirective$:ft,values:t}));var ft,bt=o(48142);const yt={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},xt=t=>1e3*t,$t=t=>t/1e3,Ct=()=>{},At=t=>t;function _t(t,e=!0){if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}const kt=t=>t(),Ot=(t,e,o=yt.duration)=>new Proxy({animations:t.map(kt).filter(Boolean),duration:o,options:e},Et),Et={get:(t,e)=>{const o=t.animations[0];switch(e){case"duration":return t.duration;case"currentTime":return $t((null==o?void 0:o[e])||0);case"playbackRate":case"playState":return null==o?void 0:o[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(It)).catch(Ct)),t.finished;case"stop":return()=>{t.animations.forEach((t=>_t(t)))};case"forEachNative":return e=>{t.animations.forEach((o=>e(o,t)))};default:return void 0===(null==o?void 0:o[e])?void 0:()=>t.animations.forEach((t=>t[e]()))}},set:(t,e,o)=>{switch(e){case"currentTime":o=xt(o);case"playbackRate":for(let r=0;r<t.animations.length;r++)t.animations[r][e]=o;return!0}return!1}},It=t=>t.finished,Pt=t=>"object"==typeof t&&Boolean(t.createAnimation),Mt=t=>"number"==typeof t,St=t=>Array.isArray(t)&&!Mt(t[0]),Lt=(t,e,o)=>-o*t+o*e+t,Rt=(t,e,o)=>e-t==0?1:(o-t)/(e-t);function Tt(t,e){const o=t[t.length-1];for(let r=1;r<=e;r++){const i=Rt(0,e,r);t.push(Lt(o,1,i))}}const jt=(t,e,o)=>Math.min(Math.max(o,t),e);const Wt=(t,e,o)=>(((1-3*o+3*e)*t+(3*o-6*e))*t+3*e)*t,Dt=1e-7,Nt=12;function Ut(t,e,o,r){if(t===e&&o===r)return At;return i=>0===i||1===i?i:Wt(function(t,e,o,r,i){let a,n,l=0;do{n=e+(o-e)/2,a=Wt(n,r,i)-t,a>0?o=n:e=n}while(Math.abs(a)>Dt&&++l<Nt);return n}(i,0,1,t,o),e,r)}const Ht=t=>"function"==typeof t,Bt=t=>Array.isArray(t)&&Mt(t[0]),Zt={ease:Ut(.25,.1,.25,1),"ease-in":Ut(.42,0,1,1),"ease-in-out":Ut(.42,0,.58,1),"ease-out":Ut(0,0,.58,1)},zt=/\((.*?)\)/;function Vt(t){if(Ht(t))return t;if(Bt(t))return Ut(...t);const e=Zt[t];if(e)return e;if(t.startsWith("steps")){const e=zt.exec(t);if(e){const t=e[1].split(",");return((t,e="end")=>o=>{const r=(o="end"===e?Math.min(o,.999):Math.max(o,.001))*t,i="end"===e?Math.floor(r):Math.ceil(r);return jt(0,1,i/t)})(parseFloat(t[0]),t[1].trim())}}return At}class Ft{constructor(t,e=[0,1],{easing:o,duration:r=yt.duration,delay:i=yt.delay,endDelay:a=yt.endDelay,repeat:n=yt.repeat,offset:l,direction:s="normal",autoplay:c=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=At,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),o=o||yt.easing,Pt(o)){const t=o.createAnimation(e);o=t.easing,e=t.keyframes||e,r=t.duration||r}this.repeat=n,this.easing=St(o)?At:Vt(o),this.updateDuration(r);const d=function(t,e=function(t){const e=[0];return Tt(e,t-1),e}(t.length),o=At){const r=t.length,i=r-e.length;return i>0&&Tt(e,i),i=>{let a=0;for(;a<r-2&&!(i<e[a+1]);a++);let n=jt(0,1,Rt(e[a],e[a+1],i));const l=function(t,e){return St(t)?t[((t,e,o)=>{const r=e-t;return((o-t)%r+r)%r+t})(0,t.length,e)]:t}(o,a);return n=l(n),Lt(t[a],t[a+1],n)}}(e,l,St(o)?o.map(Vt):At);this.tick=e=>{var o;let r=0;r=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=r,r/=1e3,r=Math.max(r-i,0),"finished"===this.playState&&void 0===this.pauseTime&&(r=this.totalDuration);const n=r/this.duration;let l=Math.floor(n),c=n%1;!c&&n>=1&&(c=1),1===c&&l--;const h=l%2;("reverse"===s||"alternate"===s&&h||"alternate-reverse"===s&&!h)&&(c=1-c);const m=r>=this.totalDuration?1:Math.min(c,1),p=d(this.easing(m));t(p),void 0===this.pauseTime&&("finished"===this.playState||r>=this.totalDuration+a)?(this.playState="finished",null===(o=this.resolve)||void 0===o||o.call(this,p)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},c&&this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}}class qt{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const Kt=new WeakMap;function Qt(t){return Kt.has(t)||Kt.set(t,{transforms:[],values:new Map}),Kt.get(t)}const Yt=["","X","Y","Z"],Gt={x:"translateX",y:"translateY",z:"translateZ"},Xt={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},Jt={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:Xt,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:At},skew:Xt},te=new Map,ee=t=>`--motion-${t}`,oe=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{Yt.forEach((e=>{oe.push(t+e),te.set(ee(t+e),Jt[t])}))}));const re=(t,e)=>oe.indexOf(t)-oe.indexOf(e),ie=new Set(oe),ae=t=>ie.has(t),ne=t=>t.sort(re).reduce(le,"").trim(),le=(t,e)=>`${t} ${e}(var(${ee(e)}))`,se=t=>t.startsWith("--"),ce=new Set,de=(t,e)=>document.createElement("div").animate(t,e),he={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{de({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(de({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{de({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},me={},pe={};for(const t in he)pe[t]=()=>(void 0===me[t]&&(me[t]=he[t]()),me[t]);const ue=(t,e)=>Ht(t)?pe.linearEasing()?`linear(${((t,e)=>{let o="";const r=Math.round(e/.015);for(let e=0;e<r;e++)o+=t(Rt(0,r-1,e))+", ";return o.substring(0,o.length-2)})(t,e)})`:yt.easing:Bt(t)?we(t):t,we=([t,e,o,r])=>`cubic-bezier(${t}, ${e}, ${o}, ${r})`;function ge(t){return Gt[t]&&(t=Gt[t]),ae(t)?ee(t):t}const ve=(t,e)=>{e=ge(e);let o=se(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!o&&0!==o){const t=te.get(e);t&&(o=t.initialValue)}return o},fe=(t,e,o)=>{e=ge(e),se(e)?t.style.setProperty(e,o):t.style[e]=o};function be(t,e,o,r={},i){const a=window.__MOTION_DEV_TOOLS_RECORD,n=!1!==r.record&&a;let l,{duration:s=yt.duration,delay:c=yt.delay,endDelay:d=yt.endDelay,repeat:h=yt.repeat,easing:m=yt.easing,persist:p=!1,direction:u,offset:w,allowWebkitAcceleration:g=!1,autoplay:v=!0}=r;const f=Qt(t),b=ae(e);let y=pe.waapi();b&&((t,e)=>{Gt[e]&&(e=Gt[e]);const{transforms:o}=Qt(t);var r,i;i=e,-1===(r=o).indexOf(i)&&r.push(i),t.style.transform=ne(o)})(t,e);const x=ge(e),$=function(t,e){return t.has(e)||t.set(e,new qt),t.get(e)}(f.values,x),C=te.get(x);return _t($.animation,!(Pt(m)&&$.generator)&&!1!==r.record),()=>{const f=()=>{var e,o;return null!==(o=null!==(e=ve(t,x))&&void 0!==e?e:null==C?void 0:C.initialValue)&&void 0!==o?o:0};let A=function(t,e){for(let o=0;o<t.length;o++)null===t[o]&&(t[o]=o?t[o-1]:e());return t}((t=>Array.isArray(t)?t:[t])(o),f);const _=function(t,e){var o;let r=(null==e?void 0:e.toDefaultUnit)||At;const i=t[t.length-1];if("string"==typeof i){const t=(null===(o=i.match(/(-?[\d.]+)([a-z%]*)/))||void 0===o?void 0:o[2])||"";t&&(r=e=>e+t)}return r}(A,C);if(Pt(m)){const t=m.createAnimation(A,"opacity"!==e,f,x,$);m=t.easing,A=t.keyframes||A,s=t.duration||s}if(se(x)&&(pe.cssRegisterProperty()?function(t){if(!ce.has(t)){ce.add(t);try{const{syntax:e,initialValue:o}=te.has(t)?te.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:o})}catch(t){}}}(x):y=!1),b&&!pe.linearEasing()&&(Ht(m)||St(m)&&m.some(Ht))&&(y=!1),y){C&&(A=A.map((t=>Mt(t)?C.toDefaultUnit(t):t))),1!==A.length||pe.partialKeyframes()&&!n||A.unshift(f());const e={delay:xt(c),duration:xt(s),endDelay:xt(d),easing:St(m)?void 0:ue(m,s),direction:u,iterations:h+1,fill:"both"};l=t.animate({[x]:A,offset:w,easing:St(m)?m.map((t=>ue(t,s))):void 0},e),l.finished||(l.finished=new Promise(((t,e)=>{l.onfinish=t,l.oncancel=e})));const o=A[A.length-1];l.finished.then((()=>{p||(fe(t,x,o),l.cancel())})).catch(Ct),g||(l.playbackRate=1.000001)}else if(i&&b)A=A.map((t=>"string"==typeof t?parseFloat(t):t)),1===A.length&&A.unshift(parseFloat(f())),l=new i((e=>{fe(t,x,_?_(e):e)}),A,Object.assign(Object.assign({},r),{duration:s,easing:m}));else{const e=A[A.length-1];fe(t,x,C&&Mt(e)?C.toDefaultUnit(e):e)}return n&&a(t,e,A,{duration:s,delay:c,easing:m,repeat:h,offset:w},"motion-one"),$.setAnimation(l),l&&!v&&l.pause(),l}}const ye=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);function xe(t,e,o){return Ht(t)?t(e,o):t}const $e=(Ce=Ft,function(t,e,o={}){const r=(t=function(t){return"string"==typeof t?t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}(t)).length;Boolean(r),Boolean(e);const i=[];for(let a=0;a<r;a++){const n=t[a];for(const t in e){const l=ye(o,t);l.delay=xe(l.delay,a,r);const s=be(n,t,e[t],l,Ce);i.push(s)}}return Ot(i,o,o.duration)});var Ce;function Ae(t,e={}){return Ot([()=>{const o=new Ft(t,[0,1],e);return o.finished.catch((()=>{})),o}],e,e.duration)}function _e(t,e,o){return(Ht(t)?Ae:$e)(t,e,o)}const ke=t=>null!=t?t:z;var Oe=o(87583),Ee=Object.defineProperty,Ie=Object.getOwnPropertySymbols,Pe=Object.prototype.hasOwnProperty,Me=Object.prototype.propertyIsEnumerable,Se=(t,e,o)=>e in t?Ee(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o,Le=(t,e)=>{for(var o in e||(e={}))Pe.call(e,o)&&Se(t,o,e[o]);if(Ie)for(var o of Ie(e))Me.call(e,o)&&Se(t,o,e[o]);return t};const Re={getPreset:t=>({"--wcm-accent-color":"#3396FF","--wcm-accent-fill-color":"#FFFFFF","--wcm-z-index":"89","--wcm-background-color":"#3396FF","--wcm-background-border-radius":"8px","--wcm-container-border-radius":"30px","--wcm-wallet-icon-border-radius":"15px","--wcm-wallet-icon-large-border-radius":"30px","--wcm-wallet-icon-small-border-radius":"7px","--wcm-input-border-radius":"28px","--wcm-button-border-radius":"10px","--wcm-notification-border-radius":"36px","--wcm-secondary-button-border-radius":"28px","--wcm-icon-button-border-radius":"50%","--wcm-button-hover-highlight-border-radius":"10px","--wcm-text-big-bold-size":"20px","--wcm-text-big-bold-weight":"600","--wcm-text-big-bold-line-height":"24px","--wcm-text-big-bold-letter-spacing":"-0.03em","--wcm-text-big-bold-text-transform":"none","--wcm-text-xsmall-bold-size":"10px","--wcm-text-xsmall-bold-weight":"700","--wcm-text-xsmall-bold-line-height":"12px","--wcm-text-xsmall-bold-letter-spacing":"0.02em","--wcm-text-xsmall-bold-text-transform":"uppercase","--wcm-text-xsmall-regular-size":"12px","--wcm-text-xsmall-regular-weight":"600","--wcm-text-xsmall-regular-line-height":"14px","--wcm-text-xsmall-regular-letter-spacing":"-0.03em","--wcm-text-xsmall-regular-text-transform":"none","--wcm-text-small-thin-size":"14px","--wcm-text-small-thin-weight":"500","--wcm-text-small-thin-line-height":"16px","--wcm-text-small-thin-letter-spacing":"-0.03em","--wcm-text-small-thin-text-transform":"none","--wcm-text-small-regular-size":"14px","--wcm-text-small-regular-weight":"600","--wcm-text-small-regular-line-height":"16px","--wcm-text-small-regular-letter-spacing":"-0.03em","--wcm-text-small-regular-text-transform":"none","--wcm-text-medium-regular-size":"16px","--wcm-text-medium-regular-weight":"600","--wcm-text-medium-regular-line-height":"20px","--wcm-text-medium-regular-letter-spacing":"-0.03em","--wcm-text-medium-regular-text-transform":"none","--wcm-font-family":"-apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, Ubuntu, 'Helvetica Neue', sans-serif","--wcm-font-feature-settings":"'tnum' on, 'lnum' on, 'case' on","--wcm-success-color":"rgb(38,181,98)","--wcm-error-color":"rgb(242, 90, 103)","--wcm-overlay-background-color":"rgba(0, 0, 0, 0.3)","--wcm-overlay-backdrop-filter":"none"}[t]),setTheme(){const t=document.querySelector(":root"),{themeVariables:e}=bt.lH.state;if(t){const o=Le(Le(Le({},function(){var t;const e={light:{foreground:{1:"rgb(20,20,20)",2:"rgb(121,134,134)",3:"rgb(158,169,169)"},background:{1:"rgb(255,255,255)",2:"rgb(241,243,243)",3:"rgb(228,231,231)"},overlay:"rgba(0,0,0,0.1)"},dark:{foreground:{1:"rgb(228,231,231)",2:"rgb(148,158,158)",3:"rgb(110,119,119)"},background:{1:"rgb(20,20,20)",2:"rgb(39,42,42)",3:"rgb(59,64,64)"},overlay:"rgba(255,255,255,0.1)"}}[null!=(t=bt.lH.state.themeMode)?t:"dark"];return{"--wcm-color-fg-1":e.foreground[1],"--wcm-color-fg-2":e.foreground[2],"--wcm-color-fg-3":e.foreground[3],"--wcm-color-bg-1":e.background[1],"--wcm-color-bg-2":e.background[2],"--wcm-color-bg-3":e.background[3],"--wcm-color-overlay":e.overlay}}()),{"--wcm-accent-color":"#3396FF","--wcm-accent-fill-color":"#FFFFFF","--wcm-z-index":"89","--wcm-background-color":"#3396FF","--wcm-background-border-radius":"8px","--wcm-container-border-radius":"30px","--wcm-wallet-icon-border-radius":"15px","--wcm-wallet-icon-large-border-radius":"30px","--wcm-wallet-icon-small-border-radius":"7px","--wcm-input-border-radius":"28px","--wcm-button-border-radius":"10px","--wcm-notification-border-radius":"36px","--wcm-secondary-button-border-radius":"28px","--wcm-icon-button-border-radius":"50%","--wcm-button-hover-highlight-border-radius":"10px","--wcm-text-big-bold-size":"20px","--wcm-text-big-bold-weight":"600","--wcm-text-big-bold-line-height":"24px","--wcm-text-big-bold-letter-spacing":"-0.03em","--wcm-text-big-bold-text-transform":"none","--wcm-text-xsmall-bold-size":"10px","--wcm-text-xsmall-bold-weight":"700","--wcm-text-xsmall-bold-line-height":"12px","--wcm-text-xsmall-bold-letter-spacing":"0.02em","--wcm-text-xsmall-bold-text-transform":"uppercase","--wcm-text-xsmall-regular-size":"12px","--wcm-text-xsmall-regular-weight":"600","--wcm-text-xsmall-regular-line-height":"14px","--wcm-text-xsmall-regular-letter-spacing":"-0.03em","--wcm-text-xsmall-regular-text-transform":"none","--wcm-text-small-thin-size":"14px","--wcm-text-small-thin-weight":"500","--wcm-text-small-thin-line-height":"16px","--wcm-text-small-thin-letter-spacing":"-0.03em","--wcm-text-small-thin-text-transform":"none","--wcm-text-small-regular-size":"14px","--wcm-text-small-regular-weight":"600","--wcm-text-small-regular-line-height":"16px","--wcm-text-small-regular-letter-spacing":"-0.03em","--wcm-text-small-regular-text-transform":"none","--wcm-text-medium-regular-size":"16px","--wcm-text-medium-regular-weight":"600","--wcm-text-medium-regular-line-height":"20px","--wcm-text-medium-regular-letter-spacing":"-0.03em","--wcm-text-medium-regular-text-transform":"none","--wcm-font-family":"-apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, Ubuntu, 'Helvetica Neue', sans-serif","--wcm-font-feature-settings":"'tnum' on, 'lnum' on, 'case' on","--wcm-success-color":"rgb(38,181,98)","--wcm-error-color":"rgb(242, 90, 103)","--wcm-overlay-background-color":"rgba(0, 0, 0, 0.3)","--wcm-overlay-backdrop-filter":"none"}),e);Object.entries(o).forEach((([e,o])=>t.style.setProperty(e,o)))}},globalCss:s`*,::after,::before{margin:0;padding:0;box-sizing:border-box;font-style:normal;text-rendering:optimizeSpeed;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-tap-highlight-color:transparent;backface-visibility:hidden}button{cursor:pointer;display:flex;justify-content:center;align-items:center;position:relative;border:none;background-color:transparent;transition:all .2s ease}@media (hover:hover) and (pointer:fine){button:active{transition:all .1s ease;transform:scale(.93)}}button::after{content:'';position:absolute;top:0;bottom:0;left:0;right:0;transition:background-color,.2s ease}button:disabled{cursor:not-allowed}button svg,button wcm-text{position:relative;z-index:1}input{border:none;outline:0;appearance:none}img{display:block}::selection{color:var(--wcm-accent-fill-color);background:var(--wcm-accent-color)}`},Te=s`button{border-radius:var(--wcm-secondary-button-border-radius);height:28px;padding:0 10px;background-color:var(--wcm-accent-color)}button path{fill:var(--wcm-accent-fill-color)}button::after{border-radius:inherit;border:1px solid var(--wcm-color-overlay)}button:disabled::after{background-color:transparent}.wcm-icon-left svg{margin-right:5px}.wcm-icon-right svg{margin-left:5px}button:active::after{background-color:var(--wcm-color-overlay)}.wcm-ghost,.wcm-ghost:active::after,.wcm-outline{background-color:transparent}.wcm-ghost:active{opacity:.5}@media(hover:hover){button:hover::after{background-color:var(--wcm-color-overlay)}.wcm-ghost:hover::after{background-color:transparent}.wcm-ghost:hover{opacity:.5}}button:disabled{background-color:var(--wcm-color-bg-3);pointer-events:none}.wcm-ghost::after{border-color:transparent}.wcm-ghost path{fill:var(--wcm-color-fg-2)}.wcm-outline path{fill:var(--wcm-accent-color)}.wcm-outline:disabled{background-color:transparent;opacity:.5}`;var je=Object.defineProperty,We=Object.getOwnPropertyDescriptor,De=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?We(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&je(e,o,a),a};let Ne=class extends st{constructor(){super(...arguments),this.disabled=!1,this.iconLeft=void 0,this.iconRight=void 0,this.onClick=()=>null,this.variant="default"}render(){const t={"wcm-icon-left":void 0!==this.iconLeft,"wcm-icon-right":void 0!==this.iconRight,"wcm-ghost":"ghost"===this.variant,"wcm-outline":"outline"===this.variant};let e="inverse";return"ghost"===this.variant&&(e="secondary"),"outline"===this.variant&&(e="accent"),H`<button class="${vt(t)}" ?disabled="${this.disabled}" @click="${this.onClick}">${this.iconLeft}<wcm-text variant="small-regular" color="${e}"><slot></slot></wcm-text>${this.iconRight}</button>`}};Ne.styles=[Re.globalCss,Te],De([pt({type:Boolean})],Ne.prototype,"disabled",2),De([pt()],Ne.prototype,"iconLeft",2),De([pt()],Ne.prototype,"iconRight",2),De([pt()],Ne.prototype,"onClick",2),De([pt()],Ne.prototype,"variant",2),Ne=De([dt("wcm-button")],Ne);const Ue=s`:host{display:inline-block}button{padding:0 15px 1px;height:40px;border-radius:var(--wcm-button-border-radius);color:var(--wcm-accent-fill-color);background-color:var(--wcm-accent-color)}button::after{content:'';top:0;bottom:0;left:0;right:0;position:absolute;background-color:transparent;border-radius:inherit;transition:background-color .2s ease;border:1px solid var(--wcm-color-overlay)}button:active::after{background-color:var(--wcm-color-overlay)}button:disabled{padding-bottom:0;background-color:var(--wcm-color-bg-3);color:var(--wcm-color-fg-3)}.wcm-secondary{color:var(--wcm-accent-color);background-color:transparent}.wcm-secondary::after{display:none}@media(hover:hover){button:hover::after{background-color:var(--wcm-color-overlay)}}`;var He=Object.defineProperty,Be=Object.getOwnPropertyDescriptor,Ze=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?Be(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&He(e,o,a),a};let ze=class extends st{constructor(){super(...arguments),this.disabled=!1,this.variant="primary"}render(){const t={"wcm-secondary":"secondary"===this.variant};return H`<button ?disabled="${this.disabled}" class="${vt(t)}"><slot></slot></button>`}};ze.styles=[Re.globalCss,Ue],Ze([pt({type:Boolean})],ze.prototype,"disabled",2),Ze([pt()],ze.prototype,"variant",2),ze=Ze([dt("wcm-button-big")],ze);const Ve=s`:host{background-color:var(--wcm-color-bg-2);border-top:1px solid var(--wcm-color-bg-3)}div{padding:10px 20px;display:inherit;flex-direction:inherit;align-items:inherit;width:inherit;justify-content:inherit}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Fe=class extends st{render(){return H`<div><slot></slot></div>`}};Fe.styles=[Re.globalCss,Ve],Fe=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-info-footer")],Fe);const qe={CROSS_ICON:B`<svg width="12" height="12" viewBox="0 0 12 12"><path d="M9.94 11A.75.75 0 1 0 11 9.94L7.414 6.353a.5.5 0 0 1 0-.708L11 2.061A.75.75 0 1 0 9.94 1L6.353 4.586a.5.5 0 0 1-.708 0L2.061 1A.75.75 0 0 0 1 2.06l3.586 3.586a.5.5 0 0 1 0 .708L1 9.939A.75.75 0 1 0 2.06 11l3.586-3.586a.5.5 0 0 1 .708 0L9.939 11Z" fill="#fff"/></svg>`,WALLET_CONNECT_LOGO:B`<svg width="178" height="29" viewBox="0 0 178 29" id="wcm-wc-logo"><path d="M10.683 7.926c5.284-5.17 13.85-5.17 19.134 0l.636.623a.652.652 0 0 1 0 .936l-2.176 2.129a.343.343 0 0 1-.478 0l-.875-.857c-3.686-3.607-9.662-3.607-13.348 0l-.937.918a.343.343 0 0 1-.479 0l-2.175-2.13a.652.652 0 0 1 0-.936l.698-.683Zm23.633 4.403 1.935 1.895a.652.652 0 0 1 0 .936l-8.73 8.543a.687.687 0 0 1-.956 0L20.37 17.64a.172.172 0 0 0-.239 0l-6.195 6.063a.687.687 0 0 1-.957 0l-8.73-8.543a.652.652 0 0 1 0-.936l1.936-1.895a.687.687 0 0 1 .957 0l6.196 6.064a.172.172 0 0 0 .239 0l6.195-6.064a.687.687 0 0 1 .957 0l6.196 6.064a.172.172 0 0 0 .24 0l6.195-6.064a.687.687 0 0 1 .956 0ZM48.093 20.948l2.338-9.355c.139-.515.258-1.07.416-1.942.12.872.258 1.427.357 1.942l2.022 9.355h4.181l3.528-13.874h-3.21l-1.943 8.523a24.825 24.825 0 0 0-.456 2.457c-.158-.931-.317-1.625-.495-2.438l-1.883-8.542h-4.201l-2.042 8.542a41.204 41.204 0 0 0-.475 2.438 41.208 41.208 0 0 0-.476-2.438l-1.903-8.542h-3.349l3.508 13.874h4.083ZM63.33 21.304c1.585 0 2.596-.654 3.11-1.605-.059.297-.078.595-.078.892v.357h2.655V15.22c0-2.735-1.248-4.32-4.3-4.32-2.636 0-4.36 1.466-4.52 3.487h2.914c.1-.891.734-1.426 1.705-1.426.911 0 1.407.515 1.407 1.11 0 .435-.258.693-1.03.792l-1.388.159c-2.061.257-3.825 1.01-3.825 3.19 0 1.982 1.645 3.092 3.35 3.092Zm.891-2.041c-.773 0-1.348-.436-1.348-1.19 0-.733.655-1.09 1.645-1.268l.674-.119c.575-.118.892-.218 1.09-.396v.912c0 1.228-.892 2.06-2.06 2.06ZM70.398 7.074v13.874h2.874V7.074h-2.874ZM74.934 7.074v13.874h2.874V7.074h-2.874ZM84.08 21.304c2.735 0 4.5-1.546 4.697-3.567h-2.893c-.139.892-.892 1.387-1.804 1.387-1.228 0-2.12-.99-2.14-2.358h6.897v-.555c0-3.21-1.764-5.312-4.816-5.312-2.933 0-4.994 2.062-4.994 5.173 0 3.37 2.12 5.232 5.053 5.232Zm-2.16-6.421c.119-1.11.932-1.922 2.081-1.922 1.11 0 1.883.772 1.903 1.922H81.92ZM94.92 21.146c.633 0 1.248-.1 1.525-.179v-2.18c-.218.04-.475.06-.693.06-1.05 0-1.427-.595-1.427-1.566v-3.805h2.338v-2.24h-2.338V7.788H91.47v3.448H89.37v2.24h2.1v4.201c0 2.3 1.15 3.469 3.45 3.469ZM104.62 21.304c3.924 0 6.302-2.299 6.599-5.608h-3.111c-.238 1.803-1.506 3.032-3.369 3.032-2.2 0-3.746-1.784-3.746-4.796 0-2.953 1.605-4.638 3.805-4.638 1.883 0 2.953 1.15 3.171 2.834h3.191c-.317-3.448-2.854-5.41-6.342-5.41-3.984 0-7.036 2.695-7.036 7.214 0 4.677 2.676 7.372 6.838 7.372ZM117.449 21.304c2.993 0 5.114-1.882 5.114-5.172 0-3.23-2.121-5.233-5.114-5.233-2.972 0-5.093 2.002-5.093 5.233 0 3.29 2.101 5.172 5.093 5.172Zm0-2.22c-1.327 0-2.18-1.09-2.18-2.952 0-1.903.892-2.973 2.18-2.973 1.308 0 2.2 1.07 2.2 2.973 0 1.862-.872 2.953-2.2 2.953ZM126.569 20.948v-5.689c0-1.208.753-2.1 1.823-2.1 1.011 0 1.606.773 1.606 2.06v5.729h2.873v-6.144c0-2.339-1.229-3.905-3.428-3.905-1.526 0-2.458.734-2.953 1.606a5.31 5.31 0 0 0 .079-.892v-.377h-2.874v9.712h2.874ZM137.464 20.948v-5.689c0-1.208.753-2.1 1.823-2.1 1.011 0 1.606.773 1.606 2.06v5.729h2.873v-6.144c0-2.339-1.228-3.905-3.428-3.905-1.526 0-2.458.734-2.953 1.606a5.31 5.31 0 0 0 .079-.892v-.377h-2.874v9.712h2.874ZM149.949 21.304c2.735 0 4.499-1.546 4.697-3.567h-2.893c-.139.892-.892 1.387-1.804 1.387-1.228 0-2.12-.99-2.14-2.358h6.897v-.555c0-3.21-1.764-5.312-4.816-5.312-2.933 0-4.994 2.062-4.994 5.173 0 3.37 2.12 5.232 5.053 5.232Zm-2.16-6.421c.119-1.11.932-1.922 2.081-1.922 1.11 0 1.883.772 1.903 1.922h-3.984ZM160.876 21.304c3.013 0 4.658-1.645 4.975-4.201h-2.874c-.099 1.07-.713 1.982-2.001 1.982-1.309 0-2.2-1.21-2.2-2.993 0-1.942 1.03-2.933 2.259-2.933 1.209 0 1.803.872 1.883 1.882h2.873c-.218-2.358-1.823-4.142-4.776-4.142-2.874 0-5.153 1.903-5.153 5.193 0 3.25 1.923 5.212 5.014 5.212ZM172.067 21.146c.634 0 1.248-.1 1.526-.179v-2.18c-.218.04-.476.06-.694.06-1.05 0-1.427-.595-1.427-1.566v-3.805h2.339v-2.24h-2.339V7.788h-2.854v3.448h-2.1v2.24h2.1v4.201c0 2.3 1.15 3.469 3.449 3.469Z" fill="#fff"/></svg>`,WALLET_CONNECT_ICON:B`<svg width="28" height="20" viewBox="0 0 28 20"><g clip-path="url(#a)"><path d="M7.386 6.482c3.653-3.576 9.575-3.576 13.228 0l.44.43a.451.451 0 0 1 0 .648L19.55 9.033a.237.237 0 0 1-.33 0l-.606-.592c-2.548-2.496-6.68-2.496-9.228 0l-.648.634a.237.237 0 0 1-.33 0L6.902 7.602a.451.451 0 0 1 0-.647l.483-.473Zm16.338 3.046 1.339 1.31a.451.451 0 0 1 0 .648l-6.035 5.909a.475.475 0 0 1-.662 0L14.083 13.2a.119.119 0 0 0-.166 0l-4.283 4.194a.475.475 0 0 1-.662 0l-6.035-5.91a.451.451 0 0 1 0-.647l1.338-1.31a.475.475 0 0 1 .662 0l4.283 4.194c.**************.166 0l4.283-4.194a.475.475 0 0 1 .662 0l4.283 4.194c.**************.166 0l4.283-4.194a.475.475 0 0 1 .662 0Z" fill="#000000"/></g><defs><clipPath id="a"><path fill="#ffffff" d="M0 0h28v20H0z"/></clipPath></defs></svg>`,WALLET_CONNECT_ICON_COLORED:B`<svg width="96" height="96" fill="none"><path fill="#fff" d="M25.322 33.597c12.525-12.263 32.83-12.263 45.355 0l1.507 1.476a1.547 1.547 0 0 1 0 2.22l-5.156 5.048a.814.814 0 0 1-1.134 0l-2.074-2.03c-8.737-8.555-22.903-8.555-31.64 0l-2.222 2.175a.814.814 0 0 1-1.134 0l-5.156-5.049a1.547 1.547 0 0 1 0-2.22l1.654-1.62Zm56.019 10.44 4.589 4.494a1.547 1.547 0 0 1 0 2.22l-20.693 20.26a1.628 1.628 0 0 1-2.267 0L48.283 56.632a.407.407 0 0 0-.567 0L33.03 71.012a1.628 1.628 0 0 1-2.268 0L10.07 50.75a1.547 1.547 0 0 1 0-2.22l4.59-4.494a1.628 1.628 0 0 1 2.267 0l14.687 14.38c.**************.567 0l14.685-14.38a1.628 1.628 0 0 1 2.268 0l14.687 14.38c.**************.567 0l14.686-14.38a1.628 1.628 0 0 1 2.268 0Z"/><path stroke="#000" d="M25.672 33.954c12.33-12.072 32.325-12.072 44.655 0l1.508 1.476a1.047 1.047 0 0 1 0 1.506l-5.157 5.048a.314.314 0 0 1-.434 0l-2.074-2.03c-8.932-8.746-23.409-8.746-32.34 0l-2.222 2.174a.314.314 0 0 1-.434 0l-5.157-5.048a1.047 1.047 0 0 1 0-1.506l1.655-1.62Zm55.319 10.44 4.59 4.494a1.047 1.047 0 0 1 0 1.506l-20.694 20.26a1.128 1.128 0 0 1-1.568 0l-14.686-14.38a.907.907 0 0 0-1.267 0L32.68 70.655a1.128 1.128 0 0 1-1.568 0L10.42 50.394a1.047 1.047 0 0 1 0-1.506l4.59-4.493a1.128 1.128 0 0 1 1.567 0l14.687 14.379a.907.907 0 0 0 1.266 0l-.35-.357.35.357 14.686-14.38a1.128 1.128 0 0 1 1.568 0l14.687 14.38a.907.907 0 0 0 1.267 0l14.686-14.38a1.128 1.128 0 0 1 1.568 0Z"/></svg>`,BACK_ICON:B`<svg width="10" height="18" viewBox="0 0 10 18"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.735.179a.75.75 0 0 1 .087 1.057L2.92 8.192a1.25 1.25 0 0 0 0 1.617l5.902 6.956a.75.75 0 1 1-1.144.97L1.776 10.78a2.75 2.75 0 0 1 0-3.559L7.678.265A.75.75 0 0 1 8.735.18Z" fill="#fff"/></svg>`,COPY_ICON:B`<svg width="24" height="24" fill="none"><path fill="#fff" fill-rule="evenodd" d="M7.01 7.01c.03-1.545.138-2.5.535-3.28A5 5 0 0 1 9.73 1.545C10.8 1 12.2 1 15 1c2.8 0 4.2 0 5.27.545a5 5 0 0 1 2.185 2.185C23 4.8 23 6.2 23 9c0 2.8 0 4.2-.545 5.27a5 5 0 0 1-2.185 2.185c-.78.397-1.735.505-3.28.534l-.001.01c-.03 1.54-.138 2.493-.534 3.27a5 5 0 0 1-2.185 2.186C13.2 23 11.8 23 9 23c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C1 19.2 1 17.8 1 15c0-2.8 0-4.2.545-5.27A5 5 0 0 1 3.73 7.545C4.508 7.149 5.46 7.04 7 7.01h.01ZM15 15.5c-1.425 0-2.403-.001-3.162-.063-.74-.06-1.139-.172-1.427-.319a3.5 3.5 0 0 1-1.53-1.529c-.146-.288-.257-.686-.318-1.427C8.501 11.403 8.5 10.425 8.5 9c0-1.425.001-2.403.063-3.162.06-.74.172-1.139.318-1.427a3.5 3.5 0 0 1 1.53-1.53c.288-.146.686-.257 1.427-.318.759-.062 1.737-.063 3.162-.063 1.425 0 2.403.001 3.162.063.74.06 1.139.172 1.427.318a3.5 3.5 0 0 1 1.53 1.53c.146.288.257.686.318 1.427.062.759.063 1.737.063 3.162 0 1.425-.001 2.403-.063 3.162-.06.74-.172 1.139-.319 1.427a3.5 3.5 0 0 1-1.529 1.53c-.288.146-.686.257-1.427.318-.759.062-1.737.063-3.162.063ZM7 8.511c-.444.009-.825.025-1.162.052-.74.06-1.139.172-1.427.318a3.5 3.5 0 0 0-1.53 1.53c-.146.288-.257.686-.318 1.427-.062.759-.063 1.737-.063 3.162 0 1.425.001 2.403.063 ***********.172 1.139.318 1.427a3.5 3.5 0 0 0 1.53 1.53c.288.146.686.257 1.427.318.759.062 1.737.063 3.162.063 1.425 0 2.403-.001 3.162-.063.74-.06 1.139-.172 1.427-.319a3.5 3.5 0 0 0 1.53-1.53c.146-.287.257-.685.318-1.426.027-.337.043-.718.052-1.162H15c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C7 13.2 7 11.8 7 9v-.489Z" clip-rule="evenodd"/></svg>`,RETRY_ICON:B`<svg width="15" height="16" viewBox="0 0 15 16"><path d="M6.464 2.03A.75.75 0 0 0 5.403.97L2.08 4.293a1 1 0 0 0 0 1.414L5.403 9.03a.75.75 0 0 0 1.06-1.06L4.672 6.177a.25.25 0 0 1 .177-.427h2.085a4 4 0 1 1-3.93 4.746c-.077-.407-.405-.746-.82-.746-.414 0-.755.338-.7.748a5.501 5.501 0 1 0 5.45-6.248H4.848a.25.25 0 0 1-.177-.427L6.464 2.03Z" fill="#fff"/></svg>`,DESKTOP_ICON:B`<svg width="16" height="16" viewBox="0 0 16 16"><path fill-rule="evenodd" clip-rule="evenodd" d="M0 5.98c0-1.85 0-2.775.394-3.466a3 3 0 0 1 1.12-1.12C2.204 1 3.13 1 4.98 1h6.04c1.85 0 2.775 0 3.466.394a3 3 0 0 1 1.12 1.12C16 3.204 16 4.13 16 5.98v1.04c0 1.85 0 2.775-.394 3.466a3 3 0 0 1-1.12 1.12C13.796 12 12.87 12 11.02 12H4.98c-1.85 0-2.775 0-3.466-.394a3 3 0 0 1-1.12-1.12C0 9.796 0 8.87 0 7.02V5.98ZM4.98 2.5h6.04c.953 0 1.568.001 2.034.043.446.04.608.108.69.154a1.5 1.5 0 0 1 .559.56c.**************.154.69.042.465.043 1.08.043 2.033v1.04c0 .952-.001 1.568-.043 2.034-.04.446-.108.608-.154.69a1.499 1.499 0 0 1-.56.559c-.08.046-.243.114-.69.154-.466.042-1.08.043-2.033.043H4.98c-.952 0-1.568-.001-2.034-.043-.446-.04-.608-.108-.69-.154a1.5 1.5 0 0 1-.559-.56c-.046-.08-.114-.243-.154-.69-.042-.465-.043-1.08-.043-2.033V5.98c0-.952.001-1.568.043-2.034.04-.446.108-.608.154-.69a1.5 1.5 0 0 1 .56-.559c.08-.046.243-.114.69-.154.465-.042 1.08-.043 2.033-.043Z" fill="#fff"/><path d="M4 14.25a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5a.75.75 0 0 1-.75-.75Z" fill="#fff"/></svg>`,MOBILE_ICON:B`<svg width="16" height="16" viewBox="0 0 16 16"><path d="M6.75 5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z" fill="#fff"/><path fill-rule="evenodd" clip-rule="evenodd" d="M3 4.98c0-1.85 0-2.775.394-3.466a3 3 0 0 1 1.12-1.12C5.204 0 6.136 0 8 0s2.795 0 3.486.394a3 3 0 0 1 1.12 1.12C13 2.204 13 3.13 13 4.98v6.04c0 1.85 0 2.775-.394 3.466a3 3 0 0 1-1.12 1.12C10.796 16 9.864 16 8 16s-2.795 0-3.486-.394a3 3 0 0 1-1.12-1.12C3 13.796 3 12.87 3 11.02V4.98Zm8.5 0v6.04c0 .953-.001 1.568-.043 2.034-.04.446-.108.608-.154.69a1.499 1.499 0 0 1-.56.559c-.08.045-.242.113-.693.154-.47.042-1.091.043-2.05.043-.959 0-1.58-.001-2.05-.043-.45-.04-.613-.109-.693-.154a1.5 1.5 0 0 1-.56-.56c-.046-.08-.114-.243-.154-.69-.042-.466-.043-1.08-.043-2.033V4.98c0-.952.001-1.568.043-2.034.04-.446.108-.608.154-.69a1.5 1.5 0 0 1 .56-.559c.08-.045.243-.113.693-.154C6.42 1.501 7.041 1.5 8 1.5c.959 0 1.58.001 ***********.04.613.109.693.154a1.5 1.5 0 0 1 .56.56c.**************.154.69.042.465.043 1.08.043 2.033Z" fill="#fff"/></svg>`,ARROW_DOWN_ICON:B`<svg width="14" height="14" viewBox="0 0 14 14"><path d="M2.28 7.47a.75.75 0 0 0-1.06 1.06l5.25 5.25a.75.75 0 0 0 1.06 0l5.25-5.25a.75.75 0 0 0-1.06-1.06l-3.544 3.543a.25.25 0 0 1-.426-.177V.75a.75.75 0 0 0-1.5 0v10.086a.25.25 0 0 1-.427.176L2.28 7.47Z" fill="#fff"/></svg>`,ARROW_UP_RIGHT_ICON:B`<svg width="15" height="14" fill="none"><path d="M4.5 1.75A.75.75 0 0 1 5.25 1H12a1.5 1.5 0 0 1 1.5 1.5v6.75a.75.75 0 0 1-1.5 0V4.164a.25.25 0 0 0-.427-.176L4.061 11.5A.75.75 0 0 1 3 10.44l7.513-7.513a.25.25 0 0 0-.177-.427H5.25a.75.75 0 0 1-.75-.75Z" fill="#fff"/></svg>`,ARROW_RIGHT_ICON:B`<svg width="6" height="14" viewBox="0 0 6 14"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.181 1.099a.75.75 0 0 1 1.024.279l2.433 4.258a2.75 2.75 0 0 1 0 2.729l-2.433 4.257a.75.75 0 1 1-1.303-.744L4.335 7.62a1.25 1.25 0 0 0 0-1.24L1.902 2.122a.75.75 0 0 1 .28-1.023Z" fill="#fff"/></svg>`,QRCODE_ICON:B`<svg width="25" height="24" viewBox="0 0 25 24"><path d="M23.748 9a.748.748 0 0 0 .748-.752c-.018-2.596-.128-4.07-.784-5.22a6 6 0 0 0-2.24-2.24c-1.15-.656-2.624-.766-5.22-.784a.748.748 0 0 0-.752.748c0 .414.335.749.748.752 1.015.007 1.82.028 2.494.088.995.09 1.561.256 1.988.5.7.398 1.28.978 1.679 1.678.243.427.41.993.498 1.988.061.675.082 1.479.09 2.493a.753.753 0 0 0 .75.749ZM3.527.788C4.677.132 6.152.022 8.747.004A.748.748 0 0 1 9.5.752a.753.753 0 0 1-.749.752c-1.014.007-1.818.028-2.493.088-.995.09-1.561.256-1.988.5-.7.398-1.28.978-1.679 1.678-.243.427-.41.993-.499 1.988-.06.675-.081 1.479-.088 2.493A.753.753 0 0 1 1.252 9a.748.748 0 0 1-.748-.752c.018-2.596.128-4.07.784-5.22a6 6 0 0 1 2.24-2.24ZM1.252 15a.748.748 0 0 0-.748.752c.018 2.596.128 4.07.784 5.22a6 6 0 0 0 2.24 2.24c1.15.656 2.624.766 5.22.784a.748.748 0 0 0 .752-.748.753.753 0 0 0-.749-.752c-1.014-.007-1.818-.028-2.493-.089-.995-.089-1.561-.255-1.988-.498a4.5 4.5 0 0 1-1.679-1.68c-.243-.426-.41-.992-.499-1.987-.06-.675-.081-1.479-.088-2.493A.753.753 0 0 0 1.252 15ZM22.996 15.749a.753.753 0 0 1 .752-.749c.415 0 .751.338.748.752-.018 2.596-.128 4.07-.784 5.22a6 6 0 0 1-2.24 2.24c-1.15.656-2.624.766-5.22.784a.748.748 0 0 1-.752-.748c0-.414.335-.749.748-.752 1.015-.007 1.82-.028 2.494-.089.995-.089 1.561-.255 1.988-.498a4.5 4.5 0 0 0 1.679-1.68c.243-.426.41-.992.498-1.987.061-.675.082-1.479.09-2.493Z" fill="#fff"/><path fill-rule="evenodd" clip-rule="evenodd" d="M7 4a2.5 2.5 0 0 0-2.5 2.5v2A2.5 2.5 0 0 0 7 11h2a2.5 2.5 0 0 0 2.5-2.5v-2A2.5 2.5 0 0 0 9 4H7Zm2 1.5H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1ZM13.5 6.5A2.5 2.5 0 0 1 16 4h2a2.5 2.5 0 0 1 2.5 2.5v2A2.5 2.5 0 0 1 18 11h-2a2.5 2.5 0 0 1-2.5-2.5v-2Zm2.5-1h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1ZM7 13a2.5 2.5 0 0 0-2.5 2.5v2A2.5 2.5 0 0 0 7 20h2a2.5 2.5 0 0 0 2.5-2.5v-2A2.5 2.5 0 0 0 9 13H7Zm2 1.5H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1Z" fill="#fff"/><path d="M13.5 15.5c0-.465 0-.697.038-.89a2 2 0 0 1 1.572-1.572C15.303 13 15.535 13 16 13v2.5h-2.5ZM18 13c.465 0 .697 0 .89.038a2 2 0 0 1 1.572 1.572c.038.193.038.425.038.89H18V13ZM18 17.5h2.5c0 .465 0 .697-.038.89a2 2 0 0 1-1.572 1.572C18.697 20 18.465 20 18 20v-2.5ZM13.5 17.5H16V20c-.465 0-.697 0-.89-.038a2 2 0 0 1-1.572-1.572c-.038-.193-.038-.425-.038-.89Z" fill="#fff"/></svg>`,SCAN_ICON:B`<svg width="16" height="16" fill="none"><path fill="#fff" d="M10 15.216c0 .422.347.763.768.74 1.202-.064 2.025-.222 2.71-.613a5.001 5.001 0 0 0 1.865-1.866c.39-.684.549-1.507.613-2.709a.735.735 0 0 0-.74-.768.768.768 0 0 0-.76.732c-.009.157-.02.306-.032.447-.073.812-.206 1.244-.384 1.555-.31.545-.761.996-1.306 1.306-.311.178-.743.311-1.555.384-.141.013-.29.023-.447.032a.768.768 0 0 0-.732.76ZM10 .784c0 .407.325.737.732.76.157.009.306.02.447.032.812.073 1.244.206 1.555.384a3.5 3.5 0 0 1 1.306 1.306c.178.311.311.743.384 1.555.013.142.023.29.032.447a.768.768 0 0 0 .76.732.734.734 0 0 0 .74-.768c-.064-1.202-.222-2.025-.613-2.71A5 5 0 0 0 13.477.658c-.684-.39-1.507-.549-2.709-.613a.735.735 0 0 0-.768.74ZM5.232.044A.735.735 0 0 1 6 .784a.768.768 0 0 1-.732.76c-.157.009-.305.02-.447.032-.812.073-1.244.206-1.555.384A3.5 3.5 0 0 0 1.96 3.266c-.178.311-.311.743-.384 1.555-.013.142-.023.29-.032.447A.768.768 0 0 1 .784 6a.735.735 0 0 1-.74-.768c.064-1.202.222-2.025.613-2.71A5 5 0 0 1 2.523.658C3.207.267 4.03.108 5.233.044ZM5.268 14.456a.768.768 0 0 1 .732.76.734.734 0 0 1-.768.74c-1.202-.064-2.025-.222-2.71-.613a5 5 0 0 1-1.865-1.866c-.39-.684-.549-1.507-.613-2.709A.735.735 0 0 1 .784 10c.407 0 .737.325.76.732.009.157.02.306.032.447.073.812.206 1.244.384 1.555a3.5 3.5 0 0 0 1.306 1.306c.311.178.743.311 1.555.384.**************.447.032Z"/></svg>`,CHECKMARK_ICON:B`<svg width="13" height="12" viewBox="0 0 13 12"><path fill-rule="evenodd" clip-rule="evenodd" d="M12.155.132a.75.75 0 0 1 .232 1.035L5.821 11.535a1 1 0 0 1-1.626.09L.665 7.21a.75.75 0 1 1 1.17-.937L4.71 9.867a.25.25 0 0 0 .406-.023L11.12.364a.75.75 0 0 1 1.035-.232Z" fill="#fff"/></svg>`,SEARCH_ICON:B`<svg width="20" height="21"><path fill-rule="evenodd" clip-rule="evenodd" d="M12.432 13.992c-.354-.353-.91-.382-1.35-.146a5.5 5.5 0 1 1 2.265-2.265c-.237.441-.208.997.145 1.35l3.296 3.296a.75.75 0 1 1-1.06 1.061l-3.296-3.296Zm.06-5a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z" fill="#949E9E"/></svg>`,WALLET_PLACEHOLDER:B`<svg width="60" height="60" fill="none" viewBox="0 0 60 60"><g clip-path="url(#q)"><path id="wallet-placeholder-fill" fill="#fff" d="M0 24.9c0-9.251 0-13.877 1.97-17.332a15 15 0 0 1 5.598-5.597C11.023 0 15.648 0 24.9 0h10.2c9.252 0 13.877 0 17.332 1.97a15 15 0 0 1 5.597 5.598C60 11.023 60 15.648 60 24.9v10.2c0 9.252 0 13.877-1.97 17.332a15.001 15.001 0 0 1-5.598 5.597C48.977 60 44.352 60 35.1 60H24.9c-9.251 0-13.877 0-17.332-1.97a15 15 0 0 1-5.597-5.598C0 48.977 0 44.352 0 35.1V24.9Z"/><path id="wallet-placeholder-dash" stroke="#000" stroke-dasharray="4 4" stroke-width="1.5" d="M.04 41.708a231.598 231.598 0 0 1-.039-4.403l.75-.001L.75 35.1v-2.55H0v-5.1h.75V24.9l.001-2.204h-.75c.003-1.617.011-3.077.039-4.404l.75.016c.034-1.65.099-3.08.218-4.343l-.746-.07c.158-1.678.412-3.083.82-4.316l.713.236c.224-.679.497-1.296.827-1.875a14.25 14.25 0 0 1 1.05-1.585L3.076 5.9A15 15 0 0 1 5.9 3.076l.455.596a14.25 14.25 0 0 1 1.585-1.05c.579-.33 1.196-.603 1.875-.827l-.236-.712C10.812.674 12.217.42 13.895.262l.07.746C15.23.89 16.66.824 18.308.79l-.016-.75C19.62.012 21.08.004 22.695.001l.001.75L24.9.75h2.55V0h5.1v.75h2.55l2.204.001v-.75c1.617.003 3.077.011 4.404.039l-.016.75c1.65.034 3.08.099 4.343.218l.07-.746c1.678.158 3.083.412 4.316.82l-.236.713c.679.224 1.296.497 1.875.827a14.24 14.24 0 0 1 1.585 1.05l.455-.596A14.999 14.999 0 0 1 56.924 5.9l-.596.455c.384.502.735 1.032 1.05 1.585.33.579.602 1.196.827 1.875l.712-.236c.409 1.233.663 2.638.822 4.316l-.747.07c.119 1.264.184 2.694.218 4.343l.75-.016c.028 1.327.036 2.787.039 4.403l-.75.001.001 2.204v2.55H60v5.1h-.75v2.55l-.001 2.204h.75a231.431 231.431 0 0 1-.039 4.404l-.75-.016c-.034 1.65-.099 3.08-.218 4.343l.747.07c-.159 1.678-.413 3.083-.822 4.316l-.712-.236a10.255 10.255 0 0 1-.827 1.875 14.242 14.242 0 0 1-1.05 1.585l.596.455a14.997 14.997 0 0 1-2.824 2.824l-.455-.596c-.502.384-1.032.735-1.585 1.05-.579.33-1.196.602-1.875.827l.236.712c-1.233.409-2.638.663-4.316.822l-.07-.747c-1.264.119-2.694.184-4.343.218l.016.75c-1.327.028-2.787.036-4.403.039l-.001-.75-2.204.001h-2.55V60h-5.1v-.75H24.9l-2.204-.001v.75a231.431 231.431 0 0 1-4.404-.039l.016-.75c-1.65-.034-3.08-.099-4.343-.218l-.07.747c-1.678-.159-3.083-.413-4.316-.822l.236-.712a10.258 10.258 0 0 1-1.875-.827 14.252 14.252 0 0 1-1.585-1.05l-.455.596A14.999 14.999 0 0 1 3.076 54.1l.596-.455a14.24 14.24 0 0 1-1.05-1.585 10.259 10.259 0 0 1-.827-1.875l-.712.236C.674 49.188.42 47.783.262 46.105l.746-.07C.89 44.77.824 43.34.79 41.692l-.75.016Z"/><path fill="#fff" fill-rule="evenodd" d="M35.643 32.145c-.297-.743-.445-1.114-.401-1.275a.42.42 0 0 1 .182-.27c.134-.1.463-.1 1.123-.1.742 0 1.499.046 2.236-.05a6 6 0 0 0 5.166-5.166c.051-.39.051-.855.051-1.784 0-.928 0-1.393-.051-1.783a6 6 0 0 0-5.166-5.165c-.39-.052-.854-.052-1.783-.052h-7.72c-4.934 0-7.401 0-9.244 1.051a8 8 0 0 0-2.985 2.986C16.057 22.28 16.003 24.58 16 29 15.998 31.075 16 33.15 16 35.224A7.778 7.778 0 0 0 23.778 43H28.5c1.394 0 2.09 0 2.67-.116a6 6 0 0 0 4.715-4.714c.115-.58.115-1.301.115-2.744 0-1.31 0-1.964-.114-2.49a4.998 4.998 0 0 0-.243-.792Z" clip-rule="evenodd"/><path fill="#9EA9A9" fill-rule="evenodd" d="M37 18h-7.72c-2.494 0-4.266.002-5.647.126-1.361.122-2.197.354-2.854.728a6.5 6.5 0 0 0-2.425 2.426c-.375.657-.607 1.492-.729 2.853-.11 1.233-.123 2.777-.125 4.867 0 .7 0 1.05.097 ************.182.181.343.2.163.02.518-.18 1.229-.581a6.195 6.195 0 0 1 3.053-.8H37c.977 0 1.32-.003 1.587-.038a4.5 4.5 0 0 0 3.874-3.874c.036-.268.039-.611.039-1.588 0-.976-.003-1.319-.038-1.587a4.5 4.5 0 0 0-3.875-3.874C38.32 18.004 37.977 18 37 18Zm-7.364 12.5h-7.414a4.722 4.722 0 0 0-4.722 4.723 6.278 6.278 0 0 0 6.278 6.278H28.5c1.466 0 1.98-.008 2.378-.087a4.5 4.5 0 0 0 3.535-3.536c.08-.397.087-.933.087-2.451 0-1.391-.009-1.843-.08-2.17a3.5 3.5 0 0 0-2.676-2.676c-.328-.072-.762-.08-2.108-.08Z" clip-rule="evenodd"/></g><defs><clipPath id="q"><path fill="#fff" d="M0 0h60v60H0z"/></clipPath></defs></svg>`,GLOBE_ICON:B`<svg width="16" height="16" fill="none" viewBox="0 0 16 16"><path fill="#fff" fill-rule="evenodd" d="M15.5 8a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Zm-2.113.75c.301 0 .535.264.47.558a6.01 6.01 0 0 1-2.867 3.896c-.203.116-.42-.103-.334-.32.409-1.018.691-2.274.797-3.657a.512.512 0 0 1 .507-.477h1.427Zm.47-2.058c.065.294-.169.558-.47.558H11.96a.512.512 0 0 1-.507-.477c-.106-1.383-.389-2.638-.797-3.656-.087-.217.13-.437.333-.32a6.01 6.01 0 0 1 2.868 3.895Zm-4.402.558c.286 0 .515-.24.49-.525-.121-1.361-.429-2.534-.83-3.393-.279-.6-.549-.93-.753-1.112a.535.535 0 0 0-.724 0c-.204.182-.474.513-.754 1.112-.4.859-.708 2.032-.828 3.393a.486.486 0 0 0 .49.525h2.909Zm-5.415 0c.267 0 .486-.21.507-.477.106-1.383.389-2.638.797-3.656.087-.217-.13-.437-.333-.32a6.01 6.01 0 0 0-2.868 3.895c-.065.294.169.558.47.558H4.04ZM2.143 9.308c-.065-.294.169-.558.47-.558H4.04c.267 0 .486.21.507.477.106 1.383.389 2.639.797 3.657.087.217-.13.436-.333.32a6.01 6.01 0 0 1-2.868-3.896Zm3.913-.033a.486.486 0 0 1 .49-.525h2.909c.286 0 .515.24.49.525-.121 1.361-.428 2.535-.83 3.394-.279.6-.549.93-.753 1.112a.535.535 0 0 1-.724 0c-.204-.182-.474-.513-.754-1.112-.4-.859-.708-2.033-.828-3.394Z" clip-rule="evenodd"/></svg>`},Ke=s`.wcm-toolbar-placeholder{top:0;bottom:0;left:0;right:0;width:100%;position:absolute;display:block;pointer-events:none;height:100px;border-radius:calc(var(--wcm-background-border-radius) * .9);background-color:var(--wcm-background-color);background-position:center;background-size:cover}.wcm-toolbar{height:38px;display:flex;position:relative;margin:5px 15px 5px 5px;justify-content:space-between;align-items:center}.wcm-toolbar img,.wcm-toolbar svg{height:28px;object-position:left center;object-fit:contain}#wcm-wc-logo path{fill:var(--wcm-accent-fill-color)}button{width:28px;height:28px;border-radius:var(--wcm-icon-button-border-radius);border:0;display:flex;justify-content:center;align-items:center;cursor:pointer;background-color:var(--wcm-color-bg-1);box-shadow:0 0 0 1px var(--wcm-color-overlay)}button:active{background-color:var(--wcm-color-bg-2)}button svg{display:block;object-position:center}button path{fill:var(--wcm-color-fg-1)}.wcm-toolbar div{display:flex}@media(hover:hover){button:hover{background-color:var(--wcm-color-bg-2)}}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Qe=class extends st{render(){return H`<div class="wcm-toolbar-placeholder"></div><div class="wcm-toolbar">${qe.WALLET_CONNECT_LOGO} <button @click="${bt.D8.close}">${qe.CROSS_ICON}</button></div>`}};Qe.styles=[Re.globalCss,Ke],Qe=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-modal-backcard")],Qe);const Ye=s`main{padding:20px;padding-top:0;width:100%}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Ge=class extends st{render(){return H`<main><slot></slot></main>`}};Ge.styles=[Re.globalCss,Ye],Ge=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-modal-content")],Ge);const Xe=s`footer{padding:10px;display:flex;flex-direction:column;align-items:inherit;justify-content:inherit;border-top:1px solid var(--wcm-color-bg-2)}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Je=class extends st{render(){return H`<footer><slot></slot></footer>`}};Je.styles=[Re.globalCss,Xe],Je=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-modal-footer")],Je);const to=s`header{display:flex;justify-content:center;align-items:center;padding:20px;position:relative}.wcm-border{border-bottom:1px solid var(--wcm-color-bg-2);margin-bottom:20px}header button{padding:15px 20px}header button:active{opacity:.5}@media(hover:hover){header button:hover{opacity:.5}}.wcm-back-btn{position:absolute;left:0}.wcm-action-btn{position:absolute;right:0}path{fill:var(--wcm-accent-color)}`;var eo=Object.defineProperty,oo=Object.getOwnPropertyDescriptor,ro=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?oo(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&eo(e,o,a),a};let io=class extends st{constructor(){super(...arguments),this.title="",this.onAction=void 0,this.actionIcon=void 0,this.border=!1}backBtnTemplate(){return H`<button class="wcm-back-btn" @click="${bt.jL.goBack}">${qe.BACK_ICON}</button>`}actionBtnTemplate(){return H`<button class="wcm-action-btn" @click="${this.onAction}">${this.actionIcon}</button>`}render(){const t={"wcm-border":this.border},e=bt.jL.state.history.length>1,o=this.title?H`<wcm-text variant="big-bold">${this.title}</wcm-text>`:H`<slot></slot>`;return H`<header class="${vt(t)}">${e?this.backBtnTemplate():null} ${o} ${this.onAction?this.actionBtnTemplate():null}</header>`}};io.styles=[Re.globalCss,to],ro([pt()],io.prototype,"title",2),ro([pt()],io.prototype,"onAction",2),ro([pt()],io.prototype,"actionIcon",2),ro([pt({type:Boolean})],io.prototype,"border",2),io=ro([dt("wcm-modal-header")],io);const ao={MOBILE_BREAKPOINT:600,WCM_RECENT_WALLET_DATA:"WCM_RECENT_WALLET_DATA",EXPLORER_WALLET_URL:"https://explorer.walletconnect.com/?type=wallet",getShadowRootElement(t,e){const o=t.renderRoot.querySelector(e);if(!o)throw new Error(`${e} not found`);return o},getWalletIcon({id:t,image_id:e}){const{walletImages:o}=bt.mb.state;return null!=o&&o[t]?o[t]:e?bt.pV.getWalletImageUrl(e):""},getWalletName:(t,e=!1)=>e&&t.length>8?`${t.substring(0,8)}..`:t,isMobileAnimation:()=>window.innerWidth<=ao.MOBILE_BREAKPOINT,async preloadImage(t){const e=new Promise(((e,o)=>{const r=new Image;r.onload=e,r.onerror=o,r.crossOrigin="anonymous",r.src=t}));return Promise.race([e,bt.Ao.wait(3e3)])},getErrorMessage:t=>t instanceof Error?t.message:"Unknown Error",debounce(t,e=500){let o;return(...r)=>{o&&clearTimeout(o),o=setTimeout((function(){t(...r)}),e)}},handleMobileLinking(t){const{walletConnectUri:e}=bt.IN.state,{mobile:o,name:r}=t,i=o?.native,a=o?.universal;ao.setRecentWallet(t),e&&function(t){let e="";i?e=bt.Ao.formatUniversalUrl(i,t,r):a&&(e=bt.Ao.formatNativeUrl(a,t,r)),bt.Ao.openHref(e,"_self")}(e)},handleAndroidLinking(){const{walletConnectUri:t}=bt.IN.state;t&&(bt.Ao.setWalletConnectAndroidDeepLink(t),bt.Ao.openHref(t,"_self"))},async handleUriCopy(){const{walletConnectUri:t}=bt.IN.state;if(t)try{await navigator.clipboard.writeText(t),bt.dC.openToast("Link copied","success")}catch{bt.dC.openToast("Failed to copy","error")}},getCustomImageUrls(){const{walletImages:t}=bt.mb.state,e=Object.values(t??{});return Object.values(e)},truncate:(t,e=8)=>t.length<=e?t:`${t.substring(0,4)}...${t.substring(t.length-4)}`,setRecentWallet(t){try{localStorage.setItem(ao.WCM_RECENT_WALLET_DATA,JSON.stringify(t))}catch{console.info("Unable to set recent wallet")}},getRecentWallet(){try{const t=localStorage.getItem(ao.WCM_RECENT_WALLET_DATA);return t?JSON.parse(t):void 0}catch{console.info("Unable to get recent wallet")}},caseSafeIncludes:(t,e)=>t.toUpperCase().includes(e.toUpperCase()),openWalletExplorerUrl(){bt.Ao.openHref(ao.EXPLORER_WALLET_URL,"_blank")},getCachedRouterWalletPlatforms(){const{desktop:t,mobile:e}=bt.Ao.getWalletRouterData(),o=Boolean(t?.native),r=Boolean(t?.universal);return{isDesktop:o,isMobile:Boolean(e?.native)||Boolean(e?.universal),isWeb:r}},goToConnectingView(t){bt.jL.setData({Wallet:t});const e=bt.Ao.isMobile(),{isDesktop:o,isWeb:r,isMobile:i}=ao.getCachedRouterWalletPlatforms();e?i?bt.jL.push("MobileConnecting"):r?bt.jL.push("WebConnecting"):bt.jL.push("InstallWallet"):o?bt.jL.push("DesktopConnecting"):r?bt.jL.push("WebConnecting"):i?bt.jL.push("MobileQrcodeConnecting"):bt.jL.push("InstallWallet")}},no=s`.wcm-router{overflow:hidden;will-change:transform}.wcm-content{display:flex;flex-direction:column}`;var lo=Object.defineProperty,so=Object.getOwnPropertyDescriptor,co=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?so(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&lo(e,o,a),a};let ho=class extends st{constructor(){super(),this.view=bt.jL.state.view,this.prevView=bt.jL.state.view,this.unsubscribe=void 0,this.oldHeight="0px",this.resizeObserver=void 0,this.unsubscribe=bt.jL.subscribe((t=>{this.view!==t.view&&this.onChangeRoute()}))}firstUpdated(){this.resizeObserver=new ResizeObserver((([t])=>{const e=`${t.contentRect.height}px`;"0px"!==this.oldHeight&&_e(this.routerEl,{height:[this.oldHeight,e]},{duration:.2}),this.oldHeight=e})),this.resizeObserver.observe(this.contentEl)}disconnectedCallback(){var t,e;null==(t=this.unsubscribe)||t.call(this),null==(e=this.resizeObserver)||e.disconnect()}get routerEl(){return ao.getShadowRootElement(this,".wcm-router")}get contentEl(){return ao.getShadowRootElement(this,".wcm-content")}viewTemplate(){switch(this.view){case"ConnectWallet":return H`<wcm-connect-wallet-view></wcm-connect-wallet-view>`;case"DesktopConnecting":return H`<wcm-desktop-connecting-view></wcm-desktop-connecting-view>`;case"MobileConnecting":return H`<wcm-mobile-connecting-view></wcm-mobile-connecting-view>`;case"WebConnecting":return H`<wcm-web-connecting-view></wcm-web-connecting-view>`;case"MobileQrcodeConnecting":return H`<wcm-mobile-qr-connecting-view></wcm-mobile-qr-connecting-view>`;case"WalletExplorer":return H`<wcm-wallet-explorer-view></wcm-wallet-explorer-view>`;case"Qrcode":return H`<wcm-qrcode-view></wcm-qrcode-view>`;case"InstallWallet":return H`<wcm-install-wallet-view></wcm-install-wallet-view>`;default:return H`<div>Not Found</div>`}}async onChangeRoute(){await _e(this.routerEl,{opacity:[1,0],scale:[1,1.02]},{duration:.15,delay:.1}).finished,this.view=bt.jL.state.view,_e(this.routerEl,{opacity:[0,1],scale:[.99,1]},{duration:.37,delay:.05})}render(){return H`<div class="wcm-router"><div class="wcm-content">${this.viewTemplate()}</div></div>`}};ho.styles=[Re.globalCss,no],co([ut()],ho.prototype,"view",2),co([ut()],ho.prototype,"prevView",2),ho=co([dt("wcm-modal-router")],ho);const mo=s`div{height:36px;width:max-content;display:flex;justify-content:center;align-items:center;padding:9px 15px 11px;position:absolute;top:12px;box-shadow:0 6px 14px -6px rgba(10,16,31,.3),0 10px 32px -4px rgba(10,16,31,.15);z-index:2;left:50%;transform:translateX(-50%);pointer-events:none;backdrop-filter:blur(20px) saturate(1.8);-webkit-backdrop-filter:blur(20px) saturate(1.8);border-radius:var(--wcm-notification-border-radius);border:1px solid var(--wcm-color-overlay);background-color:var(--wcm-color-overlay)}svg{margin-right:5px}@-moz-document url-prefix(){div{background-color:var(--wcm-color-bg-3)}}.wcm-success path{fill:var(--wcm-accent-color)}.wcm-error path{fill:var(--wcm-error-color)}`;var po=Object.defineProperty,uo=Object.getOwnPropertyDescriptor,wo=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?uo(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&po(e,o,a),a};let go=class extends st{constructor(){super(),this.open=!1,this.unsubscribe=void 0,this.timeout=void 0,this.unsubscribe=bt.dC.subscribe((t=>{t.open?(this.open=!0,this.timeout=setTimeout((()=>bt.dC.closeToast()),2200)):(this.open=!1,clearTimeout(this.timeout))}))}disconnectedCallback(){var t;null==(t=this.unsubscribe)||t.call(this),clearTimeout(this.timeout),bt.dC.closeToast()}render(){const{message:t,variant:e}=bt.dC.state,o={"wcm-success":"success"===e,"wcm-error":"error"===e};return this.open?H`<div class="${vt(o)}">${"success"===e?qe.CHECKMARK_ICON:null} ${"error"===e?qe.CROSS_ICON:null}<wcm-text variant="small-regular">${t}</wcm-text></div>`:null}};function vo(t,e,o){return t!==e&&(t-e<0?e-t:t-e)<=o+.1}go.styles=[Re.globalCss,mo],wo([ut()],go.prototype,"open",2),go=wo([dt("wcm-modal-toast")],go);const fo={generate(t,e,o){const r="#141414",i=[],a=function(t){const e=Array.prototype.slice.call(Oe.create(t,{errorCorrectionLevel:"Q"}).modules.data,0),o=Math.sqrt(e.length);return e.reduce(((t,e,r)=>(r%o==0?t.push([e]):t[t.length-1].push(e))&&t),[])}(t),n=e/a.length,l=[{x:0,y:0},{x:1,y:0},{x:0,y:1}];l.forEach((({x:t,y:e})=>{const o=(a.length-7)*n*t,s=(a.length-7)*n*e;for(let t=0;t<l.length;t+=1){const e=n*(7-2*t);i.push(B`<rect fill="${t%2==0?r:"#ffffff"}" height="${e}" rx="${.45*e}" ry="${.45*e}" width="${e}" x="${o+n*t}" y="${s+n*t}">`)}}));const s=Math.floor((o+25)/n),c=a.length/2-s/2,d=a.length/2+s/2-1,h=[];a.forEach(((t,e)=>{t.forEach(((t,o)=>{if(a[e][o]&&!(e<7&&o<7||e>a.length-8&&o<7||e<7&&o>a.length-8)&&!(e>c&&e<d&&o>c&&o<d)){const t=e*n+n/2,r=o*n+n/2;h.push([t,r])}}))}));const m={};return h.forEach((([t,e])=>{m[t]?m[t].push(e):m[t]=[e]})),Object.entries(m).map((([t,e])=>{const o=e.filter((t=>e.every((e=>!vo(t,e,n)))));return[Number(t),o]})).forEach((([t,e])=>{e.forEach((e=>{i.push(B`<circle cx="${t}" cy="${e}" fill="${r}" r="${n/2.5}">`)}))})),Object.entries(m).filter((([t,e])=>e.length>1)).map((([t,e])=>{const o=e.filter((t=>e.some((e=>vo(t,e,n)))));return[Number(t),o]})).map((([t,e])=>{e.sort(((t,e)=>t<e?-1:1));const o=[];for(const t of e){const e=o.find((e=>e.some((e=>vo(t,e,n)))));e?e.push(t):o.push([t])}return[t,o.map((t=>[t[0],t[t.length-1]]))]})).forEach((([t,e])=>{e.forEach((([e,o])=>{i.push(B`<line x1="${t}" x2="${t}" y1="${e}" y2="${o}" stroke="${r}" stroke-width="${n/1.25}" stroke-linecap="round">`)}))})),i}},bo=s`@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}div{position:relative;user-select:none;display:block;overflow:hidden;aspect-ratio:1/1;animation:fadeIn ease .2s}.wcm-dark{background-color:#fff;border-radius:var(--wcm-container-border-radius);padding:18px;box-shadow:0 2px 5px #000}svg:first-child,wcm-wallet-image{position:absolute;top:50%;left:50%;transform:translateY(-50%) translateX(-50%)}wcm-wallet-image{transform:translateY(-50%) translateX(-50%)}wcm-wallet-image{width:25%;height:25%;border-radius:var(--wcm-wallet-icon-border-radius)}svg:first-child{transform:translateY(-50%) translateX(-50%) scale(.9)}svg:first-child path:first-child{fill:var(--wcm-accent-color)}svg:first-child path:last-child{stroke:var(--wcm-color-overlay)}`;var yo=Object.defineProperty,xo=Object.getOwnPropertyDescriptor,$o=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?xo(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&yo(e,o,a),a};let Co=class extends st{constructor(){super(...arguments),this.uri="",this.size=0,this.imageId=void 0,this.walletId=void 0,this.imageUrl=void 0}svgTemplate(){const t="light"===bt.lH.state.themeMode?this.size:this.size-36;return B`<svg height="${t}" width="${t}">${fo.generate(this.uri,t,t/4)}</svg>`}render(){const t={"wcm-dark":"dark"===bt.lH.state.themeMode};return H`<div style="${`width: ${this.size}px`}" class="${vt(t)}">${this.walletId||this.imageUrl?H`<wcm-wallet-image walletId="${ke(this.walletId)}" imageId="${ke(this.imageId)}" imageUrl="${ke(this.imageUrl)}"></wcm-wallet-image>`:qe.WALLET_CONNECT_ICON_COLORED} ${this.svgTemplate()}</div>`}};Co.styles=[Re.globalCss,bo],$o([pt()],Co.prototype,"uri",2),$o([pt({type:Number})],Co.prototype,"size",2),$o([pt()],Co.prototype,"imageId",2),$o([pt()],Co.prototype,"walletId",2),$o([pt()],Co.prototype,"imageUrl",2),Co=$o([dt("wcm-qrcode")],Co);const Ao=s`:host{position:relative;height:28px;width:80%}input{width:100%;height:100%;line-height:28px!important;border-radius:var(--wcm-input-border-radius);font-style:normal;font-family:-apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,Ubuntu,'Helvetica Neue',sans-serif;font-feature-settings:'case' on;font-weight:500;font-size:16px;letter-spacing:-.03em;padding:0 10px 0 34px;transition:.2s all ease;color:var(--wcm-color-fg-1);background-color:var(--wcm-color-bg-3);box-shadow:inset 0 0 0 1px var(--wcm-color-overlay);caret-color:var(--wcm-accent-color)}input::placeholder{color:var(--wcm-color-fg-2)}svg{left:10px;top:4px;pointer-events:none;position:absolute;width:20px;height:20px}input:focus-within{box-shadow:inset 0 0 0 1px var(--wcm-accent-color)}path{fill:var(--wcm-color-fg-2)}`;var _o=Object.defineProperty,ko=Object.getOwnPropertyDescriptor,Oo=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?ko(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&_o(e,o,a),a};let Eo=class extends st{constructor(){super(...arguments),this.onChange=()=>null}render(){return H`<input type="text" @input="${this.onChange}" placeholder="Search wallets"> ${qe.SEARCH_ICON}`}};Eo.styles=[Re.globalCss,Ao],Oo([pt()],Eo.prototype,"onChange",2),Eo=Oo([dt("wcm-search-input")],Eo);const Io=s`@keyframes rotate{100%{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}100%{stroke-dasharray:90,150;stroke-dashoffset:-124}}svg{animation:rotate 2s linear infinite;display:flex;justify-content:center;align-items:center}svg circle{stroke-linecap:round;animation:dash 1.5s ease infinite;stroke:var(--wcm-accent-color)}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Po=class extends st{render(){return H`<svg viewBox="0 0 50 50" width="24" height="24"><circle cx="25" cy="25" r="20" fill="none" stroke-width="4" stroke="#fff"/></svg>`}};Po.styles=[Re.globalCss,Io],Po=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-spinner")],Po);const Mo=s`span{font-style:normal;font-family:var(--wcm-font-family);font-feature-settings:var(--wcm-font-feature-settings)}.wcm-xsmall-bold{font-family:var(--wcm-text-xsmall-bold-font-family);font-weight:var(--wcm-text-xsmall-bold-weight);font-size:var(--wcm-text-xsmall-bold-size);line-height:var(--wcm-text-xsmall-bold-line-height);letter-spacing:var(--wcm-text-xsmall-bold-letter-spacing);text-transform:var(--wcm-text-xsmall-bold-text-transform)}.wcm-xsmall-regular{font-family:var(--wcm-text-xsmall-regular-font-family);font-weight:var(--wcm-text-xsmall-regular-weight);font-size:var(--wcm-text-xsmall-regular-size);line-height:var(--wcm-text-xsmall-regular-line-height);letter-spacing:var(--wcm-text-xsmall-regular-letter-spacing);text-transform:var(--wcm-text-xsmall-regular-text-transform)}.wcm-small-thin{font-family:var(--wcm-text-small-thin-font-family);font-weight:var(--wcm-text-small-thin-weight);font-size:var(--wcm-text-small-thin-size);line-height:var(--wcm-text-small-thin-line-height);letter-spacing:var(--wcm-text-small-thin-letter-spacing);text-transform:var(--wcm-text-small-thin-text-transform)}.wcm-small-regular{font-family:var(--wcm-text-small-regular-font-family);font-weight:var(--wcm-text-small-regular-weight);font-size:var(--wcm-text-small-regular-size);line-height:var(--wcm-text-small-regular-line-height);letter-spacing:var(--wcm-text-small-regular-letter-spacing);text-transform:var(--wcm-text-small-regular-text-transform)}.wcm-medium-regular{font-family:var(--wcm-text-medium-regular-font-family);font-weight:var(--wcm-text-medium-regular-weight);font-size:var(--wcm-text-medium-regular-size);line-height:var(--wcm-text-medium-regular-line-height);letter-spacing:var(--wcm-text-medium-regular-letter-spacing);text-transform:var(--wcm-text-medium-regular-text-transform)}.wcm-big-bold{font-family:var(--wcm-text-big-bold-font-family);font-weight:var(--wcm-text-big-bold-weight);font-size:var(--wcm-text-big-bold-size);line-height:var(--wcm-text-big-bold-line-height);letter-spacing:var(--wcm-text-big-bold-letter-spacing);text-transform:var(--wcm-text-big-bold-text-transform)}:host(*){color:var(--wcm-color-fg-1)}.wcm-color-primary{color:var(--wcm-color-fg-1)}.wcm-color-secondary{color:var(--wcm-color-fg-2)}.wcm-color-tertiary{color:var(--wcm-color-fg-3)}.wcm-color-inverse{color:var(--wcm-accent-fill-color)}.wcm-color-accnt{color:var(--wcm-accent-color)}.wcm-color-error{color:var(--wcm-error-color)}`;var So=Object.defineProperty,Lo=Object.getOwnPropertyDescriptor,Ro=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?Lo(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&So(e,o,a),a};let To=class extends st{constructor(){super(...arguments),this.variant="medium-regular",this.color="primary"}render(){const t={"wcm-big-bold":"big-bold"===this.variant,"wcm-medium-regular":"medium-regular"===this.variant,"wcm-small-regular":"small-regular"===this.variant,"wcm-small-thin":"small-thin"===this.variant,"wcm-xsmall-regular":"xsmall-regular"===this.variant,"wcm-xsmall-bold":"xsmall-bold"===this.variant,"wcm-color-primary":"primary"===this.color,"wcm-color-secondary":"secondary"===this.color,"wcm-color-tertiary":"tertiary"===this.color,"wcm-color-inverse":"inverse"===this.color,"wcm-color-accnt":"accent"===this.color,"wcm-color-error":"error"===this.color};return H`<span><slot class="${vt(t)}"></slot></span>`}};To.styles=[Re.globalCss,Mo],Ro([pt()],To.prototype,"variant",2),Ro([pt()],To.prototype,"color",2),To=Ro([dt("wcm-text")],To);const jo=s`button{width:100%;height:100%;border-radius:var(--wcm-button-hover-highlight-border-radius);display:flex;align-items:flex-start}button:active{background-color:var(--wcm-color-overlay)}@media(hover:hover){button:hover{background-color:var(--wcm-color-overlay)}}button>div{width:80px;padding:5px 0;display:flex;flex-direction:column;align-items:center}wcm-text{width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-align:center}wcm-wallet-image{height:60px;width:60px;transition:all .2s ease;border-radius:var(--wcm-wallet-icon-border-radius);margin-bottom:5px}.wcm-sublabel{margin-top:2px}`;var Wo=Object.defineProperty,Do=Object.getOwnPropertyDescriptor,No=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?Do(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&Wo(e,o,a),a};let Uo=class extends st{constructor(){super(...arguments),this.onClick=()=>null,this.name="",this.walletId="",this.label=void 0,this.imageId=void 0,this.installed=!1,this.recent=!1}sublabelTemplate(){return this.recent?H`<wcm-text class="wcm-sublabel" variant="xsmall-bold" color="tertiary">RECENT</wcm-text>`:this.installed?H`<wcm-text class="wcm-sublabel" variant="xsmall-bold" color="tertiary">INSTALLED</wcm-text>`:null}handleClick(){bt.vZ.click({name:"WALLET_BUTTON",walletId:this.walletId}),this.onClick()}render(){var t;return H`<button @click="${this.handleClick.bind(this)}"><div><wcm-wallet-image walletId="${this.walletId}" imageId="${ke(this.imageId)}"></wcm-wallet-image><wcm-text variant="xsmall-regular">${null!=(t=this.label)?t:ao.getWalletName(this.name,!0)}</wcm-text>${this.sublabelTemplate()}</div></button>`}};Uo.styles=[Re.globalCss,jo],No([pt()],Uo.prototype,"onClick",2),No([pt()],Uo.prototype,"name",2),No([pt()],Uo.prototype,"walletId",2),No([pt()],Uo.prototype,"label",2),No([pt()],Uo.prototype,"imageId",2),No([pt({type:Boolean})],Uo.prototype,"installed",2),No([pt({type:Boolean})],Uo.prototype,"recent",2),Uo=No([dt("wcm-wallet-button")],Uo);const Ho=s`:host{display:block}div{overflow:hidden;position:relative;border-radius:inherit;width:100%;height:100%;background-color:var(--wcm-color-overlay)}svg{position:relative;width:100%;height:100%}div::after{content:'';position:absolute;top:0;bottom:0;left:0;right:0;border-radius:inherit;border:1px solid var(--wcm-color-overlay)}div img{width:100%;height:100%;object-fit:cover;object-position:center}#wallet-placeholder-fill{fill:var(--wcm-color-bg-3)}#wallet-placeholder-dash{stroke:var(--wcm-color-overlay)}`;var Bo=Object.defineProperty,Zo=Object.getOwnPropertyDescriptor,zo=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?Zo(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&Bo(e,o,a),a};let Vo=class extends st{constructor(){super(...arguments),this.walletId="",this.imageId=void 0,this.imageUrl=void 0}render(){var t;const e=null!=(t=this.imageUrl)&&t.length?this.imageUrl:ao.getWalletIcon({id:this.walletId,image_id:this.imageId});return H`${e.length?H`<div><img crossorigin="anonymous" src="${e}" alt="${this.id}"></div>`:qe.WALLET_PLACEHOLDER}`}};Vo.styles=[Re.globalCss,Ho],zo([pt()],Vo.prototype,"walletId",2),zo([pt()],Vo.prototype,"imageId",2),zo([pt()],Vo.prototype,"imageUrl",2),Vo=zo([dt("wcm-wallet-image")],Vo);var Fo=Object.defineProperty,qo=Object.getOwnPropertyDescriptor,Ko=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?qo(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&Fo(e,o,a),a};let Qo=class extends st{constructor(){super(),this.preload=!0,this.preloadData()}async loadImages(t){try{null!=t&&t.length&&await Promise.all(t.map((async t=>ao.preloadImage(t))))}catch{console.info("Unsuccessful attempt at preloading some images",t)}}async preloadListings(){if(bt.mb.state.enableExplorer){await bt.pV.getRecomendedWallets(),bt.IN.setIsDataLoaded(!0);const{recomendedWallets:t}=bt.pV.state,e=t.map((t=>ao.getWalletIcon(t)));await this.loadImages(e)}else bt.IN.setIsDataLoaded(!0)}async preloadCustomImages(){const t=ao.getCustomImageUrls();await this.loadImages(t)}async preloadData(){try{this.preload&&(this.preload=!1,await Promise.all([this.preloadListings(),this.preloadCustomImages()]))}catch(t){console.error(t),bt.dC.openToast("Failed preloading","error")}}};Ko([ut()],Qo.prototype,"preload",2),Qo=Ko([dt("wcm-explorer-context")],Qo);Object.defineProperty,Object.getOwnPropertyDescriptor;let Yo=class extends st{constructor(){super(),this.unsubscribeTheme=void 0,Re.setTheme(),this.unsubscribeTheme=bt.lH.subscribe(Re.setTheme)}disconnectedCallback(){var t;null==(t=this.unsubscribeTheme)||t.call(this)}};Yo=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-theme-context")],Yo);const Go=s`@keyframes scroll{0%{transform:translate3d(0,0,0)}100%{transform:translate3d(calc(-70px * 9),0,0)}}.wcm-slider{position:relative;overflow-x:hidden;padding:10px 0;margin:0 -20px;width:calc(100% + 40px)}.wcm-track{display:flex;width:calc(70px * 18);animation:scroll 20s linear infinite;opacity:.7}.wcm-track svg{margin:0 5px}wcm-wallet-image{width:60px;height:60px;margin:0 5px;border-radius:var(--wcm-wallet-icon-border-radius)}.wcm-grid{display:grid;grid-template-columns:repeat(4,80px);justify-content:space-between}.wcm-title{display:flex;align-items:center;margin-bottom:10px}.wcm-title svg{margin-right:6px}.wcm-title path{fill:var(--wcm-accent-color)}wcm-modal-footer .wcm-title{padding:0 10px}wcm-button-big{position:absolute;top:50%;left:50%;transform:translateY(-50%) translateX(-50%);filter:drop-shadow(0 0 17px var(--wcm-color-bg-1))}wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-info-footer wcm-text{text-align:center;margin-bottom:15px}#wallet-placeholder-fill{fill:var(--wcm-color-bg-3)}#wallet-placeholder-dash{stroke:var(--wcm-color-overlay)}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Xo=class extends st{onGoToQrcode(){bt.jL.push("Qrcode")}render(){const{recomendedWallets:t}=bt.pV.state,e=[...t,...t],o=2*bt.Ao.RECOMMENDED_WALLET_AMOUNT;return H`<wcm-modal-header title="Connect your wallet" .onAction="${this.onGoToQrcode}" .actionIcon="${qe.QRCODE_ICON}"></wcm-modal-header><wcm-modal-content><div class="wcm-title">${qe.MOBILE_ICON}<wcm-text variant="small-regular" color="accent">WalletConnect</wcm-text></div><div class="wcm-slider"><div class="wcm-track">${[...Array(o)].map(((t,o)=>{const r=e[o%e.length];return r?H`<wcm-wallet-image walletId="${r.id}" imageId="${r.image_id}"></wcm-wallet-image>`:qe.WALLET_PLACEHOLDER}))}</div><wcm-button-big @click="${ao.handleAndroidLinking}"><wcm-text variant="medium-regular" color="inverse">Select Wallet</wcm-text></wcm-button-big></div></wcm-modal-content><wcm-info-footer><wcm-text color="secondary" variant="small-thin">Choose WalletConnect to see supported apps on your device</wcm-text></wcm-info-footer>`}};Xo.styles=[Re.globalCss,Go],Xo=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-android-wallet-selection")],Xo);const Jo=s`@keyframes loading{to{stroke-dashoffset:0}}@keyframes shake{10%,90%{transform:translate3d(-1px,0,0)}20%,80%{transform:translate3d(1px,0,0)}30%,50%,70%{transform:translate3d(-2px,0,0)}40%,60%{transform:translate3d(2px,0,0)}}:host{display:flex;flex-direction:column;align-items:center}div{position:relative;width:110px;height:110px;display:flex;justify-content:center;align-items:center;margin:40px 0 20px 0;transform:translate3d(0,0,0)}svg{position:absolute;width:110px;height:110px;fill:none;stroke:transparent;stroke-linecap:round;stroke-width:2px;top:0;left:0}use{stroke:var(--wcm-accent-color);animation:loading 1s linear infinite}wcm-wallet-image{border-radius:var(--wcm-wallet-icon-large-border-radius);width:90px;height:90px}wcm-text{margin-bottom:40px}.wcm-error svg{stroke:var(--wcm-error-color)}.wcm-error use{display:none}.wcm-error{animation:shake .4s cubic-bezier(.36,.07,.19,.97) both}.wcm-stale svg,.wcm-stale use{display:none}`;var tr=Object.defineProperty,er=Object.getOwnPropertyDescriptor,or=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?er(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&tr(e,o,a),a};let rr=class extends st{constructor(){super(...arguments),this.walletId=void 0,this.imageId=void 0,this.isError=!1,this.isStale=!1,this.label=""}svgLoaderTemplate(){var t,e;const o=null!=(e=null==(t=bt.lH.state.themeVariables)?void 0:t["--wcm-wallet-icon-large-border-radius"])?e:Re.getPreset("--wcm-wallet-icon-large-border-radius");let r=0;return r=o.includes("%")?.88*parseInt(o,10):parseInt(o,10),r*=1.17,H`<svg viewBox="0 0 110 110" width="110" height="110"><rect id="wcm-loader" x="2" y="2" width="106" height="106" rx="${r}"/><use xlink:href="#wcm-loader" stroke-dasharray="106 ${317-1.57*r}" stroke-dashoffset="${425-1.8*r}"></use></svg>`}render(){const t={"wcm-error":this.isError,"wcm-stale":this.isStale};return H`<div class="${vt(t)}">${this.svgLoaderTemplate()}<wcm-wallet-image walletId="${ke(this.walletId)}" imageId="${ke(this.imageId)}"></wcm-wallet-image></div><wcm-text variant="medium-regular" color="${this.isError?"error":"primary"}">${this.isError?"Connection declined":this.label}</wcm-text>`}};rr.styles=[Re.globalCss,Jo],or([pt()],rr.prototype,"walletId",2),or([pt()],rr.prototype,"imageId",2),or([pt({type:Boolean})],rr.prototype,"isError",2),or([pt({type:Boolean})],rr.prototype,"isStale",2),or([pt()],rr.prototype,"label",2),rr=or([dt("wcm-connector-waiting")],rr);const ir={manualWallets(){var t,e;const{mobileWallets:o,desktopWallets:r}=bt.mb.state,i=null==(t=ir.recentWallet())?void 0:t.id,a=bt.Ao.isMobile()?o:r,n=a?.filter((t=>i!==t.id));return null!=(e=bt.Ao.isMobile()?n?.map((({id:t,name:e,links:o})=>({id:t,name:e,mobile:o,links:o}))):n?.map((({id:t,name:e,links:o})=>({id:t,name:e,desktop:o,links:o}))))?e:[]},recentWallet:()=>ao.getRecentWallet(),recomendedWallets(t=!1){var e;const o=t||null==(e=ir.recentWallet())?void 0:e.id,{recomendedWallets:r}=bt.pV.state;return r.filter((t=>o!==t.id))}},ar={onConnecting(t){ao.goToConnectingView(t)},manualWalletsTemplate(){return ir.manualWallets().map((t=>H`<wcm-wallet-button walletId="${t.id}" name="${t.name}" .onClick="${()=>this.onConnecting(t)}"></wcm-wallet-button>`))},recomendedWalletsTemplate(t=!1){return ir.recomendedWallets(t).map((t=>H`<wcm-wallet-button name="${t.name}" walletId="${t.id}" imageId="${t.image_id}" .onClick="${()=>this.onConnecting(t)}"></wcm-wallet-button>`))},recentWalletTemplate(){const t=ir.recentWallet();if(t)return H`<wcm-wallet-button name="${t.name}" walletId="${t.id}" imageId="${ke(t.image_id)}" .recent="${!0}" .onClick="${()=>this.onConnecting(t)}"></wcm-wallet-button>`}},nr=s`.wcm-grid{display:grid;grid-template-columns:repeat(4,80px);justify-content:space-between}.wcm-desktop-title,.wcm-mobile-title{display:flex;align-items:center}.wcm-mobile-title{justify-content:space-between;margin-bottom:20px;margin-top:-10px}.wcm-desktop-title{margin-bottom:10px;padding:0 10px}.wcm-subtitle{display:flex;align-items:center}.wcm-subtitle:last-child path{fill:var(--wcm-color-fg-3)}.wcm-desktop-title svg,.wcm-mobile-title svg{margin-right:6px}.wcm-desktop-title path,.wcm-mobile-title path{fill:var(--wcm-accent-color)}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let lr=class extends st{render(){const{explorerExcludedWalletIds:t,enableExplorer:e}=bt.mb.state,o="ALL"!==t&&e,r=ar.manualWalletsTemplate(),i=ar.recomendedWalletsTemplate();let a=[ar.recentWalletTemplate(),...r,...i];a=a.filter(Boolean);const n=a.length>4||o;let l=[];l=n?a.slice(0,3):a;const s=Boolean(l.length);return H`<wcm-modal-header .border="${!0}" title="Connect your wallet" .onAction="${ao.handleUriCopy}" .actionIcon="${qe.COPY_ICON}"></wcm-modal-header><wcm-modal-content><div class="wcm-mobile-title"><div class="wcm-subtitle">${qe.MOBILE_ICON}<wcm-text variant="small-regular" color="accent">Mobile</wcm-text></div><div class="wcm-subtitle">${qe.SCAN_ICON}<wcm-text variant="small-regular" color="secondary">Scan with your wallet</wcm-text></div></div><wcm-walletconnect-qr></wcm-walletconnect-qr></wcm-modal-content>${s?H`<wcm-modal-footer><div class="wcm-desktop-title">${qe.DESKTOP_ICON}<wcm-text variant="small-regular" color="accent">Desktop</wcm-text></div><div class="wcm-grid">${l} ${n?H`<wcm-view-all-wallets-button></wcm-view-all-wallets-button>`:null}</div></wcm-modal-footer>`:null}`}};lr.styles=[Re.globalCss,nr],lr=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-desktop-wallet-selection")],lr);const sr=s`div{background-color:var(--wcm-color-bg-2);padding:10px 20px 15px 20px;border-top:1px solid var(--wcm-color-bg-3);text-align:center}a{color:var(--wcm-accent-color);text-decoration:none;transition:opacity .2s ease-in-out;display:inline}a:active{opacity:.8}@media(hover:hover){a:hover{opacity:.8}}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let cr=class extends st{render(){const{termsOfServiceUrl:t,privacyPolicyUrl:e}=bt.mb.state;return t??e?H`<div><wcm-text variant="small-regular" color="secondary">By connecting your wallet to this app, you agree to the app's ${t?H`<a href="${t}" target="_blank" rel="noopener noreferrer">Terms of Service</a>`:null} ${t&&e?"and":null} ${e?H`<a href="${e}" target="_blank" rel="noopener noreferrer">Privacy Policy</a>`:null}</wcm-text></div>`:null}};cr.styles=[Re.globalCss,sr],cr=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-legal-notice")],cr);const dr=s`div{display:grid;grid-template-columns:repeat(4,80px);margin:0 -10px;justify-content:space-between;row-gap:10px}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let hr=class extends st{onQrcode(){bt.jL.push("Qrcode")}render(){const{explorerExcludedWalletIds:t,enableExplorer:e}=bt.mb.state,o="ALL"!==t&&e,r=ar.manualWalletsTemplate(),i=ar.recomendedWalletsTemplate();let a=[ar.recentWalletTemplate(),...r,...i];a=a.filter(Boolean);const n=a.length>8||o;let l=[];l=n?a.slice(0,7):a;const s=Boolean(l.length);return H`<wcm-modal-header title="Connect your wallet" .onAction="${this.onQrcode}" .actionIcon="${qe.QRCODE_ICON}"></wcm-modal-header>${s?H`<wcm-modal-content><div>${l} ${n?H`<wcm-view-all-wallets-button></wcm-view-all-wallets-button>`:null}</div></wcm-modal-content>`:null}`}};hr.styles=[Re.globalCss,dr],hr=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-mobile-wallet-selection")],hr);const mr=s`:host{all:initial}.wcm-overlay{top:0;bottom:0;left:0;right:0;position:fixed;z-index:var(--wcm-z-index);overflow:hidden;display:flex;justify-content:center;align-items:center;opacity:0;pointer-events:none;background-color:var(--wcm-overlay-background-color);backdrop-filter:var(--wcm-overlay-backdrop-filter)}@media(max-height:720px) and (orientation:landscape){.wcm-overlay{overflow:scroll;align-items:flex-start;padding:20px 0}}.wcm-active{pointer-events:auto}.wcm-container{position:relative;max-width:360px;width:100%;outline:0;border-radius:var(--wcm-background-border-radius) var(--wcm-background-border-radius) var(--wcm-container-border-radius) var(--wcm-container-border-radius);border:1px solid var(--wcm-color-overlay);overflow:hidden}.wcm-card{width:100%;position:relative;border-radius:var(--wcm-container-border-radius);overflow:hidden;box-shadow:0 6px 14px -6px rgba(10,16,31,.12),0 10px 32px -4px rgba(10,16,31,.1),0 0 0 1px var(--wcm-color-overlay);background-color:var(--wcm-color-bg-1);color:var(--wcm-color-fg-1)}@media(max-width:600px){.wcm-container{max-width:440px;border-radius:var(--wcm-background-border-radius) var(--wcm-background-border-radius) 0 0}.wcm-card{border-radius:var(--wcm-container-border-radius) var(--wcm-container-border-radius) 0 0}.wcm-overlay{align-items:flex-end}}@media(max-width:440px){.wcm-container{border:0}}`;var pr=Object.defineProperty,ur=Object.getOwnPropertyDescriptor,wr=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?ur(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&pr(e,o,a),a};let gr=class extends st{constructor(){super(),this.open=!1,this.active=!1,this.unsubscribeModal=void 0,this.abortController=void 0,this.unsubscribeModal=bt.D8.subscribe((t=>{t.open?this.onOpenModalEvent():this.onCloseModalEvent()}))}disconnectedCallback(){var t;null==(t=this.unsubscribeModal)||t.call(this)}get overlayEl(){return ao.getShadowRootElement(this,".wcm-overlay")}get containerEl(){return ao.getShadowRootElement(this,".wcm-container")}toggleBodyScroll(t){if(document.querySelector("body"))if(t){const t=document.getElementById("wcm-styles");t?.remove()}else document.head.insertAdjacentHTML("beforeend",'<style id="wcm-styles">html,body{touch-action:none;overflow:hidden;overscroll-behavior:contain;}</style>')}onCloseModal(t){t.target===t.currentTarget&&bt.D8.close()}onOpenModalEvent(){this.toggleBodyScroll(!1),this.addKeyboardEvents(),this.open=!0,setTimeout((async()=>{const t=ao.isMobileAnimation()?{y:["50vh","0vh"]}:{scale:[.98,1]};await Promise.all([_e(this.overlayEl,{opacity:[0,1]},{delay:.1,duration:.2}).finished,_e(this.containerEl,t,{delay:.1,duration:.2}).finished]),this.active=!0}),0)}async onCloseModalEvent(){this.toggleBodyScroll(!0),this.removeKeyboardEvents();const t=ao.isMobileAnimation()?{y:["0vh","50vh"]}:{scale:[1,.98]};await Promise.all([_e(this.overlayEl,{opacity:[1,0]},{duration:.2}).finished,_e(this.containerEl,t,{duration:.2}).finished]),this.containerEl.removeAttribute("style"),this.active=!1,this.open=!1}addKeyboardEvents(){this.abortController=new AbortController,window.addEventListener("keydown",(t=>{var e;"Escape"===t.key?bt.D8.close():"Tab"===t.key&&(null!=(e=t.target)&&e.tagName.includes("wcm-")||this.containerEl.focus())}),this.abortController),this.containerEl.focus()}removeKeyboardEvents(){var t;null==(t=this.abortController)||t.abort(),this.abortController=void 0}render(){const t={"wcm-overlay":!0,"wcm-active":this.active};return H`<wcm-explorer-context></wcm-explorer-context><wcm-theme-context></wcm-theme-context><div id="wcm-modal" class="${vt(t)}" @click="${this.onCloseModal}" role="alertdialog" aria-modal="true"><div class="wcm-container" tabindex="0">${this.open?H`<wcm-modal-backcard></wcm-modal-backcard><div class="wcm-card"><wcm-modal-router></wcm-modal-router><wcm-modal-toast></wcm-modal-toast></div>`:null}</div></div>`}};gr.styles=[Re.globalCss,mr],wr([ut()],gr.prototype,"open",2),wr([ut()],gr.prototype,"active",2),gr=wr([dt("wcm-modal")],gr);const vr=s`div{display:flex;margin-top:15px}slot{display:inline-block;margin:0 5px}wcm-button{margin:0 5px}`;var fr=Object.defineProperty,br=Object.getOwnPropertyDescriptor,yr=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?br(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&fr(e,o,a),a};let xr=class extends st{constructor(){super(...arguments),this.isMobile=!1,this.isDesktop=!1,this.isWeb=!1,this.isRetry=!1}onMobile(){bt.Ao.isMobile()?bt.jL.replace("MobileConnecting"):bt.jL.replace("MobileQrcodeConnecting")}onDesktop(){bt.jL.replace("DesktopConnecting")}onWeb(){bt.jL.replace("WebConnecting")}render(){return H`<div>${this.isRetry?H`<slot></slot>`:null} ${this.isMobile?H`<wcm-button .onClick="${this.onMobile}" .iconLeft="${qe.MOBILE_ICON}" variant="outline">Mobile</wcm-button>`:null} ${this.isDesktop?H`<wcm-button .onClick="${this.onDesktop}" .iconLeft="${qe.DESKTOP_ICON}" variant="outline">Desktop</wcm-button>`:null} ${this.isWeb?H`<wcm-button .onClick="${this.onWeb}" .iconLeft="${qe.GLOBE_ICON}" variant="outline">Web</wcm-button>`:null}</div>`}};xr.styles=[Re.globalCss,vr],yr([pt({type:Boolean})],xr.prototype,"isMobile",2),yr([pt({type:Boolean})],xr.prototype,"isDesktop",2),yr([pt({type:Boolean})],xr.prototype,"isWeb",2),yr([pt({type:Boolean})],xr.prototype,"isRetry",2),xr=yr([dt("wcm-platform-selection")],xr);const $r=s`button{display:flex;flex-direction:column;padding:5px 10px;border-radius:var(--wcm-button-hover-highlight-border-radius);height:100%;justify-content:flex-start}.wcm-icons{width:60px;height:60px;display:flex;flex-wrap:wrap;padding:7px;border-radius:var(--wcm-wallet-icon-border-radius);justify-content:space-between;align-items:center;margin-bottom:5px;background-color:var(--wcm-color-bg-2);box-shadow:inset 0 0 0 1px var(--wcm-color-overlay)}button:active{background-color:var(--wcm-color-overlay)}@media(hover:hover){button:hover{background-color:var(--wcm-color-overlay)}}.wcm-icons img{width:21px;height:21px;object-fit:cover;object-position:center;border-radius:calc(var(--wcm-wallet-icon-border-radius)/ 2);border:1px solid var(--wcm-color-overlay)}.wcm-icons svg{width:21px;height:21px}.wcm-icons img:nth-child(1),.wcm-icons img:nth-child(2),.wcm-icons svg:nth-child(1),.wcm-icons svg:nth-child(2){margin-bottom:4px}wcm-text{width:100%;text-align:center}#wallet-placeholder-fill{fill:var(--wcm-color-bg-3)}#wallet-placeholder-dash{stroke:var(--wcm-color-overlay)}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Cr=class extends st{onClick(){bt.jL.push("WalletExplorer")}render(){const{recomendedWallets:t}=bt.pV.state,e=[...t,...ir.manualWallets()].reverse().slice(0,4);return H`<button @click="${this.onClick}"><div class="wcm-icons">${e.map((t=>{const e=ao.getWalletIcon(t);if(e)return H`<img crossorigin="anonymous" src="${e}">`;const o=ao.getWalletIcon({id:t.id});return o?H`<img crossorigin="anonymous" src="${o}">`:qe.WALLET_PLACEHOLDER}))} ${[...Array(4-e.length)].map((()=>qe.WALLET_PLACEHOLDER))}</div><wcm-text variant="xsmall-regular">View All</wcm-text></button>`}};Cr.styles=[Re.globalCss,$r],Cr=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-view-all-wallets-button")],Cr);const Ar=s`.wcm-qr-container{width:100%;display:flex;justify-content:center;align-items:center;aspect-ratio:1/1}`;var _r=Object.defineProperty,kr=Object.getOwnPropertyDescriptor,Or=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?kr(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&_r(e,o,a),a};let Er=class extends st{constructor(){super(),this.walletId="",this.imageId="",this.uri="",setTimeout((()=>{const{walletConnectUri:t}=bt.IN.state;this.uri=t}),0)}get overlayEl(){return ao.getShadowRootElement(this,".wcm-qr-container")}render(){return H`<div class="wcm-qr-container">${this.uri?H`<wcm-qrcode size="${this.overlayEl.offsetWidth}" uri="${this.uri}" walletId="${ke(this.walletId)}" imageId="${ke(this.imageId)}"></wcm-qrcode>`:H`<wcm-spinner></wcm-spinner>`}</div>`}};Er.styles=[Re.globalCss,Ar],Or([pt()],Er.prototype,"walletId",2),Or([pt()],Er.prototype,"imageId",2),Or([ut()],Er.prototype,"uri",2),Er=Or([dt("wcm-walletconnect-qr")],Er);Object.defineProperty,Object.getOwnPropertyDescriptor;let Ir=class extends st{viewTemplate(){return bt.Ao.isAndroid()?H`<wcm-android-wallet-selection></wcm-android-wallet-selection>`:bt.Ao.isMobile()?H`<wcm-mobile-wallet-selection></wcm-mobile-wallet-selection>`:H`<wcm-desktop-wallet-selection></wcm-desktop-wallet-selection>`}render(){return H`${this.viewTemplate()}<wcm-legal-notice></wcm-legal-notice>`}};Ir.styles=[Re.globalCss],Ir=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-connect-wallet-view")],Ir);const Pr=s`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}`;var Mr=Object.defineProperty,Sr=Object.getOwnPropertyDescriptor,Lr=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?Sr(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&Mr(e,o,a),a};let Rr=class extends st{constructor(){super(),this.isError=!1,this.openDesktopApp()}onFormatAndRedirect(t){const{desktop:e,name:o}=bt.Ao.getWalletRouterData(),r=e?.native;if(r){const e=bt.Ao.formatNativeUrl(r,t,o);bt.Ao.openHref(e,"_self")}}openDesktopApp(){const{walletConnectUri:t}=bt.IN.state,e=bt.Ao.getWalletRouterData();ao.setRecentWallet(e),t&&this.onFormatAndRedirect(t)}render(){const{name:t,id:e,image_id:o}=bt.Ao.getWalletRouterData(),{isMobile:r,isWeb:i}=ao.getCachedRouterWalletPlatforms();return H`<wcm-modal-header title="${t}" .onAction="${ao.handleUriCopy}" .actionIcon="${qe.COPY_ICON}"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId="${e}" imageId="${ke(o)}" label="${`Continue in ${t}...`}" .isError="${this.isError}"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer><wcm-text color="secondary" variant="small-thin">${`Connection can continue loading if ${t} is not installed on your device`}</wcm-text><wcm-platform-selection .isMobile="${r}" .isWeb="${i}" .isRetry="${!0}"><wcm-button .onClick="${this.openDesktopApp.bind(this)}" .iconRight="${qe.RETRY_ICON}">Retry</wcm-button></wcm-platform-selection></wcm-info-footer>`}};Rr.styles=[Re.globalCss,Pr],Lr([ut()],Rr.prototype,"isError",2),Rr=Lr([dt("wcm-desktop-connecting-view")],Rr);const Tr=s`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}wcm-button{margin-top:15px}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let jr=class extends st{onInstall(t){t&&bt.Ao.openHref(t,"_blank")}render(){const{name:t,id:e,image_id:o,homepage:r}=bt.Ao.getWalletRouterData();return H`<wcm-modal-header title="${t}"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId="${e}" imageId="${ke(o)}" label="Not Detected" .isStale="${!0}"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer><wcm-text color="secondary" variant="small-thin">${`Download ${t} to continue. If multiple browser extensions are installed, disable non ${t} ones and try again`}</wcm-text><wcm-button .onClick="${()=>this.onInstall(r)}" .iconLeft="${qe.ARROW_DOWN_ICON}">Download</wcm-button></wcm-info-footer>`}};jr.styles=[Re.globalCss,Tr],jr=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-install-wallet-view")],jr);const Wr=s`wcm-wallet-image{border-radius:var(--wcm-wallet-icon-large-border-radius);width:96px;height:96px;margin-bottom:20px}wcm-info-footer{display:flex;width:100%}.wcm-app-store{justify-content:space-between}.wcm-app-store wcm-wallet-image{margin-right:10px;margin-bottom:0;width:28px;height:28px;border-radius:var(--wcm-wallet-icon-small-border-radius)}.wcm-app-store div{display:flex;align-items:center}.wcm-app-store wcm-button{margin-right:-10px}.wcm-note{flex-direction:column;align-items:center;padding:5px 0}.wcm-note wcm-text{text-align:center}wcm-platform-selection{margin-top:-15px}.wcm-note wcm-text{margin-top:15px}.wcm-note wcm-text span{color:var(--wcm-accent-color)}`;var Dr=Object.defineProperty,Nr=Object.getOwnPropertyDescriptor,Ur=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?Nr(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&Dr(e,o,a),a};let Hr=class extends st{constructor(){super(),this.isError=!1,this.openMobileApp()}onFormatAndRedirect(t,e=!1){const{mobile:o,name:r}=bt.Ao.getWalletRouterData(),i=o?.native,a=o?.universal;if(i&&!e){const e=bt.Ao.formatNativeUrl(i,t,r);bt.Ao.openHref(e,"_self")}else if(a){const e=bt.Ao.formatUniversalUrl(a,t,r);bt.Ao.openHref(e,"_self")}}openMobileApp(t=!1){const{walletConnectUri:e}=bt.IN.state,o=bt.Ao.getWalletRouterData();ao.setRecentWallet(o),e&&this.onFormatAndRedirect(e,t)}onGoToAppStore(t){t&&bt.Ao.openHref(t,"_blank")}render(){const{name:t,id:e,image_id:o,app:r,mobile:i}=bt.Ao.getWalletRouterData(),{isWeb:a}=ao.getCachedRouterWalletPlatforms(),n=r?.ios,l=i?.universal;return H`<wcm-modal-header title="${t}"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId="${e}" imageId="${ke(o)}" label="Tap 'Open' to continue…" .isError="${this.isError}"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer class="wcm-note"><wcm-platform-selection .isWeb="${a}" .isRetry="${!0}"><wcm-button .onClick="${()=>this.openMobileApp(!1)}" .iconRight="${qe.RETRY_ICON}">Retry</wcm-button></wcm-platform-selection>${l?H`<wcm-text color="secondary" variant="small-thin">Still doesn't work? <span tabindex="0" @click="${()=>this.openMobileApp(!0)}">Try this alternate link</span></wcm-text>`:null}</wcm-info-footer><wcm-info-footer class="wcm-app-store"><div><wcm-wallet-image walletId="${e}" imageId="${ke(o)}"></wcm-wallet-image><wcm-text>${`Get ${t}`}</wcm-text></div><wcm-button .iconRight="${qe.ARROW_RIGHT_ICON}" .onClick="${()=>this.onGoToAppStore(n)}" variant="ghost">App Store</wcm-button></wcm-info-footer>`}};Hr.styles=[Re.globalCss,Wr],Ur([ut()],Hr.prototype,"isError",2),Hr=Ur([dt("wcm-mobile-connecting-view")],Hr);const Br=s`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}`;Object.defineProperty,Object.getOwnPropertyDescriptor;let Zr=class extends st{render(){const{name:t,id:e,image_id:o}=bt.Ao.getWalletRouterData(),{isDesktop:r,isWeb:i}=ao.getCachedRouterWalletPlatforms();return H`<wcm-modal-header title="${t}" .onAction="${ao.handleUriCopy}" .actionIcon="${qe.COPY_ICON}"></wcm-modal-header><wcm-modal-content><wcm-walletconnect-qr walletId="${e}" imageId="${ke(o)}"></wcm-walletconnect-qr></wcm-modal-content><wcm-info-footer><wcm-text color="secondary" variant="small-thin">${`Scan this QR Code with your phone's camera or inside ${t} app`}</wcm-text><wcm-platform-selection .isDesktop="${r}" .isWeb="${i}"></wcm-platform-selection></wcm-info-footer>`}};Zr.styles=[Re.globalCss,Br],Zr=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-mobile-qr-connecting-view")],Zr);Object.defineProperty,Object.getOwnPropertyDescriptor;let zr=class extends st{render(){return H`<wcm-modal-header title="Scan the code" .onAction="${ao.handleUriCopy}" .actionIcon="${qe.COPY_ICON}"></wcm-modal-header><wcm-modal-content><wcm-walletconnect-qr></wcm-walletconnect-qr></wcm-modal-content>`}};zr.styles=[Re.globalCss],zr=((t,e)=>{for(var o,r=e,i=t.length-1;i>=0;i--)(o=t[i])&&(r=o(r)||r);return r})([dt("wcm-qrcode-view")],zr);const Vr=s`wcm-modal-content{height:clamp(200px,60vh,600px);display:block;overflow:scroll;scrollbar-width:none;position:relative;margin-top:1px}.wcm-grid{display:grid;grid-template-columns:repeat(4,80px);justify-content:space-between;margin:-15px -10px;padding-top:20px}wcm-modal-content::after,wcm-modal-content::before{content:'';position:fixed;pointer-events:none;z-index:1;width:100%;height:20px;opacity:1}wcm-modal-content::before{box-shadow:0 -1px 0 0 var(--wcm-color-bg-1);background:linear-gradient(var(--wcm-color-bg-1),rgba(255,255,255,0))}wcm-modal-content::after{box-shadow:0 1px 0 0 var(--wcm-color-bg-1);background:linear-gradient(rgba(255,255,255,0),var(--wcm-color-bg-1));top:calc(100% - 20px)}wcm-modal-content::-webkit-scrollbar{display:none}.wcm-placeholder-block{display:flex;justify-content:center;align-items:center;height:100px;overflow:hidden}.wcm-empty,.wcm-loading{display:flex}.wcm-loading .wcm-placeholder-block{height:100%}.wcm-end-reached .wcm-placeholder-block{height:0;opacity:0}.wcm-empty .wcm-placeholder-block{opacity:1;height:100%}wcm-wallet-button{margin:calc((100% - 60px)/ 3) 0}`;var Fr=Object.defineProperty,qr=Object.getOwnPropertyDescriptor,Kr=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?qr(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&Fr(e,o,a),a};let Qr=class extends st{constructor(){super(...arguments),this.loading=!bt.pV.state.wallets.listings.length,this.firstFetch=!bt.pV.state.wallets.listings.length,this.search="",this.endReached=!1,this.intersectionObserver=void 0,this.searchDebounce=ao.debounce((t=>{t.length>=1?(this.firstFetch=!0,this.endReached=!1,this.search=t,bt.pV.resetSearch(),this.fetchWallets()):this.search&&(this.search="",this.endReached=this.isLastPage(),bt.pV.resetSearch())}))}firstUpdated(){this.createPaginationObserver()}disconnectedCallback(){var t;null==(t=this.intersectionObserver)||t.disconnect()}get placeholderEl(){return ao.getShadowRootElement(this,".wcm-placeholder-block")}createPaginationObserver(){this.intersectionObserver=new IntersectionObserver((([t])=>{t.isIntersecting&&(!this.search||!this.firstFetch)&&this.fetchWallets()})),this.intersectionObserver.observe(this.placeholderEl)}isLastPage(){const{wallets:t,search:e}=bt.pV.state,{listings:o,total:r}=this.search?e:t;return r<=40||o.length>=r}async fetchWallets(){var t;const{wallets:e,search:o}=bt.pV.state,{listings:r,total:i,page:a}=this.search?o:e;if(!this.endReached&&(this.firstFetch||i>40&&r.length<i))try{this.loading=!0;const e=null==(t=bt.IN.state.chains)?void 0:t.join(","),{listings:o}=await bt.pV.getWallets({page:this.firstFetch?1:a+1,entries:40,search:this.search,version:2,chains:e}),r=o.map((t=>ao.getWalletIcon(t)));await Promise.all([...r.map((async t=>ao.preloadImage(t))),bt.Ao.wait(300)]),this.endReached=this.isLastPage()}catch(t){console.error(t),bt.dC.openToast(ao.getErrorMessage(t),"error")}finally{this.loading=!1,this.firstFetch=!1}}onConnect(t){bt.Ao.isAndroid()?ao.handleMobileLinking(t):ao.goToConnectingView(t)}onSearchChange(t){const{value:e}=t.target;this.searchDebounce(e)}render(){const{wallets:t,search:e}=bt.pV.state,{listings:o}=this.search?e:t,r=this.loading&&!o.length,i=this.search.length>=3;let a=ar.manualWalletsTemplate(),n=ar.recomendedWalletsTemplate(!0);i&&(a=a.filter((({values:t})=>ao.caseSafeIncludes(t[0],this.search))),n=n.filter((({values:t})=>ao.caseSafeIncludes(t[0],this.search))));const l=!this.loading&&!o.length&&!n.length,s={"wcm-loading":r,"wcm-end-reached":this.endReached||!this.loading,"wcm-empty":l};return H`<wcm-modal-header><wcm-search-input .onChange="${this.onSearchChange.bind(this)}"></wcm-search-input></wcm-modal-header><wcm-modal-content class="${vt(s)}"><div class="wcm-grid">${r?null:a} ${r?null:n} ${r?null:o.map((t=>H`${t?H`<wcm-wallet-button imageId="${t.image_id}" name="${t.name}" walletId="${t.id}" .onClick="${()=>this.onConnect(t)}"></wcm-wallet-button>`:null}`))}</div><div class="wcm-placeholder-block">${l?H`<wcm-text variant="big-bold" color="secondary">No results found</wcm-text>`:null} ${!l&&this.loading?H`<wcm-spinner></wcm-spinner>`:null}</div></wcm-modal-content>`}};Qr.styles=[Re.globalCss,Vr],Kr([ut()],Qr.prototype,"loading",2),Kr([ut()],Qr.prototype,"firstFetch",2),Kr([ut()],Qr.prototype,"search",2),Kr([ut()],Qr.prototype,"endReached",2),Qr=Kr([dt("wcm-wallet-explorer-view")],Qr);const Yr=s`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}`;var Gr=Object.defineProperty,Xr=Object.getOwnPropertyDescriptor,Jr=(t,e,o,r)=>{for(var i,a=r>1?void 0:r?Xr(e,o):e,n=t.length-1;n>=0;n--)(i=t[n])&&(a=(r?i(e,o,a):i(a))||a);return r&&a&&Gr(e,o,a),a};let ti=class extends st{constructor(){super(),this.isError=!1,this.openWebWallet()}onFormatAndRedirect(t){const{desktop:e,name:o}=bt.Ao.getWalletRouterData(),r=e?.universal;if(r){const e=bt.Ao.formatUniversalUrl(r,t,o);bt.Ao.openHref(e,"_blank")}}openWebWallet(){const{walletConnectUri:t}=bt.IN.state,e=bt.Ao.getWalletRouterData();ao.setRecentWallet(e),t&&this.onFormatAndRedirect(t)}render(){const{name:t,id:e,image_id:o}=bt.Ao.getWalletRouterData(),{isMobile:r,isDesktop:i}=ao.getCachedRouterWalletPlatforms(),a=bt.Ao.isMobile();return H`<wcm-modal-header title="${t}" .onAction="${ao.handleUriCopy}" .actionIcon="${qe.COPY_ICON}"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId="${e}" imageId="${ke(o)}" label="${`Continue in ${t}...`}" .isError="${this.isError}"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer><wcm-text color="secondary" variant="small-thin">${`${t} web app has opened in a new tab. Go there, accept the connection, and come back`}</wcm-text><wcm-platform-selection .isMobile="${r}" .isDesktop="${!a&&i}" .isRetry="${!0}"><wcm-button .onClick="${this.openWebWallet.bind(this)}" .iconRight="${qe.RETRY_ICON}">Retry</wcm-button></wcm-platform-selection></wcm-info-footer>`}};ti.styles=[Re.globalCss,Yr],Jr([ut()],ti.prototype,"isError",2),ti=Jr([dt("wcm-web-connecting-view")],ti)}}]);