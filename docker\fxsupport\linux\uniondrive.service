[Unit]
Description=%i Union Drive service with docker compose
After=network.target local-fs.target
Wants=network.target local-fs.target

[Service]
Type=notify
RemainAfterExit=true
WorkingDirectory=/usr/bin/fula/
ExecStart=/bin/bash -c 'chmod +x /usr/bin/fula/union-drive.sh && bash /usr/bin/fula/union-drive.sh'
ExecStop=/bin/bash -c 'pkill -f /usr/bin/fula/union-drive.sh'
Restart=on-failure
RestartSec=30
WatchdogSec=120
User=root
Group=root
NotifyAccess=all

[Install]
WantedBy=multi-user.target
