"use strict";
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkfula_webui"] = self["webpackChunkfula_webui"] || []).push([["vendors-node_modules_walletconnect_modal-ui_dist_index_js"],{

/***/ "./node_modules/@motionone/animation/dist/Animation.es.js":
/*!****************************************************************!*\
  !*** ./node_modules/@motionone/animation/dist/Animation.es.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Animation: () => (/* binding */ Animation)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/defaults.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-easing-generator.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-easing-list.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/interpolate.es.js\");\n/* harmony import */ var _utils_easing_es_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/easing.es.js */ \"./node_modules/@motionone/animation/dist/utils/easing.es.js\");\n\n\n\nclass Animation {\n    constructor(output, keyframes = [0, 1], { easing, duration: initialDuration = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.duration, delay = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.delay, endDelay = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.endDelay, repeat = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.repeat, offset, direction = \"normal\", autoplay = true, } = {}) {\n        this.startTime = null;\n        this.rate = 1;\n        this.t = 0;\n        this.cancelTimestamp = null;\n        this.easing = _motionone_utils__WEBPACK_IMPORTED_MODULE_1__.noopReturn;\n        this.duration = 0;\n        this.totalDuration = 0;\n        this.repeat = 0;\n        this.playState = \"idle\";\n        this.finished = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n        easing = easing || _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.easing;\n        if ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_2__.isEasingGenerator)(easing)) {\n            const custom = easing.createAnimation(keyframes);\n            easing = custom.easing;\n            keyframes = custom.keyframes || keyframes;\n            initialDuration = custom.duration || initialDuration;\n        }\n        this.repeat = repeat;\n        this.easing = (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_3__.isEasingList)(easing) ? _motionone_utils__WEBPACK_IMPORTED_MODULE_1__.noopReturn : (0,_utils_easing_es_js__WEBPACK_IMPORTED_MODULE_4__.getEasingFunction)(easing);\n        this.updateDuration(initialDuration);\n        const interpolate$1 = (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_5__.interpolate)(keyframes, offset, (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_3__.isEasingList)(easing) ? easing.map(_utils_easing_es_js__WEBPACK_IMPORTED_MODULE_4__.getEasingFunction) : _motionone_utils__WEBPACK_IMPORTED_MODULE_1__.noopReturn);\n        this.tick = (timestamp) => {\n            var _a;\n            // TODO: Temporary fix for OptionsResolver typing\n            delay = delay;\n            let t = 0;\n            if (this.pauseTime !== undefined) {\n                t = this.pauseTime;\n            }\n            else {\n                t = (timestamp - this.startTime) * this.rate;\n            }\n            this.t = t;\n            // Convert to seconds\n            t /= 1000;\n            // Rebase on delay\n            t = Math.max(t - delay, 0);\n            /**\n             * If this animation has finished, set the current time\n             * to the total duration.\n             */\n            if (this.playState === \"finished\" && this.pauseTime === undefined) {\n                t = this.totalDuration;\n            }\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = t / this.duration;\n            // TODO progress += iterationStart\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            iterationProgress === 1 && currentIteration--;\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const iterationIsOdd = currentIteration % 2;\n            if (direction === \"reverse\" ||\n                (direction === \"alternate\" && iterationIsOdd) ||\n                (direction === \"alternate-reverse\" && !iterationIsOdd)) {\n                iterationProgress = 1 - iterationProgress;\n            }\n            const p = t >= this.totalDuration ? 1 : Math.min(iterationProgress, 1);\n            const latest = interpolate$1(this.easing(p));\n            output(latest);\n            const isAnimationFinished = this.pauseTime === undefined &&\n                (this.playState === \"finished\" || t >= this.totalDuration + endDelay);\n            if (isAnimationFinished) {\n                this.playState = \"finished\";\n                (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, latest);\n            }\n            else if (this.playState !== \"idle\") {\n                this.frameRequestId = requestAnimationFrame(this.tick);\n            }\n        };\n        if (autoplay)\n            this.play();\n    }\n    play() {\n        const now = performance.now();\n        this.playState = \"running\";\n        if (this.pauseTime !== undefined) {\n            this.startTime = now - this.pauseTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = now;\n        }\n        this.cancelTimestamp = this.startTime;\n        this.pauseTime = undefined;\n        this.frameRequestId = requestAnimationFrame(this.tick);\n    }\n    pause() {\n        this.playState = \"paused\";\n        this.pauseTime = this.t;\n    }\n    finish() {\n        this.playState = \"finished\";\n        this.tick(0);\n    }\n    stop() {\n        var _a;\n        this.playState = \"idle\";\n        if (this.frameRequestId !== undefined) {\n            cancelAnimationFrame(this.frameRequestId);\n        }\n        (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, false);\n    }\n    cancel() {\n        this.stop();\n        this.tick(this.cancelTimestamp);\n    }\n    reverse() {\n        this.rate *= -1;\n    }\n    commitStyles() { }\n    updateDuration(duration) {\n        this.duration = duration;\n        this.totalDuration = duration * (this.repeat + 1);\n    }\n    get currentTime() {\n        return this.t;\n    }\n    set currentTime(t) {\n        if (this.pauseTime !== undefined || this.rate === 0) {\n            this.pauseTime = t;\n        }\n        else {\n            this.startTime = performance.now() - t / this.rate;\n        }\n    }\n    get playbackRate() {\n        return this.rate;\n    }\n    set playbackRate(rate) {\n        this.rate = rate;\n    }\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/animation/dist/Animation.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/animation/dist/utils/easing.es.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@motionone/animation/dist/utils/easing.es.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEasingFunction: () => (/* binding */ getEasingFunction)\n/* harmony export */ });\n/* harmony import */ var _motionone_easing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/easing */ \"./node_modules/@motionone/easing/dist/cubic-bezier.es.js\");\n/* harmony import */ var _motionone_easing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @motionone/easing */ \"./node_modules/@motionone/easing/dist/steps.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-function.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-cubic-bezier.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n\n\n\nconst namedEasings = {\n    ease: (0,_motionone_easing__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0.25, 0.1, 0.25, 1.0),\n    \"ease-in\": (0,_motionone_easing__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0.42, 0.0, 1.0, 1.0),\n    \"ease-in-out\": (0,_motionone_easing__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0.42, 0.0, 0.58, 1.0),\n    \"ease-out\": (0,_motionone_easing__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0.0, 0.0, 0.58, 1.0),\n};\nconst functionArgsRegex = /\\((.*?)\\)/;\nfunction getEasingFunction(definition) {\n    // If already an easing function, return\n    if ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_1__.isFunction)(definition))\n        return definition;\n    // If an easing curve definition, return bezier function\n    if ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_2__.isCubicBezier)(definition))\n        return (0,_motionone_easing__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(...definition);\n    // If we have a predefined easing function, return\n    const namedEasing = namedEasings[definition];\n    if (namedEasing)\n        return namedEasing;\n    // If this is a steps function, attempt to create easing curve\n    if (definition.startsWith(\"steps\")) {\n        const args = functionArgsRegex.exec(definition);\n        if (args) {\n            const argsArray = args[1].split(\",\");\n            return (0,_motionone_easing__WEBPACK_IMPORTED_MODULE_3__.steps)(parseFloat(argsArray[0]), argsArray[1].trim());\n        }\n    }\n    return _motionone_utils__WEBPACK_IMPORTED_MODULE_4__.noopReturn;\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/animation/dist/utils/easing.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/animate-style.es.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/animate-style.es.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateStyle: () => (/* binding */ animateStyle)\n/* harmony export */ });\n/* harmony import */ var _data_es_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./data.es.js */ \"./node_modules/@motionone/dom/dist/animate/data.es.js\");\n/* harmony import */ var _utils_css_var_es_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils/css-var.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/css-var.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/defaults.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-easing-generator.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-function.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-easing-list.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-number.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/time.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n/* harmony import */ var _utils_transforms_es_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/transforms.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/transforms.es.js\");\n/* harmony import */ var _utils_easing_es_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./utils/easing.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/easing.es.js\");\n/* harmony import */ var _utils_feature_detection_es_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/feature-detection.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/feature-detection.es.js\");\n/* harmony import */ var _utils_keyframes_es_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/keyframes.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/keyframes.es.js\");\n/* harmony import */ var _style_es_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./style.es.js */ \"./node_modules/@motionone/dom/dist/animate/style.es.js\");\n/* harmony import */ var _utils_get_style_name_es_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/get-style-name.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/get-style-name.es.js\");\n/* harmony import */ var _utils_stop_animation_es_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/stop-animation.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/stop-animation.es.js\");\n/* harmony import */ var _utils_get_unit_es_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/get-unit.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/get-unit.es.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction getDevToolsRecord() {\n    return window.__MOTION_DEV_TOOLS_RECORD;\n}\nfunction animateStyle(element, key, keyframesDefinition, options = {}, AnimationPolyfill) {\n    const record = getDevToolsRecord();\n    const isRecording = options.record !== false && record;\n    let animation;\n    let { duration = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.duration, delay = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.delay, endDelay = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.endDelay, repeat = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.repeat, easing = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.easing, persist = false, direction, offset, allowWebkitAcceleration = false, autoplay = true, } = options;\n    const data = (0,_data_es_js__WEBPACK_IMPORTED_MODULE_1__.getAnimationData)(element);\n    const valueIsTransform = (0,_utils_transforms_es_js__WEBPACK_IMPORTED_MODULE_2__.isTransform)(key);\n    let canAnimateNatively = _utils_feature_detection_es_js__WEBPACK_IMPORTED_MODULE_3__.supports.waapi();\n    /**\n     * If this is an individual transform, we need to map its\n     * key to a CSS variable and update the element's transform style\n     */\n    valueIsTransform && (0,_utils_transforms_es_js__WEBPACK_IMPORTED_MODULE_2__.addTransformToElement)(element, key);\n    const name = (0,_utils_get_style_name_es_js__WEBPACK_IMPORTED_MODULE_4__.getStyleName)(key);\n    const motionValue = (0,_data_es_js__WEBPACK_IMPORTED_MODULE_1__.getMotionValue)(data.values, name);\n    /**\n     * Get definition of value, this will be used to convert numerical\n     * keyframes into the default value type.\n     */\n    const definition = _utils_transforms_es_js__WEBPACK_IMPORTED_MODULE_2__.transformDefinitions.get(name);\n    /**\n     * Stop the current animation, if any. Because this will trigger\n     * commitStyles (DOM writes) and we might later trigger DOM reads,\n     * this is fired now and we return a factory function to create\n     * the actual animation that can get called in batch,\n     */\n    (0,_utils_stop_animation_es_js__WEBPACK_IMPORTED_MODULE_5__.stopAnimation)(motionValue.animation, !((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_6__.isEasingGenerator)(easing) && motionValue.generator) &&\n        options.record !== false);\n    /**\n     * Batchable factory function containing all DOM reads.\n     */\n    return () => {\n        const readInitialValue = () => { var _a, _b; return (_b = (_a = _style_es_js__WEBPACK_IMPORTED_MODULE_7__.style.get(element, name)) !== null && _a !== void 0 ? _a : definition === null || definition === void 0 ? void 0 : definition.initialValue) !== null && _b !== void 0 ? _b : 0; };\n        /**\n         * Replace null values with the previous keyframe value, or read\n         * it from the DOM if it's the first keyframe.\n         */\n        let keyframes = (0,_utils_keyframes_es_js__WEBPACK_IMPORTED_MODULE_8__.hydrateKeyframes)((0,_utils_keyframes_es_js__WEBPACK_IMPORTED_MODULE_8__.keyframesList)(keyframesDefinition), readInitialValue);\n        /**\n         * Detect unit type of keyframes.\n         */\n        const toUnit = (0,_utils_get_unit_es_js__WEBPACK_IMPORTED_MODULE_9__.getUnitConverter)(keyframes, definition);\n        if ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_6__.isEasingGenerator)(easing)) {\n            const custom = easing.createAnimation(keyframes, key !== \"opacity\", readInitialValue, name, motionValue);\n            easing = custom.easing;\n            keyframes = custom.keyframes || keyframes;\n            duration = custom.duration || duration;\n        }\n        /**\n         * If this is a CSS variable we need to register it with the browser\n         * before it can be animated natively. We also set it with setProperty\n         * rather than directly onto the element.style object.\n         */\n        if ((0,_utils_css_var_es_js__WEBPACK_IMPORTED_MODULE_10__.isCssVar)(name)) {\n            if (_utils_feature_detection_es_js__WEBPACK_IMPORTED_MODULE_3__.supports.cssRegisterProperty()) {\n                (0,_utils_css_var_es_js__WEBPACK_IMPORTED_MODULE_10__.registerCssVariable)(name);\n            }\n            else {\n                canAnimateNatively = false;\n            }\n        }\n        /**\n         * If we've been passed a custom easing function, and this browser\n         * does **not** support linear() easing, and the value is a transform\n         * (and thus a pure number) we can still support the custom easing\n         * by falling back to the animation polyfill.\n         */\n        if (valueIsTransform &&\n            !_utils_feature_detection_es_js__WEBPACK_IMPORTED_MODULE_3__.supports.linearEasing() &&\n            ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_11__.isFunction)(easing) || ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_12__.isEasingList)(easing) && easing.some(_motionone_utils__WEBPACK_IMPORTED_MODULE_11__.isFunction)))) {\n            canAnimateNatively = false;\n        }\n        /**\n         * If we can animate this value with WAAPI, do so.\n         */\n        if (canAnimateNatively) {\n            /**\n             * Convert numbers to default value types. Currently this only supports\n             * transforms but it could also support other value types.\n             */\n            if (definition) {\n                keyframes = keyframes.map((value) => (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_13__.isNumber)(value) ? definition.toDefaultUnit(value) : value);\n            }\n            /**\n             * If this browser doesn't support partial/implicit keyframes we need to\n             * explicitly provide one.\n             */\n            if (keyframes.length === 1 &&\n                (!_utils_feature_detection_es_js__WEBPACK_IMPORTED_MODULE_3__.supports.partialKeyframes() || isRecording)) {\n                keyframes.unshift(readInitialValue());\n            }\n            const animationOptions = {\n                delay: _motionone_utils__WEBPACK_IMPORTED_MODULE_14__.time.ms(delay),\n                duration: _motionone_utils__WEBPACK_IMPORTED_MODULE_14__.time.ms(duration),\n                endDelay: _motionone_utils__WEBPACK_IMPORTED_MODULE_14__.time.ms(endDelay),\n                easing: !(0,_motionone_utils__WEBPACK_IMPORTED_MODULE_12__.isEasingList)(easing)\n                    ? (0,_utils_easing_es_js__WEBPACK_IMPORTED_MODULE_15__.convertEasing)(easing, duration)\n                    : undefined,\n                direction,\n                iterations: repeat + 1,\n                fill: \"both\",\n            };\n            animation = element.animate({\n                [name]: keyframes,\n                offset,\n                easing: (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_12__.isEasingList)(easing)\n                    ? easing.map((thisEasing) => (0,_utils_easing_es_js__WEBPACK_IMPORTED_MODULE_15__.convertEasing)(thisEasing, duration))\n                    : undefined,\n            }, animationOptions);\n            /**\n             * Polyfill finished Promise in browsers that don't support it\n             */\n            if (!animation.finished) {\n                animation.finished = new Promise((resolve, reject) => {\n                    animation.onfinish = resolve;\n                    animation.oncancel = reject;\n                });\n            }\n            const target = keyframes[keyframes.length - 1];\n            animation.finished\n                .then(() => {\n                if (persist)\n                    return;\n                // Apply styles to target\n                _style_es_js__WEBPACK_IMPORTED_MODULE_7__.style.set(element, name, target);\n                // Ensure fill modes don't persist\n                animation.cancel();\n            })\n                .catch(_motionone_utils__WEBPACK_IMPORTED_MODULE_16__.noop);\n            /**\n             * This forces Webkit to run animations on the main thread by exploiting\n             * this condition:\n             * https://trac.webkit.org/browser/webkit/trunk/Source/WebCore/platform/graphics/ca/GraphicsLayerCA.cpp?rev=281238#L1099\n             *\n             * This fixes Webkit's timing bugs, like accelerated animations falling\n             * out of sync with main thread animations and massive delays in starting\n             * accelerated animations in WKWebView.\n             */\n            if (!allowWebkitAcceleration)\n                animation.playbackRate = 1.000001;\n            /**\n             * If we can't animate the value natively then we can fallback to the numbers-only\n             * polyfill for transforms.\n             */\n        }\n        else if (AnimationPolyfill && valueIsTransform) {\n            /**\n             * If any keyframe is a string (because we measured it from the DOM), we need to convert\n             * it into a number before passing to the Animation polyfill.\n             */\n            keyframes = keyframes.map((value) => typeof value === \"string\" ? parseFloat(value) : value);\n            /**\n             * If we only have a single keyframe, we need to create an initial keyframe by reading\n             * the current value from the DOM.\n             */\n            if (keyframes.length === 1) {\n                keyframes.unshift(parseFloat(readInitialValue()));\n            }\n            animation = new AnimationPolyfill((latest) => {\n                _style_es_js__WEBPACK_IMPORTED_MODULE_7__.style.set(element, name, toUnit ? toUnit(latest) : latest);\n            }, keyframes, Object.assign(Object.assign({}, options), { duration,\n                easing }));\n        }\n        else {\n            const target = keyframes[keyframes.length - 1];\n            _style_es_js__WEBPACK_IMPORTED_MODULE_7__.style.set(element, name, definition && (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_13__.isNumber)(target)\n                ? definition.toDefaultUnit(target)\n                : target);\n        }\n        if (isRecording) {\n            record(element, key, keyframes, {\n                duration,\n                delay: delay,\n                easing,\n                repeat,\n                offset,\n            }, \"motion-one\");\n        }\n        motionValue.setAnimation(animation);\n        if (animation && !autoplay)\n            animation.pause();\n        return animation;\n    };\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/animate-style.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/create-animate.es.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/create-animate.es.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAnimate: () => (/* binding */ createAnimate)\n/* harmony export */ });\n/* harmony import */ var hey_listen__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hey-listen */ \"./node_modules/hey-listen/dist/hey-listen.es.js\");\n/* harmony import */ var _animate_style_es_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./animate-style.es.js */ \"./node_modules/@motionone/dom/dist/animate/animate-style.es.js\");\n/* harmony import */ var _utils_options_es_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/options.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/options.es.js\");\n/* harmony import */ var _utils_resolve_elements_es_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/resolve-elements.es.js */ \"./node_modules/@motionone/dom/dist/utils/resolve-elements.es.js\");\n/* harmony import */ var _utils_controls_es_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/controls.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/controls.es.js\");\n/* harmony import */ var _utils_stagger_es_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/stagger.es.js */ \"./node_modules/@motionone/dom/dist/utils/stagger.es.js\");\n\n\n\n\n\n\n\nfunction createAnimate(AnimatePolyfill) {\n    return function animate(elements, keyframes, options = {}) {\n        elements = (0,_utils_resolve_elements_es_js__WEBPACK_IMPORTED_MODULE_1__.resolveElements)(elements);\n        const numElements = elements.length;\n        (0,hey_listen__WEBPACK_IMPORTED_MODULE_0__.invariant)(Boolean(numElements), \"No valid element provided.\");\n        (0,hey_listen__WEBPACK_IMPORTED_MODULE_0__.invariant)(Boolean(keyframes), \"No keyframes defined.\");\n        /**\n         * Create and start new animations\n         */\n        const animationFactories = [];\n        for (let i = 0; i < numElements; i++) {\n            const element = elements[i];\n            for (const key in keyframes) {\n                const valueOptions = (0,_utils_options_es_js__WEBPACK_IMPORTED_MODULE_2__.getOptions)(options, key);\n                valueOptions.delay = (0,_utils_stagger_es_js__WEBPACK_IMPORTED_MODULE_3__.resolveOption)(valueOptions.delay, i, numElements);\n                const animation = (0,_animate_style_es_js__WEBPACK_IMPORTED_MODULE_4__.animateStyle)(element, key, keyframes[key], valueOptions, AnimatePolyfill);\n                animationFactories.push(animation);\n            }\n        }\n        return (0,_utils_controls_es_js__WEBPACK_IMPORTED_MODULE_5__.withControls)(animationFactories, options, \n        /**\n         * TODO:\n         * If easing is set to spring or glide, duration will be dynamically\n         * generated. Ideally we would dynamically generate this from\n         * animation.effect.getComputedTiming().duration but this isn't\n         * supported in iOS13 or our number polyfill. Perhaps it's possible\n         * to Proxy animations returned from animateStyle that has duration\n         * as a getter.\n         */\n        options.duration);\n    };\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/create-animate.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/data.es.js":
/*!*************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/data.es.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAnimationData: () => (/* binding */ getAnimationData),\n/* harmony export */   getMotionValue: () => (/* binding */ getMotionValue)\n/* harmony export */ });\n/* harmony import */ var _motionone_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/types */ \"./node_modules/@motionone/types/dist/MotionValue.es.js\");\n\n\nconst data = new WeakMap();\nfunction getAnimationData(element) {\n    if (!data.has(element)) {\n        data.set(element, {\n            transforms: [],\n            values: new Map(),\n        });\n    }\n    return data.get(element);\n}\nfunction getMotionValue(motionValues, name) {\n    if (!motionValues.has(name)) {\n        motionValues.set(name, new _motionone_types__WEBPACK_IMPORTED_MODULE_0__.MotionValue());\n    }\n    return motionValues.get(name);\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/data.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/index.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/index.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animate: () => (/* binding */ animate)\n/* harmony export */ });\n/* harmony import */ var _motionone_animation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/animation */ \"./node_modules/@motionone/animation/dist/Animation.es.js\");\n/* harmony import */ var _create_animate_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-animate.es.js */ \"./node_modules/@motionone/dom/dist/animate/create-animate.es.js\");\n\n\n\nconst animate = (0,_create_animate_es_js__WEBPACK_IMPORTED_MODULE_0__.createAnimate)(_motionone_animation__WEBPACK_IMPORTED_MODULE_1__.Animation);\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/index.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/style.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/style.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   style: () => (/* binding */ style)\n/* harmony export */ });\n/* harmony import */ var _utils_css_var_es_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/css-var.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/css-var.es.js\");\n/* harmony import */ var _utils_get_style_name_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/get-style-name.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/get-style-name.es.js\");\n/* harmony import */ var _utils_transforms_es_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/transforms.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/transforms.es.js\");\n\n\n\n\nconst style = {\n    get: (element, name) => {\n        name = (0,_utils_get_style_name_es_js__WEBPACK_IMPORTED_MODULE_0__.getStyleName)(name);\n        let value = (0,_utils_css_var_es_js__WEBPACK_IMPORTED_MODULE_1__.isCssVar)(name)\n            ? element.style.getPropertyValue(name)\n            : getComputedStyle(element)[name];\n        // TODO Decide if value can be 0\n        if (!value && value !== 0) {\n            const definition = _utils_transforms_es_js__WEBPACK_IMPORTED_MODULE_2__.transformDefinitions.get(name);\n            if (definition)\n                value = definition.initialValue;\n        }\n        return value;\n    },\n    set: (element, name, value) => {\n        name = (0,_utils_get_style_name_es_js__WEBPACK_IMPORTED_MODULE_0__.getStyleName)(name);\n        if ((0,_utils_css_var_es_js__WEBPACK_IMPORTED_MODULE_1__.isCssVar)(name)) {\n            element.style.setProperty(name, value);\n        }\n        else {\n            element.style[name] = value;\n        }\n    },\n};\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/style.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/controls.es.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/controls.es.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   controls: () => (/* binding */ controls),\n/* harmony export */   withControls: () => (/* binding */ withControls)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/defaults.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/time.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n/* harmony import */ var _stop_animation_es_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stop-animation.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/stop-animation.es.js\");\n\n\n\nconst createAnimation = (factory) => factory();\nconst withControls = (animationFactory, options, duration = _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.defaults.duration) => {\n    return new Proxy({\n        animations: animationFactory.map(createAnimation).filter(Boolean),\n        duration,\n        options,\n    }, controls);\n};\n/**\n * TODO:\n * Currently this returns the first animation, ideally it would return\n * the first active animation.\n */\nconst getActiveAnimation = (state) => state.animations[0];\nconst controls = {\n    get: (target, key) => {\n        const activeAnimation = getActiveAnimation(target);\n        switch (key) {\n            case \"duration\":\n                return target.duration;\n            case \"currentTime\":\n                return _motionone_utils__WEBPACK_IMPORTED_MODULE_1__.time.s((activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) || 0);\n            case \"playbackRate\":\n            case \"playState\":\n                return activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key];\n            case \"finished\":\n                if (!target.finished) {\n                    target.finished = Promise.all(target.animations.map(selectFinished)).catch(_motionone_utils__WEBPACK_IMPORTED_MODULE_2__.noop);\n                }\n                return target.finished;\n            case \"stop\":\n                return () => {\n                    target.animations.forEach((animation) => (0,_stop_animation_es_js__WEBPACK_IMPORTED_MODULE_3__.stopAnimation)(animation));\n                };\n            case \"forEachNative\":\n                /**\n                 * This is for internal use only, fire a callback for each\n                 * underlying animation.\n                 */\n                return (callback) => {\n                    target.animations.forEach((animation) => callback(animation, target));\n                };\n            default:\n                return typeof (activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) ===\n                    \"undefined\"\n                    ? undefined\n                    : () => target.animations.forEach((animation) => animation[key]());\n        }\n    },\n    set: (target, key, value) => {\n        switch (key) {\n            case \"currentTime\":\n                value = _motionone_utils__WEBPACK_IMPORTED_MODULE_1__.time.ms(value);\n            // Fall-through\n            case \"playbackRate\":\n                for (let i = 0; i < target.animations.length; i++) {\n                    target.animations[i][key] = value;\n                }\n                return true;\n        }\n        return false;\n    },\n};\nconst selectFinished = (animation) => animation.finished;\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/controls.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/css-var.es.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/css-var.es.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isCssVar: () => (/* binding */ isCssVar),\n/* harmony export */   registerCssVariable: () => (/* binding */ registerCssVariable),\n/* harmony export */   registeredProperties: () => (/* binding */ registeredProperties)\n/* harmony export */ });\n/* harmony import */ var _transforms_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transforms.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/transforms.es.js\");\n\n\nconst isCssVar = (name) => name.startsWith(\"--\");\nconst registeredProperties = new Set();\nfunction registerCssVariable(name) {\n    if (registeredProperties.has(name))\n        return;\n    registeredProperties.add(name);\n    try {\n        const { syntax, initialValue } = _transforms_es_js__WEBPACK_IMPORTED_MODULE_0__.transformDefinitions.has(name)\n            ? _transforms_es_js__WEBPACK_IMPORTED_MODULE_0__.transformDefinitions.get(name)\n            : {};\n        CSS.registerProperty({\n            name,\n            inherits: false,\n            syntax,\n            initialValue,\n        });\n    }\n    catch (e) { }\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/css-var.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/easing.es.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/easing.es.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertEasing: () => (/* binding */ convertEasing),\n/* harmony export */   cubicBezierAsString: () => (/* binding */ cubicBezierAsString),\n/* harmony export */   generateLinearEasingPoints: () => (/* binding */ generateLinearEasingPoints)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/progress.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-function.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/defaults.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-cubic-bezier.es.js\");\n/* harmony import */ var _feature_detection_es_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./feature-detection.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/feature-detection.es.js\");\n\n\n\n// Create a linear easing point for every x second\nconst resolution = 0.015;\nconst generateLinearEasingPoints = (easing, duration) => {\n    let points = \"\";\n    const numPoints = Math.round(duration / resolution);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, numPoints - 1, i)) + \", \";\n    }\n    return points.substring(0, points.length - 2);\n};\nconst convertEasing = (easing, duration) => {\n    if ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_1__.isFunction)(easing)) {\n        return _feature_detection_es_js__WEBPACK_IMPORTED_MODULE_2__.supports.linearEasing()\n            ? `linear(${generateLinearEasingPoints(easing, duration)})`\n            : _motionone_utils__WEBPACK_IMPORTED_MODULE_3__.defaults.easing;\n    }\n    else {\n        return (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_4__.isCubicBezier)(easing) ? cubicBezierAsString(easing) : easing;\n    }\n};\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/easing.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/feature-detection.es.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/feature-detection.es.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supports: () => (/* binding */ supports)\n/* harmony export */ });\nconst testAnimation = (keyframes, options) => document.createElement(\"div\").animate(keyframes, options);\nconst featureTests = {\n    cssRegisterProperty: () => typeof CSS !== \"undefined\" &&\n        Object.hasOwnProperty.call(CSS, \"registerProperty\"),\n    waapi: () => Object.hasOwnProperty.call(Element.prototype, \"animate\"),\n    partialKeyframes: () => {\n        try {\n            testAnimation({ opacity: [1] });\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    },\n    finished: () => Boolean(testAnimation({ opacity: [0, 1] }, { duration: 0.001 }).finished),\n    linearEasing: () => {\n        try {\n            testAnimation({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    },\n};\nconst results = {};\nconst supports = {};\nfor (const key in featureTests) {\n    supports[key] = () => {\n        if (results[key] === undefined)\n            results[key] =\n                featureTests[key]();\n        return results[key];\n    };\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/feature-detection.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/get-style-name.es.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/get-style-name.es.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getStyleName: () => (/* binding */ getStyleName)\n/* harmony export */ });\n/* harmony import */ var _transforms_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transforms.es.js */ \"./node_modules/@motionone/dom/dist/animate/utils/transforms.es.js\");\n\n\nfunction getStyleName(key) {\n    if (_transforms_es_js__WEBPACK_IMPORTED_MODULE_0__.transformAlias[key])\n        key = _transforms_es_js__WEBPACK_IMPORTED_MODULE_0__.transformAlias[key];\n    return (0,_transforms_es_js__WEBPACK_IMPORTED_MODULE_0__.isTransform)(key) ? (0,_transforms_es_js__WEBPACK_IMPORTED_MODULE_0__.asTransformCssVar)(key) : key;\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/get-style-name.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/get-unit.es.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/get-unit.es.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUnitConverter: () => (/* binding */ getUnitConverter)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-string.es.js\");\n\n\nfunction getUnitConverter(keyframes, definition) {\n    var _a;\n    let toUnit = (definition === null || definition === void 0 ? void 0 : definition.toDefaultUnit) || _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.noopReturn;\n    const finalKeyframe = keyframes[keyframes.length - 1];\n    if ((0,_motionone_utils__WEBPACK_IMPORTED_MODULE_1__.isString)(finalKeyframe)) {\n        const unit = ((_a = finalKeyframe.match(/(-?[\\d.]+)([a-z%]*)/)) === null || _a === void 0 ? void 0 : _a[2]) || \"\";\n        if (unit)\n            toUnit = (value) => value + unit;\n    }\n    return toUnit;\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/get-unit.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/keyframes.es.js":
/*!************************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/keyframes.es.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrateKeyframes: () => (/* binding */ hydrateKeyframes),\n/* harmony export */   keyframesList: () => (/* binding */ keyframesList)\n/* harmony export */ });\nfunction hydrateKeyframes(keyframes, readInitialValue) {\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] === null) {\n            keyframes[i] = i ? keyframes[i - 1] : readInitialValue();\n        }\n    }\n    return keyframes;\n}\nconst keyframesList = (keyframes) => Array.isArray(keyframes) ? keyframes : [keyframes];\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/keyframes.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/options.es.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/options.es.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOptions: () => (/* binding */ getOptions)\n/* harmony export */ });\nconst getOptions = (options, key) => \n/**\n * TODO: Make test for this\n * Always return a new object otherwise delay is overwritten by results of stagger\n * and this results in no stagger\n */\noptions[key] ? Object.assign(Object.assign({}, options), options[key]) : Object.assign({}, options);\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/options.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/stop-animation.es.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/stop-animation.es.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stopAnimation: () => (/* binding */ stopAnimation)\n/* harmony export */ });\nfunction stopAnimation(animation, needsCommit = true) {\n    if (!animation || animation.playState === \"finished\")\n        return;\n    // Suppress error thrown by WAAPI\n    try {\n        if (animation.stop) {\n            animation.stop();\n        }\n        else {\n            needsCommit && animation.commitStyles();\n            animation.cancel();\n        }\n    }\n    catch (e) { }\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/stop-animation.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/animate/utils/transforms.es.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/animate/utils/transforms.es.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addTransformToElement: () => (/* binding */ addTransformToElement),\n/* harmony export */   asTransformCssVar: () => (/* binding */ asTransformCssVar),\n/* harmony export */   axes: () => (/* binding */ axes),\n/* harmony export */   buildTransformTemplate: () => (/* binding */ buildTransformTemplate),\n/* harmony export */   compareTransformOrder: () => (/* binding */ compareTransformOrder),\n/* harmony export */   isTransform: () => (/* binding */ isTransform),\n/* harmony export */   transformAlias: () => (/* binding */ transformAlias),\n/* harmony export */   transformDefinitions: () => (/* binding */ transformDefinitions)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/array.es.js\");\n/* harmony import */ var _data_es_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data.es.js */ \"./node_modules/@motionone/dom/dist/animate/data.es.js\");\n\n\n\n/**\n * A list of all transformable axes. We'll use this list to generated a version\n * of each axes for each transform.\n */\nconst axes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * An ordered array of each transformable value. By default, transform values\n * will be sorted to this order.\n */\nconst order = [\"translate\", \"scale\", \"rotate\", \"skew\"];\nconst transformAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n};\nconst rotation = {\n    syntax: \"<angle>\",\n    initialValue: \"0deg\",\n    toDefaultUnit: (v) => v + \"deg\",\n};\nconst baseTransformProperties = {\n    translate: {\n        syntax: \"<length-percentage>\",\n        initialValue: \"0px\",\n        toDefaultUnit: (v) => v + \"px\",\n    },\n    rotate: rotation,\n    scale: {\n        syntax: \"<number>\",\n        initialValue: 1,\n        toDefaultUnit: _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.noopReturn,\n    },\n    skew: rotation,\n};\nconst transformDefinitions = new Map();\nconst asTransformCssVar = (name) => `--motion-${name}`;\n/**\n * Generate a list of every possible transform key\n */\nconst transforms = [\"x\", \"y\", \"z\"];\norder.forEach((name) => {\n    axes.forEach((axis) => {\n        transforms.push(name + axis);\n        transformDefinitions.set(asTransformCssVar(name + axis), baseTransformProperties[name]);\n    });\n});\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst compareTransformOrder = (a, b) => transforms.indexOf(a) - transforms.indexOf(b);\n/**\n * Provide a quick way to check if a string is the name of a transform\n */\nconst transformLookup = new Set(transforms);\nconst isTransform = (name) => transformLookup.has(name);\nconst addTransformToElement = (element, name) => {\n    // Map x to translateX etc\n    if (transformAlias[name])\n        name = transformAlias[name];\n    const { transforms } = (0,_data_es_js__WEBPACK_IMPORTED_MODULE_1__.getAnimationData)(element);\n    (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_2__.addUniqueItem)(transforms, name);\n    /**\n     * TODO: An optimisation here could be to cache the transform in element data\n     * and only update if this has changed.\n     */\n    element.style.transform = buildTransformTemplate(transforms);\n};\nconst buildTransformTemplate = (transforms) => transforms\n    .sort(compareTransformOrder)\n    .reduce(transformListToString, \"\")\n    .trim();\nconst transformListToString = (template, name) => `${template} ${name}(var(${asTransformCssVar(name)}))`;\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/animate/utils/transforms.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/utils/resolve-elements.es.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/utils/resolve-elements.es.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: () => (/* binding */ resolveElements)\n/* harmony export */ });\nfunction resolveElements(elements, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = document.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = document.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/utils/resolve-elements.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/dom/dist/utils/stagger.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@motionone/dom/dist/utils/stagger.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFromIndex: () => (/* binding */ getFromIndex),\n/* harmony export */   resolveOption: () => (/* binding */ resolveOption),\n/* harmony export */   stagger: () => (/* binding */ stagger)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-number.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-function.es.js\");\n/* harmony import */ var _motionone_animation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/animation */ \"./node_modules/@motionone/animation/dist/utils/easing.es.js\");\n\n\n\nfunction stagger(duration = 0.1, { start = 0, from = 0, easing } = {}) {\n    return (i, total) => {\n        const fromIndex = (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_0__.isNumber)(from) ? from : getFromIndex(from, total);\n        const distance = Math.abs(fromIndex - i);\n        let delay = duration * distance;\n        if (easing) {\n            const maxDelay = total * duration;\n            const easingFunction = (0,_motionone_animation__WEBPACK_IMPORTED_MODULE_1__.getEasingFunction)(easing);\n            delay = easingFunction(delay / maxDelay) * maxDelay;\n        }\n        return start + delay;\n    };\n}\nfunction getFromIndex(from, total) {\n    if (from === \"first\") {\n        return 0;\n    }\n    else {\n        const lastIndex = total - 1;\n        return from === \"last\" ? lastIndex : lastIndex / 2;\n    }\n}\nfunction resolveOption(option, i, total) {\n    return (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_2__.isFunction)(option) ? option(i, total) : option;\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/dom/dist/utils/stagger.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/easing/dist/cubic-bezier.es.js":
/*!****************************************************************!*\
  !*** ./node_modules/@motionone/easing/dist/cubic-bezier.es.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezier: () => (/* binding */ cubicBezier)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n\n\n/*\n  Bezier function generator\n\n  This has been modified from Gaëtan Renaudeau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticiably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) * t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return _motionone_utils__WEBPACK_IMPORTED_MODULE_0__.noopReturn;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/easing/dist/cubic-bezier.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/easing/dist/steps.es.js":
/*!*********************************************************!*\
  !*** ./node_modules/@motionone/easing/dist/steps.es.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   steps: () => (/* binding */ steps)\n/* harmony export */ });\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/clamp.es.js\");\n\n\nconst steps = (steps, direction = \"end\") => (progress) => {\n    progress =\n        direction === \"end\"\n            ? Math.min(progress, 0.999)\n            : Math.max(progress, 0.001);\n    const expanded = progress * steps;\n    const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n    return (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, 1, rounded / steps);\n};\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/easing/dist/steps.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/types/dist/MotionValue.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@motionone/types/dist/MotionValue.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionValue: () => (/* binding */ MotionValue)\n/* harmony export */ });\n/**\n * The MotionValue tracks the state of a single animatable\n * value. Currently, updatedAt and current are unused. The\n * long term idea is to use this to minimise the number\n * of DOM reads, and to abstract the DOM interactions here.\n */\nclass MotionValue {\n    setAnimation(animation) {\n        this.animation = animation;\n        animation === null || animation === void 0 ? void 0 : animation.finished.then(() => this.clearAnimation()).catch(() => { });\n    }\n    clearAnimation() {\n        this.animation = this.generator = undefined;\n    }\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/types/dist/MotionValue.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/array.es.js":
/*!********************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/array.es.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUniqueItem: () => (/* binding */ addUniqueItem),\n/* harmony export */   removeItem: () => (/* binding */ removeItem)\n/* harmony export */ });\nfunction addUniqueItem(array, item) {\n    array.indexOf(item) === -1 && array.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    index > -1 && arr.splice(index, 1);\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/array.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/clamp.es.js":
/*!********************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/clamp.es.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\nconst clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/clamp.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/defaults.es.js":
/*!***********************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/defaults.es.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaults: () => (/* binding */ defaults)\n/* harmony export */ });\nconst defaults = {\n    duration: 0.3,\n    delay: 0,\n    endDelay: 0,\n    repeat: 0,\n    easing: \"ease\",\n};\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/defaults.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/easing.es.js":
/*!*********************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/easing.es.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEasingForSegment: () => (/* binding */ getEasingForSegment)\n/* harmony export */ });\n/* harmony import */ var _is_easing_list_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-easing-list.es.js */ \"./node_modules/@motionone/utils/dist/is-easing-list.es.js\");\n/* harmony import */ var _wrap_es_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wrap.es.js */ \"./node_modules/@motionone/utils/dist/wrap.es.js\");\n\n\n\nfunction getEasingForSegment(easing, i) {\n    return (0,_is_easing_list_es_js__WEBPACK_IMPORTED_MODULE_0__.isEasingList)(easing) ? easing[(0,_wrap_es_js__WEBPACK_IMPORTED_MODULE_1__.wrap)(0, easing.length, i)] : easing;\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/easing.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/interpolate.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/interpolate.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   interpolate: () => (/* binding */ interpolate)\n/* harmony export */ });\n/* harmony import */ var _mix_es_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mix.es.js */ \"./node_modules/@motionone/utils/dist/mix.es.js\");\n/* harmony import */ var _noop_es_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./noop.es.js */ \"./node_modules/@motionone/utils/dist/noop.es.js\");\n/* harmony import */ var _offset_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./offset.es.js */ \"./node_modules/@motionone/utils/dist/offset.es.js\");\n/* harmony import */ var _progress_es_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./progress.es.js */ \"./node_modules/@motionone/utils/dist/progress.es.js\");\n/* harmony import */ var _easing_es_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./easing.es.js */ \"./node_modules/@motionone/utils/dist/easing.es.js\");\n/* harmony import */ var _clamp_es_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./clamp.es.js */ \"./node_modules/@motionone/utils/dist/clamp.es.js\");\n\n\n\n\n\n\n\nfunction interpolate(output, input = (0,_offset_es_js__WEBPACK_IMPORTED_MODULE_0__.defaultOffset)(output.length), easing = _noop_es_js__WEBPACK_IMPORTED_MODULE_1__.noopReturn) {\n    const length = output.length;\n    /**\n     * If the input length is lower than the output we\n     * fill the input to match. This currently assumes the input\n     * is an animation progress value so is a good candidate for\n     * moving outside the function.\n     */\n    const remainder = length - input.length;\n    remainder > 0 && (0,_offset_es_js__WEBPACK_IMPORTED_MODULE_0__.fillOffset)(input, remainder);\n    return (t) => {\n        let i = 0;\n        for (; i < length - 2; i++) {\n            if (t < input[i + 1])\n                break;\n        }\n        let progressInRange = (0,_clamp_es_js__WEBPACK_IMPORTED_MODULE_2__.clamp)(0, 1, (0,_progress_es_js__WEBPACK_IMPORTED_MODULE_3__.progress)(input[i], input[i + 1], t));\n        const segmentEasing = (0,_easing_es_js__WEBPACK_IMPORTED_MODULE_4__.getEasingForSegment)(easing, i);\n        progressInRange = segmentEasing(progressInRange);\n        return (0,_mix_es_js__WEBPACK_IMPORTED_MODULE_5__.mix)(output[i], output[i + 1], progressInRange);\n    };\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/interpolate.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/is-cubic-bezier.es.js":
/*!******************************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/is-cubic-bezier.es.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isCubicBezier: () => (/* binding */ isCubicBezier)\n/* harmony export */ });\n/* harmony import */ var _is_number_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-number.es.js */ \"./node_modules/@motionone/utils/dist/is-number.es.js\");\n\n\nconst isCubicBezier = (easing) => Array.isArray(easing) && (0,_is_number_es_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(easing[0]);\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/is-cubic-bezier.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/is-easing-generator.es.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/is-easing-generator.es.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isEasingGenerator: () => (/* binding */ isEasingGenerator)\n/* harmony export */ });\nconst isEasingGenerator = (easing) => typeof easing === \"object\" &&\n    Boolean(easing.createAnimation);\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/is-easing-generator.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/is-easing-list.es.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/is-easing-list.es.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isEasingList: () => (/* binding */ isEasingList)\n/* harmony export */ });\n/* harmony import */ var _is_number_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-number.es.js */ \"./node_modules/@motionone/utils/dist/is-number.es.js\");\n\n\nconst isEasingList = (easing) => Array.isArray(easing) && !(0,_is_number_es_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(easing[0]);\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/is-easing-list.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/is-function.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/is-function.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFunction: () => (/* binding */ isFunction)\n/* harmony export */ });\nconst isFunction = (value) => typeof value === \"function\";\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/is-function.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/is-number.es.js":
/*!************************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/is-number.es.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNumber: () => (/* binding */ isNumber)\n/* harmony export */ });\nconst isNumber = (value) => typeof value === \"number\";\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/is-number.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/is-string.es.js":
/*!************************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/is-string.es.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isString: () => (/* binding */ isString)\n/* harmony export */ });\nconst isString = (value) => typeof value === \"string\";\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/is-string.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/mix.es.js":
/*!******************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/mix.es.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mix: () => (/* binding */ mix)\n/* harmony export */ });\nconst mix = (min, max, progress) => -progress * min + progress * max + min;\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/mix.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/noop.es.js":
/*!*******************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/noop.es.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   noopReturn: () => (/* binding */ noopReturn)\n/* harmony export */ });\nconst noop = () => { };\nconst noopReturn = (v) => v;\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/noop.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/offset.es.js":
/*!*********************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/offset.es.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultOffset: () => (/* binding */ defaultOffset),\n/* harmony export */   fillOffset: () => (/* binding */ fillOffset)\n/* harmony export */ });\n/* harmony import */ var _mix_es_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mix.es.js */ \"./node_modules/@motionone/utils/dist/mix.es.js\");\n/* harmony import */ var _progress_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./progress.es.js */ \"./node_modules/@motionone/utils/dist/progress.es.js\");\n\n\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = (0,_progress_es_js__WEBPACK_IMPORTED_MODULE_0__.progress)(0, remaining, i);\n        offset.push((0,_mix_es_js__WEBPACK_IMPORTED_MODULE_1__.mix)(min, 1, offsetProgress));\n    }\n}\nfunction defaultOffset(length) {\n    const offset = [0];\n    fillOffset(offset, length - 1);\n    return offset;\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/offset.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/progress.es.js":
/*!***********************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/progress.es.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   progress: () => (/* binding */ progress)\n/* harmony export */ });\nconst progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/progress.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/time.es.js":
/*!*******************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/time.es.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   time: () => (/* binding */ time)\n/* harmony export */ });\nconst time = {\n    ms: (seconds) => seconds * 1000,\n    s: (milliseconds) => milliseconds / 1000,\n};\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/time.es.js?");

/***/ }),

/***/ "./node_modules/@motionone/utils/dist/wrap.es.js":
/*!*******************************************************!*\
  !*** ./node_modules/@motionone/utils/dist/wrap.es.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\nconst wrap = (min, max, v) => {\n    const rangeSize = max - min;\n    return ((((v - min) % rangeSize) + rangeSize) % rangeSize) + min;\n};\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@motionone/utils/dist/wrap.es.js?");

/***/ }),

/***/ "./node_modules/hey-listen/dist/hey-listen.es.js":
/*!*******************************************************!*\
  !*** ./node_modules/hey-listen/dist/hey-listen.es.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant),\n/* harmony export */   warning: () => (/* binding */ warning)\n/* harmony export */ });\nvar warning = function () { };\r\nvar invariant = function () { };\r\nif (true) {\r\n    warning = function (check, message) {\r\n        if (!check && typeof console !== 'undefined') {\r\n            console.warn(message);\r\n        }\r\n    };\r\n    invariant = function (check, message) {\r\n        if (!check) {\r\n            throw new Error(message);\r\n        }\r\n    };\r\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/hey-listen/dist/hey-listen.es.js?");

/***/ }),

/***/ "./node_modules/motion/dist/animate.es.js":
/*!************************************************!*\
  !*** ./node_modules/motion/dist/animate.es.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animate: () => (/* binding */ animate),\n/* harmony export */   animateProgress: () => (/* binding */ animateProgress)\n/* harmony export */ });\n/* harmony import */ var _motionone_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @motionone/dom */ \"./node_modules/@motionone/dom/dist/animate/utils/controls.es.js\");\n/* harmony import */ var _motionone_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @motionone/dom */ \"./node_modules/@motionone/dom/dist/animate/index.es.js\");\n/* harmony import */ var _motionone_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @motionone/utils */ \"./node_modules/@motionone/utils/dist/is-function.es.js\");\n/* harmony import */ var _motionone_animation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @motionone/animation */ \"./node_modules/@motionone/animation/dist/Animation.es.js\");\n\n\n\n\nfunction animateProgress(target, options = {}) {\n    return (0,_motionone_dom__WEBPACK_IMPORTED_MODULE_0__.withControls)([\n        () => {\n            const animation = new _motionone_animation__WEBPACK_IMPORTED_MODULE_1__.Animation(target, [0, 1], options);\n            animation.finished.catch(() => { });\n            return animation;\n        },\n    ], options, options.duration);\n}\nfunction animate(target, keyframesOrOptions, options) {\n    const factory = (0,_motionone_utils__WEBPACK_IMPORTED_MODULE_2__.isFunction)(target) ? animateProgress : _motionone_dom__WEBPACK_IMPORTED_MODULE_3__.animate;\n    return factory(target, keyframesOrOptions, options);\n}\n\n\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/motion/dist/animate.es.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/css-tag.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/css-tag.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* binding */ CSSResult),\n/* harmony export */   adoptStyles: () => (/* binding */ adoptStyles),\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   getCompatibleStyle: () => (/* binding */ getCompatibleStyle),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* binding */ supportsAdoptingStyleSheets),\n/* harmony export */   unsafeCSS: () => (/* binding */ unsafeCSS)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nconst supportsAdoptingStyleSheets = global.ShadowRoot &&\n    (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n    'adoptedStyleSheets' in Document.prototype &&\n    'replace' in CSSStyleSheet.prototype;\nconst constructionToken = Symbol();\nconst cssTagCache = new WeakMap();\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nclass CSSResult {\n    constructor(cssText, strings, safeToken) {\n        // This property needs to remain unminified.\n        this['_$cssResult$'] = true;\n        if (safeToken !== constructionToken) {\n            throw new Error('CSSResult is not constructable. Use `unsafeCSS` or `css` instead.');\n        }\n        this.cssText = cssText;\n        this._strings = strings;\n    }\n    // This is a getter so that it's lazy. In practice, this means stylesheets\n    // are not created until the first element instance is made.\n    get styleSheet() {\n        // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n        // constructable.\n        let styleSheet = this._styleSheet;\n        const strings = this._strings;\n        if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n            const cacheable = strings !== undefined && strings.length === 1;\n            if (cacheable) {\n                styleSheet = cssTagCache.get(strings);\n            }\n            if (styleSheet === undefined) {\n                (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(this.cssText);\n                if (cacheable) {\n                    cssTagCache.set(strings, styleSheet);\n                }\n            }\n        }\n        return styleSheet;\n    }\n    toString() {\n        return this.cssText;\n    }\n}\nconst textFromCSSResult = (value) => {\n    // This property needs to remain unminified.\n    if (value['_$cssResult$'] === true) {\n        return value.cssText;\n    }\n    else if (typeof value === 'number') {\n        return value;\n    }\n    else {\n        throw new Error(`Value passed to 'css' function must be a 'css' function result: ` +\n            `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n            `to ensure page security.`);\n    }\n};\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nconst unsafeCSS = (value) => new CSSResult(typeof value === 'string' ? value : String(value), undefined, constructionToken);\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nconst css = (strings, ...values) => {\n    const cssText = strings.length === 1\n        ? strings[0]\n        : values.reduce((acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1], strings[0]);\n    return new CSSResult(cssText, strings, constructionToken);\n};\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic spec behavior](https://wicg.github.io/construct-stylesheets/#using-constructed-stylesheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nconst adoptStyles = (renderRoot, styles) => {\n    if (supportsAdoptingStyleSheets) {\n        renderRoot.adoptedStyleSheets = styles.map((s) => s instanceof CSSStyleSheet ? s : s.styleSheet);\n    }\n    else {\n        styles.forEach((s) => {\n            const style = document.createElement('style');\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const nonce = global['litNonce'];\n            if (nonce !== undefined) {\n                style.setAttribute('nonce', nonce);\n            }\n            style.textContent = s.cssText;\n            renderRoot.appendChild(style);\n        });\n    }\n};\nconst cssResultFromStyleSheet = (sheet) => {\n    let cssText = '';\n    for (const rule of sheet.cssRules) {\n        cssText += rule.cssText;\n    }\n    return unsafeCSS(cssText);\n};\nconst getCompatibleStyle = supportsAdoptingStyleSheets ||\n    (NODE_MODE && global.CSSStyleSheet === undefined)\n    ? (s) => s\n    : (s) => s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n//# sourceMappingURL=css-tag.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/css-tag.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/base.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/base.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decorateProperty: () => (/* binding */ decorateProperty),\n/* harmony export */   legacyPrototypeMethod: () => (/* binding */ legacyPrototypeMethod),\n/* harmony export */   standardPrototypeMethod: () => (/* binding */ standardPrototypeMethod)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst legacyPrototypeMethod = (descriptor, proto, name) => {\n    Object.defineProperty(proto, name, descriptor);\n};\nconst standardPrototypeMethod = (descriptor, element) => ({\n    kind: 'method',\n    placement: 'prototype',\n    key: element.key,\n    descriptor,\n});\n/**\n * Helper for decorating a property that is compatible with both TypeScript\n * and Babel decorators. The optional `finisher` can be used to perform work on\n * the class. The optional `descriptor` should return a PropertyDescriptor\n * to install for the given property.\n *\n * @param finisher {function} Optional finisher method; receives the element\n * constructor and property key as arguments and has no return value.\n * @param descriptor {function} Optional descriptor method; receives the\n * property key as an argument and returns a property descriptor to define for\n * the given property.\n * @returns {ClassElement|void}\n */\nconst decorateProperty = ({ finisher, descriptor, }) => (protoOrDescriptor, name\n// Note TypeScript requires the return type to be `void|any`\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) => {\n    var _a;\n    // TypeScript / Babel legacy mode\n    if (name !== undefined) {\n        const ctor = protoOrDescriptor\n            .constructor;\n        if (descriptor !== undefined) {\n            Object.defineProperty(protoOrDescriptor, name, descriptor(name));\n        }\n        finisher === null || finisher === void 0 ? void 0 : finisher(ctor, name);\n        // Babel standard mode\n    }\n    else {\n        // Note, the @property decorator saves `key` as `originalKey`\n        // so try to use it here.\n        const key = \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (_a = protoOrDescriptor.originalKey) !== null && _a !== void 0 ? _a : protoOrDescriptor.key;\n        const info = descriptor != undefined\n            ? {\n                kind: 'method',\n                placement: 'prototype',\n                key,\n                descriptor: descriptor(protoOrDescriptor.key),\n            }\n            : { ...protoOrDescriptor, key };\n        if (finisher != undefined) {\n            info.finisher = function (ctor) {\n                finisher(ctor, key);\n            };\n        }\n        return info;\n    }\n};\n//# sourceMappingURL=base.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/base.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/custom-element.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/custom-element.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customElement: () => (/* binding */ customElement)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst legacyCustomElement = (tagName, clazz) => {\n    customElements.define(tagName, clazz);\n    // Cast as any because TS doesn't recognize the return type as being a\n    // subtype of the decorated class when clazz is typed as\n    // `Constructor<HTMLElement>` for some reason.\n    // `Constructor<HTMLElement>` is helpful to make sure the decorator is\n    // applied to elements however.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return clazz;\n};\nconst standardCustomElement = (tagName, descriptor) => {\n    const { kind, elements } = descriptor;\n    return {\n        kind,\n        elements,\n        // This callback is called once the class is otherwise fully defined\n        finisher(clazz) {\n            customElements.define(tagName, clazz);\n        },\n    };\n};\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nconst customElement = (tagName) => (classOrDescriptor) => typeof classOrDescriptor === 'function'\n    ? legacyCustomElement(tagName, classOrDescriptor)\n    : standardCustomElement(tagName, classOrDescriptor);\n//# sourceMappingURL=custom-element.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/custom-element.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/event-options.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/event-options.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventOptions: () => (/* binding */ eventOptions)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"./node_modules/@lit/reactive-element/development/decorators/base.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Adds event listener options to a method used as an event listener in a\n * lit-html template.\n *\n * @param options An object that specifies event listener options as accepted by\n * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.\n *\n * Current browsers support the `capture`, `passive`, and `once` options. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters\n *\n * ```ts\n * class MyElement {\n *   clicked = false;\n *\n *   render() {\n *     return html`\n *       <div @click=${this._onClick}>\n *         <button></button>\n *       </div>\n *     `;\n *   }\n *\n *   @eventOptions({capture: true})\n *   _onClick(e) {\n *     this.clicked = true;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction eventOptions(options) {\n    return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.decorateProperty)({\n        finisher: (ctor, name) => {\n            Object.assign(\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            ctor.prototype[name], options);\n        },\n    });\n}\n//# sourceMappingURL=event-options.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/event-options.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/property.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/property.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   property: () => (/* binding */ property)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst standardProperty = (options, element) => {\n    // When decorating an accessor, pass it through and add property metadata.\n    // Note, the `hasOwnProperty` check in `createProperty` ensures we don't\n    // stomp over the user's accessor.\n    if (element.kind === 'method' &&\n        element.descriptor &&\n        !('value' in element.descriptor)) {\n        return {\n            ...element,\n            finisher(clazz) {\n                clazz.createProperty(element.key, options);\n            },\n        };\n    }\n    else {\n        // createProperty() takes care of defining the property, but we still\n        // must return some kind of descriptor, so return a descriptor for an\n        // unused prototype field. The finisher calls createProperty().\n        return {\n            kind: 'field',\n            key: Symbol(),\n            placement: 'own',\n            descriptor: {},\n            // store the original key so subsequent decorators have access to it.\n            originalKey: element.key,\n            // When @babel/plugin-proposal-decorators implements initializers,\n            // do this instead of the initializer below. See:\n            // https://github.com/babel/babel/issues/9260 extras: [\n            //   {\n            //     kind: 'initializer',\n            //     placement: 'own',\n            //     initializer: descriptor.initializer,\n            //   }\n            // ],\n            initializer() {\n                if (typeof element.initializer === 'function') {\n                    this[element.key] = element.initializer.call(this);\n                }\n            },\n            finisher(clazz) {\n                clazz.createProperty(element.key, options);\n            },\n        };\n    }\n};\nconst legacyProperty = (options, proto, name) => {\n    proto.constructor.createProperty(name, options);\n};\n/**\n * A property decorator which creates a reactive property that reflects a\n * corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nfunction property(options) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return (protoOrDescriptor, name) => name !== undefined\n        ? legacyProperty(options, protoOrDescriptor, name)\n        : standardProperty(options, protoOrDescriptor);\n}\n//# sourceMappingURL=property.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/property.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/query-all.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/query-all.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAll: () => (/* binding */ queryAll)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"./node_modules/@lit/reactive-element/development/decorators/base.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * A property decorator that converts a class property into a getter\n * that executes a querySelectorAll on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll\n *\n * ```ts\n * class MyElement {\n *   @queryAll('div')\n *   divs: NodeListOf<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction queryAll(selector) {\n    return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.decorateProperty)({\n        descriptor: (_name) => ({\n            get() {\n                var _a, _b;\n                return (_b = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelectorAll(selector)) !== null && _b !== void 0 ? _b : [];\n            },\n            enumerable: true,\n            configurable: true,\n        }),\n    });\n}\n//# sourceMappingURL=query-all.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/query-all.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/query-assigned-elements.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/query-assigned-elements.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAssignedElements: () => (/* binding */ queryAssignedElements)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"./node_modules/@lit/reactive-element/development/decorators/base.js\");\n/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a;\n/*\n * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n/**\n * A tiny module scoped polyfill for HTMLSlotElement.assignedElements.\n */\nconst slotAssignedElements = ((_a = global.HTMLSlotElement) === null || _a === void 0 ? void 0 : _a.prototype.assignedElements) != null\n    ? (slot, opts) => slot.assignedElements(opts)\n    : (slot, opts) => slot\n        .assignedNodes(opts)\n        .filter((node) => node.nodeType === Node.ELEMENT_NODE);\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nfunction queryAssignedElements(options) {\n    const { slot, selector } = options !== null && options !== void 0 ? options : {};\n    return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.decorateProperty)({\n        descriptor: (_name) => ({\n            get() {\n                var _a;\n                const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n                const slotEl = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(slotSelector);\n                const elements = slotEl != null ? slotAssignedElements(slotEl, options) : [];\n                if (selector) {\n                    return elements.filter((node) => node.matches(selector));\n                }\n                return elements;\n            },\n            enumerable: true,\n            configurable: true,\n        }),\n    });\n}\n//# sourceMappingURL=query-assigned-elements.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/query-assigned-elements.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/query-assigned-nodes.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/query-assigned-nodes.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAssignedNodes: () => (/* binding */ queryAssignedNodes)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"./node_modules/@lit/reactive-element/development/decorators/base.js\");\n/* harmony import */ var _query_assigned_elements_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./query-assigned-elements.js */ \"./node_modules/@lit/reactive-element/development/decorators/query-assigned-elements.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/*\n * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\n\nfunction queryAssignedNodes(slotOrOptions, flatten, selector) {\n    // Normalize the overloaded arguments.\n    let slot = slotOrOptions;\n    let assignedNodesOptions;\n    if (typeof slotOrOptions === 'object') {\n        slot = slotOrOptions.slot;\n        assignedNodesOptions = slotOrOptions;\n    }\n    else {\n        assignedNodesOptions = { flatten };\n    }\n    // For backwards compatibility, queryAssignedNodes with a selector behaves\n    // exactly like queryAssignedElements with a selector.\n    if (selector) {\n        return (0,_query_assigned_elements_js__WEBPACK_IMPORTED_MODULE_1__.queryAssignedElements)({\n            slot: slot,\n            flatten,\n            selector,\n        });\n    }\n    return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.decorateProperty)({\n        descriptor: (_name) => ({\n            get() {\n                var _a, _b;\n                const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n                const slotEl = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(slotSelector);\n                return (_b = slotEl === null || slotEl === void 0 ? void 0 : slotEl.assignedNodes(assignedNodesOptions)) !== null && _b !== void 0 ? _b : [];\n            },\n            enumerable: true,\n            configurable: true,\n        }),\n    });\n}\n//# sourceMappingURL=query-assigned-nodes.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/query-assigned-nodes.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/query-async.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/query-async.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAsync: () => (/* binding */ queryAsync)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"./node_modules/@lit/reactive-element/development/decorators/base.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// Note, in the future, we may extend this decorator to support the use case\n// where the queried element may need to do work to become ready to interact\n// with (e.g. load some implementation code). If so, we might elect to\n// add a second argument defining a function that can be run to make the\n// queried element loaded/updated/ready.\n/**\n * A property decorator that converts a class property into a getter that\n * returns a promise that resolves to the result of a querySelector on the\n * element's renderRoot done after the element's `updateComplete` promise\n * resolves. When the queried property may change with element state, this\n * decorator can be used instead of requiring users to await the\n * `updateComplete` before accessing the property.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @queryAsync('#first')\n *   first: Promise<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n *\n * // external usage\n * async doSomethingWithFirst() {\n *  (await aMyElement.first).doSomething();\n * }\n * ```\n * @category Decorator\n */\nfunction queryAsync(selector) {\n    return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.decorateProperty)({\n        descriptor: (_name) => ({\n            async get() {\n                var _a;\n                await this.updateComplete;\n                return (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(selector);\n            },\n            enumerable: true,\n            configurable: true,\n        }),\n    });\n}\n//# sourceMappingURL=query-async.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/query-async.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/query.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/query.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"./node_modules/@lit/reactive-element/development/decorators/base.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction query(selector, cache) {\n    return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.decorateProperty)({\n        descriptor: (name) => {\n            const descriptor = {\n                get() {\n                    var _a, _b;\n                    return (_b = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(selector)) !== null && _b !== void 0 ? _b : null;\n                },\n                enumerable: true,\n                configurable: true,\n            };\n            if (cache) {\n                const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n                descriptor.get = function () {\n                    var _a, _b;\n                    if (this[key] === undefined) {\n                        this[key] = (_b = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(selector)) !== null && _b !== void 0 ? _b : null;\n                    }\n                    return this[key];\n                };\n            }\n            return descriptor;\n        },\n    });\n}\n//# sourceMappingURL=query.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/query.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/decorators/state.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/decorators/state.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   state: () => (/* binding */ state)\n/* harmony export */ });\n/* harmony import */ var _property_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./property.js */ \"./node_modules/@lit/reactive-element/development/decorators/property.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/*\n * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nfunction state(options) {\n    return (0,_property_js__WEBPACK_IMPORTED_MODULE_0__.property)({\n        ...options,\n        state: true,\n    });\n}\n//# sourceMappingURL=state.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/decorators/state.js?");

/***/ }),

/***/ "./node_modules/@lit/reactive-element/development/reactive-element.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/development/reactive-element.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_0__.CSSResult),\n/* harmony export */   ReactiveElement: () => (/* binding */ ReactiveElement),\n/* harmony export */   adoptStyles: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_0__.adoptStyles),\n/* harmony export */   css: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_0__.css),\n/* harmony export */   defaultConverter: () => (/* binding */ defaultConverter),\n/* harmony export */   getCompatibleStyle: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_0__.getCompatibleStyle),\n/* harmony export */   notEqual: () => (/* binding */ notEqual),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_0__.supportsAdoptingStyleSheets),\n/* harmony export */   unsafeCSS: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_0__.unsafeCSS)\n/* harmony export */ });\n/* harmony import */ var _css_tag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./css-tag.js */ \"./node_modules/@lit/reactive-element/development/css-tag.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c, _d;\nvar _e;\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\n\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\nif (NODE_MODE) {\n    (_a = global.customElements) !== null && _a !== void 0 ? _a : (global.customElements = customElements);\n}\nconst DEV_MODE = true;\nlet requestUpdateThenable;\nlet issueWarning;\nconst trustedTypes = global\n    .trustedTypes;\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n    ? trustedTypes.emptyScript\n    : '';\nconst polyfillSupport = DEV_MODE\n    ? global.reactiveElementPolyfillSupportDevMode\n    : global.reactiveElementPolyfillSupport;\nif (DEV_MODE) {\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    const issuedWarnings = ((_b = global.litIssuedWarnings) !== null && _b !== void 0 ? _b : (global.litIssuedWarnings = new Set()));\n    // Issue a warning, if we haven't already.\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!issuedWarnings.has(warning)) {\n            console.warn(warning);\n            issuedWarnings.add(warning);\n        }\n    };\n    issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n    // Issue polyfill support warning.\n    if (((_c = global.ShadyDOM) === null || _c === void 0 ? void 0 : _c.inUse) && polyfillSupport === undefined) {\n        issueWarning('polyfill-support-missing', `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n            `the \\`polyfill-support\\` module has not been loaded.`);\n    }\n    requestUpdateThenable = (name) => ({\n        then: (onfulfilled, _onrejected) => {\n            issueWarning('request-update-promise', `The \\`requestUpdate\\` method should no longer return a Promise but ` +\n                `does so on \\`${name}\\`. Use \\`updateComplete\\` instead.`);\n            if (onfulfilled !== undefined) {\n                onfulfilled(false);\n            }\n        },\n    });\n}\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n    ? (event) => {\n        const shouldEmit = global\n            .emitLitDebugLogEvents;\n        if (!shouldEmit) {\n            return;\n        }\n        global.dispatchEvent(new CustomEvent('lit-debug', {\n            detail: event,\n        }));\n    }\n    : undefined;\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = (prop, _obj) => prop;\nconst defaultConverter = {\n    toAttribute(value, type) {\n        switch (type) {\n            case Boolean:\n                value = value ? emptyStringForBooleanAttribute : null;\n                break;\n            case Object:\n            case Array:\n                // if the value is `null` or `undefined` pass this through\n                // to allow removing/no change behavior.\n                value = value == null ? value : JSON.stringify(value);\n                break;\n        }\n        return value;\n    },\n    fromAttribute(value, type) {\n        let fromValue = value;\n        switch (type) {\n            case Boolean:\n                fromValue = value !== null;\n                break;\n            case Number:\n                fromValue = value === null ? null : Number(value);\n                break;\n            case Object:\n            case Array:\n                // Do *not* generate exception when invalid JSON is set as elements\n                // don't normally complain on being mis-configured.\n                // TODO(sorvell): Do generate exception in *dev mode*.\n                try {\n                    // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n                    fromValue = JSON.parse(value);\n                }\n                catch (e) {\n                    fromValue = null;\n                }\n                break;\n        }\n        return fromValue;\n    },\n};\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nconst notEqual = (value, old) => {\n    // This ensures (old==NaN, value==NaN) always returns false\n    return old !== value && (old === old || value === value);\n};\nconst defaultPropertyDeclaration = {\n    attribute: true,\n    type: String,\n    converter: defaultConverter,\n    reflect: false,\n    hasChanged: notEqual,\n};\n/**\n * The Closure JS Compiler doesn't currently have good support for static\n * property semantics where \"this\" is dynamic (e.g.\n * https://github.com/google/closure-compiler/issues/3177 and others) so we use\n * this hack to bypass any rewriting by the compiler.\n */\nconst finalized = 'finalized';\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclassers to render updates as desired.\n * @noInheritDoc\n */\nclass ReactiveElement\n// In the Node build, this `extends` clause will be substituted with\n// `(globalThis.HTMLElement ?? HTMLElement)`.\n//\n// This way, we will first prefer any global `HTMLElement` polyfill that the\n// user has assigned, and then fall back to the `HTMLElement` shim which has\n// been imported (see note at the top of this file about how this import is\n// generated by Rollup). Note that the `HTMLElement` variable has been\n// shadowed by this import, so it no longer refers to the global.\n extends HTMLElement {\n    constructor() {\n        super();\n        this.__instanceProperties = new Map();\n        /**\n         * True if there is a pending update as a result of calling `requestUpdate()`.\n         * Should only be read.\n         * @category updates\n         */\n        this.isUpdatePending = false;\n        /**\n         * Is set to `true` after the first update. The element code cannot assume\n         * that `renderRoot` exists before the element `hasUpdated`.\n         * @category updates\n         */\n        this.hasUpdated = false;\n        /**\n         * Name of currently reflecting property\n         */\n        this.__reflectingProperty = null;\n        this.__initialize();\n    }\n    /**\n     * Adds an initializer function to the class that is called during instance\n     * construction.\n     *\n     * This is useful for code that runs against a `ReactiveElement`\n     * subclass, such as a decorator, that needs to do work for each\n     * instance, such as setting up a `ReactiveController`.\n     *\n     * ```ts\n     * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n     *   target.addInitializer((instance: ReactiveElement) => {\n     *     // This is run during construction of the element\n     *     new MyController(instance);\n     *   });\n     * }\n     * ```\n     *\n     * Decorating a field will then cause each instance to run an initializer\n     * that adds a controller:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   @myDecorator foo;\n     * }\n     * ```\n     *\n     * Initializers are stored per-constructor. Adding an initializer to a\n     * subclass does not add it to a superclass. Since initializers are run in\n     * constructors, initializers will run in order of the class hierarchy,\n     * starting with superclasses and progressing to the instance's class.\n     *\n     * @nocollapse\n     */\n    static addInitializer(initializer) {\n        var _a;\n        this.finalize();\n        ((_a = this._initializers) !== null && _a !== void 0 ? _a : (this._initializers = [])).push(initializer);\n    }\n    /**\n     * Returns a list of attributes corresponding to the registered properties.\n     * @nocollapse\n     * @category attributes\n     */\n    static get observedAttributes() {\n        // note: piggy backing on this to ensure we're finalized.\n        this.finalize();\n        const attributes = [];\n        // Use forEach so this works even if for/of loops are compiled to for loops\n        // expecting arrays\n        this.elementProperties.forEach((v, p) => {\n            const attr = this.__attributeNameForProperty(p, v);\n            if (attr !== undefined) {\n                this.__attributeToPropertyMap.set(attr, p);\n                attributes.push(attr);\n            }\n        });\n        return attributes;\n    }\n    /**\n     * Creates a property accessor on the element prototype if one does not exist\n     * and stores a {@linkcode PropertyDeclaration} for the property with the\n     * given options. The property setter calls the property's `hasChanged`\n     * property option or uses a strict identity check to determine whether or not\n     * to request an update.\n     *\n     * This method may be overridden to customize properties; however,\n     * when doing so, it's important to call `super.createProperty` to ensure\n     * the property is setup correctly. This method calls\n     * `getPropertyDescriptor` internally to get a descriptor to install.\n     * To customize what properties do when they are get or set, override\n     * `getPropertyDescriptor`. To customize the options for a property,\n     * implement `createProperty` like this:\n     *\n     * ```ts\n     * static createProperty(name, options) {\n     *   options = Object.assign(options, {myOption: true});\n     *   super.createProperty(name, options);\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static createProperty(name, options = defaultPropertyDeclaration) {\n        var _a;\n        // if this is a state property, force the attribute to false.\n        if (options.state) {\n            // Cast as any since this is readonly.\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            options.attribute = false;\n        }\n        // Note, since this can be called by the `@property` decorator which\n        // is called before `finalize`, we ensure finalization has been kicked off.\n        this.finalize();\n        this.elementProperties.set(name, options);\n        // Do not generate an accessor if the prototype already has one, since\n        // it would be lost otherwise and that would never be the user's intention;\n        // Instead, we expect users to call `requestUpdate` themselves from\n        // user-defined accessors. Note that if the super has an accessor we will\n        // still overwrite it\n        if (!options.noAccessor && !this.prototype.hasOwnProperty(name)) {\n            const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n            const descriptor = this.getPropertyDescriptor(name, key, options);\n            if (descriptor !== undefined) {\n                Object.defineProperty(this.prototype, name, descriptor);\n                if (DEV_MODE) {\n                    // If this class doesn't have its own set, create one and initialize\n                    // with the values in the set from the nearest ancestor class, if any.\n                    if (!this.hasOwnProperty('__reactivePropertyKeys')) {\n                        this.__reactivePropertyKeys = new Set((_a = this.__reactivePropertyKeys) !== null && _a !== void 0 ? _a : []);\n                    }\n                    this.__reactivePropertyKeys.add(name);\n                }\n            }\n        }\n    }\n    /**\n     * Returns a property descriptor to be defined on the given named property.\n     * If no descriptor is returned, the property will not become an accessor.\n     * For example,\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   static getPropertyDescriptor(name, key, options) {\n     *     const defaultDescriptor =\n     *         super.getPropertyDescriptor(name, key, options);\n     *     const setter = defaultDescriptor.set;\n     *     return {\n     *       get: defaultDescriptor.get,\n     *       set(value) {\n     *         setter.call(this, value);\n     *         // custom action.\n     *       },\n     *       configurable: true,\n     *       enumerable: true\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static getPropertyDescriptor(name, key, options) {\n        return {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            get() {\n                return this[key];\n            },\n            set(value) {\n                const oldValue = this[name];\n                this[key] = value;\n                this.requestUpdate(name, oldValue, options);\n            },\n            configurable: true,\n            enumerable: true,\n        };\n    }\n    /**\n     * Returns the property options associated with the given property.\n     * These options are defined with a `PropertyDeclaration` via the `properties`\n     * object or the `@property` decorator and are registered in\n     * `createProperty(...)`.\n     *\n     * Note, this method should be considered \"final\" and not overridden. To\n     * customize the options for a given property, override\n     * {@linkcode createProperty}.\n     *\n     * @nocollapse\n     * @final\n     * @category properties\n     */\n    static getPropertyOptions(name) {\n        return this.elementProperties.get(name) || defaultPropertyDeclaration;\n    }\n    /**\n     * Creates property accessors for registered properties, sets up element\n     * styling, and ensures any superclasses are also finalized. Returns true if\n     * the element was finalized.\n     * @nocollapse\n     */\n    static finalize() {\n        if (this.hasOwnProperty(finalized)) {\n            return false;\n        }\n        this[finalized] = true;\n        // finalize any superclasses\n        const superCtor = Object.getPrototypeOf(this);\n        superCtor.finalize();\n        // Create own set of initializers for this class if any exist on the\n        // superclass and copy them down. Note, for a small perf boost, avoid\n        // creating initializers unless needed.\n        if (superCtor._initializers !== undefined) {\n            this._initializers = [...superCtor._initializers];\n        }\n        this.elementProperties = new Map(superCtor.elementProperties);\n        // initialize Map populated in observedAttributes\n        this.__attributeToPropertyMap = new Map();\n        // make any properties\n        // Note, only process \"own\" properties since this element will inherit\n        // any properties defined on the superClass, and finalization ensures\n        // the entire prototype chain is finalized.\n        if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n            const props = this.properties;\n            // support symbols in properties (IE11 does not support this)\n            const propKeys = [\n                ...Object.getOwnPropertyNames(props),\n                ...Object.getOwnPropertySymbols(props),\n            ];\n            // This for/of is ok because propKeys is an array\n            for (const p of propKeys) {\n                // note, use of `any` is due to TypeScript lack of support for symbol in\n                // index types\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this.createProperty(p, props[p]);\n            }\n        }\n        this.elementStyles = this.finalizeStyles(this.styles);\n        // DEV mode warnings\n        if (DEV_MODE) {\n            const warnRemovedOrRenamed = (name, renamed = false) => {\n                if (this.prototype.hasOwnProperty(name)) {\n                    issueWarning(renamed ? 'renamed-api' : 'removed-api', `\\`${name}\\` is implemented on class ${this.name}. It ` +\n                        `has been ${renamed ? 'renamed' : 'removed'} ` +\n                        `in this version of LitElement.`);\n                }\n            };\n            warnRemovedOrRenamed('initialize');\n            warnRemovedOrRenamed('requestUpdateInternal');\n            warnRemovedOrRenamed('_getUpdateComplete', true);\n        }\n        return true;\n    }\n    /**\n     * Takes the styles the user supplied via the `static styles` property and\n     * returns the array of styles to apply to the element.\n     * Override this method to integrate into a style management system.\n     *\n     * Styles are deduplicated preserving the _last_ instance in the list. This\n     * is a performance optimization to avoid duplicated styles that can occur\n     * especially when composing via subclassing. The last item is kept to try\n     * to preserve the cascade order with the assumption that it's most important\n     * that last added styles override previous styles.\n     *\n     * @nocollapse\n     * @category styles\n     */\n    static finalizeStyles(styles) {\n        const elementStyles = [];\n        if (Array.isArray(styles)) {\n            // Dedupe the flattened array in reverse order to preserve the last items.\n            // Casting to Array<unknown> works around TS error that\n            // appears to come from trying to flatten a type CSSResultArray.\n            const set = new Set(styles.flat(Infinity).reverse());\n            // Then preserve original order by adding the set items in reverse order.\n            for (const s of set) {\n                elementStyles.unshift((0,_css_tag_js__WEBPACK_IMPORTED_MODULE_0__.getCompatibleStyle)(s));\n            }\n        }\n        else if (styles !== undefined) {\n            elementStyles.push((0,_css_tag_js__WEBPACK_IMPORTED_MODULE_0__.getCompatibleStyle)(styles));\n        }\n        return elementStyles;\n    }\n    /**\n     * Returns the property name for the given attribute `name`.\n     * @nocollapse\n     */\n    static __attributeNameForProperty(name, options) {\n        const attribute = options.attribute;\n        return attribute === false\n            ? undefined\n            : typeof attribute === 'string'\n                ? attribute\n                : typeof name === 'string'\n                    ? name.toLowerCase()\n                    : undefined;\n    }\n    /**\n     * Internal only override point for customizing work done when elements\n     * are constructed.\n     */\n    __initialize() {\n        var _a;\n        this.__updatePromise = new Promise((res) => (this.enableUpdating = res));\n        this._$changedProperties = new Map();\n        this.__saveInstanceProperties();\n        // ensures first update will be caught by an early access of\n        // `updateComplete`\n        this.requestUpdate();\n        (_a = this.constructor._initializers) === null || _a === void 0 ? void 0 : _a.forEach((i) => i(this));\n    }\n    /**\n     * Registers a `ReactiveController` to participate in the element's reactive\n     * update cycle. The element automatically calls into any registered\n     * controllers during its lifecycle callbacks.\n     *\n     * If the element is connected when `addController()` is called, the\n     * controller's `hostConnected()` callback will be immediately called.\n     * @category controllers\n     */\n    addController(controller) {\n        var _a, _b;\n        ((_a = this.__controllers) !== null && _a !== void 0 ? _a : (this.__controllers = [])).push(controller);\n        // If a controller is added after the element has been connected,\n        // call hostConnected. Note, re-using existence of `renderRoot` here\n        // (which is set in connectedCallback) to avoid the need to track a\n        // first connected state.\n        if (this.renderRoot !== undefined && this.isConnected) {\n            (_b = controller.hostConnected) === null || _b === void 0 ? void 0 : _b.call(controller);\n        }\n    }\n    /**\n     * Removes a `ReactiveController` from the element.\n     * @category controllers\n     */\n    removeController(controller) {\n        var _a;\n        // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n        // splice do nothing.\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.splice(this.__controllers.indexOf(controller) >>> 0, 1);\n    }\n    /**\n     * Fixes any properties set on the instance before upgrade time.\n     * Otherwise these would shadow the accessor and break these properties.\n     * The properties are stored in a Map which is played back after the\n     * constructor runs. Note, on very old versions of Safari (<=9) or Chrome\n     * (<=41), properties created for native platform properties like (`id` or\n     * `name`) may not have default values set in the element constructor. On\n     * these browsers native properties appear on instances and therefore their\n     * default value will overwrite any element default (e.g. if the element sets\n     * this.id = 'id' in the constructor, the 'id' will become '' since this is\n     * the native platform default).\n     */\n    __saveInstanceProperties() {\n        // Use forEach so this works even if for/of loops are compiled to for loops\n        // expecting arrays\n        this.constructor.elementProperties.forEach((_v, p) => {\n            if (this.hasOwnProperty(p)) {\n                this.__instanceProperties.set(p, this[p]);\n                delete this[p];\n            }\n        });\n    }\n    /**\n     * Returns the node into which the element should render and by default\n     * creates and returns an open shadowRoot. Implement to customize where the\n     * element's DOM is rendered. For example, to render into the element's\n     * childNodes, return `this`.\n     *\n     * @return Returns a node into which to render.\n     * @category rendering\n     */\n    createRenderRoot() {\n        var _a;\n        const renderRoot = (_a = this.shadowRoot) !== null && _a !== void 0 ? _a : this.attachShadow(this.constructor.shadowRootOptions);\n        (0,_css_tag_js__WEBPACK_IMPORTED_MODULE_0__.adoptStyles)(renderRoot, this.constructor.elementStyles);\n        return renderRoot;\n    }\n    /**\n     * On first connection, creates the element's renderRoot, sets up\n     * element styling, and enables updating.\n     * @category lifecycle\n     */\n    connectedCallback() {\n        var _a;\n        // create renderRoot before first update.\n        if (this.renderRoot === undefined) {\n            this.renderRoot = this.createRenderRoot();\n        }\n        this.enableUpdating(true);\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach((c) => { var _a; return (_a = c.hostConnected) === null || _a === void 0 ? void 0 : _a.call(c); });\n    }\n    /**\n     * Note, this method should be considered final and not overridden. It is\n     * overridden on the element instance with a function that triggers the first\n     * update.\n     * @category updates\n     */\n    enableUpdating(_requestedUpdate) { }\n    /**\n     * Allows for `super.disconnectedCallback()` in extensions while\n     * reserving the possibility of making non-breaking feature additions\n     * when disconnecting at some point in the future.\n     * @category lifecycle\n     */\n    disconnectedCallback() {\n        var _a;\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach((c) => { var _a; return (_a = c.hostDisconnected) === null || _a === void 0 ? void 0 : _a.call(c); });\n    }\n    /**\n     * Synchronizes property values when attributes change.\n     *\n     * Specifically, when an attribute is set, the corresponding property is set.\n     * You should rarely need to implement this callback. If this method is\n     * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n     * called.\n     *\n     * See [using the lifecycle callbacks](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Using_custom_elements#using_the_lifecycle_callbacks)\n     * on MDN for more information about the `attributeChangedCallback`.\n     * @category attributes\n     */\n    attributeChangedCallback(name, _old, value) {\n        this._$attributeToProperty(name, value);\n    }\n    __propertyToAttribute(name, value, options = defaultPropertyDeclaration) {\n        var _a;\n        const attr = this.constructor.__attributeNameForProperty(name, options);\n        if (attr !== undefined && options.reflect === true) {\n            const converter = ((_a = options.converter) === null || _a === void 0 ? void 0 : _a.toAttribute) !==\n                undefined\n                ? options.converter\n                : defaultConverter;\n            const attrValue = converter.toAttribute(value, options.type);\n            if (DEV_MODE &&\n                this.constructor.enabledWarnings.indexOf('migration') >= 0 &&\n                attrValue === undefined) {\n                issueWarning('undefined-attribute-value', `The attribute value for the ${name} property is ` +\n                    `undefined on element ${this.localName}. The attribute will be ` +\n                    `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n                    `the attribute would not have changed.`);\n            }\n            // Track if the property is being reflected to avoid\n            // setting the property again via `attributeChangedCallback`. Note:\n            // 1. this takes advantage of the fact that the callback is synchronous.\n            // 2. will behave incorrectly if multiple attributes are in the reaction\n            // stack at time of calling. However, since we process attributes\n            // in `update` this should not be possible (or an extreme corner case\n            // that we'd like to discover).\n            // mark state reflecting\n            this.__reflectingProperty = name;\n            if (attrValue == null) {\n                this.removeAttribute(attr);\n            }\n            else {\n                this.setAttribute(attr, attrValue);\n            }\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /** @internal */\n    _$attributeToProperty(name, value) {\n        var _a;\n        const ctor = this.constructor;\n        // Note, hint this as an `AttributeMap` so closure clearly understands\n        // the type; it has issues with tracking types through statics\n        const propName = ctor.__attributeToPropertyMap.get(name);\n        // Use tracking info to avoid reflecting a property value to an attribute\n        // if it was just set because the attribute changed.\n        if (propName !== undefined && this.__reflectingProperty !== propName) {\n            const options = ctor.getPropertyOptions(propName);\n            const converter = typeof options.converter === 'function'\n                ? { fromAttribute: options.converter }\n                : ((_a = options.converter) === null || _a === void 0 ? void 0 : _a.fromAttribute) !== undefined\n                    ? options.converter\n                    : defaultConverter;\n            // mark state reflecting\n            this.__reflectingProperty = propName;\n            this[propName] = converter.fromAttribute(value, options.type\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            );\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /**\n     * Requests an update which is processed asynchronously. This should be called\n     * when an element should update based on some state not triggered by setting\n     * a reactive property. In this case, pass no arguments. It should also be\n     * called when manually implementing a property setter. In this case, pass the\n     * property `name` and `oldValue` to ensure that any configured property\n     * options are honored.\n     *\n     * @param name name of requesting property\n     * @param oldValue old value of requesting property\n     * @param options property options to use instead of the previously\n     *     configured options\n     * @category updates\n     */\n    requestUpdate(name, oldValue, options) {\n        let shouldRequestUpdate = true;\n        // If we have a property key, perform property update steps.\n        if (name !== undefined) {\n            options =\n                options ||\n                    this.constructor.getPropertyOptions(name);\n            const hasChanged = options.hasChanged || notEqual;\n            if (hasChanged(this[name], oldValue)) {\n                if (!this._$changedProperties.has(name)) {\n                    this._$changedProperties.set(name, oldValue);\n                }\n                // Add to reflecting properties set.\n                // Note, it's important that every change has a chance to add the\n                // property to `_reflectingProperties`. This ensures setting\n                // attribute + property reflects correctly.\n                if (options.reflect === true && this.__reflectingProperty !== name) {\n                    if (this.__reflectingProperties === undefined) {\n                        this.__reflectingProperties = new Map();\n                    }\n                    this.__reflectingProperties.set(name, options);\n                }\n            }\n            else {\n                // Abort the request if the property should not be considered changed.\n                shouldRequestUpdate = false;\n            }\n        }\n        if (!this.isUpdatePending && shouldRequestUpdate) {\n            this.__updatePromise = this.__enqueueUpdate();\n        }\n        // Note, since this no longer returns a promise, in dev mode we return a\n        // thenable which warns if it's called.\n        return DEV_MODE\n            ? requestUpdateThenable(this.localName)\n            : undefined;\n    }\n    /**\n     * Sets up the element to asynchronously update.\n     */\n    async __enqueueUpdate() {\n        this.isUpdatePending = true;\n        try {\n            // Ensure any previous update has resolved before updating.\n            // This `await` also ensures that property changes are batched.\n            await this.__updatePromise;\n        }\n        catch (e) {\n            // Refire any previous errors async so they do not disrupt the update\n            // cycle. Errors are refired so developers have a chance to observe\n            // them, and this can be done by implementing\n            // `window.onunhandledrejection`.\n            Promise.reject(e);\n        }\n        const result = this.scheduleUpdate();\n        // If `scheduleUpdate` returns a Promise, we await it. This is done to\n        // enable coordinating updates with a scheduler. Note, the result is\n        // checked to avoid delaying an additional microtask unless we need to.\n        if (result != null) {\n            await result;\n        }\n        return !this.isUpdatePending;\n    }\n    /**\n     * Schedules an element update. You can override this method to change the\n     * timing of updates by returning a Promise. The update will await the\n     * returned Promise, and you should resolve the Promise to allow the update\n     * to proceed. If this method is overridden, `super.scheduleUpdate()`\n     * must be called.\n     *\n     * For instance, to schedule updates to occur just before the next frame:\n     *\n     * ```ts\n     * override protected async scheduleUpdate(): Promise<unknown> {\n     *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n     *   super.scheduleUpdate();\n     * }\n     * ```\n     * @category updates\n     */\n    scheduleUpdate() {\n        return this.performUpdate();\n    }\n    /**\n     * Performs an element update. Note, if an exception is thrown during the\n     * update, `firstUpdated` and `updated` will not be called.\n     *\n     * Call `performUpdate()` to immediately process a pending update. This should\n     * generally not be needed, but it can be done in rare cases when you need to\n     * update synchronously.\n     *\n     * Note: To ensure `performUpdate()` synchronously completes a pending update,\n     * it should not be overridden. In LitElement 2.x it was suggested to override\n     * `performUpdate()` to also customizing update scheduling. Instead, you should now\n     * override `scheduleUpdate()`. For backwards compatibility with LitElement 2.x,\n     * scheduling updates via `performUpdate()` continues to work, but will make\n     * also calling `performUpdate()` to synchronously process updates difficult.\n     *\n     * @category updates\n     */\n    performUpdate() {\n        var _a, _b;\n        // Abort any update if one is not pending when this is called.\n        // This can happen if `performUpdate` is called early to \"flush\"\n        // the update.\n        if (!this.isUpdatePending) {\n            return;\n        }\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({ kind: 'update' });\n        // create renderRoot before first update.\n        if (!this.hasUpdated) {\n            // Produce warning if any class properties are shadowed by class fields\n            if (DEV_MODE) {\n                const shadowedProperties = [];\n                (_a = this.constructor.__reactivePropertyKeys) === null || _a === void 0 ? void 0 : _a.forEach((p) => {\n                    var _a;\n                    if (this.hasOwnProperty(p) && !((_a = this.__instanceProperties) === null || _a === void 0 ? void 0 : _a.has(p))) {\n                        shadowedProperties.push(p);\n                    }\n                });\n                if (shadowedProperties.length) {\n                    throw new Error(`The following properties on element ${this.localName} will not ` +\n                        `trigger updates as expected because they are set using class ` +\n                        `fields: ${shadowedProperties.join(', ')}. ` +\n                        `Native class fields and some compiled output will overwrite ` +\n                        `accessors used for detecting changes. See ` +\n                        `https://lit.dev/msg/class-field-shadowing ` +\n                        `for more information.`);\n                }\n            }\n        }\n        // Mixin instance properties once, if they exist.\n        if (this.__instanceProperties) {\n            // Use forEach so this works even if for/of loops are compiled to for loops\n            // expecting arrays\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.__instanceProperties.forEach((v, p) => (this[p] = v));\n            this.__instanceProperties = undefined;\n        }\n        let shouldUpdate = false;\n        const changedProperties = this._$changedProperties;\n        try {\n            shouldUpdate = this.shouldUpdate(changedProperties);\n            if (shouldUpdate) {\n                this.willUpdate(changedProperties);\n                (_b = this.__controllers) === null || _b === void 0 ? void 0 : _b.forEach((c) => { var _a; return (_a = c.hostUpdate) === null || _a === void 0 ? void 0 : _a.call(c); });\n                this.update(changedProperties);\n            }\n            else {\n                this.__markUpdated();\n            }\n        }\n        catch (e) {\n            // Prevent `firstUpdated` and `updated` from running when there's an\n            // update exception.\n            shouldUpdate = false;\n            // Ensure element can accept additional updates after an exception.\n            this.__markUpdated();\n            throw e;\n        }\n        // The update is no longer considered pending and further updates are now allowed.\n        if (shouldUpdate) {\n            this._$didUpdate(changedProperties);\n        }\n    }\n    /**\n     * Invoked before `update()` to compute values needed during the update.\n     *\n     * Implement `willUpdate` to compute property values that depend on other\n     * properties and are used in the rest of the update process.\n     *\n     * ```ts\n     * willUpdate(changedProperties) {\n     *   // only need to check changed properties for an expensive computation.\n     *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n     *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n     *   }\n     * }\n     *\n     * render() {\n     *   return html`SHA: ${this.sha}`;\n     * }\n     * ```\n     *\n     * @category updates\n     */\n    willUpdate(_changedProperties) { }\n    // Note, this is an override point for polyfill-support.\n    // @internal\n    _$didUpdate(changedProperties) {\n        var _a;\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach((c) => { var _a; return (_a = c.hostUpdated) === null || _a === void 0 ? void 0 : _a.call(c); });\n        if (!this.hasUpdated) {\n            this.hasUpdated = true;\n            this.firstUpdated(changedProperties);\n        }\n        this.updated(changedProperties);\n        if (DEV_MODE &&\n            this.isUpdatePending &&\n            this.constructor.enabledWarnings.indexOf('change-in-update') >= 0) {\n            issueWarning('change-in-update', `Element ${this.localName} scheduled an update ` +\n                `(generally because a property was set) ` +\n                `after an update completed, causing a new update to be scheduled. ` +\n                `This is inefficient and should be avoided unless the next update ` +\n                `can only be scheduled as a side effect of the previous update.`);\n        }\n    }\n    __markUpdated() {\n        this._$changedProperties = new Map();\n        this.isUpdatePending = false;\n    }\n    /**\n     * Returns a Promise that resolves when the element has completed updating.\n     * The Promise value is a boolean that is `true` if the element completed the\n     * update without triggering another update. The Promise result is `false` if\n     * a property was set inside `updated()`. If the Promise is rejected, an\n     * exception was thrown during the update.\n     *\n     * To await additional asynchronous work, override the `getUpdateComplete`\n     * method. For example, it is sometimes useful to await a rendered element\n     * before fulfilling this Promise. To do this, first await\n     * `super.getUpdateComplete()`, then any subsequent state.\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    get updateComplete() {\n        return this.getUpdateComplete();\n    }\n    /**\n     * Override point for the `updateComplete` promise.\n     *\n     * It is not safe to override the `updateComplete` getter directly due to a\n     * limitation in TypeScript which means it is not possible to call a\n     * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n     * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n     * This method should be overridden instead. For example:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   override async getUpdateComplete() {\n     *     const result = await super.getUpdateComplete();\n     *     await this._myChild.updateComplete;\n     *     return result;\n     *   }\n     * }\n     * ```\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    getUpdateComplete() {\n        return this.__updatePromise;\n    }\n    /**\n     * Controls whether or not `update()` should be called when the element requests\n     * an update. By default, this method always returns `true`, but this can be\n     * customized to control when to update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    shouldUpdate(_changedProperties) {\n        return true;\n    }\n    /**\n     * Updates the element. This method reflects property values to attributes.\n     * It can be overridden to render and keep updated element DOM.\n     * Setting properties inside this method will *not* trigger\n     * another update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    update(_changedProperties) {\n        if (this.__reflectingProperties !== undefined) {\n            // Use forEach so this works even if for/of loops are compiled to for\n            // loops expecting arrays\n            this.__reflectingProperties.forEach((v, k) => this.__propertyToAttribute(k, this[k], v));\n            this.__reflectingProperties = undefined;\n        }\n        this.__markUpdated();\n    }\n    /**\n     * Invoked whenever the element is updated. Implement to perform\n     * post-updating tasks via DOM APIs, for example, focusing an element.\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    updated(_changedProperties) { }\n    /**\n     * Invoked when the element is first updated. Implement to perform one time\n     * work on the element after update.\n     *\n     * ```ts\n     * firstUpdated() {\n     *   this.renderRoot.getElementById('my-text-area').focus();\n     * }\n     * ```\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    firstUpdated(_changedProperties) { }\n}\n_e = finalized;\n/**\n * Marks class as having finished creating properties.\n */\nReactiveElement[_e] = true;\n/**\n * Memoized list of all element properties, including any superclass properties.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category properties\n */\nReactiveElement.elementProperties = new Map();\n/**\n * Memoized list of all element styles.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category styles\n */\nReactiveElement.elementStyles = [];\n/**\n * Options used when calling `attachShadow`. Set this property to customize\n * the options for the shadowRoot; for example, to create a closed\n * shadowRoot: `{mode: 'closed'}`.\n *\n * Note, these options are used in `createRenderRoot`. If this method\n * is customized, options should be respected if possible.\n * @nocollapse\n * @category rendering\n */\nReactiveElement.shadowRootOptions = { mode: 'open' };\n// Apply polyfills if available\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport({ ReactiveElement });\n// Dev mode warnings...\nif (DEV_MODE) {\n    // Default warning set.\n    ReactiveElement.enabledWarnings = ['change-in-update'];\n    const ensureOwnWarnings = function (ctor) {\n        if (!ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))) {\n            ctor.enabledWarnings = ctor.enabledWarnings.slice();\n        }\n    };\n    ReactiveElement.enableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        if (this.enabledWarnings.indexOf(warning) < 0) {\n            this.enabledWarnings.push(warning);\n        }\n    };\n    ReactiveElement.disableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        const i = this.enabledWarnings.indexOf(warning);\n        if (i >= 0) {\n            this.enabledWarnings.splice(i, 1);\n        }\n    };\n}\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n((_d = global.reactiveElementVersions) !== null && _d !== void 0 ? _d : (global.reactiveElementVersions = [])).push('1.6.3');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n    issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`);\n}\n//# sourceMappingURL=reactive-element.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/@lit/reactive-element/development/reactive-element.js?");

/***/ }),

/***/ "./node_modules/@walletconnect/modal-ui/dist/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@walletconnect/modal-ui/dist/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WcmModal: () => (/* binding */ ae),\n/* harmony export */   WcmQrCode: () => (/* binding */ j)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"./node_modules/lit/index.js\");\n/* harmony import */ var lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lit/decorators.js */ \"./node_modules/lit/decorators.js\");\n/* harmony import */ var lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lit/directives/class-map.js */ \"./node_modules/lit/directives/class-map.js\");\n/* harmony import */ var _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @walletconnect/modal-core */ \"./node_modules/@walletconnect/modal-core/dist/index.js\");\n/* harmony import */ var lit_html__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lit-html */ \"./node_modules/lit-html/development/lit-html.js\");\n/* harmony import */ var motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! motion */ \"./node_modules/motion/dist/animate.es.js\");\n/* harmony import */ var lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lit/directives/if-defined.js */ \"./node_modules/lit/directives/if-defined.js\");\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! qrcode */ \"./node_modules/qrcode/lib/browser.js\");\nvar et=Object.defineProperty,Be=Object.getOwnPropertySymbols,tt=Object.prototype.hasOwnProperty,ot=Object.prototype.propertyIsEnumerable,Ue=(e,o,r)=>o in e?et(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,ve=(e,o)=>{for(var r in o||(o={}))tt.call(o,r)&&Ue(e,r,o[r]);if(Be)for(var r of Be(o))ot.call(o,r)&&Ue(e,r,o[r]);return e};function rt(){var e;const o=(e=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ThemeCtrl.state.themeMode)!=null?e:\"dark\",r={light:{foreground:{1:\"rgb(20,20,20)\",2:\"rgb(121,134,134)\",3:\"rgb(158,169,169)\"},background:{1:\"rgb(255,255,255)\",2:\"rgb(241,243,243)\",3:\"rgb(228,231,231)\"},overlay:\"rgba(0,0,0,0.1)\"},dark:{foreground:{1:\"rgb(228,231,231)\",2:\"rgb(148,158,158)\",3:\"rgb(110,119,119)\"},background:{1:\"rgb(20,20,20)\",2:\"rgb(39,42,42)\",3:\"rgb(59,64,64)\"},overlay:\"rgba(255,255,255,0.1)\"}}[o];return{\"--wcm-color-fg-1\":r.foreground[1],\"--wcm-color-fg-2\":r.foreground[2],\"--wcm-color-fg-3\":r.foreground[3],\"--wcm-color-bg-1\":r.background[1],\"--wcm-color-bg-2\":r.background[2],\"--wcm-color-bg-3\":r.background[3],\"--wcm-color-overlay\":r.overlay}}function He(){return{\"--wcm-accent-color\":\"#3396FF\",\"--wcm-accent-fill-color\":\"#FFFFFF\",\"--wcm-z-index\":\"89\",\"--wcm-background-color\":\"#3396FF\",\"--wcm-background-border-radius\":\"8px\",\"--wcm-container-border-radius\":\"30px\",\"--wcm-wallet-icon-border-radius\":\"15px\",\"--wcm-wallet-icon-large-border-radius\":\"30px\",\"--wcm-wallet-icon-small-border-radius\":\"7px\",\"--wcm-input-border-radius\":\"28px\",\"--wcm-button-border-radius\":\"10px\",\"--wcm-notification-border-radius\":\"36px\",\"--wcm-secondary-button-border-radius\":\"28px\",\"--wcm-icon-button-border-radius\":\"50%\",\"--wcm-button-hover-highlight-border-radius\":\"10px\",\"--wcm-text-big-bold-size\":\"20px\",\"--wcm-text-big-bold-weight\":\"600\",\"--wcm-text-big-bold-line-height\":\"24px\",\"--wcm-text-big-bold-letter-spacing\":\"-0.03em\",\"--wcm-text-big-bold-text-transform\":\"none\",\"--wcm-text-xsmall-bold-size\":\"10px\",\"--wcm-text-xsmall-bold-weight\":\"700\",\"--wcm-text-xsmall-bold-line-height\":\"12px\",\"--wcm-text-xsmall-bold-letter-spacing\":\"0.02em\",\"--wcm-text-xsmall-bold-text-transform\":\"uppercase\",\"--wcm-text-xsmall-regular-size\":\"12px\",\"--wcm-text-xsmall-regular-weight\":\"600\",\"--wcm-text-xsmall-regular-line-height\":\"14px\",\"--wcm-text-xsmall-regular-letter-spacing\":\"-0.03em\",\"--wcm-text-xsmall-regular-text-transform\":\"none\",\"--wcm-text-small-thin-size\":\"14px\",\"--wcm-text-small-thin-weight\":\"500\",\"--wcm-text-small-thin-line-height\":\"16px\",\"--wcm-text-small-thin-letter-spacing\":\"-0.03em\",\"--wcm-text-small-thin-text-transform\":\"none\",\"--wcm-text-small-regular-size\":\"14px\",\"--wcm-text-small-regular-weight\":\"600\",\"--wcm-text-small-regular-line-height\":\"16px\",\"--wcm-text-small-regular-letter-spacing\":\"-0.03em\",\"--wcm-text-small-regular-text-transform\":\"none\",\"--wcm-text-medium-regular-size\":\"16px\",\"--wcm-text-medium-regular-weight\":\"600\",\"--wcm-text-medium-regular-line-height\":\"20px\",\"--wcm-text-medium-regular-letter-spacing\":\"-0.03em\",\"--wcm-text-medium-regular-text-transform\":\"none\",\"--wcm-font-family\":\"-apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, Ubuntu, 'Helvetica Neue', sans-serif\",\"--wcm-font-feature-settings\":\"'tnum' on, 'lnum' on, 'case' on\",\"--wcm-success-color\":\"rgb(38,181,98)\",\"--wcm-error-color\":\"rgb(242, 90, 103)\",\"--wcm-overlay-background-color\":\"rgba(0, 0, 0, 0.3)\",\"--wcm-overlay-backdrop-filter\":\"none\"}}const h={getPreset(e){return He()[e]},setTheme(){const e=document.querySelector(\":root\"),{themeVariables:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ThemeCtrl.state;if(e){const r=ve(ve(ve({},rt()),He()),o);Object.entries(r).forEach(([a,t])=>e.style.setProperty(a,t))}},globalCss:(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`*,::after,::before{margin:0;padding:0;box-sizing:border-box;font-style:normal;text-rendering:optimizeSpeed;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-tap-highlight-color:transparent;backface-visibility:hidden}button{cursor:pointer;display:flex;justify-content:center;align-items:center;position:relative;border:none;background-color:transparent;transition:all .2s ease}@media (hover:hover) and (pointer:fine){button:active{transition:all .1s ease;transform:scale(.93)}}button::after{content:'';position:absolute;top:0;bottom:0;left:0;right:0;transition:background-color,.2s ease}button:disabled{cursor:not-allowed}button svg,button wcm-text{position:relative;z-index:1}input{border:none;outline:0;appearance:none}img{display:block}::selection{color:var(--wcm-accent-fill-color);background:var(--wcm-accent-color)}`},at=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`button{border-radius:var(--wcm-secondary-button-border-radius);height:28px;padding:0 10px;background-color:var(--wcm-accent-color)}button path{fill:var(--wcm-accent-fill-color)}button::after{border-radius:inherit;border:1px solid var(--wcm-color-overlay)}button:disabled::after{background-color:transparent}.wcm-icon-left svg{margin-right:5px}.wcm-icon-right svg{margin-left:5px}button:active::after{background-color:var(--wcm-color-overlay)}.wcm-ghost,.wcm-ghost:active::after,.wcm-outline{background-color:transparent}.wcm-ghost:active{opacity:.5}@media(hover:hover){button:hover::after{background-color:var(--wcm-color-overlay)}.wcm-ghost:hover::after{background-color:transparent}.wcm-ghost:hover{opacity:.5}}button:disabled{background-color:var(--wcm-color-bg-3);pointer-events:none}.wcm-ghost::after{border-color:transparent}.wcm-ghost path{fill:var(--wcm-color-fg-2)}.wcm-outline path{fill:var(--wcm-accent-color)}.wcm-outline:disabled{background-color:transparent;opacity:.5}`;var lt=Object.defineProperty,it=Object.getOwnPropertyDescriptor,F=(e,o,r,a)=>{for(var t=a>1?void 0:a?it(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&lt(o,r,t),t};let T=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.disabled=!1,this.iconLeft=void 0,this.iconRight=void 0,this.onClick=()=>null,this.variant=\"default\"}render(){const e={\"wcm-icon-left\":this.iconLeft!==void 0,\"wcm-icon-right\":this.iconRight!==void 0,\"wcm-ghost\":this.variant===\"ghost\",\"wcm-outline\":this.variant===\"outline\"};let o=\"inverse\";return this.variant===\"ghost\"&&(o=\"secondary\"),this.variant===\"outline\"&&(o=\"accent\"),(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<button class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(e)}\" ?disabled=\"${this.disabled}\" @click=\"${this.onClick}\">${this.iconLeft}<wcm-text variant=\"small-regular\" color=\"${o}\"><slot></slot></wcm-text>${this.iconRight}</button>`}};T.styles=[h.globalCss,at],F([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],T.prototype,\"disabled\",2),F([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],T.prototype,\"iconLeft\",2),F([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],T.prototype,\"iconRight\",2),F([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],T.prototype,\"onClick\",2),F([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],T.prototype,\"variant\",2),T=F([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-button\")],T);const nt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`:host{display:inline-block}button{padding:0 15px 1px;height:40px;border-radius:var(--wcm-button-border-radius);color:var(--wcm-accent-fill-color);background-color:var(--wcm-accent-color)}button::after{content:'';top:0;bottom:0;left:0;right:0;position:absolute;background-color:transparent;border-radius:inherit;transition:background-color .2s ease;border:1px solid var(--wcm-color-overlay)}button:active::after{background-color:var(--wcm-color-overlay)}button:disabled{padding-bottom:0;background-color:var(--wcm-color-bg-3);color:var(--wcm-color-fg-3)}.wcm-secondary{color:var(--wcm-accent-color);background-color:transparent}.wcm-secondary::after{display:none}@media(hover:hover){button:hover::after{background-color:var(--wcm-color-overlay)}}`;var ct=Object.defineProperty,st=Object.getOwnPropertyDescriptor,ue=(e,o,r,a)=>{for(var t=a>1?void 0:a?st(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&ct(o,r,t),t};let ee=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.disabled=!1,this.variant=\"primary\"}render(){const e={\"wcm-secondary\":this.variant===\"secondary\"};return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<button ?disabled=\"${this.disabled}\" class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(e)}\"><slot></slot></button>`}};ee.styles=[h.globalCss,nt],ue([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],ee.prototype,\"disabled\",2),ue([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],ee.prototype,\"variant\",2),ee=ue([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-button-big\")],ee);const dt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`:host{background-color:var(--wcm-color-bg-2);border-top:1px solid var(--wcm-color-bg-3)}div{padding:10px 20px;display:inherit;flex-direction:inherit;align-items:inherit;width:inherit;justify-content:inherit}`;var mt=Object.defineProperty,ht=Object.getOwnPropertyDescriptor,wt=(e,o,r,a)=>{for(var t=a>1?void 0:a?ht(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&mt(o,r,t),t};let be=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div><slot></slot></div>`}};be.styles=[h.globalCss,dt],be=wt([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-info-footer\")],be);const v={CROSS_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\"><path d=\"M9.94 11A.75.75 0 1 0 11 9.94L7.414 6.353a.5.5 0 0 1 0-.708L11 2.061A.75.75 0 1 0 9.94 1L6.353 4.586a.5.5 0 0 1-.708 0L2.061 1A.75.75 0 0 0 1 2.06l3.586 3.586a.5.5 0 0 1 0 .708L1 9.939A.75.75 0 1 0 2.06 11l3.586-3.586a.5.5 0 0 1 .708 0L9.939 11Z\" fill=\"#fff\"/></svg>`,WALLET_CONNECT_LOGO:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"178\" height=\"29\" viewBox=\"0 0 178 29\" id=\"wcm-wc-logo\"><path d=\"M10.683 7.926c5.284-5.17 13.85-5.17 19.134 0l.636.623a.652.652 0 0 1 0 .936l-2.176 2.129a.343.343 0 0 1-.478 0l-.875-.857c-3.686-3.607-9.662-3.607-13.348 0l-.937.918a.343.343 0 0 1-.479 0l-2.175-2.13a.652.652 0 0 1 0-.936l.698-.683Zm23.633 4.403 1.935 1.895a.652.652 0 0 1 0 .936l-8.73 8.543a.687.687 0 0 1-.956 0L20.37 17.64a.172.172 0 0 0-.239 0l-6.195 6.063a.687.687 0 0 1-.957 0l-8.73-8.543a.652.652 0 0 1 0-.936l1.936-1.895a.687.687 0 0 1 .957 0l6.196 6.064a.172.172 0 0 0 .239 0l6.195-6.064a.687.687 0 0 1 .957 0l6.196 6.064a.172.172 0 0 0 .24 0l6.195-6.064a.687.687 0 0 1 .956 0ZM48.093 20.948l2.338-9.355c.139-.515.258-1.07.416-1.942.12.872.258 1.427.357 1.942l2.022 9.355h4.181l3.528-13.874h-3.21l-1.943 8.523a24.825 24.825 0 0 0-.456 2.457c-.158-.931-.317-1.625-.495-2.438l-1.883-8.542h-4.201l-2.042 8.542a41.204 41.204 0 0 0-.475 2.438 41.208 41.208 0 0 0-.476-2.438l-1.903-8.542h-3.349l3.508 13.874h4.083ZM63.33 21.304c1.585 0 2.596-.654 3.11-1.605-.059.297-.078.595-.078.892v.357h2.655V15.22c0-2.735-1.248-4.32-4.3-4.32-2.636 0-4.36 1.466-4.52 3.487h2.914c.1-.891.734-1.426 1.705-1.426.911 0 1.407.515 1.407 1.11 0 .435-.258.693-1.03.792l-1.388.159c-2.061.257-3.825 1.01-3.825 3.19 0 1.982 1.645 3.092 3.35 3.092Zm.891-2.041c-.773 0-1.348-.436-1.348-1.19 0-.733.655-1.09 1.645-1.268l.674-.119c.575-.118.892-.218 1.09-.396v.912c0 1.228-.892 2.06-2.06 2.06ZM70.398 7.074v13.874h2.874V7.074h-2.874ZM74.934 7.074v13.874h2.874V7.074h-2.874ZM84.08 21.304c2.735 0 4.5-1.546 4.697-3.567h-2.893c-.139.892-.892 1.387-1.804 1.387-1.228 0-2.12-.99-2.14-2.358h6.897v-.555c0-3.21-1.764-5.312-4.816-5.312-2.933 0-4.994 2.062-4.994 5.173 0 3.37 2.12 5.232 5.053 5.232Zm-2.16-6.421c.119-1.11.932-1.922 2.081-1.922 1.11 0 1.883.772 1.903 1.922H81.92ZM94.92 21.146c.633 0 1.248-.1 1.525-.179v-2.18c-.218.04-.475.06-.693.06-1.05 0-1.427-.595-1.427-1.566v-3.805h2.338v-2.24h-2.338V7.788H91.47v3.448H89.37v2.24h2.1v4.201c0 2.3 1.15 3.469 3.45 3.469ZM104.62 21.304c3.924 0 6.302-2.299 6.599-5.608h-3.111c-.238 1.803-1.506 3.032-3.369 3.032-2.2 0-3.746-1.784-3.746-4.796 0-2.953 1.605-4.638 3.805-4.638 1.883 0 2.953 1.15 3.171 2.834h3.191c-.317-3.448-2.854-5.41-6.342-5.41-3.984 0-7.036 2.695-7.036 7.214 0 4.677 2.676 7.372 6.838 7.372ZM117.449 21.304c2.993 0 5.114-1.882 5.114-5.172 0-3.23-2.121-5.233-5.114-5.233-2.972 0-5.093 2.002-5.093 5.233 0 3.29 2.101 5.172 5.093 5.172Zm0-2.22c-1.327 0-2.18-1.09-2.18-2.952 0-1.903.892-2.973 2.18-2.973 1.308 0 2.2 1.07 2.2 2.973 0 1.862-.872 2.953-2.2 2.953ZM126.569 20.948v-5.689c0-1.208.753-2.1 1.823-2.1 1.011 0 1.606.773 1.606 2.06v5.729h2.873v-6.144c0-2.339-1.229-3.905-3.428-3.905-1.526 0-2.458.734-2.953 1.606a5.31 5.31 0 0 0 .079-.892v-.377h-2.874v9.712h2.874ZM137.464 20.948v-5.689c0-1.208.753-2.1 1.823-2.1 1.011 0 1.606.773 1.606 2.06v5.729h2.873v-6.144c0-2.339-1.228-3.905-3.428-3.905-1.526 0-2.458.734-2.953 1.606a5.31 5.31 0 0 0 .079-.892v-.377h-2.874v9.712h2.874ZM149.949 21.304c2.735 0 4.499-1.546 4.697-3.567h-2.893c-.139.892-.892 1.387-1.804 1.387-1.228 0-2.12-.99-2.14-2.358h6.897v-.555c0-3.21-1.764-5.312-4.816-5.312-2.933 0-4.994 2.062-4.994 5.173 0 3.37 2.12 5.232 5.053 5.232Zm-2.16-6.421c.119-1.11.932-1.922 2.081-1.922 1.11 0 1.883.772 1.903 1.922h-3.984ZM160.876 21.304c3.013 0 4.658-1.645 4.975-4.201h-2.874c-.099 1.07-.713 1.982-2.001 1.982-1.309 0-2.2-1.21-2.2-2.993 0-1.942 1.03-2.933 2.259-2.933 1.209 0 1.803.872 1.883 1.882h2.873c-.218-2.358-1.823-4.142-4.776-4.142-2.874 0-5.153 1.903-5.153 5.193 0 3.25 1.923 5.212 5.014 5.212ZM172.067 21.146c.634 0 1.248-.1 1.526-.179v-2.18c-.218.04-.476.06-.694.06-1.05 0-1.427-.595-1.427-1.566v-3.805h2.339v-2.24h-2.339V7.788h-2.854v3.448h-2.1v2.24h2.1v4.201c0 2.3 1.15 3.469 3.449 3.469Z\" fill=\"#fff\"/></svg>`,WALLET_CONNECT_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"28\" height=\"20\" viewBox=\"0 0 28 20\"><g clip-path=\"url(#a)\"><path d=\"M7.386 6.482c3.653-3.576 9.575-3.576 13.228 0l.44.43a.451.451 0 0 1 0 .648L19.55 9.033a.237.237 0 0 1-.33 0l-.606-.592c-2.548-2.496-6.68-2.496-9.228 0l-.648.634a.237.237 0 0 1-.33 0L6.902 7.602a.451.451 0 0 1 0-.647l.483-.473Zm16.338 3.046 1.339 1.31a.451.451 0 0 1 0 .648l-6.035 5.909a.475.475 0 0 1-.662 0L14.083 13.2a.119.119 0 0 0-.166 0l-4.283 4.194a.475.475 0 0 1-.662 0l-6.035-5.91a.451.451 0 0 1 0-.647l1.338-1.31a.475.475 0 0 1 .662 0l4.283 4.194c.**************.166 0l4.283-4.194a.475.475 0 0 1 .662 0l4.283 4.194c.**************.166 0l4.283-4.194a.475.475 0 0 1 .662 0Z\" fill=\"#000000\"/></g><defs><clipPath id=\"a\"><path fill=\"#ffffff\" d=\"M0 0h28v20H0z\"/></clipPath></defs></svg>`,WALLET_CONNECT_ICON_COLORED:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"96\" height=\"96\" fill=\"none\"><path fill=\"#fff\" d=\"M25.322 33.597c12.525-12.263 32.83-12.263 45.355 0l1.507 1.476a1.547 1.547 0 0 1 0 2.22l-5.156 5.048a.814.814 0 0 1-1.134 0l-2.074-2.03c-8.737-8.555-22.903-8.555-31.64 0l-2.222 2.175a.814.814 0 0 1-1.134 0l-5.156-5.049a1.547 1.547 0 0 1 0-2.22l1.654-1.62Zm56.019 10.44 4.589 4.494a1.547 1.547 0 0 1 0 2.22l-20.693 20.26a1.628 1.628 0 0 1-2.267 0L48.283 56.632a.407.407 0 0 0-.567 0L33.03 71.012a1.628 1.628 0 0 1-2.268 0L10.07 50.75a1.547 1.547 0 0 1 0-2.22l4.59-4.494a1.628 1.628 0 0 1 2.267 0l14.687 14.38c.**************.567 0l14.685-14.38a1.628 1.628 0 0 1 2.268 0l14.687 14.38c.**************.567 0l14.686-14.38a1.628 1.628 0 0 1 2.268 0Z\"/><path stroke=\"#000\" d=\"M25.672 33.954c12.33-12.072 32.325-12.072 44.655 0l1.508 1.476a1.047 1.047 0 0 1 0 1.506l-5.157 5.048a.314.314 0 0 1-.434 0l-2.074-2.03c-8.932-8.746-23.409-8.746-32.34 0l-2.222 2.174a.314.314 0 0 1-.434 0l-5.157-5.048a1.047 1.047 0 0 1 0-1.506l1.655-1.62Zm55.319 10.44 4.59 4.494a1.047 1.047 0 0 1 0 1.506l-20.694 20.26a1.128 1.128 0 0 1-1.568 0l-14.686-14.38a.907.907 0 0 0-1.267 0L32.68 70.655a1.128 1.128 0 0 1-1.568 0L10.42 50.394a1.047 1.047 0 0 1 0-1.506l4.59-4.493a1.128 1.128 0 0 1 1.567 0l14.687 14.379a.907.907 0 0 0 1.266 0l-.35-.357.35.357 14.686-14.38a1.128 1.128 0 0 1 1.568 0l14.687 14.38a.907.907 0 0 0 1.267 0l14.686-14.38a1.128 1.128 0 0 1 1.568 0Z\"/></svg>`,BACK_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"10\" height=\"18\" viewBox=\"0 0 10 18\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M8.735.179a.75.75 0 0 1 .087 1.057L2.92 8.192a1.25 1.25 0 0 0 0 1.617l5.902 6.956a.75.75 0 1 1-1.144.97L1.776 10.78a2.75 2.75 0 0 1 0-3.559L7.678.265A.75.75 0 0 1 8.735.18Z\" fill=\"#fff\"/></svg>`,COPY_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"24\" height=\"24\" fill=\"none\"><path fill=\"#fff\" fill-rule=\"evenodd\" d=\"M7.01 7.01c.03-1.545.138-2.5.535-3.28A5 5 0 0 1 9.73 1.545C10.8 1 12.2 1 15 1c2.8 0 4.2 0 5.27.545a5 5 0 0 1 2.185 2.185C23 4.8 23 6.2 23 9c0 2.8 0 4.2-.545 5.27a5 5 0 0 1-2.185 2.185c-.78.397-1.735.505-3.28.534l-.001.01c-.03 1.54-.138 2.493-.534 3.27a5 5 0 0 1-2.185 2.186C13.2 23 11.8 23 9 23c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C1 19.2 1 17.8 1 15c0-2.8 0-4.2.545-5.27A5 5 0 0 1 3.73 7.545C4.508 7.149 5.46 7.04 7 7.01h.01ZM15 15.5c-1.425 0-2.403-.001-3.162-.063-.74-.06-1.139-.172-1.427-.319a3.5 3.5 0 0 1-1.53-1.529c-.146-.288-.257-.686-.318-1.427C8.501 11.403 8.5 10.425 8.5 9c0-1.425.001-2.403.063-3.162.06-.74.172-1.139.318-1.427a3.5 3.5 0 0 1 1.53-1.53c.288-.146.686-.257 1.427-.318.759-.062 1.737-.063 3.162-.063 1.425 0 2.403.001 3.162.063.74.06 1.139.172 1.427.318a3.5 3.5 0 0 1 1.53 1.53c.146.288.257.686.318 1.427.062.759.063 1.737.063 3.162 0 1.425-.001 2.403-.063 3.162-.06.74-.172 1.139-.319 1.427a3.5 3.5 0 0 1-1.529 1.53c-.288.146-.686.257-1.427.318-.759.062-1.737.063-3.162.063ZM7 8.511c-.444.009-.825.025-1.162.052-.74.06-1.139.172-1.427.318a3.5 3.5 0 0 0-1.53 1.53c-.146.288-.257.686-.318 1.427-.062.759-.063 1.737-.063 3.162 0 1.425.001 2.403.063 ***********.172 1.139.318 1.427a3.5 3.5 0 0 0 1.53 1.53c.288.146.686.257 1.427.318.759.062 1.737.063 3.162.063 1.425 0 2.403-.001 3.162-.063.74-.06 1.139-.172 1.427-.319a3.5 3.5 0 0 0 1.53-1.53c.146-.287.257-.685.318-1.426.027-.337.043-.718.052-1.162H15c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C7 13.2 7 11.8 7 9v-.489Z\" clip-rule=\"evenodd\"/></svg>`,RETRY_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"15\" height=\"16\" viewBox=\"0 0 15 16\"><path d=\"M6.464 2.03A.75.75 0 0 0 5.403.97L2.08 4.293a1 1 0 0 0 0 1.414L5.403 9.03a.75.75 0 0 0 1.06-1.06L4.672 6.177a.25.25 0 0 1 .177-.427h2.085a4 4 0 1 1-3.93 4.746c-.077-.407-.405-.746-.82-.746-.414 0-.755.338-.7.748a5.501 5.501 0 1 0 5.45-6.248H4.848a.25.25 0 0 1-.177-.427L6.464 2.03Z\" fill=\"#fff\"/></svg>`,DESKTOP_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M0 5.98c0-1.85 0-2.775.394-3.466a3 3 0 0 1 1.12-1.12C2.204 1 3.13 1 4.98 1h6.04c1.85 0 2.775 0 3.466.394a3 3 0 0 1 1.12 1.12C16 3.204 16 4.13 16 5.98v1.04c0 1.85 0 2.775-.394 3.466a3 3 0 0 1-1.12 1.12C13.796 12 12.87 12 11.02 12H4.98c-1.85 0-2.775 0-3.466-.394a3 3 0 0 1-1.12-1.12C0 9.796 0 8.87 0 7.02V5.98ZM4.98 2.5h6.04c.953 0 1.568.001 2.034.043.446.04.608.108.69.154a1.5 1.5 0 0 1 .559.56c.**************.154.69.042.465.043 1.08.043 2.033v1.04c0 .952-.001 1.568-.043 2.034-.04.446-.108.608-.154.69a1.499 1.499 0 0 1-.56.559c-.08.046-.243.114-.69.154-.466.042-1.08.043-2.033.043H4.98c-.952 0-1.568-.001-2.034-.043-.446-.04-.608-.108-.69-.154a1.5 1.5 0 0 1-.559-.56c-.046-.08-.114-.243-.154-.69-.042-.465-.043-1.08-.043-2.033V5.98c0-.952.001-1.568.043-2.034.04-.446.108-.608.154-.69a1.5 1.5 0 0 1 .56-.559c.08-.046.243-.114.69-.154.465-.042 1.08-.043 2.033-.043Z\" fill=\"#fff\"/><path d=\"M4 14.25a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5a.75.75 0 0 1-.75-.75Z\" fill=\"#fff\"/></svg>`,MOBILE_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><path d=\"M6.75 5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z\" fill=\"#fff\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M3 4.98c0-1.85 0-2.775.394-3.466a3 3 0 0 1 1.12-1.12C5.204 0 6.136 0 8 0s2.795 0 3.486.394a3 3 0 0 1 1.12 1.12C13 2.204 13 3.13 13 4.98v6.04c0 1.85 0 2.775-.394 3.466a3 3 0 0 1-1.12 1.12C10.796 16 9.864 16 8 16s-2.795 0-3.486-.394a3 3 0 0 1-1.12-1.12C3 13.796 3 12.87 3 11.02V4.98Zm8.5 0v6.04c0 .953-.001 1.568-.043 2.034-.04.446-.108.608-.154.69a1.499 1.499 0 0 1-.56.559c-.08.045-.242.113-.693.154-.47.042-1.091.043-2.05.043-.959 0-1.58-.001-2.05-.043-.45-.04-.613-.109-.693-.154a1.5 1.5 0 0 1-.56-.56c-.046-.08-.114-.243-.154-.69-.042-.466-.043-1.08-.043-2.033V4.98c0-.952.001-1.568.043-2.034.04-.446.108-.608.154-.69a1.5 1.5 0 0 1 .56-.559c.08-.045.243-.113.693-.154C6.42 1.501 7.041 1.5 8 1.5c.959 0 1.58.001 ***********.04.613.109.693.154a1.5 1.5 0 0 1 .56.56c.**************.154.69.042.465.043 1.08.043 2.033Z\" fill=\"#fff\"/></svg>`,ARROW_DOWN_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\"><path d=\"M2.28 7.47a.75.75 0 0 0-1.06 1.06l5.25 5.25a.75.75 0 0 0 1.06 0l5.25-5.25a.75.75 0 0 0-1.06-1.06l-3.544 3.543a.25.25 0 0 1-.426-.177V.75a.75.75 0 0 0-1.5 0v10.086a.25.25 0 0 1-.427.176L2.28 7.47Z\" fill=\"#fff\"/></svg>`,ARROW_UP_RIGHT_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"15\" height=\"14\" fill=\"none\"><path d=\"M4.5 1.75A.75.75 0 0 1 5.25 1H12a1.5 1.5 0 0 1 1.5 1.5v6.75a.75.75 0 0 1-1.5 0V4.164a.25.25 0 0 0-.427-.176L4.061 11.5A.75.75 0 0 1 3 10.44l7.513-7.513a.25.25 0 0 0-.177-.427H5.25a.75.75 0 0 1-.75-.75Z\" fill=\"#fff\"/></svg>`,ARROW_RIGHT_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"6\" height=\"14\" viewBox=\"0 0 6 14\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2.181 1.099a.75.75 0 0 1 1.024.279l2.433 4.258a2.75 2.75 0 0 1 0 2.729l-2.433 4.257a.75.75 0 1 1-1.303-.744L4.335 7.62a1.25 1.25 0 0 0 0-1.24L1.902 2.122a.75.75 0 0 1 .28-1.023Z\" fill=\"#fff\"/></svg>`,QRCODE_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"25\" height=\"24\" viewBox=\"0 0 25 24\"><path d=\"M23.748 9a.748.748 0 0 0 .748-.752c-.018-2.596-.128-4.07-.784-5.22a6 6 0 0 0-2.24-2.24c-1.15-.656-2.624-.766-5.22-.784a.748.748 0 0 0-.752.748c0 .414.335.749.748.752 1.015.007 1.82.028 2.494.088.995.09 1.561.256 1.988.5.7.398 1.28.978 1.679 1.678.243.427.41.993.498 1.988.061.675.082 1.479.09 2.493a.753.753 0 0 0 .75.749ZM3.527.788C4.677.132 6.152.022 8.747.004A.748.748 0 0 1 9.5.752a.753.753 0 0 1-.749.752c-1.014.007-1.818.028-2.493.088-.995.09-1.561.256-1.988.5-.7.398-1.28.978-1.679 1.678-.243.427-.41.993-.499 1.988-.06.675-.081 1.479-.088 2.493A.753.753 0 0 1 1.252 9a.748.748 0 0 1-.748-.752c.018-2.596.128-4.07.784-5.22a6 6 0 0 1 2.24-2.24ZM1.252 15a.748.748 0 0 0-.748.752c.018 2.596.128 4.07.784 5.22a6 6 0 0 0 2.24 2.24c1.15.656 2.624.766 5.22.784a.748.748 0 0 0 .752-.748.753.753 0 0 0-.749-.752c-1.014-.007-1.818-.028-2.493-.089-.995-.089-1.561-.255-1.988-.498a4.5 4.5 0 0 1-1.679-1.68c-.243-.426-.41-.992-.499-1.987-.06-.675-.081-1.479-.088-2.493A.753.753 0 0 0 1.252 15ZM22.996 15.749a.753.753 0 0 1 .752-.749c.415 0 .751.338.748.752-.018 2.596-.128 4.07-.784 5.22a6 6 0 0 1-2.24 2.24c-1.15.656-2.624.766-5.22.784a.748.748 0 0 1-.752-.748c0-.414.335-.749.748-.752 1.015-.007 1.82-.028 2.494-.089.995-.089 1.561-.255 1.988-.498a4.5 4.5 0 0 0 1.679-1.68c.243-.426.41-.992.498-1.987.061-.675.082-1.479.09-2.493Z\" fill=\"#fff\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7 4a2.5 2.5 0 0 0-2.5 2.5v2A2.5 2.5 0 0 0 7 11h2a2.5 2.5 0 0 0 2.5-2.5v-2A2.5 2.5 0 0 0 9 4H7Zm2 1.5H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1ZM13.5 6.5A2.5 2.5 0 0 1 16 4h2a2.5 2.5 0 0 1 2.5 2.5v2A2.5 2.5 0 0 1 18 11h-2a2.5 2.5 0 0 1-2.5-2.5v-2Zm2.5-1h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1ZM7 13a2.5 2.5 0 0 0-2.5 2.5v2A2.5 2.5 0 0 0 7 20h2a2.5 2.5 0 0 0 2.5-2.5v-2A2.5 2.5 0 0 0 9 13H7Zm2 1.5H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1Z\" fill=\"#fff\"/><path d=\"M13.5 15.5c0-.465 0-.697.038-.89a2 2 0 0 1 1.572-1.572C15.303 13 15.535 13 16 13v2.5h-2.5ZM18 13c.465 0 .697 0 .89.038a2 2 0 0 1 1.572 1.572c.038.193.038.425.038.89H18V13ZM18 17.5h2.5c0 .465 0 .697-.038.89a2 2 0 0 1-1.572 1.572C18.697 20 18.465 20 18 20v-2.5ZM13.5 17.5H16V20c-.465 0-.697 0-.89-.038a2 2 0 0 1-1.572-1.572c-.038-.193-.038-.425-.038-.89Z\" fill=\"#fff\"/></svg>`,SCAN_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"16\" height=\"16\" fill=\"none\"><path fill=\"#fff\" d=\"M10 15.216c0 .422.347.763.768.74 1.202-.064 2.025-.222 2.71-.613a5.001 5.001 0 0 0 1.865-1.866c.39-.684.549-1.507.613-2.709a.735.735 0 0 0-.74-.768.768.768 0 0 0-.76.732c-.009.157-.02.306-.032.447-.073.812-.206 1.244-.384 1.555-.31.545-.761.996-1.306 1.306-.311.178-.743.311-1.555.384-.141.013-.29.023-.447.032a.768.768 0 0 0-.732.76ZM10 .784c0 .407.325.737.732.76.157.009.306.02.447.032.812.073 1.244.206 1.555.384a3.5 3.5 0 0 1 1.306 1.306c.178.311.311.743.384 1.555.013.142.023.29.032.447a.768.768 0 0 0 .76.732.734.734 0 0 0 .74-.768c-.064-1.202-.222-2.025-.613-2.71A5 5 0 0 0 13.477.658c-.684-.39-1.507-.549-2.709-.613a.735.735 0 0 0-.768.74ZM5.232.044A.735.735 0 0 1 6 .784a.768.768 0 0 1-.732.76c-.157.009-.305.02-.447.032-.812.073-1.244.206-1.555.384A3.5 3.5 0 0 0 1.96 3.266c-.178.311-.311.743-.384 1.555-.013.142-.023.29-.032.447A.768.768 0 0 1 .784 6a.735.735 0 0 1-.74-.768c.064-1.202.222-2.025.613-2.71A5 5 0 0 1 2.523.658C3.207.267 4.03.108 5.233.044ZM5.268 14.456a.768.768 0 0 1 .732.76.734.734 0 0 1-.768.74c-1.202-.064-2.025-.222-2.71-.613a5 5 0 0 1-1.865-1.866c-.39-.684-.549-1.507-.613-2.709A.735.735 0 0 1 .784 10c.407 0 .737.325.76.732.009.157.02.306.032.447.073.812.206 1.244.384 1.555a3.5 3.5 0 0 0 1.306 1.306c.311.178.743.311 1.555.384.**************.447.032Z\"/></svg>`,CHECKMARK_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"13\" height=\"12\" viewBox=\"0 0 13 12\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.155.132a.75.75 0 0 1 .232 1.035L5.821 11.535a1 1 0 0 1-1.626.09L.665 7.21a.75.75 0 1 1 1.17-.937L4.71 9.867a.25.25 0 0 0 .406-.023L11.12.364a.75.75 0 0 1 1.035-.232Z\" fill=\"#fff\"/></svg>`,SEARCH_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"20\" height=\"21\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.432 13.992c-.354-.353-.91-.382-1.35-.146a5.5 5.5 0 1 1 2.265-2.265c-.237.441-.208.997.145 1.35l3.296 3.296a.75.75 0 1 1-1.06 1.061l-3.296-3.296Zm.06-5a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z\" fill=\"#949E9E\"/></svg>`,WALLET_PLACEHOLDER:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"60\" height=\"60\" fill=\"none\" viewBox=\"0 0 60 60\"><g clip-path=\"url(#q)\"><path id=\"wallet-placeholder-fill\" fill=\"#fff\" d=\"M0 24.9c0-9.251 0-13.877 1.97-17.332a15 15 0 0 1 5.598-5.597C11.023 0 15.648 0 24.9 0h10.2c9.252 0 13.877 0 17.332 1.97a15 15 0 0 1 5.597 5.598C60 11.023 60 15.648 60 24.9v10.2c0 9.252 0 13.877-1.97 17.332a15.001 15.001 0 0 1-5.598 5.597C48.977 60 44.352 60 35.1 60H24.9c-9.251 0-13.877 0-17.332-1.97a15 15 0 0 1-5.597-5.598C0 48.977 0 44.352 0 35.1V24.9Z\"/><path id=\"wallet-placeholder-dash\" stroke=\"#000\" stroke-dasharray=\"4 4\" stroke-width=\"1.5\" d=\"M.04 41.708a231.598 231.598 0 0 1-.039-4.403l.75-.001L.75 35.1v-2.55H0v-5.1h.75V24.9l.001-2.204h-.75c.003-1.617.011-3.077.039-4.404l.75.016c.034-1.65.099-3.08.218-4.343l-.746-.07c.158-1.678.412-3.083.82-4.316l.713.236c.224-.679.497-1.296.827-1.875a14.25 14.25 0 0 1 1.05-1.585L3.076 5.9A15 15 0 0 1 5.9 3.076l.455.596a14.25 14.25 0 0 1 1.585-1.05c.579-.33 1.196-.603 1.875-.827l-.236-.712C10.812.674 12.217.42 13.895.262l.07.746C15.23.89 16.66.824 18.308.79l-.016-.75C19.62.012 21.08.004 22.695.001l.001.75L24.9.75h2.55V0h5.1v.75h2.55l2.204.001v-.75c1.617.003 3.077.011 4.404.039l-.016.75c1.65.034 3.08.099 4.343.218l.07-.746c1.678.158 3.083.412 4.316.82l-.236.713c.679.224 1.296.497 1.875.827a14.24 14.24 0 0 1 1.585 1.05l.455-.596A14.999 14.999 0 0 1 56.924 5.9l-.596.455c.384.502.735 1.032 1.05 1.585.33.579.602 1.196.827 1.875l.712-.236c.409 1.233.663 2.638.822 4.316l-.747.07c.119 1.264.184 2.694.218 4.343l.75-.016c.028 1.327.036 2.787.039 4.403l-.75.001.001 2.204v2.55H60v5.1h-.75v2.55l-.001 2.204h.75a231.431 231.431 0 0 1-.039 4.404l-.75-.016c-.034 1.65-.099 3.08-.218 4.343l.747.07c-.159 1.678-.413 3.083-.822 4.316l-.712-.236a10.255 10.255 0 0 1-.827 1.875 14.242 14.242 0 0 1-1.05 1.585l.596.455a14.997 14.997 0 0 1-2.824 2.824l-.455-.596c-.502.384-1.032.735-1.585 1.05-.579.33-1.196.602-1.875.827l.236.712c-1.233.409-2.638.663-4.316.822l-.07-.747c-1.264.119-2.694.184-4.343.218l.016.75c-1.327.028-2.787.036-4.403.039l-.001-.75-2.204.001h-2.55V60h-5.1v-.75H24.9l-2.204-.001v.75a231.431 231.431 0 0 1-4.404-.039l.016-.75c-1.65-.034-3.08-.099-4.343-.218l-.07.747c-1.678-.159-3.083-.413-4.316-.822l.236-.712a10.258 10.258 0 0 1-1.875-.827 14.252 14.252 0 0 1-1.585-1.05l-.455.596A14.999 14.999 0 0 1 3.076 54.1l.596-.455a14.24 14.24 0 0 1-1.05-1.585 10.259 10.259 0 0 1-.827-1.875l-.712.236C.674 49.188.42 47.783.262 46.105l.746-.07C.89 44.77.824 43.34.79 41.692l-.75.016Z\"/><path fill=\"#fff\" fill-rule=\"evenodd\" d=\"M35.643 32.145c-.297-.743-.445-1.114-.401-1.275a.42.42 0 0 1 .182-.27c.134-.1.463-.1 1.123-.1.742 0 1.499.046 2.236-.05a6 6 0 0 0 5.166-5.166c.051-.39.051-.855.051-1.784 0-.928 0-1.393-.051-1.783a6 6 0 0 0-5.166-5.165c-.39-.052-.854-.052-1.783-.052h-7.72c-4.934 0-7.401 0-9.244 1.051a8 8 0 0 0-2.985 2.986C16.057 22.28 16.003 24.58 16 29 15.998 31.075 16 33.15 16 35.224A7.778 7.778 0 0 0 23.778 43H28.5c1.394 0 2.09 0 2.67-.116a6 6 0 0 0 4.715-4.714c.115-.58.115-1.301.115-2.744 0-1.31 0-1.964-.114-2.49a4.998 4.998 0 0 0-.243-.792Z\" clip-rule=\"evenodd\"/><path fill=\"#9EA9A9\" fill-rule=\"evenodd\" d=\"M37 18h-7.72c-2.494 0-4.266.002-5.647.126-1.361.122-2.197.354-2.854.728a6.5 6.5 0 0 0-2.425 2.426c-.375.657-.607 1.492-.729 2.853-.11 1.233-.123 2.777-.125 4.867 0 .7 0 1.05.097 ************.182.181.343.2.163.02.518-.18 1.229-.581a6.195 6.195 0 0 1 3.053-.8H37c.977 0 1.32-.003 1.587-.038a4.5 4.5 0 0 0 3.874-3.874c.036-.268.039-.611.039-1.588 0-.976-.003-1.319-.038-1.587a4.5 4.5 0 0 0-3.875-3.874C38.32 18.004 37.977 18 37 18Zm-7.364 12.5h-7.414a4.722 4.722 0 0 0-4.722 4.723 6.278 6.278 0 0 0 6.278 6.278H28.5c1.466 0 1.98-.008 2.378-.087a4.5 4.5 0 0 0 3.535-3.536c.08-.397.087-.933.087-2.451 0-1.391-.009-1.843-.08-2.17a3.5 3.5 0 0 0-2.676-2.676c-.328-.072-.762-.08-2.108-.08Z\" clip-rule=\"evenodd\"/></g><defs><clipPath id=\"q\"><path fill=\"#fff\" d=\"M0 0h60v60H0z\"/></clipPath></defs></svg>`,GLOBE_ICON:(0,lit_html__WEBPACK_IMPORTED_MODULE_4__.svg)`<svg width=\"16\" height=\"16\" fill=\"none\" viewBox=\"0 0 16 16\"><path fill=\"#fff\" fill-rule=\"evenodd\" d=\"M15.5 8a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Zm-2.113.75c.301 0 .535.264.47.558a6.01 6.01 0 0 1-2.867 3.896c-.203.116-.42-.103-.334-.32.409-1.018.691-2.274.797-3.657a.512.512 0 0 1 .507-.477h1.427Zm.47-2.058c.065.294-.169.558-.47.558H11.96a.512.512 0 0 1-.507-.477c-.106-1.383-.389-2.638-.797-3.656-.087-.217.13-.437.333-.32a6.01 6.01 0 0 1 2.868 3.895Zm-4.402.558c.286 0 .515-.24.49-.525-.121-1.361-.429-2.534-.83-3.393-.279-.6-.549-.93-.753-1.112a.535.535 0 0 0-.724 0c-.204.182-.474.513-.754 1.112-.4.859-.708 2.032-.828 3.393a.486.486 0 0 0 .49.525h2.909Zm-5.415 0c.267 0 .486-.21.507-.477.106-1.383.389-2.638.797-3.656.087-.217-.13-.437-.333-.32a6.01 6.01 0 0 0-2.868 3.895c-.065.294.169.558.47.558H4.04ZM2.143 9.308c-.065-.294.169-.558.47-.558H4.04c.267 0 .486.21.507.477.106 1.383.389 2.639.797 3.657.087.217-.13.436-.333.32a6.01 6.01 0 0 1-2.868-3.896Zm3.913-.033a.486.486 0 0 1 .49-.525h2.909c.286 0 .515.24.49.525-.121 1.361-.428 2.535-.83 3.394-.279.6-.549.93-.753 1.112a.535.535 0 0 1-.724 0c-.204-.182-.474-.513-.754-1.112-.4-.859-.708-2.033-.828-3.394Z\" clip-rule=\"evenodd\"/></svg>`},pt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`.wcm-toolbar-placeholder{top:0;bottom:0;left:0;right:0;width:100%;position:absolute;display:block;pointer-events:none;height:100px;border-radius:calc(var(--wcm-background-border-radius) * .9);background-color:var(--wcm-background-color);background-position:center;background-size:cover}.wcm-toolbar{height:38px;display:flex;position:relative;margin:5px 15px 5px 5px;justify-content:space-between;align-items:center}.wcm-toolbar img,.wcm-toolbar svg{height:28px;object-position:left center;object-fit:contain}#wcm-wc-logo path{fill:var(--wcm-accent-fill-color)}button{width:28px;height:28px;border-radius:var(--wcm-icon-button-border-radius);border:0;display:flex;justify-content:center;align-items:center;cursor:pointer;background-color:var(--wcm-color-bg-1);box-shadow:0 0 0 1px var(--wcm-color-overlay)}button:active{background-color:var(--wcm-color-bg-2)}button svg{display:block;object-position:center}button path{fill:var(--wcm-color-fg-1)}.wcm-toolbar div{display:flex}@media(hover:hover){button:hover{background-color:var(--wcm-color-bg-2)}}`;var gt=Object.defineProperty,vt=Object.getOwnPropertyDescriptor,ut=(e,o,r,a)=>{for(var t=a>1?void 0:a?vt(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&gt(o,r,t),t};let fe=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div class=\"wcm-toolbar-placeholder\"></div><div class=\"wcm-toolbar\">${v.WALLET_CONNECT_LOGO} <button @click=\"${_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ModalCtrl.close}\">${v.CROSS_ICON}</button></div>`}};fe.styles=[h.globalCss,pt],fe=ut([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-modal-backcard\")],fe);const bt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`main{padding:20px;padding-top:0;width:100%}`;var ft=Object.defineProperty,xt=Object.getOwnPropertyDescriptor,yt=(e,o,r,a)=>{for(var t=a>1?void 0:a?xt(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&ft(o,r,t),t};let xe=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<main><slot></slot></main>`}};xe.styles=[h.globalCss,bt],xe=yt([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-modal-content\")],xe);const $t=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`footer{padding:10px;display:flex;flex-direction:column;align-items:inherit;justify-content:inherit;border-top:1px solid var(--wcm-color-bg-2)}`;var Ct=Object.defineProperty,kt=Object.getOwnPropertyDescriptor,Ot=(e,o,r,a)=>{for(var t=a>1?void 0:a?kt(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Ct(o,r,t),t};let ye=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<footer><slot></slot></footer>`}};ye.styles=[h.globalCss,$t],ye=Ot([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-modal-footer\")],ye);const Wt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`header{display:flex;justify-content:center;align-items:center;padding:20px;position:relative}.wcm-border{border-bottom:1px solid var(--wcm-color-bg-2);margin-bottom:20px}header button{padding:15px 20px}header button:active{opacity:.5}@media(hover:hover){header button:hover{opacity:.5}}.wcm-back-btn{position:absolute;left:0}.wcm-action-btn{position:absolute;right:0}path{fill:var(--wcm-accent-color)}`;var It=Object.defineProperty,Et=Object.getOwnPropertyDescriptor,te=(e,o,r,a)=>{for(var t=a>1?void 0:a?Et(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&It(o,r,t),t};let S=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.title=\"\",this.onAction=void 0,this.actionIcon=void 0,this.border=!1}backBtnTemplate(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<button class=\"wcm-back-btn\" @click=\"${_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.goBack}\">${v.BACK_ICON}</button>`}actionBtnTemplate(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<button class=\"wcm-action-btn\" @click=\"${this.onAction}\">${this.actionIcon}</button>`}render(){const e={\"wcm-border\":this.border},o=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.state.history.length>1,r=this.title?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-text variant=\"big-bold\">${this.title}</wcm-text>`:(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<slot></slot>`;return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<header class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(e)}\">${o?this.backBtnTemplate():null} ${r} ${this.onAction?this.actionBtnTemplate():null}</header>`}};S.styles=[h.globalCss,Wt],te([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],S.prototype,\"title\",2),te([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],S.prototype,\"onAction\",2),te([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],S.prototype,\"actionIcon\",2),te([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],S.prototype,\"border\",2),S=te([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-modal-header\")],S);const c={MOBILE_BREAKPOINT:600,WCM_RECENT_WALLET_DATA:\"WCM_RECENT_WALLET_DATA\",EXPLORER_WALLET_URL:\"https://explorer.walletconnect.com/?type=wallet\",getShadowRootElement(e,o){const r=e.renderRoot.querySelector(o);if(!r)throw new Error(`${o} not found`);return r},getWalletIcon({id:e,image_id:o}){const{walletImages:r}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ConfigCtrl.state;return r!=null&&r[e]?r[e]:o?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.getWalletImageUrl(o):\"\"},getWalletName(e,o=!1){return o&&e.length>8?`${e.substring(0,8)}..`:e},isMobileAnimation(){return window.innerWidth<=c.MOBILE_BREAKPOINT},async preloadImage(e){const o=new Promise((r,a)=>{const t=new Image;t.onload=r,t.onerror=a,t.crossOrigin=\"anonymous\",t.src=e});return Promise.race([o,_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.wait(3e3)])},getErrorMessage(e){return e instanceof Error?e.message:\"Unknown Error\"},debounce(e,o=500){let r;return(...a)=>{function t(){e(...a)}r&&clearTimeout(r),r=setTimeout(t,o)}},handleMobileLinking(e){const{walletConnectUri:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state,{mobile:r,name:a}=e,t=r?.native,l=r?.universal;c.setRecentWallet(e);function i(s){let $=\"\";t?$=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.formatUniversalUrl(t,s,a):l&&($=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.formatNativeUrl(l,s,a)),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref($,\"_self\")}o&&i(o)},handleAndroidLinking(){const{walletConnectUri:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state;e&&(_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.setWalletConnectAndroidDeepLink(e),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(e,\"_self\"))},async handleUriCopy(){const{walletConnectUri:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state;if(e)try{await navigator.clipboard.writeText(e),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.openToast(\"Link copied\",\"success\")}catch{_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.openToast(\"Failed to copy\",\"error\")}},getCustomImageUrls(){const{walletImages:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ConfigCtrl.state,o=Object.values(e??{});return Object.values(o)},truncate(e,o=8){return e.length<=o?e:`${e.substring(0,4)}...${e.substring(e.length-4)}`},setRecentWallet(e){try{localStorage.setItem(c.WCM_RECENT_WALLET_DATA,JSON.stringify(e))}catch{console.info(\"Unable to set recent wallet\")}},getRecentWallet(){try{const e=localStorage.getItem(c.WCM_RECENT_WALLET_DATA);return e?JSON.parse(e):void 0}catch{console.info(\"Unable to get recent wallet\")}},caseSafeIncludes(e,o){return e.toUpperCase().includes(o.toUpperCase())},openWalletExplorerUrl(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(c.EXPLORER_WALLET_URL,\"_blank\")},getCachedRouterWalletPlatforms(){const{desktop:e,mobile:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),r=Boolean(e?.native),a=Boolean(e?.universal),t=Boolean(o?.native)||Boolean(o?.universal);return{isDesktop:r,isMobile:t,isWeb:a}},goToConnectingView(e){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.setData({Wallet:e});const o=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isMobile(),{isDesktop:r,isWeb:a,isMobile:t}=c.getCachedRouterWalletPlatforms();o?t?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"MobileConnecting\"):a?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"WebConnecting\"):_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"InstallWallet\"):r?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"DesktopConnecting\"):a?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"WebConnecting\"):t?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"MobileQrcodeConnecting\"):_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"InstallWallet\")}},Mt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`.wcm-router{overflow:hidden;will-change:transform}.wcm-content{display:flex;flex-direction:column}`;var Lt=Object.defineProperty,Rt=Object.getOwnPropertyDescriptor,$e=(e,o,r,a)=>{for(var t=a>1?void 0:a?Rt(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Lt(o,r,t),t};let oe=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.view=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.state.view,this.prevView=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.state.view,this.unsubscribe=void 0,this.oldHeight=\"0px\",this.resizeObserver=void 0,this.unsubscribe=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.subscribe(e=>{this.view!==e.view&&this.onChangeRoute()})}firstUpdated(){this.resizeObserver=new ResizeObserver(([e])=>{const o=`${e.contentRect.height}px`;this.oldHeight!==\"0px\"&&(0,motion__WEBPACK_IMPORTED_MODULE_7__.animate)(this.routerEl,{height:[this.oldHeight,o]},{duration:.2}),this.oldHeight=o}),this.resizeObserver.observe(this.contentEl)}disconnectedCallback(){var e,o;(e=this.unsubscribe)==null||e.call(this),(o=this.resizeObserver)==null||o.disconnect()}get routerEl(){return c.getShadowRootElement(this,\".wcm-router\")}get contentEl(){return c.getShadowRootElement(this,\".wcm-content\")}viewTemplate(){switch(this.view){case\"ConnectWallet\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-connect-wallet-view></wcm-connect-wallet-view>`;case\"DesktopConnecting\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-desktop-connecting-view></wcm-desktop-connecting-view>`;case\"MobileConnecting\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-mobile-connecting-view></wcm-mobile-connecting-view>`;case\"WebConnecting\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-web-connecting-view></wcm-web-connecting-view>`;case\"MobileQrcodeConnecting\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-mobile-qr-connecting-view></wcm-mobile-qr-connecting-view>`;case\"WalletExplorer\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-wallet-explorer-view></wcm-wallet-explorer-view>`;case\"Qrcode\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-qrcode-view></wcm-qrcode-view>`;case\"InstallWallet\":return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-install-wallet-view></wcm-install-wallet-view>`;default:return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div>Not Found</div>`}}async onChangeRoute(){await (0,motion__WEBPACK_IMPORTED_MODULE_7__.animate)(this.routerEl,{opacity:[1,0],scale:[1,1.02]},{duration:.15,delay:.1}).finished,this.view=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.state.view,(0,motion__WEBPACK_IMPORTED_MODULE_7__.animate)(this.routerEl,{opacity:[0,1],scale:[.99,1]},{duration:.37,delay:.05})}render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div class=\"wcm-router\"><div class=\"wcm-content\">${this.viewTemplate()}</div></div>`}};oe.styles=[h.globalCss,Mt],$e([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],oe.prototype,\"view\",2),$e([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],oe.prototype,\"prevView\",2),oe=$e([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-modal-router\")],oe);const At=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`div{height:36px;width:max-content;display:flex;justify-content:center;align-items:center;padding:9px 15px 11px;position:absolute;top:12px;box-shadow:0 6px 14px -6px rgba(10,16,31,.3),0 10px 32px -4px rgba(10,16,31,.15);z-index:2;left:50%;transform:translateX(-50%);pointer-events:none;backdrop-filter:blur(20px) saturate(1.8);-webkit-backdrop-filter:blur(20px) saturate(1.8);border-radius:var(--wcm-notification-border-radius);border:1px solid var(--wcm-color-overlay);background-color:var(--wcm-color-overlay)}svg{margin-right:5px}@-moz-document url-prefix(){div{background-color:var(--wcm-color-bg-3)}}.wcm-success path{fill:var(--wcm-accent-color)}.wcm-error path{fill:var(--wcm-error-color)}`;var Pt=Object.defineProperty,Tt=Object.getOwnPropertyDescriptor,ze=(e,o,r,a)=>{for(var t=a>1?void 0:a?Tt(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Pt(o,r,t),t};let ne=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.open=!1,this.unsubscribe=void 0,this.timeout=void 0,this.unsubscribe=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.subscribe(e=>{e.open?(this.open=!0,this.timeout=setTimeout(()=>_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.closeToast(),2200)):(this.open=!1,clearTimeout(this.timeout))})}disconnectedCallback(){var e;(e=this.unsubscribe)==null||e.call(this),clearTimeout(this.timeout),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.closeToast()}render(){const{message:e,variant:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.state,r={\"wcm-success\":o===\"success\",\"wcm-error\":o===\"error\"};return this.open?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(r)}\">${o===\"success\"?v.CHECKMARK_ICON:null} ${o===\"error\"?v.CROSS_ICON:null}<wcm-text variant=\"small-regular\">${e}</wcm-text></div>`:null}};ne.styles=[h.globalCss,At],ze([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],ne.prototype,\"open\",2),ne=ze([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-modal-toast\")],ne);const jt=.1,Ve=2.5,A=7;function Ce(e,o,r){return e===o?!1:(e-o<0?o-e:e-o)<=r+jt}function _t(e,o){const r=Array.prototype.slice.call(qrcode__WEBPACK_IMPORTED_MODULE_6__.create(e,{errorCorrectionLevel:o}).modules.data,0),a=Math.sqrt(r.length);return r.reduce((t,l,i)=>(i%a===0?t.push([l]):t[t.length-1].push(l))&&t,[])}const Dt={generate(e,o,r){const a=\"#141414\",t=\"#ffffff\",l=[],i=_t(e,\"Q\"),s=o/i.length,$=[{x:0,y:0},{x:1,y:0},{x:0,y:1}];$.forEach(({x:y,y:u})=>{const O=(i.length-A)*s*y,b=(i.length-A)*s*u,E=.45;for(let M=0;M<$.length;M+=1){const V=s*(A-M*2);l.push((0,lit__WEBPACK_IMPORTED_MODULE_0__.svg)`<rect fill=\"${M%2===0?a:t}\" height=\"${V}\" rx=\"${V*E}\" ry=\"${V*E}\" width=\"${V}\" x=\"${O+s*M}\" y=\"${b+s*M}\">`)}});const f=Math.floor((r+25)/s),Ne=i.length/2-f/2,Ze=i.length/2+f/2-1,Se=[];i.forEach((y,u)=>{y.forEach((O,b)=>{if(i[u][b]&&!(u<A&&b<A||u>i.length-(A+1)&&b<A||u<A&&b>i.length-(A+1))&&!(u>Ne&&u<Ze&&b>Ne&&b<Ze)){const E=u*s+s/2,M=b*s+s/2;Se.push([E,M])}})});const J={};return Se.forEach(([y,u])=>{J[y]?J[y].push(u):J[y]=[u]}),Object.entries(J).map(([y,u])=>{const O=u.filter(b=>u.every(E=>!Ce(b,E,s)));return[Number(y),O]}).forEach(([y,u])=>{u.forEach(O=>{l.push((0,lit__WEBPACK_IMPORTED_MODULE_0__.svg)`<circle cx=\"${y}\" cy=\"${O}\" fill=\"${a}\" r=\"${s/Ve}\">`)})}),Object.entries(J).filter(([y,u])=>u.length>1).map(([y,u])=>{const O=u.filter(b=>u.some(E=>Ce(b,E,s)));return[Number(y),O]}).map(([y,u])=>{u.sort((b,E)=>b<E?-1:1);const O=[];for(const b of u){const E=O.find(M=>M.some(V=>Ce(b,V,s)));E?E.push(b):O.push([b])}return[y,O.map(b=>[b[0],b[b.length-1]])]}).forEach(([y,u])=>{u.forEach(([O,b])=>{l.push((0,lit__WEBPACK_IMPORTED_MODULE_0__.svg)`<line x1=\"${y}\" x2=\"${y}\" y1=\"${O}\" y2=\"${b}\" stroke=\"${a}\" stroke-width=\"${s/(Ve/2)}\" stroke-linecap=\"round\">`)})}),l}},Nt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}div{position:relative;user-select:none;display:block;overflow:hidden;aspect-ratio:1/1;animation:fadeIn ease .2s}.wcm-dark{background-color:#fff;border-radius:var(--wcm-container-border-radius);padding:18px;box-shadow:0 2px 5px #000}svg:first-child,wcm-wallet-image{position:absolute;top:50%;left:50%;transform:translateY(-50%) translateX(-50%)}wcm-wallet-image{transform:translateY(-50%) translateX(-50%)}wcm-wallet-image{width:25%;height:25%;border-radius:var(--wcm-wallet-icon-border-radius)}svg:first-child{transform:translateY(-50%) translateX(-50%) scale(.9)}svg:first-child path:first-child{fill:var(--wcm-accent-color)}svg:first-child path:last-child{stroke:var(--wcm-color-overlay)}`;var Zt=Object.defineProperty,St=Object.getOwnPropertyDescriptor,q=(e,o,r,a)=>{for(var t=a>1?void 0:a?St(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Zt(o,r,t),t};let j=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.uri=\"\",this.size=0,this.imageId=void 0,this.walletId=void 0,this.imageUrl=void 0}svgTemplate(){const e=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ThemeCtrl.state.themeMode===\"light\"?this.size:this.size-36;return (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg)`<svg height=\"${e}\" width=\"${e}\">${Dt.generate(this.uri,e,e/4)}</svg>`}render(){const e={\"wcm-dark\":_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ThemeCtrl.state.themeMode===\"dark\"};return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div style=\"${`width: ${this.size}px`}\" class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(e)}\">${this.walletId||this.imageUrl?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-wallet-image walletId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.walletId)}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.imageId)}\" imageUrl=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.imageUrl)}\"></wcm-wallet-image>`:v.WALLET_CONNECT_ICON_COLORED} ${this.svgTemplate()}</div>`}};j.styles=[h.globalCss,Nt],q([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],j.prototype,\"uri\",2),q([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Number})],j.prototype,\"size\",2),q([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],j.prototype,\"imageId\",2),q([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],j.prototype,\"walletId\",2),q([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],j.prototype,\"imageUrl\",2),j=q([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-qrcode\")],j);const Bt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`:host{position:relative;height:28px;width:80%}input{width:100%;height:100%;line-height:28px!important;border-radius:var(--wcm-input-border-radius);font-style:normal;font-family:-apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,Ubuntu,'Helvetica Neue',sans-serif;font-feature-settings:'case' on;font-weight:500;font-size:16px;letter-spacing:-.03em;padding:0 10px 0 34px;transition:.2s all ease;color:var(--wcm-color-fg-1);background-color:var(--wcm-color-bg-3);box-shadow:inset 0 0 0 1px var(--wcm-color-overlay);caret-color:var(--wcm-accent-color)}input::placeholder{color:var(--wcm-color-fg-2)}svg{left:10px;top:4px;pointer-events:none;position:absolute;width:20px;height:20px}input:focus-within{box-shadow:inset 0 0 0 1px var(--wcm-accent-color)}path{fill:var(--wcm-color-fg-2)}`;var Ut=Object.defineProperty,Ht=Object.getOwnPropertyDescriptor,Fe=(e,o,r,a)=>{for(var t=a>1?void 0:a?Ht(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Ut(o,r,t),t};let ce=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.onChange=()=>null}render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<input type=\"text\" @input=\"${this.onChange}\" placeholder=\"Search wallets\"> ${v.SEARCH_ICON}`}};ce.styles=[h.globalCss,Bt],Fe([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],ce.prototype,\"onChange\",2),ce=Fe([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-search-input\")],ce);const zt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`@keyframes rotate{100%{transform:rotate(360deg)}}@keyframes dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}100%{stroke-dasharray:90,150;stroke-dashoffset:-124}}svg{animation:rotate 2s linear infinite;display:flex;justify-content:center;align-items:center}svg circle{stroke-linecap:round;animation:dash 1.5s ease infinite;stroke:var(--wcm-accent-color)}`;var Vt=Object.defineProperty,Ft=Object.getOwnPropertyDescriptor,qt=(e,o,r,a)=>{for(var t=a>1?void 0:a?Ft(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Vt(o,r,t),t};let ke=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<svg viewBox=\"0 0 50 50\" width=\"24\" height=\"24\"><circle cx=\"25\" cy=\"25\" r=\"20\" fill=\"none\" stroke-width=\"4\" stroke=\"#fff\"/></svg>`}};ke.styles=[h.globalCss,zt],ke=qt([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-spinner\")],ke);const Qt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`span{font-style:normal;font-family:var(--wcm-font-family);font-feature-settings:var(--wcm-font-feature-settings)}.wcm-xsmall-bold{font-family:var(--wcm-text-xsmall-bold-font-family);font-weight:var(--wcm-text-xsmall-bold-weight);font-size:var(--wcm-text-xsmall-bold-size);line-height:var(--wcm-text-xsmall-bold-line-height);letter-spacing:var(--wcm-text-xsmall-bold-letter-spacing);text-transform:var(--wcm-text-xsmall-bold-text-transform)}.wcm-xsmall-regular{font-family:var(--wcm-text-xsmall-regular-font-family);font-weight:var(--wcm-text-xsmall-regular-weight);font-size:var(--wcm-text-xsmall-regular-size);line-height:var(--wcm-text-xsmall-regular-line-height);letter-spacing:var(--wcm-text-xsmall-regular-letter-spacing);text-transform:var(--wcm-text-xsmall-regular-text-transform)}.wcm-small-thin{font-family:var(--wcm-text-small-thin-font-family);font-weight:var(--wcm-text-small-thin-weight);font-size:var(--wcm-text-small-thin-size);line-height:var(--wcm-text-small-thin-line-height);letter-spacing:var(--wcm-text-small-thin-letter-spacing);text-transform:var(--wcm-text-small-thin-text-transform)}.wcm-small-regular{font-family:var(--wcm-text-small-regular-font-family);font-weight:var(--wcm-text-small-regular-weight);font-size:var(--wcm-text-small-regular-size);line-height:var(--wcm-text-small-regular-line-height);letter-spacing:var(--wcm-text-small-regular-letter-spacing);text-transform:var(--wcm-text-small-regular-text-transform)}.wcm-medium-regular{font-family:var(--wcm-text-medium-regular-font-family);font-weight:var(--wcm-text-medium-regular-weight);font-size:var(--wcm-text-medium-regular-size);line-height:var(--wcm-text-medium-regular-line-height);letter-spacing:var(--wcm-text-medium-regular-letter-spacing);text-transform:var(--wcm-text-medium-regular-text-transform)}.wcm-big-bold{font-family:var(--wcm-text-big-bold-font-family);font-weight:var(--wcm-text-big-bold-weight);font-size:var(--wcm-text-big-bold-size);line-height:var(--wcm-text-big-bold-line-height);letter-spacing:var(--wcm-text-big-bold-letter-spacing);text-transform:var(--wcm-text-big-bold-text-transform)}:host(*){color:var(--wcm-color-fg-1)}.wcm-color-primary{color:var(--wcm-color-fg-1)}.wcm-color-secondary{color:var(--wcm-color-fg-2)}.wcm-color-tertiary{color:var(--wcm-color-fg-3)}.wcm-color-inverse{color:var(--wcm-accent-fill-color)}.wcm-color-accnt{color:var(--wcm-accent-color)}.wcm-color-error{color:var(--wcm-error-color)}`;var Kt=Object.defineProperty,Yt=Object.getOwnPropertyDescriptor,Oe=(e,o,r,a)=>{for(var t=a>1?void 0:a?Yt(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Kt(o,r,t),t};let re=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.variant=\"medium-regular\",this.color=\"primary\"}render(){const e={\"wcm-big-bold\":this.variant===\"big-bold\",\"wcm-medium-regular\":this.variant===\"medium-regular\",\"wcm-small-regular\":this.variant===\"small-regular\",\"wcm-small-thin\":this.variant===\"small-thin\",\"wcm-xsmall-regular\":this.variant===\"xsmall-regular\",\"wcm-xsmall-bold\":this.variant===\"xsmall-bold\",\"wcm-color-primary\":this.color===\"primary\",\"wcm-color-secondary\":this.color===\"secondary\",\"wcm-color-tertiary\":this.color===\"tertiary\",\"wcm-color-inverse\":this.color===\"inverse\",\"wcm-color-accnt\":this.color===\"accent\",\"wcm-color-error\":this.color===\"error\"};return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<span><slot class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(e)}\"></slot></span>`}};re.styles=[h.globalCss,Qt],Oe([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],re.prototype,\"variant\",2),Oe([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],re.prototype,\"color\",2),re=Oe([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-text\")],re);const Gt=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`button{width:100%;height:100%;border-radius:var(--wcm-button-hover-highlight-border-radius);display:flex;align-items:flex-start}button:active{background-color:var(--wcm-color-overlay)}@media(hover:hover){button:hover{background-color:var(--wcm-color-overlay)}}button>div{width:80px;padding:5px 0;display:flex;flex-direction:column;align-items:center}wcm-text{width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-align:center}wcm-wallet-image{height:60px;width:60px;transition:all .2s ease;border-radius:var(--wcm-wallet-icon-border-radius);margin-bottom:5px}.wcm-sublabel{margin-top:2px}`;var Xt=Object.defineProperty,Jt=Object.getOwnPropertyDescriptor,_=(e,o,r,a)=>{for(var t=a>1?void 0:a?Jt(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Xt(o,r,t),t};let L=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.onClick=()=>null,this.name=\"\",this.walletId=\"\",this.label=void 0,this.imageId=void 0,this.installed=!1,this.recent=!1}sublabelTemplate(){return this.recent?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-text class=\"wcm-sublabel\" variant=\"xsmall-bold\" color=\"tertiary\">RECENT</wcm-text>`:this.installed?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-text class=\"wcm-sublabel\" variant=\"xsmall-bold\" color=\"tertiary\">INSTALLED</wcm-text>`:null}handleClick(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.EventsCtrl.click({name:\"WALLET_BUTTON\",walletId:this.walletId}),this.onClick()}render(){var e;return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<button @click=\"${this.handleClick.bind(this)}\"><div><wcm-wallet-image walletId=\"${this.walletId}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.imageId)}\"></wcm-wallet-image><wcm-text variant=\"xsmall-regular\">${(e=this.label)!=null?e:c.getWalletName(this.name,!0)}</wcm-text>${this.sublabelTemplate()}</div></button>`}};L.styles=[h.globalCss,Gt],_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],L.prototype,\"onClick\",2),_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],L.prototype,\"name\",2),_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],L.prototype,\"walletId\",2),_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],L.prototype,\"label\",2),_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],L.prototype,\"imageId\",2),_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],L.prototype,\"installed\",2),_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],L.prototype,\"recent\",2),L=_([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-wallet-button\")],L);const eo=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`:host{display:block}div{overflow:hidden;position:relative;border-radius:inherit;width:100%;height:100%;background-color:var(--wcm-color-overlay)}svg{position:relative;width:100%;height:100%}div::after{content:'';position:absolute;top:0;bottom:0;left:0;right:0;border-radius:inherit;border:1px solid var(--wcm-color-overlay)}div img{width:100%;height:100%;object-fit:cover;object-position:center}#wallet-placeholder-fill{fill:var(--wcm-color-bg-3)}#wallet-placeholder-dash{stroke:var(--wcm-color-overlay)}`;var to=Object.defineProperty,oo=Object.getOwnPropertyDescriptor,se=(e,o,r,a)=>{for(var t=a>1?void 0:a?oo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&to(o,r,t),t};let Q=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.walletId=\"\",this.imageId=void 0,this.imageUrl=void 0}render(){var e;const o=(e=this.imageUrl)!=null&&e.length?this.imageUrl:c.getWalletIcon({id:this.walletId,image_id:this.imageId});return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`${o.length?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div><img crossorigin=\"anonymous\" src=\"${o}\" alt=\"${this.id}\"></div>`:v.WALLET_PLACEHOLDER}`}};Q.styles=[h.globalCss,eo],se([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],Q.prototype,\"walletId\",2),se([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],Q.prototype,\"imageId\",2),se([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],Q.prototype,\"imageUrl\",2),Q=se([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-wallet-image\")],Q);var ro=Object.defineProperty,ao=Object.getOwnPropertyDescriptor,qe=(e,o,r,a)=>{for(var t=a>1?void 0:a?ao(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&ro(o,r,t),t};let We=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.preload=!0,this.preloadData()}async loadImages(e){try{e!=null&&e.length&&await Promise.all(e.map(async o=>c.preloadImage(o)))}catch{console.info(\"Unsuccessful attempt at preloading some images\",e)}}async preloadListings(){if(_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ConfigCtrl.state.enableExplorer){await _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.getRecomendedWallets(),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.setIsDataLoaded(!0);const{recomendedWallets:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state,o=e.map(r=>c.getWalletIcon(r));await this.loadImages(o)}else _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.setIsDataLoaded(!0)}async preloadCustomImages(){const e=c.getCustomImageUrls();await this.loadImages(e)}async preloadData(){try{this.preload&&(this.preload=!1,await Promise.all([this.preloadListings(),this.preloadCustomImages()]))}catch(e){console.error(e),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.openToast(\"Failed preloading\",\"error\")}}};qe([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],We.prototype,\"preload\",2),We=qe([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-explorer-context\")],We);var lo=Object.defineProperty,io=Object.getOwnPropertyDescriptor,no=(e,o,r,a)=>{for(var t=a>1?void 0:a?io(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&lo(o,r,t),t};let Qe=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.unsubscribeTheme=void 0,h.setTheme(),this.unsubscribeTheme=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ThemeCtrl.subscribe(h.setTheme)}disconnectedCallback(){var e;(e=this.unsubscribeTheme)==null||e.call(this)}};Qe=no([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-theme-context\")],Qe);const co=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`@keyframes scroll{0%{transform:translate3d(0,0,0)}100%{transform:translate3d(calc(-70px * 9),0,0)}}.wcm-slider{position:relative;overflow-x:hidden;padding:10px 0;margin:0 -20px;width:calc(100% + 40px)}.wcm-track{display:flex;width:calc(70px * 18);animation:scroll 20s linear infinite;opacity:.7}.wcm-track svg{margin:0 5px}wcm-wallet-image{width:60px;height:60px;margin:0 5px;border-radius:var(--wcm-wallet-icon-border-radius)}.wcm-grid{display:grid;grid-template-columns:repeat(4,80px);justify-content:space-between}.wcm-title{display:flex;align-items:center;margin-bottom:10px}.wcm-title svg{margin-right:6px}.wcm-title path{fill:var(--wcm-accent-color)}wcm-modal-footer .wcm-title{padding:0 10px}wcm-button-big{position:absolute;top:50%;left:50%;transform:translateY(-50%) translateX(-50%);filter:drop-shadow(0 0 17px var(--wcm-color-bg-1))}wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-info-footer wcm-text{text-align:center;margin-bottom:15px}#wallet-placeholder-fill{fill:var(--wcm-color-bg-3)}#wallet-placeholder-dash{stroke:var(--wcm-color-overlay)}`;var so=Object.defineProperty,mo=Object.getOwnPropertyDescriptor,ho=(e,o,r,a)=>{for(var t=a>1?void 0:a?mo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&so(o,r,t),t};let Ie=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{onGoToQrcode(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"Qrcode\")}render(){const{recomendedWallets:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state,o=[...e,...e],r=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.RECOMMENDED_WALLET_AMOUNT*2;return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"Connect your wallet\" .onAction=\"${this.onGoToQrcode}\" .actionIcon=\"${v.QRCODE_ICON}\"></wcm-modal-header><wcm-modal-content><div class=\"wcm-title\">${v.MOBILE_ICON}<wcm-text variant=\"small-regular\" color=\"accent\">WalletConnect</wcm-text></div><div class=\"wcm-slider\"><div class=\"wcm-track\">${[...Array(r)].map((a,t)=>{const l=o[t%o.length];return l?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-wallet-image walletId=\"${l.id}\" imageId=\"${l.image_id}\"></wcm-wallet-image>`:v.WALLET_PLACEHOLDER})}</div><wcm-button-big @click=\"${c.handleAndroidLinking}\"><wcm-text variant=\"medium-regular\" color=\"inverse\">Select Wallet</wcm-text></wcm-button-big></div></wcm-modal-content><wcm-info-footer><wcm-text color=\"secondary\" variant=\"small-thin\">Choose WalletConnect to see supported apps on your device</wcm-text></wcm-info-footer>`}};Ie.styles=[h.globalCss,co],Ie=ho([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-android-wallet-selection\")],Ie);const wo=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`@keyframes loading{to{stroke-dashoffset:0}}@keyframes shake{10%,90%{transform:translate3d(-1px,0,0)}20%,80%{transform:translate3d(1px,0,0)}30%,50%,70%{transform:translate3d(-2px,0,0)}40%,60%{transform:translate3d(2px,0,0)}}:host{display:flex;flex-direction:column;align-items:center}div{position:relative;width:110px;height:110px;display:flex;justify-content:center;align-items:center;margin:40px 0 20px 0;transform:translate3d(0,0,0)}svg{position:absolute;width:110px;height:110px;fill:none;stroke:transparent;stroke-linecap:round;stroke-width:2px;top:0;left:0}use{stroke:var(--wcm-accent-color);animation:loading 1s linear infinite}wcm-wallet-image{border-radius:var(--wcm-wallet-icon-large-border-radius);width:90px;height:90px}wcm-text{margin-bottom:40px}.wcm-error svg{stroke:var(--wcm-error-color)}.wcm-error use{display:none}.wcm-error{animation:shake .4s cubic-bezier(.36,.07,.19,.97) both}.wcm-stale svg,.wcm-stale use{display:none}`;var po=Object.defineProperty,go=Object.getOwnPropertyDescriptor,K=(e,o,r,a)=>{for(var t=a>1?void 0:a?go(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&po(o,r,t),t};let D=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.walletId=void 0,this.imageId=void 0,this.isError=!1,this.isStale=!1,this.label=\"\"}svgLoaderTemplate(){var e,o;const r=(o=(e=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ThemeCtrl.state.themeVariables)==null?void 0:e[\"--wcm-wallet-icon-large-border-radius\"])!=null?o:h.getPreset(\"--wcm-wallet-icon-large-border-radius\");let a=0;r.includes(\"%\")?a=88/100*parseInt(r,10):a=parseInt(r,10),a*=1.17;const t=317-a*1.57,l=425-a*1.8;return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<svg viewBox=\"0 0 110 110\" width=\"110\" height=\"110\"><rect id=\"wcm-loader\" x=\"2\" y=\"2\" width=\"106\" height=\"106\" rx=\"${a}\"/><use xlink:href=\"#wcm-loader\" stroke-dasharray=\"106 ${t}\" stroke-dashoffset=\"${l}\"></use></svg>`}render(){const e={\"wcm-error\":this.isError,\"wcm-stale\":this.isStale};return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(e)}\">${this.svgLoaderTemplate()}<wcm-wallet-image walletId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.walletId)}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.imageId)}\"></wcm-wallet-image></div><wcm-text variant=\"medium-regular\" color=\"${this.isError?\"error\":\"primary\"}\">${this.isError?\"Connection declined\":this.label}</wcm-text>`}};D.styles=[h.globalCss,wo],K([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],D.prototype,\"walletId\",2),K([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],D.prototype,\"imageId\",2),K([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],D.prototype,\"isError\",2),K([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],D.prototype,\"isStale\",2),K([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],D.prototype,\"label\",2),D=K([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-connector-waiting\")],D);const G={manualWallets(){var e,o;const{mobileWallets:r,desktopWallets:a}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ConfigCtrl.state,t=(e=G.recentWallet())==null?void 0:e.id,l=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isMobile()?r:a,i=l?.filter(s=>t!==s.id);return(o=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isMobile()?i?.map(({id:s,name:$,links:f})=>({id:s,name:$,mobile:f,links:f})):i?.map(({id:s,name:$,links:f})=>({id:s,name:$,desktop:f,links:f})))!=null?o:[]},recentWallet(){return c.getRecentWallet()},recomendedWallets(e=!1){var o;const r=e||(o=G.recentWallet())==null?void 0:o.id,{recomendedWallets:a}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state;return a.filter(t=>r!==t.id)}},Z={onConnecting(e){c.goToConnectingView(e)},manualWalletsTemplate(){return G.manualWallets().map(e=>(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-wallet-button walletId=\"${e.id}\" name=\"${e.name}\" .onClick=\"${()=>this.onConnecting(e)}\"></wcm-wallet-button>`)},recomendedWalletsTemplate(e=!1){return G.recomendedWallets(e).map(o=>(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-wallet-button name=\"${o.name}\" walletId=\"${o.id}\" imageId=\"${o.image_id}\" .onClick=\"${()=>this.onConnecting(o)}\"></wcm-wallet-button>`)},recentWalletTemplate(){const e=G.recentWallet();if(e)return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-wallet-button name=\"${e.name}\" walletId=\"${e.id}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(e.image_id)}\" .recent=\"${!0}\" .onClick=\"${()=>this.onConnecting(e)}\"></wcm-wallet-button>`}},vo=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`.wcm-grid{display:grid;grid-template-columns:repeat(4,80px);justify-content:space-between}.wcm-desktop-title,.wcm-mobile-title{display:flex;align-items:center}.wcm-mobile-title{justify-content:space-between;margin-bottom:20px;margin-top:-10px}.wcm-desktop-title{margin-bottom:10px;padding:0 10px}.wcm-subtitle{display:flex;align-items:center}.wcm-subtitle:last-child path{fill:var(--wcm-color-fg-3)}.wcm-desktop-title svg,.wcm-mobile-title svg{margin-right:6px}.wcm-desktop-title path,.wcm-mobile-title path{fill:var(--wcm-accent-color)}`;var uo=Object.defineProperty,bo=Object.getOwnPropertyDescriptor,fo=(e,o,r,a)=>{for(var t=a>1?void 0:a?bo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&uo(o,r,t),t};let Ee=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){const{explorerExcludedWalletIds:e,enableExplorer:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ConfigCtrl.state,r=e!==\"ALL\"&&o,a=Z.manualWalletsTemplate(),t=Z.recomendedWalletsTemplate();let l=[Z.recentWalletTemplate(),...a,...t];l=l.filter(Boolean);const i=l.length>4||r;let s=[];i?s=l.slice(0,3):s=l;const $=Boolean(s.length);return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header .border=\"${!0}\" title=\"Connect your wallet\" .onAction=\"${c.handleUriCopy}\" .actionIcon=\"${v.COPY_ICON}\"></wcm-modal-header><wcm-modal-content><div class=\"wcm-mobile-title\"><div class=\"wcm-subtitle\">${v.MOBILE_ICON}<wcm-text variant=\"small-regular\" color=\"accent\">Mobile</wcm-text></div><div class=\"wcm-subtitle\">${v.SCAN_ICON}<wcm-text variant=\"small-regular\" color=\"secondary\">Scan with your wallet</wcm-text></div></div><wcm-walletconnect-qr></wcm-walletconnect-qr></wcm-modal-content>${$?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-footer><div class=\"wcm-desktop-title\">${v.DESKTOP_ICON}<wcm-text variant=\"small-regular\" color=\"accent\">Desktop</wcm-text></div><div class=\"wcm-grid\">${s} ${i?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-view-all-wallets-button></wcm-view-all-wallets-button>`:null}</div></wcm-modal-footer>`:null}`}};Ee.styles=[h.globalCss,vo],Ee=fo([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-desktop-wallet-selection\")],Ee);const xo=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`div{background-color:var(--wcm-color-bg-2);padding:10px 20px 15px 20px;border-top:1px solid var(--wcm-color-bg-3);text-align:center}a{color:var(--wcm-accent-color);text-decoration:none;transition:opacity .2s ease-in-out;display:inline}a:active{opacity:.8}@media(hover:hover){a:hover{opacity:.8}}`;var yo=Object.defineProperty,$o=Object.getOwnPropertyDescriptor,Co=(e,o,r,a)=>{for(var t=a>1?void 0:a?$o(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&yo(o,r,t),t};let Me=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){const{termsOfServiceUrl:e,privacyPolicyUrl:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ConfigCtrl.state;return e??o?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div><wcm-text variant=\"small-regular\" color=\"secondary\">By connecting your wallet to this app, you agree to the app's ${e?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<a href=\"${e}\" target=\"_blank\" rel=\"noopener noreferrer\">Terms of Service</a>`:null} ${e&&o?\"and\":null} ${o?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<a href=\"${o}\" target=\"_blank\" rel=\"noopener noreferrer\">Privacy Policy</a>`:null}</wcm-text></div>`:null}};Me.styles=[h.globalCss,xo],Me=Co([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-legal-notice\")],Me);const ko=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`div{display:grid;grid-template-columns:repeat(4,80px);margin:0 -10px;justify-content:space-between;row-gap:10px}`;var Oo=Object.defineProperty,Wo=Object.getOwnPropertyDescriptor,Io=(e,o,r,a)=>{for(var t=a>1?void 0:a?Wo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Oo(o,r,t),t};let Le=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{onQrcode(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"Qrcode\")}render(){const{explorerExcludedWalletIds:e,enableExplorer:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ConfigCtrl.state,r=e!==\"ALL\"&&o,a=Z.manualWalletsTemplate(),t=Z.recomendedWalletsTemplate();let l=[Z.recentWalletTemplate(),...a,...t];l=l.filter(Boolean);const i=l.length>8||r;let s=[];i?s=l.slice(0,7):s=l;const $=Boolean(s.length);return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"Connect your wallet\" .onAction=\"${this.onQrcode}\" .actionIcon=\"${v.QRCODE_ICON}\"></wcm-modal-header>${$?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-content><div>${s} ${i?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-view-all-wallets-button></wcm-view-all-wallets-button>`:null}</div></wcm-modal-content>`:null}`}};Le.styles=[h.globalCss,ko],Le=Io([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-mobile-wallet-selection\")],Le);const Eo=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`:host{all:initial}.wcm-overlay{top:0;bottom:0;left:0;right:0;position:fixed;z-index:var(--wcm-z-index);overflow:hidden;display:flex;justify-content:center;align-items:center;opacity:0;pointer-events:none;background-color:var(--wcm-overlay-background-color);backdrop-filter:var(--wcm-overlay-backdrop-filter)}@media(max-height:720px) and (orientation:landscape){.wcm-overlay{overflow:scroll;align-items:flex-start;padding:20px 0}}.wcm-active{pointer-events:auto}.wcm-container{position:relative;max-width:360px;width:100%;outline:0;border-radius:var(--wcm-background-border-radius) var(--wcm-background-border-radius) var(--wcm-container-border-radius) var(--wcm-container-border-radius);border:1px solid var(--wcm-color-overlay);overflow:hidden}.wcm-card{width:100%;position:relative;border-radius:var(--wcm-container-border-radius);overflow:hidden;box-shadow:0 6px 14px -6px rgba(10,16,31,.12),0 10px 32px -4px rgba(10,16,31,.1),0 0 0 1px var(--wcm-color-overlay);background-color:var(--wcm-color-bg-1);color:var(--wcm-color-fg-1)}@media(max-width:600px){.wcm-container{max-width:440px;border-radius:var(--wcm-background-border-radius) var(--wcm-background-border-radius) 0 0}.wcm-card{border-radius:var(--wcm-container-border-radius) var(--wcm-container-border-radius) 0 0}.wcm-overlay{align-items:flex-end}}@media(max-width:440px){.wcm-container{border:0}}`;var Mo=Object.defineProperty,Lo=Object.getOwnPropertyDescriptor,Re=(e,o,r,a)=>{for(var t=a>1?void 0:a?Lo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Mo(o,r,t),t};let ae=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.open=!1,this.active=!1,this.unsubscribeModal=void 0,this.abortController=void 0,this.unsubscribeModal=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ModalCtrl.subscribe(e=>{e.open?this.onOpenModalEvent():this.onCloseModalEvent()})}disconnectedCallback(){var e;(e=this.unsubscribeModal)==null||e.call(this)}get overlayEl(){return c.getShadowRootElement(this,\".wcm-overlay\")}get containerEl(){return c.getShadowRootElement(this,\".wcm-container\")}toggleBodyScroll(e){if(document.querySelector(\"body\"))if(e){const o=document.getElementById(\"wcm-styles\");o?.remove()}else document.head.insertAdjacentHTML(\"beforeend\",'<style id=\"wcm-styles\">html,body{touch-action:none;overflow:hidden;overscroll-behavior:contain;}</style>')}onCloseModal(e){e.target===e.currentTarget&&_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ModalCtrl.close()}onOpenModalEvent(){this.toggleBodyScroll(!1),this.addKeyboardEvents(),this.open=!0,setTimeout(async()=>{const e=c.isMobileAnimation()?{y:[\"50vh\",\"0vh\"]}:{scale:[.98,1]},o=.1,r=.2;await Promise.all([(0,motion__WEBPACK_IMPORTED_MODULE_7__.animate)(this.overlayEl,{opacity:[0,1]},{delay:o,duration:r}).finished,(0,motion__WEBPACK_IMPORTED_MODULE_7__.animate)(this.containerEl,e,{delay:o,duration:r}).finished]),this.active=!0},0)}async onCloseModalEvent(){this.toggleBodyScroll(!0),this.removeKeyboardEvents();const e=c.isMobileAnimation()?{y:[\"0vh\",\"50vh\"]}:{scale:[1,.98]},o=.2;await Promise.all([(0,motion__WEBPACK_IMPORTED_MODULE_7__.animate)(this.overlayEl,{opacity:[1,0]},{duration:o}).finished,(0,motion__WEBPACK_IMPORTED_MODULE_7__.animate)(this.containerEl,e,{duration:o}).finished]),this.containerEl.removeAttribute(\"style\"),this.active=!1,this.open=!1}addKeyboardEvents(){this.abortController=new AbortController,window.addEventListener(\"keydown\",e=>{var o;e.key===\"Escape\"?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ModalCtrl.close():e.key===\"Tab\"&&((o=e.target)!=null&&o.tagName.includes(\"wcm-\")||this.containerEl.focus())},this.abortController),this.containerEl.focus()}removeKeyboardEvents(){var e;(e=this.abortController)==null||e.abort(),this.abortController=void 0}render(){const e={\"wcm-overlay\":!0,\"wcm-active\":this.active};return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-explorer-context></wcm-explorer-context><wcm-theme-context></wcm-theme-context><div id=\"wcm-modal\" class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)(e)}\" @click=\"${this.onCloseModal}\" role=\"alertdialog\" aria-modal=\"true\"><div class=\"wcm-container\" tabindex=\"0\">${this.open?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-backcard></wcm-modal-backcard><div class=\"wcm-card\"><wcm-modal-router></wcm-modal-router><wcm-modal-toast></wcm-modal-toast></div>`:null}</div></div>`}};ae.styles=[h.globalCss,Eo],Re([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],ae.prototype,\"open\",2),Re([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],ae.prototype,\"active\",2),ae=Re([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-modal\")],ae);const Ro=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`div{display:flex;margin-top:15px}slot{display:inline-block;margin:0 5px}wcm-button{margin:0 5px}`;var Ao=Object.defineProperty,Po=Object.getOwnPropertyDescriptor,le=(e,o,r,a)=>{for(var t=a>1?void 0:a?Po(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Ao(o,r,t),t};let B=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.isMobile=!1,this.isDesktop=!1,this.isWeb=!1,this.isRetry=!1}onMobile(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isMobile()?_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.replace(\"MobileConnecting\"):_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.replace(\"MobileQrcodeConnecting\")}onDesktop(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.replace(\"DesktopConnecting\")}onWeb(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.replace(\"WebConnecting\")}render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div>${this.isRetry?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<slot></slot>`:null} ${this.isMobile?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-button .onClick=\"${this.onMobile}\" .iconLeft=\"${v.MOBILE_ICON}\" variant=\"outline\">Mobile</wcm-button>`:null} ${this.isDesktop?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-button .onClick=\"${this.onDesktop}\" .iconLeft=\"${v.DESKTOP_ICON}\" variant=\"outline\">Desktop</wcm-button>`:null} ${this.isWeb?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-button .onClick=\"${this.onWeb}\" .iconLeft=\"${v.GLOBE_ICON}\" variant=\"outline\">Web</wcm-button>`:null}</div>`}};B.styles=[h.globalCss,Ro],le([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],B.prototype,\"isMobile\",2),le([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],B.prototype,\"isDesktop\",2),le([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],B.prototype,\"isWeb\",2),le([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)({type:Boolean})],B.prototype,\"isRetry\",2),B=le([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-platform-selection\")],B);const To=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`button{display:flex;flex-direction:column;padding:5px 10px;border-radius:var(--wcm-button-hover-highlight-border-radius);height:100%;justify-content:flex-start}.wcm-icons{width:60px;height:60px;display:flex;flex-wrap:wrap;padding:7px;border-radius:var(--wcm-wallet-icon-border-radius);justify-content:space-between;align-items:center;margin-bottom:5px;background-color:var(--wcm-color-bg-2);box-shadow:inset 0 0 0 1px var(--wcm-color-overlay)}button:active{background-color:var(--wcm-color-overlay)}@media(hover:hover){button:hover{background-color:var(--wcm-color-overlay)}}.wcm-icons img{width:21px;height:21px;object-fit:cover;object-position:center;border-radius:calc(var(--wcm-wallet-icon-border-radius)/ 2);border:1px solid var(--wcm-color-overlay)}.wcm-icons svg{width:21px;height:21px}.wcm-icons img:nth-child(1),.wcm-icons img:nth-child(2),.wcm-icons svg:nth-child(1),.wcm-icons svg:nth-child(2){margin-bottom:4px}wcm-text{width:100%;text-align:center}#wallet-placeholder-fill{fill:var(--wcm-color-bg-3)}#wallet-placeholder-dash{stroke:var(--wcm-color-overlay)}`;var jo=Object.defineProperty,_o=Object.getOwnPropertyDescriptor,Do=(e,o,r,a)=>{for(var t=a>1?void 0:a?_o(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&jo(o,r,t),t};let Ae=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{onClick(){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.RouterCtrl.push(\"WalletExplorer\")}render(){const{recomendedWallets:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state,o=G.manualWallets(),r=[...e,...o].reverse().slice(0,4);return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<button @click=\"${this.onClick}\"><div class=\"wcm-icons\">${r.map(a=>{const t=c.getWalletIcon(a);if(t)return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<img crossorigin=\"anonymous\" src=\"${t}\">`;const l=c.getWalletIcon({id:a.id});return l?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<img crossorigin=\"anonymous\" src=\"${l}\">`:v.WALLET_PLACEHOLDER})} ${[...Array(4-r.length)].map(()=>v.WALLET_PLACEHOLDER)}</div><wcm-text variant=\"xsmall-regular\">View All</wcm-text></button>`}};Ae.styles=[h.globalCss,To],Ae=Do([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-view-all-wallets-button\")],Ae);const No=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`.wcm-qr-container{width:100%;display:flex;justify-content:center;align-items:center;aspect-ratio:1/1}`;var Zo=Object.defineProperty,So=Object.getOwnPropertyDescriptor,de=(e,o,r,a)=>{for(var t=a>1?void 0:a?So(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Zo(o,r,t),t};let Y=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.walletId=\"\",this.imageId=\"\",this.uri=\"\",setTimeout(()=>{const{walletConnectUri:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state;this.uri=e},0)}get overlayEl(){return c.getShadowRootElement(this,\".wcm-qr-container\")}render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<div class=\"wcm-qr-container\">${this.uri?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-qrcode size=\"${this.overlayEl.offsetWidth}\" uri=\"${this.uri}\" walletId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.walletId)}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(this.imageId)}\"></wcm-qrcode>`:(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-spinner></wcm-spinner>`}</div>`}};Y.styles=[h.globalCss,No],de([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],Y.prototype,\"walletId\",2),de([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.property)()],Y.prototype,\"imageId\",2),de([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],Y.prototype,\"uri\",2),Y=de([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-walletconnect-qr\")],Y);var Bo=Object.defineProperty,Uo=Object.getOwnPropertyDescriptor,Ho=(e,o,r,a)=>{for(var t=a>1?void 0:a?Uo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Bo(o,r,t),t};let Pe=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{viewTemplate(){return _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isAndroid()?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-android-wallet-selection></wcm-android-wallet-selection>`:_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isMobile()?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-mobile-wallet-selection></wcm-mobile-wallet-selection>`:(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-desktop-wallet-selection></wcm-desktop-wallet-selection>`}render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`${this.viewTemplate()}<wcm-legal-notice></wcm-legal-notice>`}};Pe.styles=[h.globalCss],Pe=Ho([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-connect-wallet-view\")],Pe);const zo=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}`;var Vo=Object.defineProperty,Fo=Object.getOwnPropertyDescriptor,Ke=(e,o,r,a)=>{for(var t=a>1?void 0:a?Fo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Vo(o,r,t),t};let me=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.isError=!1,this.openDesktopApp()}onFormatAndRedirect(e){const{desktop:o,name:r}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),a=o?.native;if(a){const t=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.formatNativeUrl(a,e,r);_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(t,\"_self\")}}openDesktopApp(){const{walletConnectUri:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state,o=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData();c.setRecentWallet(o),e&&this.onFormatAndRedirect(e)}render(){const{name:e,id:o,image_id:r}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),{isMobile:a,isWeb:t}=c.getCachedRouterWalletPlatforms();return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"${e}\" .onAction=\"${c.handleUriCopy}\" .actionIcon=\"${v.COPY_ICON}\"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId=\"${o}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(r)}\" label=\"${`Continue in ${e}...`}\" .isError=\"${this.isError}\"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer><wcm-text color=\"secondary\" variant=\"small-thin\">${`Connection can continue loading if ${e} is not installed on your device`}</wcm-text><wcm-platform-selection .isMobile=\"${a}\" .isWeb=\"${t}\" .isRetry=\"${!0}\"><wcm-button .onClick=\"${this.openDesktopApp.bind(this)}\" .iconRight=\"${v.RETRY_ICON}\">Retry</wcm-button></wcm-platform-selection></wcm-info-footer>`}};me.styles=[h.globalCss,zo],Ke([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],me.prototype,\"isError\",2),me=Ke([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-desktop-connecting-view\")],me);const qo=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}wcm-button{margin-top:15px}`;var Qo=Object.defineProperty,Ko=Object.getOwnPropertyDescriptor,Yo=(e,o,r,a)=>{for(var t=a>1?void 0:a?Ko(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Qo(o,r,t),t};let Te=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{onInstall(e){e&&_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(e,\"_blank\")}render(){const{name:e,id:o,image_id:r,homepage:a}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData();return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"${e}\"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId=\"${o}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(r)}\" label=\"Not Detected\" .isStale=\"${!0}\"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer><wcm-text color=\"secondary\" variant=\"small-thin\">${`Download ${e} to continue. If multiple browser extensions are installed, disable non ${e} ones and try again`}</wcm-text><wcm-button .onClick=\"${()=>this.onInstall(a)}\" .iconLeft=\"${v.ARROW_DOWN_ICON}\">Download</wcm-button></wcm-info-footer>`}};Te.styles=[h.globalCss,qo],Te=Yo([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-install-wallet-view\")],Te);const Go=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`wcm-wallet-image{border-radius:var(--wcm-wallet-icon-large-border-radius);width:96px;height:96px;margin-bottom:20px}wcm-info-footer{display:flex;width:100%}.wcm-app-store{justify-content:space-between}.wcm-app-store wcm-wallet-image{margin-right:10px;margin-bottom:0;width:28px;height:28px;border-radius:var(--wcm-wallet-icon-small-border-radius)}.wcm-app-store div{display:flex;align-items:center}.wcm-app-store wcm-button{margin-right:-10px}.wcm-note{flex-direction:column;align-items:center;padding:5px 0}.wcm-note wcm-text{text-align:center}wcm-platform-selection{margin-top:-15px}.wcm-note wcm-text{margin-top:15px}.wcm-note wcm-text span{color:var(--wcm-accent-color)}`;var Xo=Object.defineProperty,Jo=Object.getOwnPropertyDescriptor,Ye=(e,o,r,a)=>{for(var t=a>1?void 0:a?Jo(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&Xo(o,r,t),t};let he=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.isError=!1,this.openMobileApp()}onFormatAndRedirect(e,o=!1){const{mobile:r,name:a}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),t=r?.native,l=r?.universal;if(t&&!o){const i=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.formatNativeUrl(t,e,a);_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(i,\"_self\")}else if(l){const i=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.formatUniversalUrl(l,e,a);_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(i,\"_self\")}}openMobileApp(e=!1){const{walletConnectUri:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state,r=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData();c.setRecentWallet(r),o&&this.onFormatAndRedirect(o,e)}onGoToAppStore(e){e&&_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(e,\"_blank\")}render(){const{name:e,id:o,image_id:r,app:a,mobile:t}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),{isWeb:l}=c.getCachedRouterWalletPlatforms(),i=a?.ios,s=t?.universal;return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"${e}\"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId=\"${o}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(r)}\" label=\"Tap 'Open' to continue…\" .isError=\"${this.isError}\"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer class=\"wcm-note\"><wcm-platform-selection .isWeb=\"${l}\" .isRetry=\"${!0}\"><wcm-button .onClick=\"${()=>this.openMobileApp(!1)}\" .iconRight=\"${v.RETRY_ICON}\">Retry</wcm-button></wcm-platform-selection>${s?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-text color=\"secondary\" variant=\"small-thin\">Still doesn't work? <span tabindex=\"0\" @click=\"${()=>this.openMobileApp(!0)}\">Try this alternate link</span></wcm-text>`:null}</wcm-info-footer><wcm-info-footer class=\"wcm-app-store\"><div><wcm-wallet-image walletId=\"${o}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(r)}\"></wcm-wallet-image><wcm-text>${`Get ${e}`}</wcm-text></div><wcm-button .iconRight=\"${v.ARROW_RIGHT_ICON}\" .onClick=\"${()=>this.onGoToAppStore(i)}\" variant=\"ghost\">App Store</wcm-button></wcm-info-footer>`}};he.styles=[h.globalCss,Go],Ye([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],he.prototype,\"isError\",2),he=Ye([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-mobile-connecting-view\")],he);const er=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}`;var tr=Object.defineProperty,or=Object.getOwnPropertyDescriptor,rr=(e,o,r,a)=>{for(var t=a>1?void 0:a?or(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&tr(o,r,t),t};let je=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){const{name:e,id:o,image_id:r}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),{isDesktop:a,isWeb:t}=c.getCachedRouterWalletPlatforms();return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"${e}\" .onAction=\"${c.handleUriCopy}\" .actionIcon=\"${v.COPY_ICON}\"></wcm-modal-header><wcm-modal-content><wcm-walletconnect-qr walletId=\"${o}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(r)}\"></wcm-walletconnect-qr></wcm-modal-content><wcm-info-footer><wcm-text color=\"secondary\" variant=\"small-thin\">${`Scan this QR Code with your phone's camera or inside ${e} app`}</wcm-text><wcm-platform-selection .isDesktop=\"${a}\" .isWeb=\"${t}\"></wcm-platform-selection></wcm-info-footer>`}};je.styles=[h.globalCss,er],je=rr([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-mobile-qr-connecting-view\")],je);var ar=Object.defineProperty,lr=Object.getOwnPropertyDescriptor,ir=(e,o,r,a)=>{for(var t=a>1?void 0:a?lr(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&ar(o,r,t),t};let _e=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{render(){return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"Scan the code\" .onAction=\"${c.handleUriCopy}\" .actionIcon=\"${v.COPY_ICON}\"></wcm-modal-header><wcm-modal-content><wcm-walletconnect-qr></wcm-walletconnect-qr></wcm-modal-content>`}};_e.styles=[h.globalCss],_e=ir([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-qrcode-view\")],_e);const nr=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`wcm-modal-content{height:clamp(200px,60vh,600px);display:block;overflow:scroll;scrollbar-width:none;position:relative;margin-top:1px}.wcm-grid{display:grid;grid-template-columns:repeat(4,80px);justify-content:space-between;margin:-15px -10px;padding-top:20px}wcm-modal-content::after,wcm-modal-content::before{content:'';position:fixed;pointer-events:none;z-index:1;width:100%;height:20px;opacity:1}wcm-modal-content::before{box-shadow:0 -1px 0 0 var(--wcm-color-bg-1);background:linear-gradient(var(--wcm-color-bg-1),rgba(255,255,255,0))}wcm-modal-content::after{box-shadow:0 1px 0 0 var(--wcm-color-bg-1);background:linear-gradient(rgba(255,255,255,0),var(--wcm-color-bg-1));top:calc(100% - 20px)}wcm-modal-content::-webkit-scrollbar{display:none}.wcm-placeholder-block{display:flex;justify-content:center;align-items:center;height:100px;overflow:hidden}.wcm-empty,.wcm-loading{display:flex}.wcm-loading .wcm-placeholder-block{height:100%}.wcm-end-reached .wcm-placeholder-block{height:0;opacity:0}.wcm-empty .wcm-placeholder-block{opacity:1;height:100%}wcm-wallet-button{margin:calc((100% - 60px)/ 3) 0}`;var cr=Object.defineProperty,sr=Object.getOwnPropertyDescriptor,ie=(e,o,r,a)=>{for(var t=a>1?void 0:a?sr(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&cr(o,r,t),t};const De=40;let U=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(...arguments),this.loading=!_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state.wallets.listings.length,this.firstFetch=!_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state.wallets.listings.length,this.search=\"\",this.endReached=!1,this.intersectionObserver=void 0,this.searchDebounce=c.debounce(e=>{e.length>=1?(this.firstFetch=!0,this.endReached=!1,this.search=e,_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.resetSearch(),this.fetchWallets()):this.search&&(this.search=\"\",this.endReached=this.isLastPage(),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.resetSearch())})}firstUpdated(){this.createPaginationObserver()}disconnectedCallback(){var e;(e=this.intersectionObserver)==null||e.disconnect()}get placeholderEl(){return c.getShadowRootElement(this,\".wcm-placeholder-block\")}createPaginationObserver(){this.intersectionObserver=new IntersectionObserver(([e])=>{e.isIntersecting&&!(this.search&&this.firstFetch)&&this.fetchWallets()}),this.intersectionObserver.observe(this.placeholderEl)}isLastPage(){const{wallets:e,search:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state,{listings:r,total:a}=this.search?o:e;return a<=De||r.length>=a}async fetchWallets(){var e;const{wallets:o,search:r}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state,{listings:a,total:t,page:l}=this.search?r:o;if(!this.endReached&&(this.firstFetch||t>De&&a.length<t))try{this.loading=!0;const i=(e=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state.chains)==null?void 0:e.join(\",\"),{listings:s}=await _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.getWallets({page:this.firstFetch?1:l+1,entries:De,search:this.search,version:2,chains:i}),$=s.map(f=>c.getWalletIcon(f));await Promise.all([...$.map(async f=>c.preloadImage(f)),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.wait(300)]),this.endReached=this.isLastPage()}catch(i){console.error(i),_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ToastCtrl.openToast(c.getErrorMessage(i),\"error\")}finally{this.loading=!1,this.firstFetch=!1}}onConnect(e){_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isAndroid()?c.handleMobileLinking(e):c.goToConnectingView(e)}onSearchChange(e){const{value:o}=e.target;this.searchDebounce(o)}render(){const{wallets:e,search:o}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.ExplorerCtrl.state,{listings:r}=this.search?o:e,a=this.loading&&!r.length,t=this.search.length>=3;let l=Z.manualWalletsTemplate(),i=Z.recomendedWalletsTemplate(!0);t&&(l=l.filter(({values:f})=>c.caseSafeIncludes(f[0],this.search)),i=i.filter(({values:f})=>c.caseSafeIncludes(f[0],this.search)));const s=!this.loading&&!r.length&&!i.length,$={\"wcm-loading\":a,\"wcm-end-reached\":this.endReached||!this.loading,\"wcm-empty\":s};return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header><wcm-search-input .onChange=\"${this.onSearchChange.bind(this)}\"></wcm-search-input></wcm-modal-header><wcm-modal-content class=\"${(0,lit_directives_class_map_js__WEBPACK_IMPORTED_MODULE_2__.classMap)($)}\"><div class=\"wcm-grid\">${a?null:l} ${a?null:i} ${a?null:r.map(f=>(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`${f?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-wallet-button imageId=\"${f.image_id}\" name=\"${f.name}\" walletId=\"${f.id}\" .onClick=\"${()=>this.onConnect(f)}\"></wcm-wallet-button>`:null}`)}</div><div class=\"wcm-placeholder-block\">${s?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-text variant=\"big-bold\" color=\"secondary\">No results found</wcm-text>`:null} ${!s&&this.loading?(0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-spinner></wcm-spinner>`:null}</div></wcm-modal-content>`}};U.styles=[h.globalCss,nr],ie([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],U.prototype,\"loading\",2),ie([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],U.prototype,\"firstFetch\",2),ie([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],U.prototype,\"search\",2),ie([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],U.prototype,\"endReached\",2),U=ie([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-wallet-explorer-view\")],U);const dr=(0,lit__WEBPACK_IMPORTED_MODULE_0__.css)`wcm-info-footer{flex-direction:column;align-items:center;display:flex;width:100%;padding:5px 0}wcm-text{text-align:center}`;var mr=Object.defineProperty,hr=Object.getOwnPropertyDescriptor,Ge=(e,o,r,a)=>{for(var t=a>1?void 0:a?hr(o,r):o,l=e.length-1,i;l>=0;l--)(i=e[l])&&(t=(a?i(o,r,t):i(t))||t);return a&&t&&mr(o,r,t),t};let we=class extends lit__WEBPACK_IMPORTED_MODULE_0__.LitElement{constructor(){super(),this.isError=!1,this.openWebWallet()}onFormatAndRedirect(e){const{desktop:o,name:r}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),a=o?.universal;if(a){const t=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.formatUniversalUrl(a,e,r);_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.openHref(t,\"_blank\")}}openWebWallet(){const{walletConnectUri:e}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.OptionsCtrl.state,o=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData();c.setRecentWallet(o),e&&this.onFormatAndRedirect(e)}render(){const{name:e,id:o,image_id:r}=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.getWalletRouterData(),{isMobile:a,isDesktop:t}=c.getCachedRouterWalletPlatforms(),l=_walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_3__.CoreUtil.isMobile();return (0,lit__WEBPACK_IMPORTED_MODULE_0__.html)`<wcm-modal-header title=\"${e}\" .onAction=\"${c.handleUriCopy}\" .actionIcon=\"${v.COPY_ICON}\"></wcm-modal-header><wcm-modal-content><wcm-connector-waiting walletId=\"${o}\" imageId=\"${(0,lit_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_5__.ifDefined)(r)}\" label=\"${`Continue in ${e}...`}\" .isError=\"${this.isError}\"></wcm-connector-waiting></wcm-modal-content><wcm-info-footer><wcm-text color=\"secondary\" variant=\"small-thin\">${`${e} web app has opened in a new tab. Go there, accept the connection, and come back`}</wcm-text><wcm-platform-selection .isMobile=\"${a}\" .isDesktop=\"${l?!1:t}\" .isRetry=\"${!0}\"><wcm-button .onClick=\"${this.openWebWallet.bind(this)}\" .iconRight=\"${v.RETRY_ICON}\">Retry</wcm-button></wcm-platform-selection></wcm-info-footer>`}};we.styles=[h.globalCss,dr],Ge([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.state)()],we.prototype,\"isError\",2),we=Ge([(0,lit_decorators_js__WEBPACK_IMPORTED_MODULE_1__.customElement)(\"wcm-web-connecting-view\")],we);\n//# sourceMappingURL=index.js.map\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/@walletconnect/modal-ui/dist/index.js?");

/***/ }),

/***/ "./node_modules/lit-element/development/lit-element.js":
/*!*************************************************************!*\
  !*** ./node_modules/lit-element/development/lit-element.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.CSSResult),\n/* harmony export */   LitElement: () => (/* binding */ LitElement),\n/* harmony export */   ReactiveElement: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.ReactiveElement),\n/* harmony export */   UpdatingElement: () => (/* binding */ UpdatingElement),\n/* harmony export */   _$LE: () => (/* binding */ _$LE),\n/* harmony export */   _$LH: () => (/* reexport safe */ lit_html__WEBPACK_IMPORTED_MODULE_1__._$LH),\n/* harmony export */   adoptStyles: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.adoptStyles),\n/* harmony export */   css: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.css),\n/* harmony export */   defaultConverter: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.defaultConverter),\n/* harmony export */   getCompatibleStyle: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.getCompatibleStyle),\n/* harmony export */   html: () => (/* reexport safe */ lit_html__WEBPACK_IMPORTED_MODULE_1__.html),\n/* harmony export */   noChange: () => (/* reexport safe */ lit_html__WEBPACK_IMPORTED_MODULE_1__.noChange),\n/* harmony export */   notEqual: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.notEqual),\n/* harmony export */   nothing: () => (/* reexport safe */ lit_html__WEBPACK_IMPORTED_MODULE_1__.nothing),\n/* harmony export */   render: () => (/* reexport safe */ lit_html__WEBPACK_IMPORTED_MODULE_1__.render),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.supportsAdoptingStyleSheets),\n/* harmony export */   svg: () => (/* reexport safe */ lit_html__WEBPACK_IMPORTED_MODULE_1__.svg),\n/* harmony export */   unsafeCSS: () => (/* reexport safe */ _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.unsafeCSS)\n/* harmony export */ });\n/* harmony import */ var _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lit/reactive-element */ \"./node_modules/@lit/reactive-element/development/reactive-element.js\");\n/* harmony import */ var lit_html__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lit-html */ \"./node_modules/lit-html/development/lit-html.js\");\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c;\n/**\n * The main LitElement module, which defines the {@linkcode LitElement} base\n * class and related APIs.\n *\n *  LitElement components can define a template and a set of observed\n * properties. Changing an observed property triggers a re-render of the\n * element.\n *\n *  Import {@linkcode LitElement} and {@linkcode html} from this module to\n * create a component:\n *\n *  ```js\n * import {LitElement, html} from 'lit-element';\n *\n * class MyElement extends LitElement {\n *\n *   // Declare observed properties\n *   static get properties() {\n *     return {\n *       adjective: {}\n *     }\n *   }\n *\n *   constructor() {\n *     this.adjective = 'awesome';\n *   }\n *\n *   // Define the element's template\n *   render() {\n *     return html`<p>your ${adjective} template here</p>`;\n *   }\n * }\n *\n * customElements.define('my-element', MyElement);\n * ```\n *\n * `LitElement` extends {@linkcode ReactiveElement} and adds lit-html\n * templating. The `ReactiveElement` class is provided for users that want to\n * build their own custom element base classes that don't use lit-html.\n *\n * @packageDocumentation\n */\n\n\n\n\n// For backwards compatibility export ReactiveElement as UpdatingElement. Note,\n// IE transpilation requires exporting like this.\nconst UpdatingElement = _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.ReactiveElement;\nconst DEV_MODE = true;\nlet issueWarning;\nif (DEV_MODE) {\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    const issuedWarnings = ((_a = globalThis.litIssuedWarnings) !== null && _a !== void 0 ? _a : (globalThis.litIssuedWarnings = new Set()));\n    // Issue a warning, if we haven't already.\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!issuedWarnings.has(warning)) {\n            console.warn(warning);\n            issuedWarnings.add(warning);\n        }\n    };\n}\n/**\n * Base element class that manages element properties and attributes, and\n * renders a lit-html template.\n *\n * To define a component, subclass `LitElement` and implement a\n * `render` method to provide the component's template. Define properties\n * using the {@linkcode LitElement.properties properties} property or the\n * {@linkcode property} decorator.\n */\nclass LitElement extends _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.ReactiveElement {\n    constructor() {\n        super(...arguments);\n        /**\n         * @category rendering\n         */\n        this.renderOptions = { host: this };\n        this.__childPart = undefined;\n    }\n    /**\n     * @category rendering\n     */\n    createRenderRoot() {\n        var _a;\n        var _b;\n        const renderRoot = super.createRenderRoot();\n        // When adoptedStyleSheets are shimmed, they are inserted into the\n        // shadowRoot by createRenderRoot. Adjust the renderBefore node so that\n        // any styles in Lit content render before adoptedStyleSheets. This is\n        // important so that adoptedStyleSheets have precedence over styles in\n        // the shadowRoot.\n        (_a = (_b = this.renderOptions).renderBefore) !== null && _a !== void 0 ? _a : (_b.renderBefore = renderRoot.firstChild);\n        return renderRoot;\n    }\n    /**\n     * Updates the element. This method reflects property values to attributes\n     * and calls `render` to render DOM via lit-html. Setting properties inside\n     * this method will *not* trigger another update.\n     * @param changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    update(changedProperties) {\n        // Setting properties in `render` should not trigger an update. Since\n        // updates are allowed after super.update, it's important to call `render`\n        // before that.\n        const value = this.render();\n        if (!this.hasUpdated) {\n            this.renderOptions.isConnected = this.isConnected;\n        }\n        super.update(changedProperties);\n        this.__childPart = (0,lit_html__WEBPACK_IMPORTED_MODULE_1__.render)(value, this.renderRoot, this.renderOptions);\n    }\n    /**\n     * Invoked when the component is added to the document's DOM.\n     *\n     * In `connectedCallback()` you should setup tasks that should only occur when\n     * the element is connected to the document. The most common of these is\n     * adding event listeners to nodes external to the element, like a keydown\n     * event handler added to the window.\n     *\n     * ```ts\n     * connectedCallback() {\n     *   super.connectedCallback();\n     *   addEventListener('keydown', this._handleKeydown);\n     * }\n     * ```\n     *\n     * Typically, anything done in `connectedCallback()` should be undone when the\n     * element is disconnected, in `disconnectedCallback()`.\n     *\n     * @category lifecycle\n     */\n    connectedCallback() {\n        var _a;\n        super.connectedCallback();\n        (_a = this.__childPart) === null || _a === void 0 ? void 0 : _a.setConnected(true);\n    }\n    /**\n     * Invoked when the component is removed from the document's DOM.\n     *\n     * This callback is the main signal to the element that it may no longer be\n     * used. `disconnectedCallback()` should ensure that nothing is holding a\n     * reference to the element (such as event listeners added to nodes external\n     * to the element), so that it is free to be garbage collected.\n     *\n     * ```ts\n     * disconnectedCallback() {\n     *   super.disconnectedCallback();\n     *   window.removeEventListener('keydown', this._handleKeydown);\n     * }\n     * ```\n     *\n     * An element may be re-connected after being disconnected.\n     *\n     * @category lifecycle\n     */\n    disconnectedCallback() {\n        var _a;\n        super.disconnectedCallback();\n        (_a = this.__childPart) === null || _a === void 0 ? void 0 : _a.setConnected(false);\n    }\n    /**\n     * Invoked on each update to perform rendering tasks. This method may return\n     * any value renderable by lit-html's `ChildPart` - typically a\n     * `TemplateResult`. Setting properties inside this method will *not* trigger\n     * the element to update.\n     * @category rendering\n     */\n    render() {\n        return lit_html__WEBPACK_IMPORTED_MODULE_1__.noChange;\n    }\n}\n/**\n * Ensure this class is marked as `finalized` as an optimization ensuring\n * it will not needlessly try to `finalize`.\n *\n * Note this property name is a string to prevent breaking Closure JS Compiler\n * optimizations. See @lit/reactive-element for more information.\n */\nLitElement['finalized'] = true;\n// This property needs to remain unminified.\nLitElement['_$litElement$'] = true;\n// Install hydration if available\n(_b = globalThis.litElementHydrateSupport) === null || _b === void 0 ? void 0 : _b.call(globalThis, { LitElement });\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n    ? globalThis.litElementPolyfillSupportDevMode\n    : globalThis.litElementPolyfillSupport;\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport({ LitElement });\n// DEV mode warnings\nif (DEV_MODE) {\n    /* eslint-disable @typescript-eslint/no-explicit-any */\n    // Note, for compatibility with closure compilation, this access\n    // needs to be as a string property index.\n    LitElement['finalize'] = function () {\n        const finalized = _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__.ReactiveElement.finalize.call(this);\n        if (!finalized) {\n            return false;\n        }\n        const warnRemovedOrRenamed = (obj, name, renamed = false) => {\n            if (obj.hasOwnProperty(name)) {\n                const ctorName = (typeof obj === 'function' ? obj : obj.constructor)\n                    .name;\n                issueWarning(renamed ? 'renamed-api' : 'removed-api', `\\`${name}\\` is implemented on class ${ctorName}. It ` +\n                    `has been ${renamed ? 'renamed' : 'removed'} ` +\n                    `in this version of LitElement.`);\n            }\n        };\n        warnRemovedOrRenamed(this, 'render');\n        warnRemovedOrRenamed(this, 'getStyles', true);\n        warnRemovedOrRenamed(this.prototype, 'adoptStyles');\n        return true;\n    };\n    /* eslint-enable @typescript-eslint/no-explicit-any */\n}\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-html, since this module re-exports all of lit-html.\n *\n * @private\n */\nconst _$LE = {\n    _$attributeToProperty: (el, name, value) => {\n        // eslint-disable-next-line\n        el._$attributeToProperty(name, value);\n    },\n    // eslint-disable-next-line\n    _$changedProperties: (el) => el._$changedProperties,\n};\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for LitElement usage.\n((_c = globalThis.litElementVersions) !== null && _c !== void 0 ? _c : (globalThis.litElementVersions = [])).push('3.3.3');\nif (DEV_MODE && globalThis.litElementVersions.length > 1) {\n    issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`);\n}\n//# sourceMappingURL=lit-element.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit-element/development/lit-element.js?");

/***/ }),

/***/ "./node_modules/lit-html/development/directive.js":
/*!********************************************************!*\
  !*** ./node_modules/lit-html/development/directive.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Directive: () => (/* binding */ Directive),\n/* harmony export */   PartType: () => (/* binding */ PartType),\n/* harmony export */   directive: () => (/* binding */ directive)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst PartType = {\n    ATTRIBUTE: 1,\n    CHILD: 2,\n    PROPERTY: 3,\n    BOOLEAN_ATTRIBUTE: 4,\n    EVENT: 5,\n    ELEMENT: 6,\n};\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n */\nconst directive = (c) => (...values) => ({\n    // This property needs to remain unminified.\n    ['_$litDirective$']: c,\n    values,\n});\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nclass Directive {\n    constructor(_partInfo) { }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    /** @internal */\n    _$initialize(part, parent, attributeIndex) {\n        this.__part = part;\n        this._$parent = parent;\n        this.__attributeIndex = attributeIndex;\n    }\n    /** @internal */\n    _$resolve(part, props) {\n        return this.update(part, props);\n    }\n    update(_part, props) {\n        return this.render(...props);\n    }\n}\n//# sourceMappingURL=directive.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit-html/development/directive.js?");

/***/ }),

/***/ "./node_modules/lit-html/development/directives/class-map.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lit-html/development/directives/class-map.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   classMap: () => (/* binding */ classMap)\n/* harmony export */ });\n/* harmony import */ var _lit_html_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lit-html.js */ \"./node_modules/lit-html/development/lit-html.js\");\n/* harmony import */ var _directive_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../directive.js */ \"./node_modules/lit-html/development/directive.js\");\n/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n\nclass ClassMapDirective extends _directive_js__WEBPACK_IMPORTED_MODULE_1__.Directive {\n    constructor(partInfo) {\n        var _a;\n        super(partInfo);\n        if (partInfo.type !== _directive_js__WEBPACK_IMPORTED_MODULE_1__.PartType.ATTRIBUTE ||\n            partInfo.name !== 'class' ||\n            ((_a = partInfo.strings) === null || _a === void 0 ? void 0 : _a.length) > 2) {\n            throw new Error('`classMap()` can only be used in the `class` attribute ' +\n                'and must be the only part in the attribute.');\n        }\n    }\n    render(classInfo) {\n        // Add spaces to ensure separation from static classes\n        return (' ' +\n            Object.keys(classInfo)\n                .filter((key) => classInfo[key])\n                .join(' ') +\n            ' ');\n    }\n    update(part, [classInfo]) {\n        var _a, _b;\n        // Remember dynamic classes on the first render\n        if (this._previousClasses === undefined) {\n            this._previousClasses = new Set();\n            if (part.strings !== undefined) {\n                this._staticClasses = new Set(part.strings\n                    .join(' ')\n                    .split(/\\s/)\n                    .filter((s) => s !== ''));\n            }\n            for (const name in classInfo) {\n                if (classInfo[name] && !((_a = this._staticClasses) === null || _a === void 0 ? void 0 : _a.has(name))) {\n                    this._previousClasses.add(name);\n                }\n            }\n            return this.render(classInfo);\n        }\n        const classList = part.element.classList;\n        // Remove old classes that no longer apply\n        // We use forEach() instead of for-of so that we don't require down-level\n        // iteration.\n        this._previousClasses.forEach((name) => {\n            if (!(name in classInfo)) {\n                classList.remove(name);\n                this._previousClasses.delete(name);\n            }\n        });\n        // Add or remove classes based on their classMap value\n        for (const name in classInfo) {\n            // We explicitly want a loose truthy check of `value` because it seems\n            // more convenient that '' and 0 are skipped.\n            const value = !!classInfo[name];\n            if (value !== this._previousClasses.has(name) &&\n                !((_b = this._staticClasses) === null || _b === void 0 ? void 0 : _b.has(name))) {\n                if (value) {\n                    classList.add(name);\n                    this._previousClasses.add(name);\n                }\n                else {\n                    classList.remove(name);\n                    this._previousClasses.delete(name);\n                }\n            }\n        }\n        return _lit_html_js__WEBPACK_IMPORTED_MODULE_0__.noChange;\n    }\n}\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsey, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nconst classMap = (0,_directive_js__WEBPACK_IMPORTED_MODULE_1__.directive)(ClassMapDirective);\n//# sourceMappingURL=class-map.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit-html/development/directives/class-map.js?");

/***/ }),

/***/ "./node_modules/lit-html/development/directives/if-defined.js":
/*!********************************************************************!*\
  !*** ./node_modules/lit-html/development/directives/if-defined.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ifDefined: () => (/* binding */ ifDefined)\n/* harmony export */ });\n/* harmony import */ var _lit_html_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lit-html.js */ \"./node_modules/lit-html/development/lit-html.js\");\n/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * For AttributeParts, sets the attribute if the value is defined and removes\n * the attribute if the value is undefined.\n *\n * For other part types, this directive is a no-op.\n */\nconst ifDefined = (value) => value !== null && value !== void 0 ? value : _lit_html_js__WEBPACK_IMPORTED_MODULE_0__.nothing;\n//# sourceMappingURL=if-defined.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit-html/development/directives/if-defined.js?");

/***/ }),

/***/ "./node_modules/lit-html/development/is-server.js":
/*!********************************************************!*\
  !*** ./node_modules/lit-html/development/is-server.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isServer: () => (/* binding */ isServer)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * @fileoverview\n *\n * This file exports a boolean const whose value will depend on what environment\n * the module is being imported from.\n */\nconst NODE_MODE = false;\n/**\n * A boolean that will be `true` in server environments like Node, and `false`\n * in browser environments. Note that your server environment or toolchain must\n * support the `\"node\"` export condition for this to be `true`.\n *\n * This can be used when authoring components to change behavior based on\n * whether or not the component is executing in an SSR context.\n */\nconst isServer = NODE_MODE;\n//# sourceMappingURL=is-server.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit-html/development/is-server.js?");

/***/ }),

/***/ "./node_modules/lit-html/development/lit-html.js":
/*!*******************************************************!*\
  !*** ./node_modules/lit-html/development/lit-html.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _$LH: () => (/* binding */ _$LH),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   noChange: () => (/* binding */ noChange),\n/* harmony export */   nothing: () => (/* binding */ nothing),\n/* harmony export */   render: () => (/* binding */ render),\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c, _d;\nconst DEV_MODE = true;\nconst ENABLE_EXTRA_SECURITY_HOOKS = true;\nconst ENABLE_SHADYDOM_NOPATCH = true;\nconst NODE_MODE = false;\n// Use window for browser builds because IE11 doesn't have globalThis.\nconst global = NODE_MODE ? globalThis : window;\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n    ? (event) => {\n        const shouldEmit = global\n            .emitLitDebugLogEvents;\n        if (!shouldEmit) {\n            return;\n        }\n        global.dispatchEvent(new CustomEvent('lit-debug', {\n            detail: event,\n        }));\n    }\n    : undefined;\n// Used for connecting beginRender and endRender events when there are nested\n// renders when errors are thrown preventing an endRender event from being\n// called.\nlet debugLogRenderId = 0;\nlet issueWarning;\nif (DEV_MODE) {\n    (_a = global.litIssuedWarnings) !== null && _a !== void 0 ? _a : (global.litIssuedWarnings = new Set());\n    // Issue a warning, if we haven't already.\n    issueWarning = (code, warning) => {\n        warning += code\n            ? ` See https://lit.dev/msg/${code} for more information.`\n            : '';\n        if (!global.litIssuedWarnings.has(warning)) {\n            console.warn(warning);\n            global.litIssuedWarnings.add(warning);\n        }\n    };\n    issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n}\nconst wrap = ENABLE_SHADYDOM_NOPATCH &&\n    ((_b = global.ShadyDOM) === null || _b === void 0 ? void 0 : _b.inUse) &&\n    ((_c = global.ShadyDOM) === null || _c === void 0 ? void 0 : _c.noPatch) === true\n    ? global.ShadyDOM.wrap\n    : (node) => node;\nconst trustedTypes = global.trustedTypes;\n/**\n * Our TrustedTypePolicy for HTML which is declared using the html template\n * tag function.\n *\n * That HTML is a developer-authored constant, and is parsed with innerHTML\n * before any untrusted expressions have been mixed in. Therefor it is\n * considered safe by construction.\n */\nconst policy = trustedTypes\n    ? trustedTypes.createPolicy('lit-html', {\n        createHTML: (s) => s,\n    })\n    : undefined;\nconst identityFunction = (value) => value;\nconst noopSanitizer = (_node, _name, _type) => identityFunction;\n/** Sets the global sanitizer factory. */\nconst setSanitizer = (newSanitizer) => {\n    if (!ENABLE_EXTRA_SECURITY_HOOKS) {\n        return;\n    }\n    if (sanitizerFactoryInternal !== noopSanitizer) {\n        throw new Error(`Attempted to overwrite existing lit-html security policy.` +\n            ` setSanitizeDOMValueFactory should be called at most once.`);\n    }\n    sanitizerFactoryInternal = newSanitizer;\n};\n/**\n * Only used in internal tests, not a part of the public API.\n */\nconst _testOnlyClearSanitizerFactoryDoNotCallOrElse = () => {\n    sanitizerFactoryInternal = noopSanitizer;\n};\nconst createSanitizer = (node, name, type) => {\n    return sanitizerFactoryInternal(node, name, type);\n};\n// Added to an attribute name to mark the attribute as bound so we can find\n// it easily.\nconst boundAttributeSuffix = '$lit$';\n// This marker is used in many syntactic positions in HTML, so it must be\n// a valid element name and attribute name. We don't support dynamic names (yet)\n// but this at least ensures that the parse tree is closer to the template\n// intention.\nconst marker = `lit$${String(Math.random()).slice(9)}$`;\n// String used to tell if a comment is a marker comment\nconst markerMatch = '?' + marker;\n// Text used to insert a comment marker node. We use processing instruction\n// syntax because it's slightly smaller, but parses as a comment node.\nconst nodeMarker = `<${markerMatch}>`;\nconst d = NODE_MODE && global.document === undefined\n    ? {\n        createTreeWalker() {\n            return {};\n        },\n    }\n    : document;\n// Creates a dynamic marker. We never have to search for these in the DOM.\nconst createMarker = () => d.createComment('');\nconst isPrimitive = (value) => value === null || (typeof value != 'object' && typeof value != 'function');\nconst isArray = Array.isArray;\nconst isIterable = (value) => isArray(value) ||\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    typeof (value === null || value === void 0 ? void 0 : value[Symbol.iterator]) === 'function';\nconst SPACE_CHAR = `[ \\t\\n\\f\\r]`;\nconst ATTR_VALUE_CHAR = `[^ \\t\\n\\f\\r\"'\\`<>=]`;\nconst NAME_CHAR = `[^\\\\s\"'>=/]`;\n// These regexes represent the five parsing states that we care about in the\n// Template's HTML scanner. They match the *end* of the state they're named\n// after.\n// Depending on the match, we transition to a new state. If there's no match,\n// we stay in the same state.\n// Note that the regexes are stateful. We utilize lastIndex and sync it\n// across the multiple regexes used. In addition to the five regexes below\n// we also dynamically create a regex to find the matching end tags for raw\n// text elements.\n/**\n * End of text is: `<` followed by:\n *   (comment start) or (tag) or (dynamic tag binding)\n */\nconst textEndRegex = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g;\nconst COMMENT_START = 1;\nconst TAG_NAME = 2;\nconst DYNAMIC_TAG_NAME = 3;\nconst commentEndRegex = /-->/g;\n/**\n * Comments not started with <!--, like </{, can be ended by a single `>`\n */\nconst comment2EndRegex = />/g;\n/**\n * The tagEnd regex matches the end of the \"inside an opening\" tag syntax\n * position. It either matches a `>`, an attribute-like sequence, or the end\n * of the string after a space (attribute-name position ending).\n *\n * See attributes in the HTML spec:\n * https://www.w3.org/TR/html5/syntax.html#elements-attributes\n *\n * \" \\t\\n\\f\\r\" are HTML space characters:\n * https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * So an attribute is:\n *  * The name: any character except a whitespace character, (\"), ('), \">\",\n *    \"=\", or \"/\". Note: this is different from the HTML spec which also excludes control characters.\n *  * Followed by zero or more space characters\n *  * Followed by \"=\"\n *  * Followed by zero or more space characters\n *  * Followed by:\n *    * Any character except space, ('), (\"), \"<\", \">\", \"=\", (`), or\n *    * (\") then any non-(\"), or\n *    * (') then any non-(')\n */\nconst tagEndRegex = new RegExp(`>|${SPACE_CHAR}(?:(${NAME_CHAR}+)(${SPACE_CHAR}*=${SPACE_CHAR}*(?:${ATTR_VALUE_CHAR}|(\"|')|))|$)`, 'g');\nconst ENTIRE_MATCH = 0;\nconst ATTRIBUTE_NAME = 1;\nconst SPACES_AND_EQUALS = 2;\nconst QUOTE_CHAR = 3;\nconst singleQuoteAttrEndRegex = /'/g;\nconst doubleQuoteAttrEndRegex = /\"/g;\n/**\n * Matches the raw text elements.\n *\n * Comments are not parsed within raw text elements, so we need to search their\n * text content for marker strings.\n */\nconst rawTextElement = /^(?:script|style|textarea|title)$/i;\n/** TemplateResult types */\nconst HTML_RESULT = 1;\nconst SVG_RESULT = 2;\n// TemplatePart types\n// IMPORTANT: these must match the values in PartType\nconst ATTRIBUTE_PART = 1;\nconst CHILD_PART = 2;\nconst PROPERTY_PART = 3;\nconst BOOLEAN_ATTRIBUTE_PART = 4;\nconst EVENT_PART = 5;\nconst ELEMENT_PART = 6;\nconst COMMENT_PART = 7;\n/**\n * Generates a template literal tag function that returns a TemplateResult with\n * the given result type.\n */\nconst tag = (type) => (strings, ...values) => {\n    // Warn against templates octal escape sequences\n    // We do this here rather than in render so that the warning is closer to the\n    // template definition.\n    if (DEV_MODE && strings.some((s) => s === undefined)) {\n        console.warn('Some template strings are undefined.\\n' +\n            'This is probably caused by illegal octal escape sequences.');\n    }\n    return {\n        // This property needs to remain unminified.\n        ['_$litType$']: type,\n        strings,\n        values,\n    };\n};\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const header = (title: string) => html`<h1>${title}</h1>`;\n * ```\n *\n * The `html` tag returns a description of the DOM to render as a value. It is\n * lazy, meaning no work is done until the template is rendered. When rendering,\n * if a template comes from the same expression as a previously rendered result,\n * it's efficiently updated instead of replaced.\n */\nconst html = tag(HTML_RESULT);\n/**\n * Interprets a template literal as an SVG fragment that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const rect = svg`<rect width=\"10\" height=\"10\"></rect>`;\n *\n * const myImage = html`\n *   <svg viewBox=\"0 0 10 10\" xmlns=\"http://www.w3.org/2000/svg\">\n *     ${rect}\n *   </svg>`;\n * ```\n *\n * The `svg` *tag function* should only be used for SVG fragments, or elements\n * that would be contained **inside** an `<svg>` HTML element. A common error is\n * placing an `<svg>` *element* in a template tagged with the `svg` tag\n * function. The `<svg>` element is an HTML element and should be used within a\n * template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an SVG fragment from the\n * `render()` method, as the SVG fragment will be contained within the element's\n * shadow root and thus cannot be used within an `<svg>` HTML element.\n */\nconst svg = tag(SVG_RESULT);\n/**\n * A sentinel value that signals that a value was handled by a directive and\n * should not be written to the DOM.\n */\nconst noChange = Symbol.for('lit-noChange');\n/**\n * A sentinel value that signals a ChildPart to fully clear its content.\n *\n * ```ts\n * const button = html`${\n *  user.isAdmin\n *    ? html`<button>DELETE</button>`\n *    : nothing\n * }`;\n * ```\n *\n * Prefer using `nothing` over other falsy values as it provides a consistent\n * behavior between various expression binding contexts.\n *\n * In child expressions, `undefined`, `null`, `''`, and `nothing` all behave the\n * same and render no nodes. In attribute expressions, `nothing` _removes_ the\n * attribute, while `undefined` and `null` will render an empty string. In\n * property expressions `nothing` becomes `undefined`.\n */\nconst nothing = Symbol.for('lit-nothing');\n/**\n * The cache of prepared templates, keyed by the tagged TemplateStringsArray\n * and _not_ accounting for the specific template tag used. This means that\n * template tags cannot be dynamic - the must statically be one of html, svg,\n * or attr. This restriction simplifies the cache lookup, which is on the hot\n * path for rendering.\n */\nconst templateCache = new WeakMap();\nconst walker = d.createTreeWalker(d, 129 /* NodeFilter.SHOW_{ELEMENT|COMMENT} */, null, false);\nlet sanitizerFactoryInternal = noopSanitizer;\nfunction trustFromTemplateString(tsa, stringFromTSA) {\n    // A security check to prevent spoofing of Lit template results.\n    // In the future, we may be able to replace this with Array.isTemplateObject,\n    // though we might need to make that check inside of the html and svg\n    // functions, because precompiled templates don't come in as\n    // TemplateStringArray objects.\n    if (!Array.isArray(tsa) || !tsa.hasOwnProperty('raw')) {\n        let message = 'invalid template strings array';\n        if (DEV_MODE) {\n            message = `\n          Internal Error: expected template strings to be an array\n          with a 'raw' field. Faking a template strings array by\n          calling html or svg like an ordinary function is effectively\n          the same as calling unsafeHtml and can lead to major security\n          issues, e.g. opening your code up to XSS attacks.\n          If you're using the html or svg tagged template functions normally\n          and still seeing this error, please file a bug at\n          https://github.com/lit/lit/issues/new?template=bug_report.md\n          and include information about your build tooling, if any.\n        `\n                .trim()\n                .replace(/\\n */g, '\\n');\n        }\n        throw new Error(message);\n    }\n    return policy !== undefined\n        ? policy.createHTML(stringFromTSA)\n        : stringFromTSA;\n}\n/**\n * Returns an HTML string for the given TemplateStringsArray and result type\n * (HTML or SVG), along with the case-sensitive bound attribute names in\n * template order. The HTML contains comment markers denoting the `ChildPart`s\n * and suffixes on bound attributes denoting the `AttributeParts`.\n *\n * @param strings template strings array\n * @param type HTML or SVG\n * @return Array containing `[html, attrNames]` (array returned for terseness,\n *     to avoid object fields since this code is shared with non-minified SSR\n *     code)\n */\nconst getTemplateHtml = (strings, type) => {\n    // Insert makers into the template HTML to represent the position of\n    // bindings. The following code scans the template strings to determine the\n    // syntactic position of the bindings. They can be in text position, where\n    // we insert an HTML comment, attribute value position, where we insert a\n    // sentinel string and re-write the attribute name, or inside a tag where\n    // we insert the sentinel string.\n    const l = strings.length - 1;\n    // Stores the case-sensitive bound attribute names in the order of their\n    // parts. ElementParts are also reflected in this array as undefined\n    // rather than a string, to disambiguate from attribute bindings.\n    const attrNames = [];\n    let html = type === SVG_RESULT ? '<svg>' : '';\n    // When we're inside a raw text tag (not it's text content), the regex\n    // will still be tagRegex so we can find attributes, but will switch to\n    // this regex when the tag ends.\n    let rawTextEndRegex;\n    // The current parsing state, represented as a reference to one of the\n    // regexes\n    let regex = textEndRegex;\n    for (let i = 0; i < l; i++) {\n        const s = strings[i];\n        // The index of the end of the last attribute name. When this is\n        // positive at end of a string, it means we're in an attribute value\n        // position and need to rewrite the attribute name.\n        // We also use a special value of -2 to indicate that we encountered\n        // the end of a string in attribute name position.\n        let attrNameEndIndex = -1;\n        let attrName;\n        let lastIndex = 0;\n        let match;\n        // The conditions in this loop handle the current parse state, and the\n        // assignments to the `regex` variable are the state transitions.\n        while (lastIndex < s.length) {\n            // Make sure we start searching from where we previously left off\n            regex.lastIndex = lastIndex;\n            match = regex.exec(s);\n            if (match === null) {\n                break;\n            }\n            lastIndex = regex.lastIndex;\n            if (regex === textEndRegex) {\n                if (match[COMMENT_START] === '!--') {\n                    regex = commentEndRegex;\n                }\n                else if (match[COMMENT_START] !== undefined) {\n                    // We started a weird comment, like </{\n                    regex = comment2EndRegex;\n                }\n                else if (match[TAG_NAME] !== undefined) {\n                    if (rawTextElement.test(match[TAG_NAME])) {\n                        // Record if we encounter a raw-text element. We'll switch to\n                        // this regex at the end of the tag.\n                        rawTextEndRegex = new RegExp(`</${match[TAG_NAME]}`, 'g');\n                    }\n                    regex = tagEndRegex;\n                }\n                else if (match[DYNAMIC_TAG_NAME] !== undefined) {\n                    if (DEV_MODE) {\n                        throw new Error('Bindings in tag names are not supported. Please use static templates instead. ' +\n                            'See https://lit.dev/docs/templates/expressions/#static-expressions');\n                    }\n                    regex = tagEndRegex;\n                }\n            }\n            else if (regex === tagEndRegex) {\n                if (match[ENTIRE_MATCH] === '>') {\n                    // End of a tag. If we had started a raw-text element, use that\n                    // regex\n                    regex = rawTextEndRegex !== null && rawTextEndRegex !== void 0 ? rawTextEndRegex : textEndRegex;\n                    // We may be ending an unquoted attribute value, so make sure we\n                    // clear any pending attrNameEndIndex\n                    attrNameEndIndex = -1;\n                }\n                else if (match[ATTRIBUTE_NAME] === undefined) {\n                    // Attribute name position\n                    attrNameEndIndex = -2;\n                }\n                else {\n                    attrNameEndIndex = regex.lastIndex - match[SPACES_AND_EQUALS].length;\n                    attrName = match[ATTRIBUTE_NAME];\n                    regex =\n                        match[QUOTE_CHAR] === undefined\n                            ? tagEndRegex\n                            : match[QUOTE_CHAR] === '\"'\n                                ? doubleQuoteAttrEndRegex\n                                : singleQuoteAttrEndRegex;\n                }\n            }\n            else if (regex === doubleQuoteAttrEndRegex ||\n                regex === singleQuoteAttrEndRegex) {\n                regex = tagEndRegex;\n            }\n            else if (regex === commentEndRegex || regex === comment2EndRegex) {\n                regex = textEndRegex;\n            }\n            else {\n                // Not one of the five state regexes, so it must be the dynamically\n                // created raw text regex and we're at the close of that element.\n                regex = tagEndRegex;\n                rawTextEndRegex = undefined;\n            }\n        }\n        if (DEV_MODE) {\n            // If we have a attrNameEndIndex, which indicates that we should\n            // rewrite the attribute name, assert that we're in a valid attribute\n            // position - either in a tag, or a quoted attribute value.\n            console.assert(attrNameEndIndex === -1 ||\n                regex === tagEndRegex ||\n                regex === singleQuoteAttrEndRegex ||\n                regex === doubleQuoteAttrEndRegex, 'unexpected parse state B');\n        }\n        // We have four cases:\n        //  1. We're in text position, and not in a raw text element\n        //     (regex === textEndRegex): insert a comment marker.\n        //  2. We have a non-negative attrNameEndIndex which means we need to\n        //     rewrite the attribute name to add a bound attribute suffix.\n        //  3. We're at the non-first binding in a multi-binding attribute, use a\n        //     plain marker.\n        //  4. We're somewhere else inside the tag. If we're in attribute name\n        //     position (attrNameEndIndex === -2), add a sequential suffix to\n        //     generate a unique attribute name.\n        // Detect a binding next to self-closing tag end and insert a space to\n        // separate the marker from the tag end:\n        const end = regex === tagEndRegex && strings[i + 1].startsWith('/>') ? ' ' : '';\n        html +=\n            regex === textEndRegex\n                ? s + nodeMarker\n                : attrNameEndIndex >= 0\n                    ? (attrNames.push(attrName),\n                        s.slice(0, attrNameEndIndex) +\n                            boundAttributeSuffix +\n                            s.slice(attrNameEndIndex)) +\n                        marker +\n                        end\n                    : s +\n                        marker +\n                        (attrNameEndIndex === -2 ? (attrNames.push(undefined), i) : end);\n    }\n    const htmlResult = html + (strings[l] || '<?>') + (type === SVG_RESULT ? '</svg>' : '');\n    // Returned as an array for terseness\n    return [trustFromTemplateString(strings, htmlResult), attrNames];\n};\nclass Template {\n    constructor(\n    // This property needs to remain unminified.\n    { strings, ['_$litType$']: type }, options) {\n        this.parts = [];\n        let node;\n        let nodeIndex = 0;\n        let attrNameIndex = 0;\n        const partCount = strings.length - 1;\n        const parts = this.parts;\n        // Create template element\n        const [html, attrNames] = getTemplateHtml(strings, type);\n        this.el = Template.createElement(html, options);\n        walker.currentNode = this.el.content;\n        // Reparent SVG nodes into template root\n        if (type === SVG_RESULT) {\n            const content = this.el.content;\n            const svgElement = content.firstChild;\n            svgElement.remove();\n            content.append(...svgElement.childNodes);\n        }\n        // Walk the template to find binding markers and create TemplateParts\n        while ((node = walker.nextNode()) !== null && parts.length < partCount) {\n            if (node.nodeType === 1) {\n                if (DEV_MODE) {\n                    const tag = node.localName;\n                    // Warn if `textarea` includes an expression and throw if `template`\n                    // does since these are not supported. We do this by checking\n                    // innerHTML for anything that looks like a marker. This catches\n                    // cases like bindings in textarea there markers turn into text nodes.\n                    if (/^(?:textarea|template)$/i.test(tag) &&\n                        node.innerHTML.includes(marker)) {\n                        const m = `Expressions are not supported inside \\`${tag}\\` ` +\n                            `elements. See https://lit.dev/msg/expression-in-${tag} for more ` +\n                            `information.`;\n                        if (tag === 'template') {\n                            throw new Error(m);\n                        }\n                        else\n                            issueWarning('', m);\n                    }\n                }\n                // TODO (justinfagnani): for attempted dynamic tag names, we don't\n                // increment the bindingIndex, and it'll be off by 1 in the element\n                // and off by two after it.\n                if (node.hasAttributes()) {\n                    // We defer removing bound attributes because on IE we might not be\n                    // iterating attributes in their template order, and would sometimes\n                    // remove an attribute that we still need to create a part for.\n                    const attrsToRemove = [];\n                    for (const name of node.getAttributeNames()) {\n                        // `name` is the name of the attribute we're iterating over, but not\n                        // _necessarily_ the name of the attribute we will create a part\n                        // for. They can be different in browsers that don't iterate on\n                        // attributes in source order. In that case the attrNames array\n                        // contains the attribute name we'll process next. We only need the\n                        // attribute name here to know if we should process a bound attribute\n                        // on this element.\n                        if (name.endsWith(boundAttributeSuffix) ||\n                            name.startsWith(marker)) {\n                            const realName = attrNames[attrNameIndex++];\n                            attrsToRemove.push(name);\n                            if (realName !== undefined) {\n                                // Lowercase for case-sensitive SVG attributes like viewBox\n                                const value = node.getAttribute(realName.toLowerCase() + boundAttributeSuffix);\n                                const statics = value.split(marker);\n                                const m = /([.?@])?(.*)/.exec(realName);\n                                parts.push({\n                                    type: ATTRIBUTE_PART,\n                                    index: nodeIndex,\n                                    name: m[2],\n                                    strings: statics,\n                                    ctor: m[1] === '.'\n                                        ? PropertyPart\n                                        : m[1] === '?'\n                                            ? BooleanAttributePart\n                                            : m[1] === '@'\n                                                ? EventPart\n                                                : AttributePart,\n                                });\n                            }\n                            else {\n                                parts.push({\n                                    type: ELEMENT_PART,\n                                    index: nodeIndex,\n                                });\n                            }\n                        }\n                    }\n                    for (const name of attrsToRemove) {\n                        node.removeAttribute(name);\n                    }\n                }\n                // TODO (justinfagnani): benchmark the regex against testing for each\n                // of the 3 raw text element names.\n                if (rawTextElement.test(node.tagName)) {\n                    // For raw text elements we need to split the text content on\n                    // markers, create a Text node for each segment, and create\n                    // a TemplatePart for each marker.\n                    const strings = node.textContent.split(marker);\n                    const lastIndex = strings.length - 1;\n                    if (lastIndex > 0) {\n                        node.textContent = trustedTypes\n                            ? trustedTypes.emptyScript\n                            : '';\n                        // Generate a new text node for each literal section\n                        // These nodes are also used as the markers for node parts\n                        // We can't use empty text nodes as markers because they're\n                        // normalized when cloning in IE (could simplify when\n                        // IE is no longer supported)\n                        for (let i = 0; i < lastIndex; i++) {\n                            node.append(strings[i], createMarker());\n                            // Walk past the marker node we just added\n                            walker.nextNode();\n                            parts.push({ type: CHILD_PART, index: ++nodeIndex });\n                        }\n                        // Note because this marker is added after the walker's current\n                        // node, it will be walked to in the outer loop (and ignored), so\n                        // we don't need to adjust nodeIndex here\n                        node.append(strings[lastIndex], createMarker());\n                    }\n                }\n            }\n            else if (node.nodeType === 8) {\n                const data = node.data;\n                if (data === markerMatch) {\n                    parts.push({ type: CHILD_PART, index: nodeIndex });\n                }\n                else {\n                    let i = -1;\n                    while ((i = node.data.indexOf(marker, i + 1)) !== -1) {\n                        // Comment node has a binding marker inside, make an inactive part\n                        // The binding won't work, but subsequent bindings will\n                        parts.push({ type: COMMENT_PART, index: nodeIndex });\n                        // Move to the end of the match\n                        i += marker.length - 1;\n                    }\n                }\n            }\n            nodeIndex++;\n        }\n        // We could set walker.currentNode to another node here to prevent a memory\n        // leak, but every time we prepare a template, we immediately render it\n        // and re-use the walker in new TemplateInstance._clone().\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'template prep',\n            template: this,\n            clonableTemplate: this.el,\n            parts: this.parts,\n            strings,\n        });\n    }\n    // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n    /** @nocollapse */\n    static createElement(html, _options) {\n        const el = d.createElement('template');\n        el.innerHTML = html;\n        return el;\n    }\n}\nfunction resolveDirective(part, value, parent = part, attributeIndex) {\n    var _a, _b, _c;\n    var _d;\n    // Bail early if the value is explicitly noChange. Note, this means any\n    // nested directive is still attached and is not run.\n    if (value === noChange) {\n        return value;\n    }\n    let currentDirective = attributeIndex !== undefined\n        ? (_a = parent.__directives) === null || _a === void 0 ? void 0 : _a[attributeIndex]\n        : parent.__directive;\n    const nextDirectiveConstructor = isPrimitive(value)\n        ? undefined\n        : // This property needs to remain unminified.\n            value['_$litDirective$'];\n    if ((currentDirective === null || currentDirective === void 0 ? void 0 : currentDirective.constructor) !== nextDirectiveConstructor) {\n        // This property needs to remain unminified.\n        (_b = currentDirective === null || currentDirective === void 0 ? void 0 : currentDirective['_$notifyDirectiveConnectionChanged']) === null || _b === void 0 ? void 0 : _b.call(currentDirective, false);\n        if (nextDirectiveConstructor === undefined) {\n            currentDirective = undefined;\n        }\n        else {\n            currentDirective = new nextDirectiveConstructor(part);\n            currentDirective._$initialize(part, parent, attributeIndex);\n        }\n        if (attributeIndex !== undefined) {\n            ((_c = (_d = parent).__directives) !== null && _c !== void 0 ? _c : (_d.__directives = []))[attributeIndex] =\n                currentDirective;\n        }\n        else {\n            parent.__directive = currentDirective;\n        }\n    }\n    if (currentDirective !== undefined) {\n        value = resolveDirective(part, currentDirective._$resolve(part, value.values), currentDirective, attributeIndex);\n    }\n    return value;\n}\n/**\n * An updateable instance of a Template. Holds references to the Parts used to\n * update the template instance.\n */\nclass TemplateInstance {\n    constructor(template, parent) {\n        this._$parts = [];\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this._$template = template;\n        this._$parent = parent;\n    }\n    // Called by ChildPart parentNode getter\n    get parentNode() {\n        return this._$parent.parentNode;\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    // This method is separate from the constructor because we need to return a\n    // DocumentFragment and we don't want to hold onto it with an instance field.\n    _clone(options) {\n        var _a;\n        const { el: { content }, parts: parts, } = this._$template;\n        const fragment = ((_a = options === null || options === void 0 ? void 0 : options.creationScope) !== null && _a !== void 0 ? _a : d).importNode(content, true);\n        walker.currentNode = fragment;\n        let node = walker.nextNode();\n        let nodeIndex = 0;\n        let partIndex = 0;\n        let templatePart = parts[0];\n        while (templatePart !== undefined) {\n            if (nodeIndex === templatePart.index) {\n                let part;\n                if (templatePart.type === CHILD_PART) {\n                    part = new ChildPart(node, node.nextSibling, this, options);\n                }\n                else if (templatePart.type === ATTRIBUTE_PART) {\n                    part = new templatePart.ctor(node, templatePart.name, templatePart.strings, this, options);\n                }\n                else if (templatePart.type === ELEMENT_PART) {\n                    part = new ElementPart(node, this, options);\n                }\n                this._$parts.push(part);\n                templatePart = parts[++partIndex];\n            }\n            if (nodeIndex !== (templatePart === null || templatePart === void 0 ? void 0 : templatePart.index)) {\n                node = walker.nextNode();\n                nodeIndex++;\n            }\n        }\n        // We need to set the currentNode away from the cloned tree so that we\n        // don't hold onto the tree even if the tree is detached and should be\n        // freed.\n        walker.currentNode = d;\n        return fragment;\n    }\n    _update(values) {\n        let i = 0;\n        for (const part of this._$parts) {\n            if (part !== undefined) {\n                debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                    kind: 'set part',\n                    part,\n                    value: values[i],\n                    valueIndex: i,\n                    values,\n                    templateInstance: this,\n                });\n                if (part.strings !== undefined) {\n                    part._$setValue(values, part, i);\n                    // The number of values the part consumes is part.strings.length - 1\n                    // since values are in between template spans. We increment i by 1\n                    // later in the loop, so increment it by part.strings.length - 2 here\n                    i += part.strings.length - 2;\n                }\n                else {\n                    part._$setValue(values[i]);\n                }\n            }\n            i++;\n        }\n    }\n}\nclass ChildPart {\n    constructor(startNode, endNode, parent, options) {\n        var _a;\n        this.type = CHILD_PART;\n        this._$committedValue = nothing;\n        // The following fields will be patched onto ChildParts when required by\n        // AsyncDirective\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this._$startNode = startNode;\n        this._$endNode = endNode;\n        this._$parent = parent;\n        this.options = options;\n        // Note __isConnected is only ever accessed on RootParts (i.e. when there is\n        // no _$parent); the value on a non-root-part is \"don't care\", but checking\n        // for parent would be more code\n        this.__isConnected = (_a = options === null || options === void 0 ? void 0 : options.isConnected) !== null && _a !== void 0 ? _a : true;\n        if (ENABLE_EXTRA_SECURITY_HOOKS) {\n            // Explicitly initialize for consistent class shape.\n            this._textSanitizer = undefined;\n        }\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        var _a, _b;\n        // ChildParts that are not at the root should always be created with a\n        // parent; only RootChildNode's won't, so they return the local isConnected\n        // state\n        return (_b = (_a = this._$parent) === null || _a === void 0 ? void 0 : _a._$isConnected) !== null && _b !== void 0 ? _b : this.__isConnected;\n    }\n    /**\n     * The parent node into which the part renders its content.\n     *\n     * A ChildPart's content consists of a range of adjacent child nodes of\n     * `.parentNode`, possibly bordered by 'marker nodes' (`.startNode` and\n     * `.endNode`).\n     *\n     * - If both `.startNode` and `.endNode` are non-null, then the part's content\n     * consists of all siblings between `.startNode` and `.endNode`, exclusively.\n     *\n     * - If `.startNode` is non-null but `.endNode` is null, then the part's\n     * content consists of all siblings following `.startNode`, up to and\n     * including the last child of `.parentNode`. If `.endNode` is non-null, then\n     * `.startNode` will always be non-null.\n     *\n     * - If both `.endNode` and `.startNode` are null, then the part's content\n     * consists of all child nodes of `.parentNode`.\n     */\n    get parentNode() {\n        let parentNode = wrap(this._$startNode).parentNode;\n        const parent = this._$parent;\n        if (parent !== undefined &&\n            (parentNode === null || parentNode === void 0 ? void 0 : parentNode.nodeType) === 11 /* Node.DOCUMENT_FRAGMENT */) {\n            // If the parentNode is a DocumentFragment, it may be because the DOM is\n            // still in the cloned fragment during initial render; if so, get the real\n            // parentNode the part will be committed into by asking the parent.\n            parentNode = parent.parentNode;\n        }\n        return parentNode;\n    }\n    /**\n     * The part's leading marker node, if any. See `.parentNode` for more\n     * information.\n     */\n    get startNode() {\n        return this._$startNode;\n    }\n    /**\n     * The part's trailing marker node, if any. See `.parentNode` for more\n     * information.\n     */\n    get endNode() {\n        return this._$endNode;\n    }\n    _$setValue(value, directiveParent = this) {\n        var _a;\n        if (DEV_MODE && this.parentNode === null) {\n            throw new Error(`This \\`ChildPart\\` has no \\`parentNode\\` and therefore cannot accept a value. This likely means the element containing the part was manipulated in an unsupported way outside of Lit's control such that the part's marker nodes were ejected from DOM. For example, setting the element's \\`innerHTML\\` or \\`textContent\\` can do this.`);\n        }\n        value = resolveDirective(this, value, directiveParent);\n        if (isPrimitive(value)) {\n            // Non-rendering child values. It's important that these do not render\n            // empty text nodes to avoid issues with preventing default <slot>\n            // fallback content.\n            if (value === nothing || value == null || value === '') {\n                if (this._$committedValue !== nothing) {\n                    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                        kind: 'commit nothing to child',\n                        start: this._$startNode,\n                        end: this._$endNode,\n                        parent: this._$parent,\n                        options: this.options,\n                    });\n                    this._$clear();\n                }\n                this._$committedValue = nothing;\n            }\n            else if (value !== this._$committedValue && value !== noChange) {\n                this._commitText(value);\n            }\n            // This property needs to remain unminified.\n        }\n        else if (value['_$litType$'] !== undefined) {\n            this._commitTemplateResult(value);\n        }\n        else if (value.nodeType !== undefined) {\n            if (DEV_MODE && ((_a = this.options) === null || _a === void 0 ? void 0 : _a.host) === value) {\n                this._commitText(`[probable mistake: rendered a template's host in itself ` +\n                    `(commonly caused by writing \\${this} in a template]`);\n                console.warn(`Attempted to render the template host`, value, `inside itself. This is almost always a mistake, and in dev mode `, `we render some warning text. In production however, we'll `, `render it, which will usually result in an error, and sometimes `, `in the element disappearing from the DOM.`);\n                return;\n            }\n            this._commitNode(value);\n        }\n        else if (isIterable(value)) {\n            this._commitIterable(value);\n        }\n        else {\n            // Fallback, will render the string representation\n            this._commitText(value);\n        }\n    }\n    _insert(node) {\n        return wrap(wrap(this._$startNode).parentNode).insertBefore(node, this._$endNode);\n    }\n    _commitNode(value) {\n        var _a;\n        if (this._$committedValue !== value) {\n            this._$clear();\n            if (ENABLE_EXTRA_SECURITY_HOOKS &&\n                sanitizerFactoryInternal !== noopSanitizer) {\n                const parentNodeName = (_a = this._$startNode.parentNode) === null || _a === void 0 ? void 0 : _a.nodeName;\n                if (parentNodeName === 'STYLE' || parentNodeName === 'SCRIPT') {\n                    let message = 'Forbidden';\n                    if (DEV_MODE) {\n                        if (parentNodeName === 'STYLE') {\n                            message =\n                                `Lit does not support binding inside style nodes. ` +\n                                    `This is a security risk, as style injection attacks can ` +\n                                    `exfiltrate data and spoof UIs. ` +\n                                    `Consider instead using css\\`...\\` literals ` +\n                                    `to compose styles, and make do dynamic styling with ` +\n                                    `css custom properties, ::parts, <slot>s, ` +\n                                    `and by mutating the DOM rather than stylesheets.`;\n                        }\n                        else {\n                            message =\n                                `Lit does not support binding inside script nodes. ` +\n                                    `This is a security risk, as it could allow arbitrary ` +\n                                    `code execution.`;\n                        }\n                    }\n                    throw new Error(message);\n                }\n            }\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'commit node',\n                start: this._$startNode,\n                parent: this._$parent,\n                value: value,\n                options: this.options,\n            });\n            this._$committedValue = this._insert(value);\n        }\n    }\n    _commitText(value) {\n        // If the committed value is a primitive it means we called _commitText on\n        // the previous render, and we know that this._$startNode.nextSibling is a\n        // Text node. We can now just replace the text content (.data) of the node.\n        if (this._$committedValue !== nothing &&\n            isPrimitive(this._$committedValue)) {\n            const node = wrap(this._$startNode).nextSibling;\n            if (ENABLE_EXTRA_SECURITY_HOOKS) {\n                if (this._textSanitizer === undefined) {\n                    this._textSanitizer = createSanitizer(node, 'data', 'property');\n                }\n                value = this._textSanitizer(value);\n            }\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'commit text',\n                node,\n                value,\n                options: this.options,\n            });\n            node.data = value;\n        }\n        else {\n            if (ENABLE_EXTRA_SECURITY_HOOKS) {\n                const textNode = d.createTextNode('');\n                this._commitNode(textNode);\n                // When setting text content, for security purposes it matters a lot\n                // what the parent is. For example, <style> and <script> need to be\n                // handled with care, while <span> does not. So first we need to put a\n                // text node into the document, then we can sanitize its content.\n                if (this._textSanitizer === undefined) {\n                    this._textSanitizer = createSanitizer(textNode, 'data', 'property');\n                }\n                value = this._textSanitizer(value);\n                debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                    kind: 'commit text',\n                    node: textNode,\n                    value,\n                    options: this.options,\n                });\n                textNode.data = value;\n            }\n            else {\n                this._commitNode(d.createTextNode(value));\n                debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                    kind: 'commit text',\n                    node: wrap(this._$startNode).nextSibling,\n                    value,\n                    options: this.options,\n                });\n            }\n        }\n        this._$committedValue = value;\n    }\n    _commitTemplateResult(result) {\n        var _a;\n        // This property needs to remain unminified.\n        const { values, ['_$litType$']: type } = result;\n        // If $litType$ is a number, result is a plain TemplateResult and we get\n        // the template from the template cache. If not, result is a\n        // CompiledTemplateResult and _$litType$ is a CompiledTemplate and we need\n        // to create the <template> element the first time we see it.\n        const template = typeof type === 'number'\n            ? this._$getTemplate(result)\n            : (type.el === undefined &&\n                (type.el = Template.createElement(trustFromTemplateString(type.h, type.h[0]), this.options)),\n                type);\n        if (((_a = this._$committedValue) === null || _a === void 0 ? void 0 : _a._$template) === template) {\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'template updating',\n                template,\n                instance: this._$committedValue,\n                parts: this._$committedValue._$parts,\n                options: this.options,\n                values,\n            });\n            this._$committedValue._update(values);\n        }\n        else {\n            const instance = new TemplateInstance(template, this);\n            const fragment = instance._clone(this.options);\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'template instantiated',\n                template,\n                instance,\n                parts: instance._$parts,\n                options: this.options,\n                fragment,\n                values,\n            });\n            instance._update(values);\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'template instantiated and updated',\n                template,\n                instance,\n                parts: instance._$parts,\n                options: this.options,\n                fragment,\n                values,\n            });\n            this._commitNode(fragment);\n            this._$committedValue = instance;\n        }\n    }\n    // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n    /** @internal */\n    _$getTemplate(result) {\n        let template = templateCache.get(result.strings);\n        if (template === undefined) {\n            templateCache.set(result.strings, (template = new Template(result)));\n        }\n        return template;\n    }\n    _commitIterable(value) {\n        // For an Iterable, we create a new InstancePart per item, then set its\n        // value to the item. This is a little bit of overhead for every item in\n        // an Iterable, but it lets us recurse easily and efficiently update Arrays\n        // of TemplateResults that will be commonly returned from expressions like:\n        // array.map((i) => html`${i}`), by reusing existing TemplateInstances.\n        // If value is an array, then the previous render was of an\n        // iterable and value will contain the ChildParts from the previous\n        // render. If value is not an array, clear this part and make a new\n        // array for ChildParts.\n        if (!isArray(this._$committedValue)) {\n            this._$committedValue = [];\n            this._$clear();\n        }\n        // Lets us keep track of how many items we stamped so we can clear leftover\n        // items from a previous render\n        const itemParts = this._$committedValue;\n        let partIndex = 0;\n        let itemPart;\n        for (const item of value) {\n            if (partIndex === itemParts.length) {\n                // If no existing part, create a new one\n                // TODO (justinfagnani): test perf impact of always creating two parts\n                // instead of sharing parts between nodes\n                // https://github.com/lit/lit/issues/1266\n                itemParts.push((itemPart = new ChildPart(this._insert(createMarker()), this._insert(createMarker()), this, this.options)));\n            }\n            else {\n                // Reuse an existing part\n                itemPart = itemParts[partIndex];\n            }\n            itemPart._$setValue(item);\n            partIndex++;\n        }\n        if (partIndex < itemParts.length) {\n            // itemParts always have end nodes\n            this._$clear(itemPart && wrap(itemPart._$endNode).nextSibling, partIndex);\n            // Truncate the parts array so _value reflects the current state\n            itemParts.length = partIndex;\n        }\n    }\n    /**\n     * Removes the nodes contained within this Part from the DOM.\n     *\n     * @param start Start node to clear from, for clearing a subset of the part's\n     *     DOM (used when truncating iterables)\n     * @param from  When `start` is specified, the index within the iterable from\n     *     which ChildParts are being removed, used for disconnecting directives in\n     *     those Parts.\n     *\n     * @internal\n     */\n    _$clear(start = wrap(this._$startNode).nextSibling, from) {\n        var _a;\n        (_a = this._$notifyConnectionChanged) === null || _a === void 0 ? void 0 : _a.call(this, false, true, from);\n        while (start && start !== this._$endNode) {\n            const n = wrap(start).nextSibling;\n            wrap(start).remove();\n            start = n;\n        }\n    }\n    /**\n     * Implementation of RootPart's `isConnected`. Note that this metod\n     * should only be called on `RootPart`s (the `ChildPart` returned from a\n     * top-level `render()` call). It has no effect on non-root ChildParts.\n     * @param isConnected Whether to set\n     * @internal\n     */\n    setConnected(isConnected) {\n        var _a;\n        if (this._$parent === undefined) {\n            this.__isConnected = isConnected;\n            (_a = this._$notifyConnectionChanged) === null || _a === void 0 ? void 0 : _a.call(this, isConnected);\n        }\n        else if (DEV_MODE) {\n            throw new Error('part.setConnected() may only be called on a ' +\n                'RootPart returned from render().');\n        }\n    }\n}\nclass AttributePart {\n    constructor(element, name, strings, parent, options) {\n        this.type = ATTRIBUTE_PART;\n        /** @internal */\n        this._$committedValue = nothing;\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this.element = element;\n        this.name = name;\n        this._$parent = parent;\n        this.options = options;\n        if (strings.length > 2 || strings[0] !== '' || strings[1] !== '') {\n            this._$committedValue = new Array(strings.length - 1).fill(new String());\n            this.strings = strings;\n        }\n        else {\n            this._$committedValue = nothing;\n        }\n        if (ENABLE_EXTRA_SECURITY_HOOKS) {\n            this._sanitizer = undefined;\n        }\n    }\n    get tagName() {\n        return this.element.tagName;\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    /**\n     * Sets the value of this part by resolving the value from possibly multiple\n     * values and static strings and committing it to the DOM.\n     * If this part is single-valued, `this._strings` will be undefined, and the\n     * method will be called with a single value argument. If this part is\n     * multi-value, `this._strings` will be defined, and the method is called\n     * with the value array of the part's owning TemplateInstance, and an offset\n     * into the value array from which the values should be read.\n     * This method is overloaded this way to eliminate short-lived array slices\n     * of the template instance values, and allow a fast-path for single-valued\n     * parts.\n     *\n     * @param value The part value, or an array of values for multi-valued parts\n     * @param valueIndex the index to start reading values from. `undefined` for\n     *   single-valued parts\n     * @param noCommit causes the part to not commit its value to the DOM. Used\n     *   in hydration to prime attribute parts with their first-rendered value,\n     *   but not set the attribute, and in SSR to no-op the DOM operation and\n     *   capture the value for serialization.\n     *\n     * @internal\n     */\n    _$setValue(value, directiveParent = this, valueIndex, noCommit) {\n        const strings = this.strings;\n        // Whether any of the values has changed, for dirty-checking\n        let change = false;\n        if (strings === undefined) {\n            // Single-value binding case\n            value = resolveDirective(this, value, directiveParent, 0);\n            change =\n                !isPrimitive(value) ||\n                    (value !== this._$committedValue && value !== noChange);\n            if (change) {\n                this._$committedValue = value;\n            }\n        }\n        else {\n            // Interpolation case\n            const values = value;\n            value = strings[0];\n            let i, v;\n            for (i = 0; i < strings.length - 1; i++) {\n                v = resolveDirective(this, values[valueIndex + i], directiveParent, i);\n                if (v === noChange) {\n                    // If the user-provided value is `noChange`, use the previous value\n                    v = this._$committedValue[i];\n                }\n                change || (change = !isPrimitive(v) || v !== this._$committedValue[i]);\n                if (v === nothing) {\n                    value = nothing;\n                }\n                else if (value !== nothing) {\n                    value += (v !== null && v !== void 0 ? v : '') + strings[i + 1];\n                }\n                // We always record each value, even if one is `nothing`, for future\n                // change detection.\n                this._$committedValue[i] = v;\n            }\n        }\n        if (change && !noCommit) {\n            this._commitValue(value);\n        }\n    }\n    /** @internal */\n    _commitValue(value) {\n        if (value === nothing) {\n            wrap(this.element).removeAttribute(this.name);\n        }\n        else {\n            if (ENABLE_EXTRA_SECURITY_HOOKS) {\n                if (this._sanitizer === undefined) {\n                    this._sanitizer = sanitizerFactoryInternal(this.element, this.name, 'attribute');\n                }\n                value = this._sanitizer(value !== null && value !== void 0 ? value : '');\n            }\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'commit attribute',\n                element: this.element,\n                name: this.name,\n                value,\n                options: this.options,\n            });\n            wrap(this.element).setAttribute(this.name, (value !== null && value !== void 0 ? value : ''));\n        }\n    }\n}\nclass PropertyPart extends AttributePart {\n    constructor() {\n        super(...arguments);\n        this.type = PROPERTY_PART;\n    }\n    /** @internal */\n    _commitValue(value) {\n        if (ENABLE_EXTRA_SECURITY_HOOKS) {\n            if (this._sanitizer === undefined) {\n                this._sanitizer = sanitizerFactoryInternal(this.element, this.name, 'property');\n            }\n            value = this._sanitizer(value);\n        }\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit property',\n            element: this.element,\n            name: this.name,\n            value,\n            options: this.options,\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.element[this.name] = value === nothing ? undefined : value;\n    }\n}\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n    ? trustedTypes.emptyScript\n    : '';\nclass BooleanAttributePart extends AttributePart {\n    constructor() {\n        super(...arguments);\n        this.type = BOOLEAN_ATTRIBUTE_PART;\n    }\n    /** @internal */\n    _commitValue(value) {\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit boolean attribute',\n            element: this.element,\n            name: this.name,\n            value: !!(value && value !== nothing),\n            options: this.options,\n        });\n        if (value && value !== nothing) {\n            wrap(this.element).setAttribute(this.name, emptyStringForBooleanAttribute);\n        }\n        else {\n            wrap(this.element).removeAttribute(this.name);\n        }\n    }\n}\nclass EventPart extends AttributePart {\n    constructor(element, name, strings, parent, options) {\n        super(element, name, strings, parent, options);\n        this.type = EVENT_PART;\n        if (DEV_MODE && this.strings !== undefined) {\n            throw new Error(`A \\`<${element.localName}>\\` has a \\`@${name}=...\\` listener with ` +\n                'invalid content. Event listeners in templates must have exactly ' +\n                'one expression and no surrounding text.');\n        }\n    }\n    // EventPart does not use the base _$setValue/_resolveValue implementation\n    // since the dirty checking is more complex\n    /** @internal */\n    _$setValue(newListener, directiveParent = this) {\n        var _a;\n        newListener =\n            (_a = resolveDirective(this, newListener, directiveParent, 0)) !== null && _a !== void 0 ? _a : nothing;\n        if (newListener === noChange) {\n            return;\n        }\n        const oldListener = this._$committedValue;\n        // If the new value is nothing or any options change we have to remove the\n        // part as a listener.\n        const shouldRemoveListener = (newListener === nothing && oldListener !== nothing) ||\n            newListener.capture !==\n                oldListener.capture ||\n            newListener.once !==\n                oldListener.once ||\n            newListener.passive !==\n                oldListener.passive;\n        // If the new value is not nothing and we removed the listener, we have\n        // to add the part as a listener.\n        const shouldAddListener = newListener !== nothing &&\n            (oldListener === nothing || shouldRemoveListener);\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit event listener',\n            element: this.element,\n            name: this.name,\n            value: newListener,\n            options: this.options,\n            removeListener: shouldRemoveListener,\n            addListener: shouldAddListener,\n            oldListener,\n        });\n        if (shouldRemoveListener) {\n            this.element.removeEventListener(this.name, this, oldListener);\n        }\n        if (shouldAddListener) {\n            // Beware: IE11 and Chrome 41 don't like using the listener as the\n            // options object. Figure out how to deal w/ this in IE11 - maybe\n            // patch addEventListener?\n            this.element.addEventListener(this.name, this, newListener);\n        }\n        this._$committedValue = newListener;\n    }\n    handleEvent(event) {\n        var _a, _b;\n        if (typeof this._$committedValue === 'function') {\n            this._$committedValue.call((_b = (_a = this.options) === null || _a === void 0 ? void 0 : _a.host) !== null && _b !== void 0 ? _b : this.element, event);\n        }\n        else {\n            this._$committedValue.handleEvent(event);\n        }\n    }\n}\nclass ElementPart {\n    constructor(element, parent, options) {\n        this.element = element;\n        this.type = ELEMENT_PART;\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this._$parent = parent;\n        this.options = options;\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    _$setValue(value) {\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit to element binding',\n            element: this.element,\n            value,\n            options: this.options,\n        });\n        resolveDirective(this, value);\n    }\n}\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-element, which re-exports all of lit-html.\n *\n * @private\n */\nconst _$LH = {\n    // Used in lit-ssr\n    _boundAttributeSuffix: boundAttributeSuffix,\n    _marker: marker,\n    _markerMatch: markerMatch,\n    _HTML_RESULT: HTML_RESULT,\n    _getTemplateHtml: getTemplateHtml,\n    // Used in tests and private-ssr-support\n    _TemplateInstance: TemplateInstance,\n    _isIterable: isIterable,\n    _resolveDirective: resolveDirective,\n    _ChildPart: ChildPart,\n    _AttributePart: AttributePart,\n    _BooleanAttributePart: BooleanAttributePart,\n    _EventPart: EventPart,\n    _PropertyPart: PropertyPart,\n    _ElementPart: ElementPart,\n};\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n    ? global.litHtmlPolyfillSupportDevMode\n    : global.litHtmlPolyfillSupport;\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport(Template, ChildPart);\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n((_d = global.litHtmlVersions) !== null && _d !== void 0 ? _d : (global.litHtmlVersions = [])).push('2.8.0');\nif (DEV_MODE && global.litHtmlVersions.length > 1) {\n    issueWarning('multiple-versions', `Multiple versions of Lit loaded. ` +\n        `Loading multiple versions is not recommended.`);\n}\n/**\n * Renders a value, usually a lit-html TemplateResult, to the container.\n *\n * This example renders the text \"Hello, Zoe!\" inside a paragraph tag, appending\n * it to the container `document.body`.\n *\n * ```js\n * import {html, render} from 'lit';\n *\n * const name = \"Zoe\";\n * render(html`<p>Hello, ${name}!</p>`, document.body);\n * ```\n *\n * @param value Any [renderable\n *   value](https://lit.dev/docs/templates/expressions/#child-expressions),\n *   typically a {@linkcode TemplateResult} created by evaluating a template tag\n *   like {@linkcode html} or {@linkcode svg}.\n * @param container A DOM container to render to. The first render will append\n *   the rendered value to the container, and subsequent renders will\n *   efficiently update the rendered value if the same result type was\n *   previously rendered there.\n * @param options See {@linkcode RenderOptions} for options documentation.\n * @see\n * {@link https://lit.dev/docs/libraries/standalone-templates/#rendering-lit-html-templates| Rendering Lit HTML Templates}\n */\nconst render = (value, container, options) => {\n    var _a, _b;\n    if (DEV_MODE && container == null) {\n        // Give a clearer error message than\n        //     Uncaught TypeError: Cannot read properties of null (reading\n        //     '_$litPart$')\n        // which reads like an internal Lit error.\n        throw new TypeError(`The container to render into may not be ${container}`);\n    }\n    const renderId = DEV_MODE ? debugLogRenderId++ : 0;\n    const partOwnerNode = (_a = options === null || options === void 0 ? void 0 : options.renderBefore) !== null && _a !== void 0 ? _a : container;\n    // This property needs to remain unminified.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let part = partOwnerNode['_$litPart$'];\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'begin render',\n        id: renderId,\n        value,\n        container,\n        options,\n        part,\n    });\n    if (part === undefined) {\n        const endNode = (_b = options === null || options === void 0 ? void 0 : options.renderBefore) !== null && _b !== void 0 ? _b : null;\n        // This property needs to remain unminified.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        partOwnerNode['_$litPart$'] = part = new ChildPart(container.insertBefore(createMarker(), endNode), endNode, undefined, options !== null && options !== void 0 ? options : {});\n    }\n    part._$setValue(value);\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'end render',\n        id: renderId,\n        value,\n        container,\n        options,\n        part,\n    });\n    return part;\n};\nif (ENABLE_EXTRA_SECURITY_HOOKS) {\n    render.setSanitizer = setSanitizer;\n    render.createSanitizer = createSanitizer;\n    if (DEV_MODE) {\n        render._testOnlyClearSanitizerFactoryDoNotCallOrElse =\n            _testOnlyClearSanitizerFactoryDoNotCallOrElse;\n    }\n}\n//# sourceMappingURL=lit-html.js.map\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit-html/development/lit-html.js?");

/***/ }),

/***/ "./node_modules/lit/decorators.js":
/*!****************************************!*\
  !*** ./node_modules/lit/decorators.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customElement: () => (/* reexport safe */ _lit_reactive_element_decorators_custom_element_js__WEBPACK_IMPORTED_MODULE_0__.customElement),\n/* harmony export */   eventOptions: () => (/* reexport safe */ _lit_reactive_element_decorators_event_options_js__WEBPACK_IMPORTED_MODULE_3__.eventOptions),\n/* harmony export */   property: () => (/* reexport safe */ _lit_reactive_element_decorators_property_js__WEBPACK_IMPORTED_MODULE_1__.property),\n/* harmony export */   query: () => (/* reexport safe */ _lit_reactive_element_decorators_query_js__WEBPACK_IMPORTED_MODULE_4__.query),\n/* harmony export */   queryAll: () => (/* reexport safe */ _lit_reactive_element_decorators_query_all_js__WEBPACK_IMPORTED_MODULE_5__.queryAll),\n/* harmony export */   queryAssignedElements: () => (/* reexport safe */ _lit_reactive_element_decorators_query_assigned_elements_js__WEBPACK_IMPORTED_MODULE_7__.queryAssignedElements),\n/* harmony export */   queryAssignedNodes: () => (/* reexport safe */ _lit_reactive_element_decorators_query_assigned_nodes_js__WEBPACK_IMPORTED_MODULE_8__.queryAssignedNodes),\n/* harmony export */   queryAsync: () => (/* reexport safe */ _lit_reactive_element_decorators_query_async_js__WEBPACK_IMPORTED_MODULE_6__.queryAsync),\n/* harmony export */   state: () => (/* reexport safe */ _lit_reactive_element_decorators_state_js__WEBPACK_IMPORTED_MODULE_2__.state)\n/* harmony export */ });\n/* harmony import */ var _lit_reactive_element_decorators_custom_element_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lit/reactive-element/decorators/custom-element.js */ \"./node_modules/@lit/reactive-element/development/decorators/custom-element.js\");\n/* harmony import */ var _lit_reactive_element_decorators_property_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lit/reactive-element/decorators/property.js */ \"./node_modules/@lit/reactive-element/development/decorators/property.js\");\n/* harmony import */ var _lit_reactive_element_decorators_state_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lit/reactive-element/decorators/state.js */ \"./node_modules/@lit/reactive-element/development/decorators/state.js\");\n/* harmony import */ var _lit_reactive_element_decorators_event_options_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lit/reactive-element/decorators/event-options.js */ \"./node_modules/@lit/reactive-element/development/decorators/event-options.js\");\n/* harmony import */ var _lit_reactive_element_decorators_query_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lit/reactive-element/decorators/query.js */ \"./node_modules/@lit/reactive-element/development/decorators/query.js\");\n/* harmony import */ var _lit_reactive_element_decorators_query_all_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @lit/reactive-element/decorators/query-all.js */ \"./node_modules/@lit/reactive-element/development/decorators/query-all.js\");\n/* harmony import */ var _lit_reactive_element_decorators_query_async_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lit/reactive-element/decorators/query-async.js */ \"./node_modules/@lit/reactive-element/development/decorators/query-async.js\");\n/* harmony import */ var _lit_reactive_element_decorators_query_assigned_elements_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @lit/reactive-element/decorators/query-assigned-elements.js */ \"./node_modules/@lit/reactive-element/development/decorators/query-assigned-elements.js\");\n/* harmony import */ var _lit_reactive_element_decorators_query_assigned_nodes_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lit/reactive-element/decorators/query-assigned-nodes.js */ \"./node_modules/@lit/reactive-element/development/decorators/query-assigned-nodes.js\");\n\n//# sourceMappingURL=decorators.js.map\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit/decorators.js?");

/***/ }),

/***/ "./node_modules/lit/directives/class-map.js":
/*!**************************************************!*\
  !*** ./node_modules/lit/directives/class-map.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   classMap: () => (/* reexport safe */ lit_html_directives_class_map_js__WEBPACK_IMPORTED_MODULE_0__.classMap)\n/* harmony export */ });\n/* harmony import */ var lit_html_directives_class_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit-html/directives/class-map.js */ \"./node_modules/lit-html/development/directives/class-map.js\");\n\n//# sourceMappingURL=class-map.js.map\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit/directives/class-map.js?");

/***/ }),

/***/ "./node_modules/lit/directives/if-defined.js":
/*!***************************************************!*\
  !*** ./node_modules/lit/directives/if-defined.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ifDefined: () => (/* reexport safe */ lit_html_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_0__.ifDefined)\n/* harmony export */ });\n/* harmony import */ var lit_html_directives_if_defined_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit-html/directives/if-defined.js */ \"./node_modules/lit-html/development/directives/if-defined.js\");\n\n//# sourceMappingURL=if-defined.js.map\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit/directives/if-defined.js?");

/***/ }),

/***/ "./node_modules/lit/index.js":
/*!***********************************!*\
  !*** ./node_modules/lit/index.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.CSSResult),\n/* harmony export */   LitElement: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.LitElement),\n/* harmony export */   ReactiveElement: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.ReactiveElement),\n/* harmony export */   UpdatingElement: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.UpdatingElement),\n/* harmony export */   _$LE: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__._$LE),\n/* harmony export */   _$LH: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__._$LH),\n/* harmony export */   adoptStyles: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.adoptStyles),\n/* harmony export */   css: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.css),\n/* harmony export */   defaultConverter: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.defaultConverter),\n/* harmony export */   getCompatibleStyle: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.getCompatibleStyle),\n/* harmony export */   html: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.html),\n/* harmony export */   isServer: () => (/* reexport safe */ lit_html_is_server_js__WEBPACK_IMPORTED_MODULE_3__.isServer),\n/* harmony export */   noChange: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.noChange),\n/* harmony export */   notEqual: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.notEqual),\n/* harmony export */   nothing: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.nothing),\n/* harmony export */   render: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.render),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.supportsAdoptingStyleSheets),\n/* harmony export */   svg: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.svg),\n/* harmony export */   unsafeCSS: () => (/* reexport safe */ lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__.unsafeCSS)\n/* harmony export */ });\n/* harmony import */ var _lit_reactive_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lit/reactive-element */ \"./node_modules/@lit/reactive-element/development/reactive-element.js\");\n/* harmony import */ var lit_html__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lit-html */ \"./node_modules/lit-html/development/lit-html.js\");\n/* harmony import */ var lit_element_lit_element_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lit-element/lit-element.js */ \"./node_modules/lit-element/development/lit-element.js\");\n/* harmony import */ var lit_html_is_server_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lit-html/is-server.js */ \"./node_modules/lit-html/development/is-server.js\");\n\n//# sourceMappingURL=index.js.map\n\n\n//# sourceURL=webpack://fula-webui/./node_modules/lit/index.js?");

/***/ })

}]);